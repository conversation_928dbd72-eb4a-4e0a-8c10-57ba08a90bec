from datetime import datetime, timezone
from decimal import Decimal

import pytest
from model_bakery import baker

from core.models_grupo import Grupo, GrupoClasse
from core.service.remanejamento.solver.solver_svc import get_grupo_rotina_par
from optools.forms import RemanejamentoSolverModel, SolverBooking


def test_add_all_bookings_reais(cenario_0_sem_dummies):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)
    bookings_reais = [booking for booking in solver_model.all_bookings.values() if booking.dummy is False]
    assert len(bookings_reais) == len(cenario_0_sem_dummies.travels)


def test_add_all_trips(cenario_0_sem_dummies):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)
    assert len(solver_model.all_trips) == len(cenario_0_sem_dummies.trechos)


def test_add_all_groups(cenario_0_sem_dummies):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_0_sem_dummies)
    assert len(solver_model.all_chartereds) == len(cenario_0_sem_dummies.grupos)


def test_custo_remanejamento_dummy(cenario_3):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_3)
    for booking in solver_model.all_bookings.values():
        if booking.dummy:
            assert set(booking.remanejamento.values()) != {0}


def test_receita_dummies_sem_buckets(cenario_3):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_3)
    for booking in solver_model.all_bookings.values():
        if booking.dummy:
            assert booking.receita == solver_model.all_trips[booking.trecho_classe_id].preco_atual


@pytest.mark.parametrize(
    "trecho_classe_id, receitas_esperadas",
    [
        (0, [Decimal("89.90"), Decimal("89.90"), Decimal("99.90"), Decimal("99.90"), Decimal("99.90")]),
        (
            1,
            [
                Decimal("79.90"),
                Decimal("89.90"),
                Decimal("89.90"),
                Decimal("99.90"),
                Decimal("99.90"),
                Decimal("99.90"),
            ],
        ),
    ],
    ids=["trecho com 1 pessoa", "trecho sem pessoas"],
)
def test_receita_dummies_com_buckets(cenario_3_com_buckets, trecho_classe_id, receitas_esperadas):
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_3_com_buckets)
    dummies = [b for b in solver_model.all_bookings.values() if b.dummy and b.trecho_classe_id == trecho_classe_id]
    for idx, dummy in enumerate(dummies):
        assert dummy.receita == receitas_esperadas[idx]


def test_receita_dummies_com_buckets_cheios(cenario_8_com_buckets):
    trecho_classe_id = 0
    receitas_esperadas = [Decimal("99.90"), Decimal("99.90"), Decimal("99.90"), Decimal("99.90"), Decimal("99.90")]
    solver_model = RemanejamentoSolverModel.from_solver_input_form(cenario_8_com_buckets)
    dummies = [b for b in solver_model.all_bookings.values() if b.dummy and b.trecho_classe_id == trecho_classe_id]
    for idx, dummy in enumerate(dummies):
        assert dummy.receita == receitas_esperadas[idx]


@pytest.fixture
def mock_rotina():
    return baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7")


@pytest.fixture
def mock_grupo(mock_rotina):
    return baker.make("core.Grupo", rotina=mock_rotina)


@pytest.fixture
def mock_grupo_classe(mock_grupo):
    return baker.make("core.grupoclasse", tipo_assento="leito", grupo=mock_grupo)


def test_get_grupo_rotina_par_sucesso(rota_sao_bhz, rota_bhz_sao):
    """Deve retornar mapeamento correto quando grupos são compatíveis."""

    rotina_1 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7")
    rotina_2 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7", rotina_par=rotina_1)
    rotina_1.rotina_par_id = rotina_2.id
    rotina_1.save()

    grupo_ida_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 27, 12, tzinfo=timezone.utc),
    )
    grupo_volta_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 28, 12, tzinfo=timezone.utc),
    )

    grupo_ida_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 27, 12, tzinfo=timezone.utc),
    )
    grupo_volta_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 28, 12, tzinfo=timezone.utc),
    )
    gcs = [
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r2),
    ]

    GrupoClasse.objects.bulk_create(gcs)
    grupos = Grupo.objects.all()
    grupos_ida_volta = {grupo_ida_r1.id: grupo_volta_r1.id, grupo_ida_r2.id: grupo_volta_r2.id}
    resultado = get_grupo_rotina_par(grupos, grupos_ida_volta)
    assert resultado == {
        grupo_ida_r1.id: grupo_ida_r2.id,
        grupo_ida_r2.id: grupo_ida_r1.id,
    }


def test_get_grupo_rotina_par_sem_rotina_par(rota_sao_bhz, rota_bhz_sao):
    """Deve retornar dicionário vazio quando grupo não tem rotina par."""
    rotina_1 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7", rotina_par=None)
    rotina_2 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7", rotina_par=None)

    grupo_ida_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 27, 17, tzinfo=timezone.utc),
    )
    grupo_volta_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 28, 12, tzinfo=timezone.utc),
    )

    grupo_ida_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 27, 12, tzinfo=timezone.utc),
    )
    grupo_volta_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 28, 12, tzinfo=timezone.utc),
    )
    gcs = [
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r2),
    ]

    GrupoClasse.objects.bulk_create(gcs)
    grupos = Grupo.objects.all()

    grupos_ida_volta = {grupo_ida_r1.id: grupo_volta_r1.id, grupo_ida_r2.id: grupo_volta_r2.id}
    resultado = get_grupo_rotina_par(grupos, grupos_ida_volta)
    assert resultado == {}


def test_get_grupo_rotina_par_frequencia_diferente(rota_sao_bhz, rota_bhz_sao):
    """Mesmo quando frequencia diverge, deve retornar grupo_rotina_par_id quando o grupo é no sentido contrário e mesma data"""
    rotina_1 = baker.make("core.rotinaonibus", frequencia="1,2,3,5,6,7")
    rotina_2 = baker.make("core.rotinaonibus", frequencia="1,2,3,6,7", rotina_par=rotina_1)
    rotina_1.rotina_par_id = rotina_2.id
    rotina_1.save()

    grupo_ida_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 27, 10, tzinfo=timezone.utc),
    )
    grupo_volta_r1 = baker.make(
        "core.Grupo",
        rota=rota_sao_bhz,
        rotina_onibus=rotina_1,
        datetime_ida=datetime(2025, 1, 28, 10, tzinfo=timezone.utc),
    )

    grupo_ida_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 27, 22, tzinfo=timezone.utc),
    )
    grupo_volta_r2 = baker.make(
        "core.Grupo",
        rota=rota_bhz_sao,
        rotina_onibus=rotina_2,
        datetime_ida=datetime(2025, 1, 28, 22, tzinfo=timezone.utc),
    )
    gcs = [
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r2),
    ]

    GrupoClasse.objects.bulk_create(gcs)
    grupos = Grupo.objects.all()

    grupos_ida_volta = {grupo_ida_r1.id: grupo_volta_r1.id, grupo_ida_r2.id: grupo_volta_r2.id}
    resultado = get_grupo_rotina_par(grupos, grupos_ida_volta)
    assert resultado == {
        grupo_ida_r1.id: grupo_ida_r2.id,
        grupo_ida_r2.id: grupo_ida_r1.id,
    }


def test_get_grupo_rotina_par_classes_diferentes(rota_sao_bhz, rota_bhz_sao):
    """Deve retornar dicionário vazio quando grupos têm classes diferentes."""
    rotina_1 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7")
    rotina_2 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7", rotina_par=rotina_1)
    rotina_1.rotina_par_id = rotina_2.id
    rotina_1.save()

    grupo_ida_r1 = baker.make(
        "core.Grupo", rota=rota_sao_bhz, rotina_onibus=rotina_1, datetime_ida=datetime(2025, 1, 27, tzinfo=timezone.utc)
    )
    grupo_volta_r1 = baker.make(
        "core.Grupo", rota=rota_sao_bhz, rotina_onibus=rotina_1, datetime_ida=datetime(2025, 1, 28, tzinfo=timezone.utc)
    )

    grupo_ida_r2 = baker.make(
        "core.Grupo", rota=rota_bhz_sao, rotina_onibus=rotina_2, datetime_ida=datetime(2025, 1, 27, tzinfo=timezone.utc)
    )
    grupo_volta_r2 = baker.make(
        "core.Grupo", rota=rota_bhz_sao, rotina_onibus=rotina_2, datetime_ida=datetime(2025, 1, 28, tzinfo=timezone.utc)
    )
    gcs = [
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="semi leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="semi leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito cama", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito cama", grupo=grupo_volta_r2),
    ]

    GrupoClasse.objects.bulk_create(gcs)
    grupos = Grupo.objects.all()

    grupos_ida_volta = {grupo_ida_r1.id: grupo_volta_r1.id, grupo_ida_r2.id: grupo_volta_r2.id}
    resultado = get_grupo_rotina_par(grupos, grupos_ida_volta)
    assert resultado == {}


def test_get_grupo_rotina_par_datas_diferentes(rota_sao_bhz, rota_bhz_sao):
    """Deve retornar dicionário vazio quando grupos viajam mais que 2 dias de diferença."""
    rotina_1 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7")
    rotina_2 = baker.make("core.rotinaonibus", frequencia="1,2,3,4,5,6,7", rotina_par=rotina_1)
    rotina_1.rotina_par_id = rotina_2.id
    rotina_1.save()

    grupo_ida_r1 = baker.make(
        "core.Grupo", rota=rota_sao_bhz, rotina_onibus=rotina_1, datetime_ida=datetime(2025, 1, 27, tzinfo=timezone.utc)
    )
    grupo_volta_r1 = baker.make(
        "core.Grupo", rota=rota_sao_bhz, rotina_onibus=rotina_1, datetime_ida=datetime(2025, 1, 28, tzinfo=timezone.utc)
    )

    grupo_ida_r2 = baker.make(
        "core.Grupo", rota=rota_bhz_sao, rotina_onibus=rotina_2, datetime_ida=datetime(2025, 1, 28, tzinfo=timezone.utc)
    )
    grupo_volta_r2 = baker.make(
        "core.Grupo", rota=rota_bhz_sao, rotina_onibus=rotina_2, datetime_ida=datetime(2025, 1, 29, tzinfo=timezone.utc)
    )
    gcs = [
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r1),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_ida_r2),
        baker.prepare("core.grupoclasse", tipo_assento="leito", grupo=grupo_volta_r2),
    ]

    GrupoClasse.objects.bulk_create(gcs)
    grupos = Grupo.objects.all()

    grupos_ida_volta = {grupo_ida_r1.id: grupo_volta_r1.id, grupo_ida_r2.id: grupo_volta_r2.id}
    resultado = get_grupo_rotina_par(grupos, grupos_ida_volta)
    assert resultado == {grupo_ida_r2.id: grupo_ida_r1.id, grupo_ida_r1.id: grupo_ida_r2.id}


def test_solver_booking_limita_qtd_remanejamentos_pelo_threshold():
    """
    Testa se o SolverBooking limita a quantidade de remanejamentos pelo threshold.
    """
    solver_booking = SolverBooking(
        travel_id=1,
        trecho_classe_id_inicial=0,
        count_seats=39,
        receita=Decimal(3900),
        receita_agregada=Decimal(3900),
        remanejamento={
            1: Decimal(130),
            2: Decimal(220),
            3: Decimal(100),
            4: Decimal(400),
            5: Decimal(25),
            6: Decimal(40),
            7: Decimal(430),
            8: Decimal(30),
            9: Decimal(20),
            10: Decimal(10),
            11: Decimal(5),
            12: Decimal(4000),
        },
        dummy=False,
    )
    assert len(solver_booking.remanejamento) == 10
    # ordena os remanejamentos pelo valor
    # os 2 maiores são excluídos (7, 12)
    assert set(solver_booking.remanejamento.keys()) == {1, 2, 3, 4, 5, 6, 8, 9, 10, 11}
