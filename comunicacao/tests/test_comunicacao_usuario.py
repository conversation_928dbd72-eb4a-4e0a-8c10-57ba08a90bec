import json
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from unittest.mock import ANY, call

import pytest
import time_machine
from django.core.management import call_command
from django.utils.timezone import get_default_timezone
from model_bakery import baker

import auth.views as views_auth
from commons import dateutils
from commons.dateutils import now, to_default_tz_required
from commons.enum import ModeloVenda
from comunicacao.forms.comunicacao_forms import EnviarComunicacaoForm
from comunicacao.models import Comunicacao, Envio, Template
from comunicacao.service import comunicacao_svc
from comunicacao.service.comunicacao_svc import StatusEnvio, enviar_comunicacao
from core.forms.itens_adicionais_forms import ItemAdicionalUpsellInfo, ItemAdicionalUpsellInfoItem
from core.models_commons import Lead
from core.models_travel import CupomLead, Estorno, ItemAdicional
from core.service import itens_adicionais_svc, security_svc
from core.service.grupos_staff import ressarcimento_svc
from core.service.lead_svc import send_confirmation_link_lead_email_opt_out, send_confirmation_link_lead_phone_opt_out
from core.service.notifications import user_notification_svc
from core.service.ressarcimento_downgrade_svc import bulk_ressarcimento_unico_downgrade
from core.tests import fixtures
from promo.service import fidelidade_svc


@pytest.mark.parametrize(
    "com_comunicacao",
    [True, False],
)
def test_fluxo_novo_comunicacao_sem_templates(com_comunicacao, mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)

    if com_comunicacao:
        schema = {"adjetivo": "testado", "user": {"first_name": "Jose"}, "url": "url.test.com"}
        baker.make("comunicacao.Comunicacao", schema=schema)

    lead = baker.make(
        Lead,
        email="<EMAIL>",
        phone_opt_out_at=None,
        email_opt_out_at=None,
    )

    evento = "views_auth.send_confirmation_link_lead_opt_out"

    with pytest.raises(
        ValueError, match=f"Não foi encontrado template para email para a comunicação do evento {evento}"
    ):
        send_confirmation_link_lead_email_opt_out("<EMAIL>", evento)

        comunicacao_envio = Envio.objects.filter(lead=lead)
        assert len(comunicacao_envio) == 0


def test_comunicacao_reserva_cancelada_nao_paga(mocker):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_cancelada_nao_paga",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    joao = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=joao, trecho_classe=trecho_classe, _fill_optional=True)
    evento = "CancelByNoPayment - cancela_reserva - credit_card_expired"
    user_notification_svc.viagem_cancelada_nao_paga(travel, evento)

    comunicacao_envio = Envio.objects.filter(user=joao)
    assert len(comunicacao_envio) == 3

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")

    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_push[0].status == "enviado"
    assert comunicacao_envio_inbox[0].status == "enviado"


def test_comunicacao_reserva_cancelada_por_erro_emissao_rodoviaria(mocker):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_cancelada_por_erro_emissao_rodoviaria",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    joao = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=joao, trecho_classe=trecho_classe, _fill_optional=True)
    evento = "CancelByRodoviariaError - cancela_reserva - RODOVIARIA_ERROR"
    user_notification_svc.viagem_cancelada_por_erro_emissao_rodoviaria(travel, "mensagem de erro", evento)

    comunicacao_envio = Envio.objects.filter(user=joao)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")

    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_push[0].status == "enviado"
    assert comunicacao_envio_inbox[0].status == "enviado"
    assert comunicacao_envio_sms[0].status == "enviado"


def test_comunicacao_reserva_cancelada_por_erro_marketplace(mocker):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_cancelada_por_erro_emissao_rodoviaria",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    joao = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=joao, trecho_classe=trecho_classe, _fill_optional=True)
    evento = "reserva.CancelByMarketplaceError"
    user_notification_svc.viagem_cancelada_por_erro_marketplace(travel, "Divergência", evento)

    comunicacao_envio = Envio.objects.filter(user=joao)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")

    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_push[0].status == "enviado"
    assert comunicacao_envio_inbox[0].status == "enviado"
    assert comunicacao_envio_sms[0].status == "enviado"


def test_staff_ressarcir_travel_dois_buseiros_com_fluxo_novo_comunicacao(user_staff, mocker):
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("ressarcimento_lanche")

    user_joao = fixtures.user_ze()
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=to_default_tz_required(now()))
    travel = baker.make("core.Travel", trecho_classe=trecho_classe, user=user_joao)
    baker.make("core.Buseiro", name="Passageiro1", user=user_joao, passageiro_set__travel=travel)
    baker.make("core.Buseiro", name="Passageiro2", user=user_joao, passageiro_set__travel=travel)
    passageiros_ids = list(travel.passageiro_set.values_list("id", flat=True))
    fromuser = user_staff
    travel_zap_context = mocker.spy(user_notification_svc, "travel_zap_ctx")
    ressarcimento_svc.ressarcir_lanche(
        passageiros_ids,
        "20.0",
        "FRT_LANCHE_AVULSO",
        "Lanche avulso",
        "FRT_ATRASO_61MIN_A_2H",
        fromuser,
        evento_comunicacao="views_staff.ressarcir_buseiros",
    )
    travel_zap_context.assert_called_once_with(
        travel, ressarcimento_lanche=True, valor_ressarcimento="40.0", novo_fluxo_comunicacao=True
    )
    comunicacao_envio = Envio.objects.filter(user=user_joao)
    comunicacao_envio_zap = comunicacao_envio.filter(template__canal="whatsapp")

    assert len(comunicacao_envio) == 1
    assert comunicacao_envio_zap[0].status == "enviado"
    assert comunicacao_envio_zap[0].evento == "views_staff.ressarcir_buseiros"
    assert comunicacao_envio_zap[0].destinatario == user_joao.profile.cell_phone


def test_comunicacao_descadastro_lead_email(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("descadastro_lead")

    lead = baker.make(
        Lead,
        email="<EMAIL>",
        phone_opt_out_at=None,
        email_opt_out_at=None,
    )
    evento = "views_auth.send_confirmation_link_lead_opt_out"
    send_confirmation_link_lead_email_opt_out("<EMAIL>", evento)

    comunicacao_envio = Envio.objects.get(lead=lead)

    assert comunicacao_envio.tipo_destinatario == "lead"
    assert comunicacao_envio.destinatario == lead.email
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == evento


def teste_comunicacao_troca_de_email(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("troca_de_email")

    user_joao = fixtures.user_ze()

    user_notification_svc.notifica_old_email(user_joao, "<EMAIL>")

    comunicacao_envio = Envio.objects.get(destinatario="<EMAIL>")

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == "view_staff.edit_user"
    assert comunicacao_envio.user == user_joao


def test_usuario_deletar_conta(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("deletar_conta")
    user_joao = fixtures.user_ze()

    user_notification_svc.notifica_pedido_delecao_dados(user_joao)

    comunicacao_envio = Envio.objects.get(user=user_joao)

    assert comunicacao_envio.destinatario == user_joao.email
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == "auth.views.user_asked_for_account_deletion"


def test_usuario_login_token(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("login_com_token")
    user_joao = fixtures.user_ze()

    user_notification_svc.send_login_token(user_joao, "XPTO123")

    comunicacao_envio = Envio.objects.filter(user=user_joao)
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")

    assert len(comunicacao_envio) == 2
    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_email[0].destinatario == user_joao.email
    assert comunicacao_envio_email[0].evento == "auth.views.send_login_token"
    assert comunicacao_envio_sms[0].status == "enviado"
    assert comunicacao_envio_sms[0].destinatario == user_joao.profile.cell_phone
    assert comunicacao_envio_sms[0].evento == "auth.views.send_login_token"


def test_confirmacao_cancelamento_pax(mocker):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="confirmacao_cancelamento_pax",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )

    user_joao = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=to_default_tz_required(now()))
    travel = baker.make("core.Travel", trecho_classe=trecho_classe, user=user_joao)
    user_reason = "teste"

    user_notification_svc.viagem_cancelada_pelo_usuario(travel, user_reason, "reserva.CancelByUserRequest")

    comunicacao_envio = Envio.objects.get(user=user_joao)

    assert comunicacao_envio.destinatario == user_joao.email
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == "reserva.CancelByUserRequest"


def test_usuario_esqueceu_senha(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("redefinir_senha")
    user_joao = fixtures.user_ze()

    user_notification_svc.redefinir_senha(user_joao, "redefinirsenha.buser.com", "comunicacao.teste.redefinir_senha")

    comunicacao_envio = Envio.objects.filter(user=user_joao)
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")

    assert len(comunicacao_envio) == 2
    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_email[0].destinatario == user_joao.email
    assert comunicacao_envio_email[0].evento == "comunicacao.teste.redefinir_senha"
    assert comunicacao_envio_sms[0].status == "enviado"
    assert comunicacao_envio_sms[0].destinatario == user_joao.profile.cell_phone
    assert comunicacao_envio_sms[0].evento == "comunicacao.teste.redefinir_senha"


def test_comunicacao_descadastro_lead_sms(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    user_lead = baker.make("auth.User")
    call_command("descadastro_lead")
    lead = baker.make(
        Lead,
        user=user_lead,
        phone="11994002892",
        phone_opt_out_at=None,
        email_opt_out_at=None,
    )
    evento = "views_auth.send_confirmation_link_lead_opt_out"
    send_confirmation_link_lead_phone_opt_out("11994002892", evento)

    comunicacao_envio = Envio.objects.get(lead=lead)

    assert comunicacao_envio.tipo_destinatario == "lead"
    assert comunicacao_envio.lead == lead
    assert comunicacao_envio.user == user_lead
    assert comunicacao_envio.destinatario == lead.phone
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "sms"
    assert comunicacao_envio.evento == evento


def test_comunicacao_confirmar_troca_email(mocker, rf):
    mocker.patch("core.service.user_svc._validate_edit_interval")

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="confirmar_troca_email",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Troca de email",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    user = baker.make("auth.User", _fill_optional=True)
    profile = baker.make(
        "core.Profile", user=user, cell_phone="***********", cell_phone_confirmed=True, _fill_optional=True
    )
    baker.make("core.Lead", user=user, phone=profile.cell_phone)

    novo_email = "<EMAIL>"
    params = json.dumps({"email": novo_email})
    request = rf.post("/api/save_user", {"params": params})
    request.user = user

    views_auth.save_user(request)

    comunicacao_envios = Envio.objects.filter(user=user)
    comunicacao_envio_email = comunicacao_envios.filter(template__canal="email")
    comunicacao_envio_sms = comunicacao_envios.filter(template__canal="sms")

    evento = "Edição usuário - user_request"

    assert len(comunicacao_envios) == 2
    assert comunicacao_envio_email[0].status == "enviado"
    assert comunicacao_envio_email[0].destinatario == user.email
    assert comunicacao_envio_email[0].evento == evento
    assert comunicacao_envio_sms[0].status == "enviado"
    assert comunicacao_envio_sms[0].destinatario == user.profile.cell_phone
    assert comunicacao_envio_sms[0].evento == evento


def test_comunicacao_embarque_proximo_telemetria(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)

    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("embarque_proximo")
    joao = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=joao, trecho_classe=trecho_classe, _fill_optional=True)
    user_notification_svc.send_push_near_boarding_telemetry(travel)

    comunicacao_envio = Envio.objects.filter(user=joao)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == "user_notification_svc.push_available_telemetry_notification"

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == "user_notification_svc.push_available_telemetry_notification"


@pytest.mark.parametrize(
    "com_adicionais",
    [True, False],
)
def test_comunicacao_grupo_cancelado(mocker, com_adicionais):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_cancelada_pela_buser",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo='<p>Sentimos muito, mas tivemos que cancelar sua viagem para <b class="fw-bold">{{travel.destino_name}}, às {{travel.horario_partida_str}}, pois {{reason_description}}.</p>',
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        comunicacao=comunicacao,
        corpo="Olá",
        remetente="suporte",
    )

    ze = fixtures.user_ze()
    grupo = baker.make("core.Grupo", status="travel_confirmed")
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True, datetime_ida=now())
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, grupo=grupo, _fill_optional=True)
    # tem que dar cupom 30% por ter outra reserva ativa
    grupo2 = baker.make("core.Grupo", status="travel_confirmed")
    trecho_classe2 = baker.make("core.TrechoClasse", _fill_optional=True)
    baker.make("core.Travel", user=ze, trecho_classe=trecho_classe2, grupo=grupo2, _fill_optional=True)
    baker.make("core.AccountingOperation", source="RESERVA", value_real=Decimal("-79"), travel=travel)
    if com_adicionais:
        baker.make("core.AccountingOperation", source="BAGAGEM_ADICIONAL", value_real=Decimal("-14.9"), travel=travel)
        baker.make("core.AccountingOperation", source="SEGURO_EXTRA", value_real=Decimal("-1.2"), travel=travel)

    evento = "grupo_status_svc.cancel_group"

    user_notification_svc.grupo_cancelado(
        grupo,
        grupo_status="travel_confirmed",
        reason="COMPANY_PROBLEM",
        custom_description=None,
        travels_estornaveis=set(),
        channels={"email", "inbox", "push", "zap"},
        acao_contabil="CREDITAR_APENAS",
        evento_comunicacao=evento,
    )

    comunicacao_envio = Envio.objects.filter(user=ze)
    promos_criadas = CupomLead.objects.filter(lead__user=ze)
    assert len(comunicacao_envio) == 4
    assert len(promos_criadas) == 1

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_whatsapp = comunicacao_envio.filter(template__canal="whatsapp")[0]

    valor_esperado_reembolso = 95.1 if com_adicionais else 79

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento
    assert comunicacao_envio_email.contexto
    assert comunicacao_envio_email.contexto["percentual_cupom"] == Decimal("30")
    assert comunicacao_envio_email.contexto["reembolso"] == valor_esperado_reembolso

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_whatsapp.status == "enviado"
    assert comunicacao_envio_whatsapp.destinatario == "***********"
    assert comunicacao_envio_whatsapp.evento == evento


def test_comunicacao_viagem_cancelada_pela_buser(mocker):
    pagamento = {"method": "credit_card", "status": "done"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_cancelada_pela_buser",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        comunicacao=comunicacao,
        corpo="Olá",
        remetente="suporte",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    baker.make("core.AccountingOperation", source="RESERVA", value_real="79", travel=travel)
    evento = "CancelByEditRota"

    user_notification_svc.viagem_cancelada_pela_buser(
        travel,
        reason="COMPANY_PROBLEM",
        custom_description=None,
        acao_contabil="CREDITAR_APENAS",
        evento_comunicacao=evento,
    )

    comunicacao_envio = Envio.objects.filter(user=ze)
    promos_criadas = CupomLead.objects.filter(lead__user=ze)
    assert len(promos_criadas) == 1
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_whatsapp = comunicacao_envio.filter(template__canal="whatsapp")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_whatsapp.status == "enviado"
    assert comunicacao_envio_whatsapp.destinatario == "***********"
    assert comunicacao_envio_whatsapp.evento == evento


def test_comunicacao_alteracoes_na_viagem_agrupamento(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="alteracoes_na_viagem",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo="alteracoes_na_viagem_v3",
        canal="email",
        comunicacao=comunicacao,
        titulo="Fizemos uma alteração na sua viagem",
        corpo=(
            "Olá, {{name}}! Alteramos a sua viagem {{travel.reservation_code}}. Embarque {{dict_travel_atual.embarque}}"
            "Chegada {{dict_travel_atual.chegada}}",
        ),
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Fizemos uma alteração na sua viagem",
        corpo="Veja as alteraões na sua reserva.",
        push_url="{{site_base_url}}/perfil/viagens/{{travel_id}}/",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        remetente="5511991142263",
        comunicacao=comunicacao,
        corpo="""Olá, {{name}}! Fizemos {{introducao}} na sua viagem de {{origem}} para {{destino}}.
        Foi necessário {{descritivo}}
        Partida: {{partida}}
        Chegada: {{chegada}}
        Categoria de poltrona: {{tipo_assento}}
        Local de embarque: {{embarque}}
        Local de desembarque: {{desembarque}}
        Acompanhe sua reserva: {{url}}.""",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="Fizemos uma alteração na sua viagem",
        corpo=(
            "Olá, {{name}}! Alteramos a sua viagem {{travel.reservation_code}}. Embarque {{dict_travel_atual.embarque}}"
            "Chegada {{dict_travel_atual.chegada}}",
        ),
        inbox_rota="TRAVEL",
    )
    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)

    multiplas_mudancas_dict = dict(
        origem=baker.make("core.LocalEmbarque"),
        duracao_ida=timedelta(hours=3),
    )
    evento = "travel_instructions"
    user_notification_svc.notifica_alteracoes_agrupadas(
        travel.id, multiplas_mudancas_dict, viagem_amanha=True, evento_comunicacao=evento
    )

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_whatsapp = comunicacao_envio.filter(template__canal="whatsapp")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_whatsapp.status == "enviado"
    assert comunicacao_envio_whatsapp.destinatario == "***********"
    assert comunicacao_envio_whatsapp.evento == evento

    contexto_zap = comunicacao_envio_whatsapp.contexto
    assert contexto_zap
    assert "*" in contexto_zap["partida"]
    assert "*" in contexto_zap["chegada"]
    assert "*" in contexto_zap["embarque"]


def test_comunicacao_alteracao_de_upgrade_agrupamento(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="alteracao_de_upgrade",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Você ganhou uma poltrona melhor!",
        corpo=(
            "Olá, {{name}}! Alteramos a sua viagem {{travel.reservation_code}}. "
            "Poltrona {{dict_travel_atual.tipo_assento}}"
        ),
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Você ganhou uma poltrona melhor!",
        corpo="Veja as alteraões na sua reserva.",
        push_url="{{site_base_url}}/perfil/viagens/{{travel_id}}/",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        remetente="5511991142263",
        comunicacao=comunicacao,
        titulo="Fizemos uma alteração na sua viagem",
        corpo="Olá, {{name}}! Alteramos a sua viagem {{travel.reservation_code}}. {{alteracoes}}",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="Fizemos uma alteração na sua viagem",
        corpo=(
            "Olá, {{name}}! Alteramos a sua viagem {{travel.reservation_code}}. "
            "Poltrona {{dict_travel_atual.tipo_assento}}"
        ),
        inbox_rota="TRAVEL",
    )
    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    grupo_classe = travel.grupo_classe
    grupo_classe.tipo_assento = "leito cama"
    grupo_classe.save(update_fields=["tipo_assento"])

    uma_mudanca_dict = {"tipo_assento": "semi leito"}

    evento = "travel_instructions"
    user_notification_svc.notifica_alteracoes_agrupadas(
        travel.id, uma_mudanca_dict, viagem_amanha=True, evento_comunicacao=evento
    )

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_whatsapp = comunicacao_envio.filter(template__canal="whatsapp")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_whatsapp.status == "enviado"
    assert comunicacao_envio_whatsapp.destinatario == "***********"
    assert comunicacao_envio_whatsapp.evento == evento


def test_comunicacao_alteracao_de_placa_agrupamento(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="alteracao_de_placa",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Atualizamos a placa do seu ônibus",
        corpo=(
            """<p>Ônibus placa {{travel.onibus_placa}} da empresa {{travel.company}}.</p> """
            """{% include "notificacao/commons/molecules/_card_reserva.html" with travel=travel %} """
            """<p>Conte com a Buser!</p>"""
        ),
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Atualizamos a placa do seu ônibus",
        corpo=(
            "Ônibus placa {{travel.onibus_placa}} Para acompanhar sua viagem basta acessar as informações da sua "
            "reserva."
        ),
        push_url="{{site_base_url}}/perfil/viagens/{{travel_id}}/",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="BUSER: Atualizamos a placa do seu onibus. Onibus placa {{travel.onibus_placa}}. {{travel.url}}",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="Atualizamos a placa do seu ônibus",
        corpo=(
            "Ônibus placa {{travel.onibus_placa}} Para acompanhar sua viagem basta acessar as informações da sua "
            "reserva."
        ),
        inbox_rota="TRAVEL",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)

    evento = "comunica_alteracoes_agrupadas_travel"
    user_notification_svc.notifica_alteracao_placa(travel.id, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.destinatario == "***********"
    assert comunicacao_envio_sms.evento == evento


def test_comunicacao_confirmacao_de_linkagem_de_usuario(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="confirmacao_de_linkagem_de_usuario",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="corpo mensagem",
    )
    ze = fixtures.user_ze()
    buseiro = baker.make("core.Buseiro", user=ze, phone="12996051047")

    evento = "auth.views.send_phone_confirmation_linked_user"
    user_notification_svc.send_phone_confirmation_linked_user(buseiro.id, buseiro.phone)

    comunicacao_envio = Envio.objects.get(destinatario=buseiro.phone)

    assert comunicacao_envio.tipo_destinatario == "telefone"
    assert comunicacao_envio.destinatario == buseiro.phone
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "sms"
    assert comunicacao_envio.evento == evento


def test_comunicacao_comprovante_de_saque(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="comprovante_de_saque",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=False,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="saque",
        corpo="saque",
    )

    ze = fixtures.user_ze()
    bank_account_json = (
        '{"agencia": "2983",'
        '"agencia_dv": "3",'
        '"bank_name": "Banco do braza",'
        '"conta": "39187",'
        '"conta_dv": "X",'
        '"legal_name": "Tony Ariel",'
        '"document_number": "986766 - SAP"}'
    )
    transacao = baker.make("core.AccountingTransaction", user=ze, bank_account_json=bank_account_json)

    evento = "Verificação aprovada - bank_svc.executa_saques_com_verificacao_pendente"
    user_notification_svc.saque_realizado(transacao, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.get(destinatario="<EMAIL>")

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == evento


def test_comunicacao_comprovante_de_reserva(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    mocker.patch(
        "core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": {"payments": []}}
    )
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("comprovante_de_reserva")
    datetime_ida = datetime(2021, 9, 19, 23, 55, 59, 342380, tzinfo=get_default_timezone())
    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=datetime_ida)
    travel = baker.make(
        "core.Travel",
        user=ze,
        pagamento=None,
        trecho_classe=trecho_classe,
        travel_ida=None,
        travel_conexao=None,
        trecho_conexao=None,
        _fill_optional=True,
    )
    evento = "views.resend_comprovante_pagamento"
    user_notification_svc.enviar_comprovante_pagamento(travel, evento=evento)

    comunicacao_envio = Envio.objects.get(destinatario="<EMAIL>")

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.travel == travel


def test_ressarcimento_unico_downgrade_generico(mocker, passageiro_com_historico_remanejamento_e_alteracao_travel):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    passageiro, _ = passageiro_com_historico_remanejamento_e_alteracao_travel()
    user = passageiro.buseiro.user
    user.email = "<EMAIL>"
    baker.make("core.Profile", user=user, cell_phone="***********", cell_phone_confirmed=True, _fill_optional=True)
    user.save(update_fields=["email"])
    baker.make("core.Device", profile=user.profile, kind="fcm")

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="ressarcimento_de_viagem",
        tipo_disparo="transacional",
        notificar_buseiro=True,
        notificar_user=False,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="ressarcimento",
        corpo="ressarcimento",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="ressarcimento",
        corpo="ressarcimento",
        push_url="{{site_base_url}}/perfil/viagens/{{travel_id}}/",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        remetente="5511913590868",
        comunicacao=comunicacao,
        corpo="ressarcimento",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="ressarcimento",
        corpo="ressarcimento",
        inbox_rota="TRAVEL",
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    evento = "Command - paga_ressarcimento_unico_downgrade"

    comunicacao_envio = Envio.objects.filter(user=user)
    assert len(comunicacao_envio) == 4

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_whatsapp = comunicacao_envio.filter(template__canal="whatsapp")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == user.email
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == user.email
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == user.email
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_whatsapp.status == "enviado"
    assert comunicacao_envio_whatsapp.destinatario == "***********"
    assert comunicacao_envio_whatsapp.evento == evento


def test_comunicacao_falha_no_saque(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("falha_no_saque")

    ze = fixtures.user_ze()
    bank_account_json = (
        '{"agencia": "2983",'
        '"agencia_dv": "3",'
        '"bank_name": "Banco do braza",'
        '"conta": "39187",'
        '"conta_dv": "X",'
        '"legal_name": "Tony Ariel",'
        '"document_number": "986766 - SAP"}'
    )
    transacao = baker.make("core.AccountingTransaction", user=ze, bank_account_json=bank_account_json)

    evento = "Erro na transação - bank_svc.executa_saques_com_verificao_pendente"
    user_notification_svc.saque_falhou(transacao, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]

    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_sms.destinatario == "***********"
    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.evento == evento


def test_comunicacao_saldo_a_expirar(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("saldo_a_expirar")

    ze = fixtures.user_ze()

    user_notification_svc.notifica_saldo_a_expirar([ze.id], 2)

    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == "command.notifica_pax_saldo_a_expirar"


def test_comunicacao_nps_cancelada(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="pesquisa_nps_cancelada",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Avalie sua experiência",
        corpo="Queremos saber como podemos melhorar a sua experiência com a Buser.",
        push_url="{{site_base_url}}",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)

    travel = baker.make(
        "core.Travel",
        user=ze,
        trecho_classe=trecho_classe,
        grupo__canceled_at=to_default_tz_required(now()),
        _fill_optional=True,
    )

    user_notification_svc.send_push_nps_survey_canceled_travel(travel)

    comunicacao_envio = Envio.objects.get(destinatario="<EMAIL>")

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "push"
    assert comunicacao_envio.evento == "user_notification_svc.nps_viagem_cancelada"


def test_comunicacao_criar_senha(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="criar_senha",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Crie sua senha de acesso",
        corpo="Para criar a sua nova senha de acesso na Buser é só clicar no botão abaixo:",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Para criar a sua nova senha de acesso na Buser é só clicar no botão abaixo:",
    )

    ze = fixtures.user_ze()

    evento = "criar_senha_monolito - Motorista"
    resetlink = security_svc.make_reset_password_link(ze)
    user_notification_svc.criar_senha(ze, resetlink, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.destinatario == "***********"
    assert comunicacao_envio_sms.evento == evento


def test_comunicacao_sua_viagem_e_amanha(mocker, time_machine):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="sua_viagem_e_amanha",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    time_machine.move_to("2022-01-01T04:00:00.000+00:00")

    ze = fixtures.user_ze()
    grupo = baker.make("core.Grupo", status="travel_confirmed")
    trecho_classe = baker.make(
        "core.TrechoClasse", datetime_ida=to_default_tz_required(now() + timedelta(days=1)), grupo=grupo
    )
    baker.make("core.Travel", user=ze, trecho_classe=trecho_classe)

    call_command("travel_instructions")

    evento = "Command travel_instructions - Sua viagem é amanhã"
    comunicacao_envio = Envio.objects.filter(user=ze)
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_email.tipo_destinatario == "user"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_inbox.tipo_destinatario == "user"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_push.tipo_destinatario == "user"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.evento == evento


def test_comunicacao_novo_acesso():
    ze = fixtures.user_ze()

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="novo_acesso",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )

    evento = "auth_svc.logged_in - Log In"
    user_notification_svc.login_realizado(ze, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.get(user=ze)
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento


def test_comunicacao_reenviar_comprovante_pagamento_reserva_staff(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("comprovante_pagamento_reserva")

    ze = fixtures.user_ze()
    pagamento = baker.make(
        "core.Pagamento",
        method="dinheiro",
        user=ze,
        jsondata="{}",
        _fill_optional=True,
    )
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    baker.make(
        "core.Travel", user=ze, trecho_classe=trecho_classe, pagamento=pagamento, grupo_classe__tipo_assento="executivo"
    )

    evento = "Staff - reenviar comprovante"
    user_notification_svc.payment_confirmed(pagamento, send_push=True, resend=True, evento=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_email.tipo_destinatario == "user"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_inbox.tipo_destinatario == "user"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_push.tipo_destinatario == "user"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.evento == evento


def test_comunicacao_confirmacao_de_remarcacao(mocker):
    mocker.patch(
        "core.service.notifications.user_notification_svc._extrato_travel",
        return_value={"extrato": {"paid_value": Decimal("89")}},
    )
    pagamento_dict = {"method": "credit_card", "status": "done", "method_display": "Cartão de crédito"}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento_dict)

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="confirmacao_de_remarcacao",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel_cancelada = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    travel_nova = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    passageiro = baker.make("core.Passageiro", travel=travel_nova)
    quantidade_bagagem = 2
    baker.make(
        "core.AccountingOperation",
        travel=travel_nova,
        passageiro=passageiro,
        source="BAGAGEM_ADICIONAL",
        value_real=Decimal("-18.90"),
        jsondata=json.dumps({"quantidade_bagagem_adicional": quantidade_bagagem}),
    )
    pagamento = baker.make("core.Pagamento")
    evento = "reserva_svc.remarcar"
    user_notification_svc.viagem_remarcada_pelo_usuario(
        travel_cancelada, [travel_nova], pagamento, evento_comunicacao=evento
    )

    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.travel == travel_nova
    assert comunicacao_envio.contexto["politica_bagagem"]["viagens"][0]["bagagem_adicional"] == quantidade_bagagem


def test_send_push_to_user_with_unpaid_pix():
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="pix_nao_pago",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Pix aguardando pagamento.",
        corpo="Não deixe o pix expirar! Garanta sua viagem e pague agora a sua reserva!",
        push_url="{{site_base_url}}/perfil/viagens/{{travel_id}}/",
    )

    ze = fixtures.user_ze()
    travel = baker.make(
        "core.Travel",
        user=ze,
        _fill_optional=True,
    )

    user_notification_svc.send_push_to_user_with_unpaid_pix(travel)

    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "push"
    assert comunicacao_envio.evento == "user_notification_svc.send_push_to_user_with_unpaid_pix"


def test_comunicacao_compre_sua_volta(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("compre_sua_volta")

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    reserva = baker.make("core.Reserva")
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, reserva=reserva)
    evento = "reserva_svc._efetuar_reserva - Reserva concluída"

    user_notification_svc.agendar_compre_sua_volta_10min(reserva, ze, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento
    assert comunicacao_envio_inbox.travel == travel
    assert "route_params" in comunicacao_envio_inbox.contexto.keys()

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento
    assert comunicacao_envio_push.travel == travel


def test_comunicacao_reserva_realizada(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    mocker.patch(
        "core.service.notifications.user_notification_svc._extrato_travel",
        return_value={"extrato": {"paid_value": Decimal("89")}},
    )
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="reserva_realizada",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Email de reserva realizada",
        corpo="Reserva realizada!!",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    reserva = baker.make("core.Reserva")
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, reserva=reserva, _fill_optional=True)
    passageiros = baker.make("core.Passageiro", travel=travel, _quantity=2)
    quantidade_bagagem = 2
    baker.make(
        "core.AccountingOperation",
        travel=travel,
        passageiro=passageiros[0],
        source="BAGAGEM_ADICIONAL",
        value_real=Decimal("-18.90"),
        jsondata=json.dumps({"quantidade_bagagem_adicional": quantidade_bagagem}),
    )
    evento = "reserva_svc._efetuar_reserva - Reserva concluída"

    user_notification_svc.reserva_realizada(reserva, None, evento=evento)

    comunicacao_envio = Envio.objects.filter(user=ze).first()
    assert comunicacao_envio

    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.travel == travel
    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.contexto["politica_bagagem"]["viagens"][0]["bagagem_adicional"] == quantidade_bagagem


def test_comunicacao_saque_autorizacao_transacao(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("saque_autorizacao_de_transacao")

    ze = fixtures.user_ze()

    evento = "Solicitar saque"
    user_notification_svc.autorizacao_transacao(ze, evento_comunicacao=evento, confirmation_code="ABCDE1234")

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.destinatario == ze.profile.cell_phone
    assert comunicacao_envio_sms.evento == evento


def test_comunicacao_retirada_de_passagem_marketplace(mocker):
    reminder = dict(
        tipo_local_retirada="guiche",
        nome_empresa="Primar",
        local_motorista="",
        local_retirada="",
    )
    mocker.patch("core.service.notifications.user_notification_svc._marketplace_ctx_reminder", return_value=reminder)
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="retirada_de_passagem_marketplace",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    grupo = baker.make("core.Grupo", modelo_venda="marketplace", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, grupo=grupo)
    evento = "Cron - lembrete_local_retirada_marketplace"

    user_notification_svc.enviar_lembrete_local_retirada_marketplace(grupo, evento=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.destinatario == "***********"
    assert comunicacao_envio_sms.evento == evento
    assert comunicacao_envio_sms.travel == travel

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento


def test_comunicacao_comprovante_pagamento_divida(mocker):
    pagamento = {"method": "credit_card", "status": "done", "value": Decimal("80")}
    mocker.patch("core.service.notifications.user_notification_svc._pagamento_ctx", return_value=pagamento)

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="comprovante_de_pagamento_divida",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )

    ze = fixtures.user_ze()
    evento = "reserva_svc.pagamento_confirmado_divida_callback"

    pagamento = baker.make("core.Pagamento", user=ze, method="pix", jsondata="{}", _fill_optional=True)
    user_notification_svc.pagamento_divida_confirmado(pagamento, evento=evento)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento


def test_comunicacao_confirmar_telefone(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="confirmar_telefone",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Confirme seu telefone",
    )

    ze = fixtures.user_ze()
    ze.profile.cell_phone_to_confirm = "12996051047"
    ze.profile.save(update_fields=["cell_phone_to_confirm", "updated_at"])
    ze.refresh_from_db()

    evento = "user_svc.save_user"
    user_notification_svc.confirmar_telefone(ze, evento=evento)

    comunicacao_envio = Envio.objects.get(destinatario="12996051047")

    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.destinatario == "12996051047"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "sms"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.user == ze


def test_comunicacao_pesquisa_pos_viagem(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("pesquisa_pos_viagem")

    ze = fixtures.user_ze()
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)

    travel = baker.make(
        "core.Travel",
        user=ze,
        trecho_classe=trecho_classe,
        grupo__canceled_at=to_default_tz_required(now()),
        _fill_optional=True,
    )

    user_notification_svc.send_push_post_travel_survey(travel)

    comunicacao_envio = Envio.objects.filter(user=ze)
    assert len(comunicacao_envio) == 2

    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == "user_notification_svc.pesquisa_pos_viagem"
    assert comunicacao_envio_inbox.travel == travel

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == "user_notification_svc.pesquisa_pos_viagem"
    assert comunicacao_envio_push.travel == travel

    status_envio = StatusEnvio.objects.filter(envio_id=comunicacao_envio_push.id)

    assert len(status_envio) == 2
    assert status_envio[0].status == "agendado"
    assert status_envio[1].status == "enviado"


def teste_comprovante_de_estorno(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="comprovante_de_estorno",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Fizemos um estorno para você",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Fizemos um estorno para você",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="Fizemos um estorno para você",
    )

    valor = Decimal("176")
    ze = fixtures.user_ze()
    evento = "test_comprovante_estorno"
    pagamento = baker.make(
        "core.Pagamento",
        paid_value=valor,
        refunded_net_value=valor,
        status="paid",
        net_value=valor,
        method="credit_card",
        user_id=ze.id,
        provider="mercadopago",
        jsondata=json.dumps({"paydata": {}}),
    )
    baker.make("core.Estorno", status_interno="sucesso", external_id=None, pagamento=pagamento, value=Decimal("88"))
    baker.make("core.AccountingOperation", pagamento=pagamento, date=now(), source="ESTORNO")

    user_notification_svc.pagamento_estornado(
        pagamento, dopush=True, staff_user=None, status=Estorno.Status.SUCESSO, evento=evento
    )

    comunicacao_envio = Envio.objects.filter(user=ze)
    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento
    assert comunicacao_envio_inbox.tipo_destinatario == "user"
    assert comunicacao_envio_inbox.user == ze

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento
    assert comunicacao_envio_email.tipo_destinatario == "user"
    assert comunicacao_envio_email.user == ze

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.destinatario == "<EMAIL>"
    assert comunicacao_envio_push.evento == evento
    assert comunicacao_envio_push.tipo_destinatario == "user"
    assert comunicacao_envio_push.user == ze


def test_comunicacao_reativacao_de_conta(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("reativacao_de_conta")

    ze = fixtures.user_ze()
    evento = "auth.views.login"
    user_notification_svc.conta_reativada(ze, evento=evento)
    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.user == ze


def test_fidelidade_completa(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="completou_fidelidade",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="corpo",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="titulo",
        comunicacao=comunicacao,
        corpo="corpo",
    )

    user_joao = fixtures.user_ze()
    travel = baker.make("core.Travel", user=user_joao, _fill_optional=True)
    datetime_lower = datetime(2023, 9, 19, 23, 55, 59, 342380, tzinfo=timezone.utc)
    datetime_upper = datetime(2024, 9, 19, 23, 55, 59, 342380, tzinfo=timezone.utc)
    cupom = baker.make("core.Cupom", code="FIDELIDADE", is_voucher=True, discount=10, value=10)
    programa_fidelidade = baker.make(
        "promo.Fidelidade",
        title="3 viagens",
        modelos_venda=[ModeloVenda.BUSER],
        travel_count=3,
        cupom=cupom,
        datetime_ida_range=(datetime_lower, datetime_upper),
    )
    fidelidade_svc.envia_comunicacao_completou_fidelidade(programa_fidelidade, travel)

    comunicacao_envio = Envio.objects.filter(user=user_joao)

    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == "fidelidade_svc.envia_comunicacao_completou_fidelidade"
    assert comunicacao_envio_inbox.travel == travel

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == "fidelidade_svc.envia_comunicacao_completou_fidelidade"
    assert comunicacao_envio_inbox.travel == travel


def test_comunicacao_ressarcimento_voucher(mocker):
    comunicacao = baker.make("comunicacao.Comunicacao", codigo="ressarcimento_voucher", tipo_disparo="transacional")
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Segue o voucher",
        corpo="Viaje de Buser!",
    )
    ze = fixtures.user_ze()
    evento = "useradm_svc.give_voucher"
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    travel_price = Decimal("80")
    cupom = baker.make("core.Cupom", _fill_optional=True)
    due_date = dateutils.now() + timedelta(days=5)
    user_notification_svc.user_recebeu_voucher(ze, cupom, due_date, travel, travel_price, evento=evento)

    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.user == ze


def test_comunicacao_ressarcimento_por_apreensao(mocker):
    mocker.patch("core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": ""})
    comunicacao = baker.make(
        "comunicacao.Comunicacao", codigo="ressarcimento_por_apreensao", tipo_disparo="transacional"
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Ressarcindo",
        corpo="Viaje de Buser!",
    )

    ze = fixtures.user_ze()
    evento = "Staff - ressarcir buseiros"
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=ze, trecho_classe=trecho_classe, _fill_optional=True)
    user_notification_svc.passageiro_ressarcido(
        [{"user": ze, "travel": travel}], "APREENSAO_ANTT", ["email"], send_zap=False, evento=evento
    )

    comunicacao_envio = Envio.objects.get(user=ze)

    assert comunicacao_envio.template.canal == "email"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == evento
    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.user == ze


def test_progresso_fidelidade(mocker):
    comunicacao = baker.make("comunicacao.Comunicacao", codigo="progresso_fidelidade", tipo_disparo="transacional")
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="fidelidade",
        corpo="Parabéns!",
    )

    user_joao = fixtures.user_ze()
    travel = baker.make("core.Travel", user=user_joao, _fill_optional=True)
    fidelidade_svc.envia_comunicacao_progresso_fidelidade(travel)

    comunicacao_envio = Envio.objects.get(user=user_joao)

    assert comunicacao_envio.template.canal == "inbox"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == "<EMAIL>"
    assert comunicacao_envio.evento == "fidelidade_svc.envia_comunicacao_progresso_fidelidade"
    assert comunicacao_envio.tipo_destinatario == "user"
    assert comunicacao_envio.user == user_joao
    assert comunicacao_envio.travel == travel


def test_notifica_pax_embarque_encerrado(mocker, travels_factory):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="embarque_iniciado_ou_encerrado",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="titulo",
        corpo="Olá",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Olá",
    )

    travels = travels_factory()
    user = travels[0].user
    baker.make("core.Device", profile=user.profile, kind="fcm")

    evento = "views_everyone.notify_pax_about_travel_status_by_group"
    user_notification_svc.notify_pax_about_travel_status_by_group(
        travels[0].grupo, "embarque_encerrado", travels[0].grupo.checkpoint_idx, evento_comunicacao=evento
    )

    comunicacao_envio = Envio.objects.get(travel=travels[0])

    assert comunicacao_envio.template.canal == "push"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.user == user
    assert comunicacao_envio.evento == evento


def test_notifica_usuario_saque_verificacao_aprovada(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao", codigo="saque_verificacao_aprovada", tipo_disparo="transacional"
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="Verificação aprovada!",
        corpo="Verificamos e tudo ok",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        comunicacao=comunicacao,
        titulo="Verificação aprovada!",
        corpo="Verificamos e tudo ok",
        push_url="{{site_base_url}}/",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="Verificação aprovada!",
        corpo="Verificamos e tudo ok",
        inbox_rota="TRAVEL",
    )

    user = fixtures.user_ze()

    evento = "views_staff.verify_user"
    user_notification_svc.account_verification_accepted(user, evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=user)

    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.user == user
    assert comunicacao_envio_push.evento == evento


def test_notifica_usuario_saque_verificacao_negada(mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    baker.make("auth.User", username="admin", _fill_optional=True)
    call_command("saque_verificacao_negada")

    user = fixtures.user_ze()

    evento = "views_staff.verify_user"
    user_notification_svc.account_verification_rejected(user, "teste", evento_comunicacao=evento)

    comunicacao_envio = Envio.objects.filter(user=user)

    comunicacao_envio_inbox = comunicacao_envio.filter(template__canal="inbox")[0]
    comunicacao_envio_email = comunicacao_envio.filter(template__canal="email")[0]
    comunicacao_envio_push = comunicacao_envio.filter(template__canal="push")[0]
    comunicacao_envio_sms = comunicacao_envio.filter(template__canal="sms")[0]

    assert comunicacao_envio_inbox.status == "enviado"
    assert comunicacao_envio_inbox.destinatario == "<EMAIL>"
    assert comunicacao_envio_inbox.evento == evento

    assert comunicacao_envio_email.status == "enviado"
    assert comunicacao_envio_email.destinatario == "<EMAIL>"
    assert comunicacao_envio_email.evento == evento

    assert comunicacao_envio_push.status == "enviado"
    assert comunicacao_envio_push.user == user
    assert comunicacao_envio_push.evento == evento

    assert comunicacao_envio_sms.status == "enviado"
    assert comunicacao_envio_sms.destinatario == user.profile.cell_phone
    assert comunicacao_envio_email.evento == evento


def test_comunicacao_recupera_pax_pix_expirado(mocker):
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="recupera_cliente_pix_expirado",
        tipo_disparo="transacional",
        schema={"user": {"first_name": "Bia"}},
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        remetente="5511913590868",
        comunicacao=comunicacao,
        corpo=(
            "Olá, {{user.first_name}}! Seu pagamento por Pix expirou. Nosso time está disponível para te ajudar. "
            "Vamos continuar a compra da sua reserva?"
        ),
    )
    user_caloteiro = fixtures.user_ze()
    grupo = baker.make("core.Grupo", status="travel_confirmed")

    with time_machine.travel(now() - timedelta(minutes=40)):
        payment_wait = baker.make("core.Pagamento", user=user_caloteiro, method="pix", status="waiting_payment")

    baker.make("core.Travel", pagamento=payment_wait, user=user_caloteiro, grupo=grupo, status="pending")

    call_command("dispara_zap_para_pax_pagar_pix")

    comunicacao_envio = Envio.objects.get(user=user_caloteiro)

    assert comunicacao_envio.destinatario == "***********"
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.template.canal == "whatsapp"
    assert comunicacao_envio.evento == "commands.dispara_zap_para_pax_pagar_pix"


@pytest.mark.parametrize(
    "canal",
    ["push", "inbox", "sms", "whatsapp", "email"],
)
def test_enviar_comunicacao_grupo(canal, mocker):
    enviar_comunicacao_chunk_mock = mocker.spy(comunicacao_svc, "_enviar_comunicacao_chunk")
    user = fixtures.user_ze()
    grupo = baker.make("core.Grupo", _fill_optional=True)
    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make("core.Travel", user=user, trecho_classe=trecho_classe, grupo=grupo, _fill_optional=True)

    comunicacao, params = _get_envio_params("grupo", [grupo.id], canal)

    form = EnviarComunicacaoForm(**params)
    user_staff = fixtures.user_staff()
    lote_envio = enviar_comunicacao(comunicacao, form, user_staff)

    assert lote_envio.comunicacao == comunicacao
    assert lote_envio.tipo_destinatario == "grupo"
    assert grupo.id in lote_envio.destinatarios_ids

    comunicacao_envio = Envio.objects.filter(user=user)
    envio = comunicacao_envio[0]

    assert len(comunicacao_envio) == 1
    assert envio.user == user
    assert envio.status == "enviado"
    assert envio.travel == travel
    assert envio.template.canal == canal
    assert envio.lote_envio == lote_envio
    # para verificar se é a queue correta
    enviar_comunicacao_chunk_mock.assert_called_once_with(
        ANY, form.templates, comunicacao, "grupo", None, lote_envio, user_staff, "staff"
    )
    if envio.template.canal == "whatsapp":
        # certifica que o template enviado não é o template base
        assert envio.template.codigo == "apreensao_1_hora"


def test_enviar_comunicacao_lead_envio_marketing_fila_masssend(mocker):
    enviar_mock = mocker.patch("comunicacao.service.comunicacao_svc._enviar_comunicacao_chunk_task.apply_async")
    # mock para entrar no comportamento de envio grande
    mocker.patch("comunicacao.service.comunicacao_svc.CHUNK_SIZE", 3)
    leads = baker.make("core.Lead", _quantity=4)
    leads_ids = [lead.id for lead in leads]
    comunicacao, params = _get_envio_params("lead", leads_ids, "push")
    comunicacao.tipo_disparo = Comunicacao.TipoDisparo.MARKETING
    comunicacao.save()

    form = EnviarComunicacaoForm(**params)
    user_staff = fixtures.user_staff()
    lote_envio = enviar_comunicacao(comunicacao, form, user_staff)

    assert lote_envio.comunicacao == comunicacao
    assert lote_envio.tipo_destinatario == "lead"
    assert leads_ids == lote_envio.destinatarios_ids
    templates = [template.dict() for template in form.templates]
    # dois chunks na fila masssend
    calls = [
        call(
            args=[
                ANY,
                templates,
                comunicacao,
                form.tipo_destinatario,
                form.agendado_para,
                lote_envio,
                user_staff,
                "masssend",
            ],
            queue="masssend",
        ),
    ]
    enviar_mock.assert_has_calls(calls)
    # são dois chunks
    assert enviar_mock.call_count == 2


def _get_envio_params(tipo, dest_ids, canal):
    template_base = baker.make(
        "comunicacao.Template",
        comunicacao__codigo="important_information",
        comunicacao__schema='{"user": {"first_name"}, "subconteudo": "teste"}',
        comunicacao__categoria="travel",
        canal="whatsapp",
        codigo="important_information",
        corpo="Oieeeeeee, {{user.first_name}}! {{subconteudo}}",
        remetente="5511991142263",
    )

    template = baker.make(
        "comunicacao.Template",
        comunicacao__codigo="apreensao_1_hora",
        comunicacao__schema='{"user": {"first_name"}}',
        comunicacao__tipo_disparo="manual",
        comunicacao__categoria="travel",
        canal=canal,
        codigo="apreensao_1_hora",
        corpo="sou um template manual!",
        titulo="teste" if canal in ["email", "push", "inbox"] else None,
        remetente="5511991142263",
        inbox_rota="Travel" if canal == "inbox" else None,
        push_url="http://buser.teste" if canal == "push" else None,
        whatsapp_template_base=template_base if canal == "whatsapp" else None,
    )

    return template.comunicacao, {
        "tipo_destinatario": tipo,
        "comunicacao_id": template.comunicacao.id,
        "comunicacao_marketing": False,
        "comunicacao_nome": template.comunicacao.nome,
        "ids": dest_ids,
        "templates": [
            {
                "id": template.id,
                "codigo": "apreensao_1_hora",
                "comunicacao_id": template.comunicacao.id,
                "canal": Template.Canal[canal.upper()].label,
                "titulo": template.titulo,
                "corpo": (
                    "BUSER: Olá, {{user.first_name}}! Temos atualizações sobre a sua viagem. "
                    "Confira as comunicações que enviamos para você\n"
                ),
                "remetente": 5511991142263 if canal == "whatsapp" else None,
                "inbox_rota": template.inbox_rota,
                "push_url": template.push_url,
                "ativo": True,
                "template_base": template_base.codigo if canal == "whatsapp" else None,
                "rendered": {},
                "selecionado": True,
            }
        ],
    }


def test_send_zap_credit_card_refused():
    user = fixtures.user_ze()
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="recompra_cc_rejected",
        tipo_disparo="transacional",
        schema={"user": {"first_name": "Bia"}},
    )
    baker.make(
        "comunicacao.Template",
        codigo=f"{comunicacao.codigo}_v2",
        canal="whatsapp",
        remetente="5511913590868",
        comunicacao=comunicacao,
        corpo=(
            "Olá, {{user.first_name}}! Vimos que houve um problema com a sua transação. Mas não se preocupe, o nosso "
            "time está disponível para te ajudar! Responda essa mensagem para garantir sua reserva."
        ),
    )
    user_notification_svc.send_zap_pax_with_credit_card_refused([user])
    comunicacao_envio = Envio.objects.get(user=user, template__canal="whatsapp")
    assert comunicacao_envio.status == "enviado"
    assert comunicacao_envio.destinatario == user.profile.cell_phone
    assert comunicacao_envio.evento == "Command send_zap_payment_refused_credit_card"


def test_staff_comunica_cancelamento_risco(mocker):
    mocker.patch(
        "core.service.notifications.user_notification_svc._extrato_travel", return_value={"extrato": {"paid_value": 31}}
    )

    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="cancelamento_risco",
        tipo_disparo="transacional",
        schema={
            "nome": "Bia",
            "reservation_code": "ABC123",
            "link_reembolso": "https://www.buser.com.br/reembolso",
            "link_ajuda": "https://www.buser.com.br/ajuda",
            "origem_fullname": "Belo Horizonte - MG",
            "destino_fullname": "São Paulo - SP",
            "valor_ressarcimento": 31.00,
        },
        notificar_user=True,
        notificar_buseiro=True,
    )

    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="whatsapp",
        remetente="5511913590868",
        comunicacao=comunicacao,
        corpo=(
            "Olá {{nome}}."
            "\nTivemos que cancelar a sua viagem para {{destino_fullname}}, "
            "porque não conseguiremos realizá-la nessa data."
            "\n\nReserva cancelada: {{reservation_code}}\nDe: {{origem_fullname}}\n"
            "Para: {{destino_fullname}}nValor reembolsado: R$ {{valor_ressarcimento}}"
            "\n\nVocê pode adquirir uma passagem similar à sua reserva original "
            "(data, hora, local e classe) e faremos o reembolso caso haja diferença de preço."
            "\n\nEnvie o comprovante pelo link {{link_reembolso}} <NAME_EMAIL>"
            "\n\nSe sua viagem foi paga com cupom e ele ainda for válido, será devolvido ao seu cadastro."
            "\n\nAcesse {{link_ajuda}} se precisar de ajuda."
        ),
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="sms",
        comunicacao=comunicacao,
        corpo="Reserva cancelada pra você, pax",
    )

    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        titulo="Cancelado por risco",
        comunicacao=comunicacao,
        corpo="Reserva cancelada pra você, pax",
    )

    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="push",
        titulo="Cancelado por risco",
        comunicacao=comunicacao,
        corpo="Reserva cancelada pra você, pax",
    )

    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        titulo="Cancelado por risco",
        comunicacao=comunicacao,
        corpo="Reserva cancelada pra você, pax",
    )

    user_joao = fixtures.user_ze()
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    trecho_classe = baker.make("core.TrechoClasse", datetime_ida=to_default_tz_required(now()))
    travel = baker.make(
        "core.Travel",
        trecho_classe=trecho_classe,
        user=user_joao,
        reservation_code="ABC123",
        max_split_value=Decimal("90"),
    )
    baker.make("core.Buseiro", name="Passageiro1", user=user_joao, passageiro_set__travel=travel)
    baker.make(
        "core.Buseiro",
        name="Passageiro2",
        user=user_joao,
        passageiro_set__travel=travel,
        phone=12999990002,
        email="<EMAIL>",
    )
    passageiro = travel.passageiro_set.last()
    user_notification_svc.viagem_cancelada_por_risco(travel, "reserva.CancelByRisco")

    comunicacao_envio_pax = Envio.objects.filter(buseiro=passageiro.buseiro)
    comunicacao_envio_user = Envio.objects.filter(user=user_joao)

    assert len(comunicacao_envio_pax) == 2
    assert len(comunicacao_envio_user) == 5
    assert comunicacao_envio_user[0].status == "enviado"
    assert comunicacao_envio_user[0].evento == "reserva.CancelByRisco"
    assert comunicacao_envio_user[0].user == user_joao
    assert comunicacao_envio_pax[0].status == "enviado"
    assert comunicacao_envio_pax[0].evento == "reserva.CancelByRisco"
    assert comunicacao_envio_pax[0].destinatario == passageiro.buseiro.phone


def test_comunica_compra_addons(mocker):
    user_joao = fixtures.user_joao()
    comunicacao = baker.make(
        "comunicacao.Comunicacao",
        codigo="itens_adicionais_comprados",
        tipo_disparo="transacional",
        notificar_buseiro=False,
        notificar_user=True,
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="email",
        comunicacao=comunicacao,
        titulo="{{titulo}}",
        corpo="<p>Você contratou {{itens_contratados.0.tipo}}.</p>",
    )
    baker.make(
        "comunicacao.Template",
        codigo=comunicacao.codigo,
        canal="inbox",
        comunicacao=comunicacao,
        titulo="{{titulo}}",
        corpo="<p>Você contratou {{itens_contratados.0.tipo}} bagagem extra.</p>",
    )

    trecho_classe = baker.make("core.TrechoClasse", _fill_optional=True)
    travel = baker.make(
        "core.Travel",
        user=user_joao,
        trecho_classe=trecho_classe,
        grupo_classe__tipo_assento="executivo individual",
        _fill_optional=True,
    )
    passageiro = baker.make("core.Passageiro", travel=travel)

    data = {
        "valor_total": Decimal("50.00"),
        "seguro_extra": False,
        "quantidade_bagagem_adicional": 2,
        "passengers": [{"pid": passageiro.id}],
        "payment": {"method": "credit_card"},
    }

    mocker.patch("accounting.service.accounting_svc._get_valor_bagagem_adicional", return_value=Decimal("16.22"))
    mocker.patch("core.service.itens_adicionais_svc.validate_expected_value", return_value=True)
    mocker.patch(
        "core.service.itens_adicionais_svc._order_itens_adicionais_with_payment",
        return_value=[
            ItemAdicional(
                travel=travel,
                tipo=ItemAdicional.TipoItemAdicional.BAGAGEM_ADICIONAL,
                valor=Decimal("50.00"),
                quantidade=2,
                status=ItemAdicional.StatusItemAdicional.CONCLUIDO,
            )
        ],
    )

    upsell_info_bagagem = ItemAdicionalUpsellInfoItem(upgrade_available=True, price=Decimal("10.00"))
    upsell_info_seguro = ItemAdicionalUpsellInfoItem(upgrade_available=True, price=Decimal("20.00"))
    upsell_info = ItemAdicionalUpsellInfo(
        assento=None,
        bagagem=upsell_info_bagagem,
        seguro=upsell_info_seguro,
    )
    mocker.patch("core.service.itens_adicionais_svc.get_travel_upsell_info", return_value=upsell_info)

    itens_adicionais = itens_adicionais_svc.validate_and_purchase_itens_adicionais(
        travel_id=travel.id, user=user_joao, data=data
    )
    user_notification_svc.notifica_itens_adicionais_comprados(travel, itens_adicionais)

    comunicacao_envio = Envio.objects.filter(user=user_joao)
    assert len(comunicacao_envio) == 2
