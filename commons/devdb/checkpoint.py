from core.models_parada import CheckpointParada
from core.models_rota import Checkpoint

BHZ_LOCAL = 2008
RIO_LOCAL = 1515
SAO_LOCAL = 376
RAO_LOCAL = 94
NITEROI_LOCAL = 1917
IPA_LOCAL = 1513
SJK_LOCAL_SETE_ESTRELAS = 1077
SJK_COLINAS = 1101
CGR_LOCAL = 9185
CBA_LOCAL = 7774

ITINERARIO_ROTA_15282 = [
    Checkpoint(
        pk=3221,
        local_id=RIO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=15282,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=3631,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=15282,
        idx=1,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_12595 = [
    Checkpoint(
        pk=3551,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=12595,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=2318,
        local_id=IPA_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=12595,
        idx=1,
        distancia_km=15,
        duracao="00:20:00",
        tempo_embarque="00:20:00",
    ),
    Checkpoint(
        pk=3442,
        local_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=12595,
        idx=2,
        distancia_km=210,
        duracao="01:00:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_13409 = [
    Checkpoint(
        pk=3560,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=13409,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=3362,
        local_id=RAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=13409,
        idx=1,
        distancia_km=75,
        duracao="01:10:00",
        tempo_embarque="00:00:00",
    ),
]
ITINERARIO_ROTA_12393 = [
    Checkpoint(
        pk=3033,
        local_id=RIO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=12393,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=3973,
        local_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=12393,
        idx=1,
        distancia_km=45,
        duracao="10:00:00",
        tempo_embarque="00:00:00",
    ),
]
ITINERARIO_ROTA_14591 = [
    Checkpoint(
        pk=3592,
        local_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14591,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=3527,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14591,
        idx=1,
        distancia_km=431,
        duracao="06:30:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_14592 = [
    Checkpoint(
        pk=36570,
        local_id=SAO_LOCAL,  # Barra Funda - SP
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14592,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=36571,
        local_id=SJK_LOCAL_SETE_ESTRELAS,  # SJK POSTO SETE ESTRELAS- SP
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14592,
        idx=1,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=36572,
        local_id=RIO_LOCAL,  # Tijuca - RJ
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14592,
        idx=2,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=36573,
        local_id=NITEROI_LOCAL,  # Niteroi - RJ
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=14592,
        idx=3,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_DOUBLE_STOP = [
    Checkpoint(
        pk=570,
        local_id=SAO_LOCAL,  # Barra Funda - SP
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=592,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=571,
        local_id=SJK_LOCAL_SETE_ESTRELAS,  # SJK POSTO SETE ESTRELAS- SP
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=592,
        idx=1,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=572,
        local_id=SJK_COLINAS,  # SJK COLINAS - SP
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=592,
        idx=2,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=573,
        local_id=RIO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=592,
        idx=3,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
]
ITINERARIO_ROTA_CONEXAO_RIO_SAO = [
    Checkpoint(
        pk=32715,
        local_id=RIO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=19105,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    CheckpointParada(
        pk=36000,
        checkpoint_origem_id=32715,
        checkpoint_destino_id=32716,
        local_id=SJK_COLINAS,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=19105,
        idx=0,
        distance_km=400,
        duracao="05:30:00",
        tempo_parada="00:10:00",
    ),
    CheckpointParada(
        pk=36001,
        checkpoint_origem_id=32715,
        checkpoint_destino_id=32716,
        local_id=SJK_LOCAL_SETE_ESTRELAS,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=19105,
        idx=1,
        distance_km=410,
        duracao="05:50:00",
        tempo_parada="00:10:00",
    ),
    Checkpoint(
        pk=32716,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=19105,
        idx=1,
        distancia_km=500,
        duracao="07:30:00",
        tempo_embarque="00:00:00",
    ),
]


ITINERARIO_ROTA_CONEXAO_SAO_BHZ = [
    Checkpoint(
        pk=32717,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=15914,
        idx=0,
        distancia_km=None,
        duracao="00:00:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=32718,
        local_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=15914,
        idx=1,
        distancia_km=600,
        duracao="09:30:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_MARKETPLACE_INTEGRADA = [
    Checkpoint(
        pk=35270,
        local_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=16689,
        idx=0,
        distancia_km=431,
        duracao="06:30:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=35271,
        local_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=16689,
        idx=1,
        distancia_km=431,
        duracao="06:30:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_118203 = [
    Checkpoint(
        pk=1182031,
        local_id=CBA_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=118203,
        idx=0,
        distancia_km=0,
        duracao="06:30:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=1182032,
        local_id=CGR_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=118203,
        idx=1,
        distancia_km=760,
        duracao="12:40:00",
        tempo_embarque="00:00:00",
    ),
]

ITINERARIO_ROTA_118012 = [
    Checkpoint(
        pk=1180121,
        local_id=CBA_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=118012,
        idx=0,
        distancia_km=0,
        duracao="12:36:00",
        tempo_embarque="00:00:00",
    ),
    Checkpoint(
        pk=1180122,
        local_id=CGR_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        rota_id=118012,
        idx=1,
        distancia_km=754,
        duracao="12:36:00",
        tempo_embarque="00:00:00",
    ),
]
CHECKPOINT_DATA = (
    ITINERARIO_ROTA_15282
    + ITINERARIO_ROTA_12595
    + ITINERARIO_ROTA_13409
    + ITINERARIO_ROTA_12393
    + ITINERARIO_ROTA_14591
    + ITINERARIO_ROTA_MARKETPLACE_INTEGRADA
    + ITINERARIO_ROTA_14592
    + ITINERARIO_ROTA_CONEXAO_RIO_SAO
    + ITINERARIO_ROTA_CONEXAO_SAO_BHZ
    + ITINERARIO_ROTA_DOUBLE_STOP
    + ITINERARIO_ROTA_118203
    + ITINERARIO_ROTA_118012
)
