from core.models_rota import Rota

BHZ_LOCAL = 2008
RIO_LOCAL = 1515
SAO_LOCAL = 376
RAO_LOCAL = 94
NITEROI_LOCAL = 1917
CGR_LOCAL = 9185
CBA_LOCAL = 7774

ROTAS_DATA = [
    Rota(
        pk=15282,
        origem_id=RIO_LOCAL,
        destino_id=SAO_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=57.3,
        distancia_total=453,
        duracao_total="8:00:00",
    ),
    Rota(
        pk=12595,
        origem_id=SAO_LOCAL,
        destino_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        distancia_total=663,
        duracao_total="10:00:00",
    ),
    Rota(
        pk=13409,
        origem_id=SAO_LOCAL,
        destino_id=RAO_LOCAL,
        distancia_total=310,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=69.8,
        duracao_total="04:00:00",
    ),
    Rota(
        pk=12393,
        origem_id=RIO_LOCAL,
        destino_id=BHZ_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        distancia_total=460,
        duracao_total="06:00:00",
    ),
    Rota(
        pk=14591,
        origem_id=BHZ_LOCAL,
        destino_id=SAO_LOCAL,
        distancia_total=668,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=21.6,
        duracao_total="10:00:00",
    ),
    Rota(
        pk=14592,
        origem_id=SAO_LOCAL,
        destino_id=NITEROI_LOCAL,  # Niteroi
        distancia_total=668,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=21.6,
        duracao_total="10:00:00",
    ),
    Rota(
        pk=592,
        origem_id=SAO_LOCAL,
        destino_id=NITEROI_LOCAL,  # Niteroi
        distancia_total=678,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=21.6,
        duracao_total="10:20:00",
    ),
]

ROTA_BUSER = ROTAS_DATA[2]


ROTA_CONEXAO_RIO_SAO = Rota(
    pk=19105,
    origem_id=RIO_LOCAL,
    destino_id=SAO_LOCAL,
    distancia_total=441,
    created_at="2024-02-06T00:00:12.128645-03:00",
    updated_at="2024-02-06T00:00:12.128645-03:00",
    pedagio_por_eixo=21.6,
    duracao_total="07:10:00",
)
ROTA_CONEXAO_SAO_BHZ = Rota(
    pk=15914,
    origem_id=SAO_LOCAL,
    destino_id=BHZ_LOCAL,
    distancia_total=584,
    created_at="2024-02-06T00:00:12.128645-03:00",
    updated_at="2024-02-06T00:00:12.128645-03:00",
    pedagio_por_eixo=21.6,
    duracao_total="08:40:00",
)

# Registros marketplace integrado precisam ter ID estatico, pq são referenciados no devdb do projeto rodoviaria
ROTA_MARKETPLACE_INTEGRADA_DATA = Rota(
    pk=16689,
    origem_id=BHZ_LOCAL,
    destino_id=SAO_LOCAL,
    created_at="2024-02-06T00:00:12.128645-03:00",
    updated_at="2024-02-06T00:00:12.128645-03:00",
    pedagio_por_eixo=57.3,
    distancia_total=453,
    duracao_total="8:00:00",
)
ROTA_LOCAL_TZ_DIFERENTE = [
    Rota(
        pk=118203,
        origem_id=CBA_LOCAL,
        destino_id=CGR_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=57.3,
        distancia_total=760,
        duracao_total="12:40:00",
    ),
    Rota(
        pk=118012,
        origem_id=CGR_LOCAL,
        destino_id=CBA_LOCAL,
        created_at="2024-02-06T00:00:12.128645-03:00",
        updated_at="2024-02-06T00:00:12.128645-03:00",
        pedagio_por_eixo=57.3,
        distancia_total=756,
        duracao_total="12:36:00",
    ),
]

ROTAS_DATA.append(ROTA_CONEXAO_RIO_SAO)
ROTAS_DATA.append(ROTA_CONEXAO_SAO_BHZ)

ROTAS_DATA.append(ROTA_MARKETPLACE_INTEGRADA_DATA)
ROTAS_DATA.extend(ROTA_LOCAL_TZ_DIFERENTE)
