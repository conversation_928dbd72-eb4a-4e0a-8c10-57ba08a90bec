import json
import random
from contextlib import contextmanager
from datetime import timed<PERSON><PERSON>
from decimal import Decimal as D
from http import HTTPStatus
from typing import Any, Callable
from unittest import mock
from unittest.mock import MagicMock

import django_cache_mock
import pytest
import time_machine
from decouple import config
from django.conf import settings
from django.contrib.auth.models import Permission
from django.test import SimpleTestCase
from model_bakery import baker
from requests import Response

from accounting.service import accounting_svc
from adapters.mock import mock_pix_adapter
from adapters.mock import mock_smartcam_adapter_requests as smartcammocker
from adapters.mock import mock_stark_adapter_requests as starkmocker
from adapters.sms_adapter import SMSMessage
from adapters.torre_de_controle_adapter.client import TorreDeControle
from buser import roles
from buser.celery import app
from commons import dateutils, guard
from commons.faker.automotive import AutomotiveProvider
from commons.utils import hashint, only_numbers
from core.enums import CategoriaEspecial
from core.forms.buckets_forms import TrechoClasseRateioForm
from core.models_grupo import Grupo, TrechoClasse
from core.models_rota import Rota, TrechoVendido
from core.models_travel import Travel
from core.service import device_svc, globalsettings_svc
from core.service.preco_svc import _rateio_process
from core.service.reserva import gratuidade_marketplace_svc, reserva_svc
from core.tests import fixtures
from core.tests.common_operations import clear_cache, clear_outbox
from core.tests.testes_staff.conftest import _grupo_1_classe, _local_embarque, _make_rota_multi_trecho
from integrations.infobip_client.dummy import InfobipDummyClient

pytest_plugins = ("celery.contrib.pytest",)

PYTEST_RANDOMLY_ENABLED = config("PYTEST_RANDOMLY_ENABLED", cast=bool, default=False)

if settings.DISABLE_TESTS:  # pragma: no cover
    raise Exception("WTF?! Não pode rodar testes nesse ambiente.")

app.conf.task_always_eager = True


def pytest_configure(config):
    configure_pytest_randomly(config)
    configure_test_settings()


def configure_pytest_randomly(config):
    # CI normal (fixed seed / fixed order)
    if config.option.randomly_seed == 0:
        config.option.randomly_reorganize = False

    # CI "pytest randomly" (random seed / random order)
    elif PYTEST_RANDOMLY_ENABLED:
        config.option.randomly_reorganize = True

    # local `pytest` (random seed / fixed order)
    elif config.option.randomly_seed == "default":
        config.option.randomly_reorganize = False

    # local `pytest --randomly-seed=<...>` (fixed seed / random order)
    else:
        config.option.randomly_reorganize = True


@pytest.fixture(scope="session", autouse=True)
def faker_session_locale():
    return ["pt-BR"]


@pytest.fixture(scope="session", autouse=True)
def faker_add_providers(_session_faker):
    return _session_faker.add_provider(AutomotiveProvider)


@pytest.fixture(scope="session")
def user_factory():
    return fixtures.user_factory


@pytest.fixture
def travels_factory(user_factory):
    def factory(
        datetime_ida=dateutils.to_default_tz_required(dateutils.now()),
        qty_users=6,
        qty_pax_per_user=1,
        with_payment=True,
        with_device=False,
        modelo_venda=Grupo.ModeloVenda.BUSER,
    ):
        """
        Mock para travels.
        O ideal eh manter o parametro qty_users numeros pares por conta de processos durante os testes
        """
        users = [user_factory(lead=True, profile=True) for u in range(qty_users)]

        rota = baker.make(Rota)
        novo_local_embarque = baker.make("core.LocalEmbarque")
        rota_multitrecho = fixtures._mock_rota_3_locais(rota.origem, rota.destino, novo_local_embarque)
        grupo = baker.make(Grupo, rota=rota_multitrecho, datetime_ida=datetime_ida, modelo_venda=modelo_venda)
        _update_checkpoints_datetime_and_status(grupo)

        origem = rota_multitrecho.itinerario.all()[0].local
        destino = rota_multitrecho.itinerario.all()[1].local

        trecho_vendido = baker.make(TrechoVendido, origem=origem, destino=destino, rota=rota_multitrecho)
        trecho_classe = baker.make(
            TrechoClasse,
            trecho_vendido=trecho_vendido,
            grupo=grupo,
            datetime_ida=datetime_ida,
            grupo_classe__tipo_assento="leito",
            grupo_classe__grupo=grupo,
            duracao_ida=timedelta(hours=2),
        )

        for u in users:
            travel = baker.make(
                Travel,
                user=u,
                grupo=grupo,
                trecho_classe=trecho_classe,
                trecho_vendido=trecho_vendido,
                grupo_classe=trecho_classe.grupo_classe,
            )
            travel.reservation_code = travel.generate_reservation_code()
            if with_payment:
                travel.pagamento = baker.make(
                    "core.Pagamento", status="paid", method="naoimporta", jsondata='{"json": "data"}'
                )
            if with_device:
                baker.make("core.Device", profile=u.profile, kind="fcm")
            travel.save()
            buseiro = baker.make("core.Buseiro", user=u, _fill_optional=["rg_number", "rg_orgao"])
            baker.make("core.Passageiro", travel=travel, _quantity=qty_pax_per_user, buseiro=buseiro)

        return Travel.objects.filter(grupo=grupo)

    return factory


def _update_checkpoints_datetime_and_status(grupo, datetime_checkin=dateutils.to_default_tz_required(dateutils.now())):
    checkpoints = json.dumps(
        {
            0: {
                "checkin_open": datetime_checkin.isoformat(),
                "checkin_closed": (datetime_checkin + timedelta(minutes=5)).isoformat(),
            }
        }
    )
    grupo.checkpoints_datetime = checkpoints
    grupo.status = Grupo.Status.TRAVEL_CONFIRMED
    grupo.save(update_fields=["checkpoints_datetime", "status"])


@pytest.fixture
def user(db, faker):
    username = "user"
    user = fixtures.user_factory(
        username=username,
        first_name=faker.first_name(),
        last_name=faker.last_name(),
        email=f"{username}@gmail.com",
        password="password",
        profile__cpf="57188327068",
        profile__cell_phone="88923282383",
        profile__cell_phone_confirmation_code="123546",
    )
    device_svc.update_or_create_device(user.profile, "xyz", kind=device_svc.FCM)
    return user


@pytest.fixture
def driver(db):
    company = baker.make("core.Company", name="name")
    return fixtures.user_factory(
        roles=["driver"],
        email="<EMAIL>",
        profile__company=company,
    )


@pytest.fixture
def user_buser(db):
    return fixtures.user_factory(
        email="<EMAIL>",
    )


@pytest.fixture
def driver2(db):
    company = baker.make("core.Company", name="nome")
    return fixtures.user_factory(
        roles=["driver"],
        email="<EMAIL>",
        profile__company=company,
    )


@pytest.fixture
def client_with_logged_user(client, user):
    client.force_login(user)
    return client


def is_old_test(test_instance):
    return isinstance(test_instance, SimpleTestCase)


@pytest.fixture(autouse=True)
def infobip_sms_client_mock():
    dummy_client = InfobipDummyClient()
    with mock.patch("adapters.sms_adapter._get_client", return_value=dummy_client):
        yield


@pytest.fixture()
def test_settings():  # precisar existir porque alguns testes ainda usam
    return configure_test_settings()


def configure_test_settings():
    settings.CELERY_TASK_ALWAYS_EAGER = True
    settings.PAYMENT_PROVIDERS = {
        "mercadopago": {
            "class": "core.payment.providers.mercadopago.MercadoPagoProvider",
            "api_key": "api_key_mercadopago",
            "notification_url": "https://example.com",
            "provider": "mercadopago",
        },
        "dummy": {
            "class": "core.payment.providers.dummy.DummyProvider",
        },
        "mercadopago_sa": {
            "class": "core.payment.providers.mercadopago.MercadoPagoProvider",
            "api_key": "api_key_mercadopago",
            "notification_url": "https://example.com",
            "provider": "mercadopago_sa",
        },
    }
    settings.RODOVIARIA_API_URL = "http://buserpassagens.service.consul"
    settings.RODOVIARIA_OPTIONS = {
        # TODO: usar cliente dummy sempre, exceto nos testes do próprio cliente
        "class": "integrations.rodoviaria_client.RodoviariaClient",
        "url": settings.RODOVIARIA_API_URL,
    }
    settings.PRICING_OPTIONS = {
        "class": "integrations.pricing_client.dummy.PricingClientDummy",
        "url": settings.PRICING_BASE_URL,
    }
    settings.GOOGLE_MAPS_OPTIONS = {
        "class": "integrations.google_maps_client.dummy.GoogleMapsDummyClient",
        "url": None,
    }
    settings.MOTORISTA_OPTIONS = {
        "class": "integrations.motorista_client.dummy.MotoristaClientDummy",
        "url": None,
    }
    settings.HIPLATFORM_OPTIONS = {
        "class": "integrations.hiplatform_client.dummy.HiPlatformClientDummy",
        "url": None,
    }
    settings.INFOBIP_OPTIONS = {
        "class": "integrations.infobip_client.dummy.InfobipDummyClient",
        "url": None,
    }
    settings.BITLY_OPTIONS = {
        "class": "integrations.bitly_client.dummy.BitLyClientDummy",
        "url": "https://bitly.example.com",
    }
    # _TEST_DOMAIN é uma configuração específica para os testes,
    # funciona em conjunto com a commons.sessions.cache_testing.
    settings._TEST_DOMAIN = "testserver"
    settings.PASSWORD_HASHERS = [
        "django.contrib.auth.hashers.UnsaltedMD5PasswordHasher",
    ]
    settings.SESSION_ENGINE = "commons.sessions.cache_testing"
    settings.AMPLITUDE_ADAPTER = "adapters.amplitude_adapter.dummy"
    settings.SMS_PROVIDER = "infobip"
    settings.SNS_ADAPTER = "adapters.sns_adapter.dummy"
    settings.CHAT_ADAPTER = "adapters.zap_adapter.dummy"
    settings.FRESHCHAT_ADAPTER = "adapters.freshchat_adapter.dummy"
    settings.DOC_CHECK_ADAPTER = "adapters.doc_check_adapter.dummy"
    settings.STARKV2_PROJECT_ID = "**********"
    settings.STARKV2_PKPEM_B64 = starkmocker.UMA_CHAVE_PRIVADA_QUALQUER
    settings.MERCADOPAGO_API_KEY = "api_key_mercadopago"
    settings.SMARTCAM_IDENTIFY_CODE = smartcammocker.IDENTIFY_CODE_QUALQUER
    settings.SMARTCAM_SECRET = smartcammocker.UMA_SECRET_QUALQUER
    settings.UNLEASH_URL = None
    settings.CELERY_TASK_ALWAYS_EAGER = True
    settings.BATCH_TASK_EAGER = True
    settings.BATCH_TASK_EAGER_PROPAGATES = True

    django_cache_mock.patch(settings.CACHES, "default", "mockcache", force=True)
    django_cache_mock.patch(settings.CACHES, "redis", "fakeredis", force=True)

    # Para o Django logar os SQLs, precisa estar em debug mode.
    settings.DEBUG = config("DEBUG_SQL", default=False, cast=bool)

    # # usa um só banco durante os testes
    # # https://github.com/pytest-dev/pytest-django/issues/924
    # # pytest-django tem bugs com vários bancos de dados, desisti :(
    # # e faz essa configuração aqui e não na fixture test_settings por causa
    # # dessa outra issue: https://github.com/pytest-dev/pytest-django/issues/643
    # settings.DATABASE_ROUTERS = []
    # del settings.DATABASES['aurora']
    # settings.DATABASES['default']['ENGINE'] = 'psqlextra.backend'

    settings.SKIP_SLEEP = True
    settings.DJAMAIL_USE_PREMAILER = False
    return settings


@pytest.fixture(autouse=True)
# test_settings is here to execute it before this fixture
def pre_setup(request, db):
    # Setup fails for SimpleTestCase instances
    # This setup is used for all tests and is needed for "pytest-like" tests.
    # In old test case suite, this setup is done using monkey patching. Check it on
    # core/teste/__init__.py
    if is_old_test(request.instance):
        yield
    else:
        clear_outbox()
        clear_cache()
        yield
        clear_cache()


@pytest.fixture
def user_staff(db):
    return fixtures.user_staff()


@pytest.fixture
def user_ops(db):
    return fixtures.user_ops()


@pytest.fixture
def user_suporte(db):
    return fixtures.user_suporte()


@pytest.fixture
def user_marketing(db):
    return fixtures.user_marketing()


@pytest.fixture
def user_coupon_creator(db):
    return fixtures.user_coupon_creator()


@pytest.fixture
def user_dev(db):
    return fixtures.user_dev()


@pytest.fixture
def client_with_logged_staff(client, user_staff):
    client.force_login(user_staff)
    return client


@pytest.fixture
def client_with_pagar_bo_permission(client, user_staff):
    guard.assign_role(user_staff, "Operacoes")
    guard.set_permission(user_staff, roles.PAGAR_BO)
    client.force_login(user_staff)
    return client


@pytest.fixture
def bank_account(faker):
    return baker.make("core.BankAccount", agencia="0001", agencia_dv="0", bank_code="260", conta="132144", conta_dv="3")


@pytest.fixture
def empresa(faker, bank_account):
    return baker.make("core.Company", name=faker.company(), cnpj=only_numbers(faker.cnpj()), bank_account=bank_account)


@pytest.fixture
def user_parceiro(empresa):
    return fixtures.user_empresario(empresa, permissions=[roles.CARTEIRA_PARCEIRO])


@pytest.fixture
def client_parceiro(client, user_parceiro):
    client.force_login(user_parceiro)
    return client


@pytest.fixture
def user_qualidade():
    return fixtures.user_qualidade()


@pytest.fixture(scope="session", autouse=True)
def mock_validate_email_deliverability_fixture():
    with mock.patch("core.validators.validate_email_deliverability") as m:
        m.return_value = True
        yield


@pytest.fixture
def user_comercial(client):
    client.force_login(fixtures.user_comercial())
    return client


@pytest.fixture
def client_rotas(client):
    client.force_login(fixtures.user_rotas())
    return client


@pytest.fixture
def sms_outbox(settings):
    settings.SMS_ADAPTER = "adapters.mock.mock_sms_adapter"

    outbox = []

    def add_to_outbox(to, msg, template=None, *args, **kwargs):
        outbox.append(SMSMessage(to=to, message=msg, template=template))

    with mock.patch("adapters.sms_adapter.send_sms", side_effect=add_to_outbox):
        yield outbox


@pytest.fixture(scope="session")
def bypass_auto_now():
    """
    Essa fixture serve para criar objetos no banco que você precisa que tenham um created_at ou updated_at específico,
    sem fazer um patch no 'django.utils.timezone.now'. Só passar uma string com o model (ex: 'core.Lead'),
    qual o datetime que você quer que o objeto use e quaisquer outros parâmetros para a criação do objeto dado.
    """

    def factory(model, datetime, **kwargs):
        # - eu odeio o auto_now do ORM Django
        # Carrara, Erle - 2021
        with time_machine.travel(datetime):
            return baker.make(model, **kwargs)

    return factory


@pytest.fixture
def buckets_sujos():
    return [
        {"max_split_value": D(0), "tamanho": 10, "expiration_days": None},
        {"max_split_value": "22.00", "tamanho": 10, "expiration_days": None},
        {"max_split_value": 33.33, "tamanho": 20, "expiration_days": None},
    ]


@pytest.fixture
def buckets_limpos(buckets_sujos):
    match_rateio_buckets = _rateio_process(TrechoClasseRateioForm(buckets=buckets_sujos))
    return match_rateio_buckets.buckets_to_dict()


@pytest.fixture
def globalsettings_mock(mocker):
    original_get = globalsettings_svc.get
    original_set = globalsettings_svc.set
    flags = {}

    def get_side_effect(key, default=None):
        try:
            return flags[key]
        except KeyError:
            return original_get(key, default=default)

    def set_side_effect(key, value):
        if key in flags:
            flags[key] = value
        else:
            original_set(key, value)

    @contextmanager
    def fn(key, value):
        flags[key] = value

    get_mock = mocker.patch("core.service.globalsettings_svc.get", side_effect=get_side_effect)
    get_mock.uncached = get_side_effect
    get_mock.delete_memoized = original_get.delete_memoized
    mocker.patch("core.service.globalsettings_svc.set", side_effect=set_side_effect)
    return fn


@pytest.fixture
def mock_capacity_manager(mocker):
    mocker.patch("core.service.multitrecho_svc.CapacityManager.vagas", return_value=float("inf"))


@pytest.fixture(scope="session")
def featureflag_mock():
    def function(feature_flag_name, value=True, ff_function="is_user_enabled"):
        def side_effect(flag, user_id=None):
            if flag == feature_flag_name:
                return value
            return not value

        return mock.patch(f"commons.feature_flags.{ff_function}", side_effect=side_effect)

    return function


@pytest.fixture(params=[pytest.param(0, id="sync"), pytest.param(1, id="async")])
def enable_reserva_async(request, globalsettings_mock):
    flag = request.param
    globalsettings_mock("porcentagem_reserva_async", flag)
    return bool(flag)


@pytest.fixture
def pix_chain_use_stark_first(mocker):
    """
    We created this to make the implementation of Chain easier, most of our tests expected to use Stark as the payment
    provider, so instead of moving every test to MP we created this fixture that makes stark the first option.
    """
    from payments.chain import PaymentChain
    from payments.chain.pix import MercadoPagoPixHandler, StarkPixHandler

    def mock_payment_chain(payment_data, chain_type_key):
        from core.models_travel import Pagamento

        if payment_data.pagamento.method != Pagamento.Method.PIX:
            from payments.chain import get_chain_implementation

            return get_chain_implementation(payment_data, chain_type_key)
        return PaymentChain(
            [
                StarkPixHandler(),
                MercadoPagoPixHandler(),
            ]
        )

    mocker.patch("payments.service.chain_svc.get_chain_implementation", mock_payment_chain)


@pytest.fixture
def mocked_torre_response_delayed():
    return dict(
        arrivalTime=None,
        etaInSeconds=None,
        updatedEtaAt=None,
        busLocation=None,
        updatedBusLocationAt=None,
        delayedTime=300.0,
        categoryLabel="Algum atraso não previsto",
        reasonDelay="other",
    )


@pytest.fixture
def mocked_torre_response():
    datetime_base = dateutils.now()
    return dict(
        arrivalTime=datetime_base,
        etaInSeconds=(60 * 15),
        updatedEtaAt=(datetime_base - timedelta(minutes=15)),
        busLocation=[-14.12345, -34.12345],
        updatedBusLocationAt=(datetime_base - timedelta(minutes=5)),
        distanceInMeters=300.5,
        delayedTime=None,
        categoryLabel=None,
        reasonDelay=None,
    )


@pytest.fixture
def mocked_torre_request(mocker) -> Callable:
    def factory(
        status_code: int = HTTPStatus.OK,
        response_dict: dict | None = None,
    ) -> tuple[TorreDeControle, MagicMock]:
        if response_dict is None:
            response_dict = {}
        serialized_response = json.dumps(response_dict)
        adapter = TorreDeControle()
        mocked_response = Response()
        mocked_response.status_code = status_code
        mocked_response._content = serialized_response.encode()

        return adapter, mocker.patch.object(adapter.session, "request", return_value=mocked_response)

    return factory


@pytest.fixture
def grupos_ida_e_volta_factory(par_rotas_multitrecho, par_rotas_multitrecho_factory):
    def factory(distancia_viagem, percentual_taxa_servico=D("0"), modelo_venda=Grupo.ModeloVenda.BUSER):
        if distancia_viagem is None:
            rota_ida, rota_volta = par_rotas_multitrecho
        else:
            rota_ida, rota_volta = par_rotas_multitrecho_factory(distancia_viagem=distancia_viagem)
        ida = dateutils.now() + timedelta(days=7)
        volta = ida + timedelta(days=1)
        return _grupo_1_classe(
            rota_ida, ida, percentual_taxa_servico=percentual_taxa_servico, modelo_venda=modelo_venda
        ), _grupo_1_classe(
            rota_volta, volta, percentual_taxa_servico=percentual_taxa_servico, modelo_venda=modelo_venda
        )

    return factory


@pytest.fixture
def mock_pix(monkeypatch):
    monkeypatch.setattr("adapters.stark_adapter.pix.create_pix", mock_pix_adapter.gerar_pix)
    from core.payment.providers.mercadopago import MercadoPagoProvider

    monkeypatch.setattr(MercadoPagoProvider, "create_pix", mock_pix_adapter.gerar_pix)


@pytest.fixture
def par_rotas_multitrecho(faker):
    locais = [_local_embarque(faker) for _ in range(3)]
    rota_ida = _make_rota_multi_trecho(locais)
    rota_volta = _make_rota_multi_trecho(list(reversed(locais)))
    return rota_ida, rota_volta


@pytest.fixture
def rota_multitrecho(par_rotas_multitrecho):
    rota, _ = par_rotas_multitrecho
    return rota


@pytest.fixture
def par_rotas_multitrecho_factory(faker):
    def factory(distancia_viagem: int | None = None):
        if not distancia_viagem:
            distancia_viagem = 200
        locais = [_local_embarque(faker) for _ in range(3)]
        rota_ida = _make_rota_multi_trecho(locais, distancia_viagem=distancia_viagem)
        rota_volta = _make_rota_multi_trecho(list(reversed(locais)))
        return rota_ida, rota_volta

    return factory


@pytest.fixture
def user_pax(faker, user_factory):
    f_profile = faker.simple_profile()
    first_name, last_name = f_profile["name"].split(maxsplit=1)
    user = user_factory(
        first_name=first_name,
        last_name=last_name,
        profile__cpf=faker.cpf(),
        profile__cell_phone=faker.msisdn()[2:],
        profile__cell_phone_confirmation_code="123546",
    )
    device_svc.update_or_create_device(user.profile, "xyz", kind=device_svc.FCM)
    return user


@pytest.fixture
def user_viajar_gratis(faker, user_factory):
    f_profile = faker.simple_profile()
    first_name, last_name = f_profile["name"].split(maxsplit=1)
    user = user_factory(first_name=first_name, last_name=last_name, profile__cpf=faker.cpf())
    permissao = Permission.objects.get(codename="viajar_gratis")
    user.user_permissions.add(permissao)
    return user


@pytest.fixture
def grupos_ida_e_volta(par_rotas_multitrecho):
    rota_ida, rota_volta = par_rotas_multitrecho
    ida = dateutils.now() + timedelta(days=7)
    volta = ida + timedelta(days=1)
    return _grupo_1_classe(rota_ida, ida), _grupo_1_classe(rota_volta, volta)


@pytest.fixture
def reserva(
    grupos_ida_e_volta_factory, user_pax, user_staff, faker, mock_pix, db, modelo_venda=Grupo.ModeloVenda.BUSER
):
    def factory(
        pax=1,
        volta=False,
        user=user_pax,
        payment_method="pix",
        cupom=None,
        credito_carbono=False,
        seguro_extra=False,
        quantidade_bagagem_adicional=0,
        passengers=None,
        distancia_viagem=None,
        saldo_utilizado=D("0"),
        percentual_taxa_servico=D("0"),
        provider="mercadopago",
        categoria_especial=CategoriaEspecial.NORMAL,
        modelo_venda=modelo_venda,
        user_revendedor_id=None,
        qtd_assentos_selecionados=None,
    ):
        grupo_ida, grupo_volta = grupos_ida_e_volta_factory(
            distancia_viagem=distancia_viagem,
            percentual_taxa_servico=percentual_taxa_servico,
            modelo_venda=modelo_venda,
        )

        if not volta:
            grupo_volta = None
        comprador, dados = _dados_reserva(
            grupo_ida,
            pax,
            user,
            faker,
            grupo_volta,
            payment_method,
            cupom=cupom,
            credito_carbono=credito_carbono,
            seguro_extra=seguro_extra,
            quantidade_bagagem_adicional=quantidade_bagagem_adicional,
            passengers=passengers,
            provider=provider,
            categoria_especial=categoria_especial,
            user_revendedor_id=user_revendedor_id,
            qtd_assentos_selecionados=qtd_assentos_selecionados,
        )
        if saldo_utilizado > D("0"):
            dados["payment"]["value"] -= saldo_utilizado
            dados["payment"]["net_value"] -= saldo_utilizado
            fixtures.dar_reais(comprador, user_staff, saldo_utilizado)
        travels, _ = reserva_svc.efetuar_reserva_sincrona(dados, comprador)
        for travel in travels:
            if travel.pagamento:
                travel.pagamento.status = "paid"
                travel.pagamento.save()
        return travels

    return factory


def _dados_reserva(
    grupo_ida,
    qtd_pax,
    comprador,
    faker,
    grupo_volta=None,
    payment_method="pix",
    cupom=None,
    credito_carbono=False,
    seguro_extra=False,
    quantidade_bagagem_adicional=0,
    passengers=None,
    provider="mercadopago",
    categoria_especial=CategoriaEspecial.NORMAL,
    user_revendedor_id=None,
    qtd_assentos_selecionados=None,
):
    tc_ida = grupo_ida.trechoclasse_set.first()
    ticket = tc_ida.max_split_value

    dgroups = [
        {
            "id": str(tc_ida.id),
            "promoCode": cupom.code if cupom else None,
        }
    ]

    if grupo_volta:
        tc_volta = grupo_volta.trechoclasse_set.last()
        ticket += tc_volta.max_split_value
        dgroups.append(
            {
                "id": str(tc_volta.id),
                "promoCode": cupom.code if cupom else None,
            }
        )

    if not passengers:
        passengers = _fake_pax(faker, qtd_pax)
    if qtd_assentos_selecionados is None:
        passengers[0]["name"] = comprador.get_full_name()
        passengers[0]["cpf"] = comprador.profile.cpf

    valor_pgto = ticket * qtd_pax
    poltronas_selected = {}
    keys_identify_assentos_selecionados = ["id", "cpf", "rg_number"]
    if qtd_assentos_selecionados is not None:
        quant_assentos = qtd_assentos_selecionados
        poltronas_selected = {
            hashint(grupo_ida.trechoclasse_set.first().id): {
                passenger.get(random.choice(keys_identify_assentos_selecionados)): 13
                for passenger in passengers[0:quant_assentos]
            }
        }
        if grupo_volta:
            poltronas_selected[hashint(grupo_volta.trechoclasse_set.last().id)] = {
                passenger.get(random.choice(keys_identify_assentos_selecionados)): 13
                for passenger in passengers[0:quant_assentos]
            }

    if cupom and grupo_volta:
        valor_pgto -= ticket / D("2.0") * cupom.discount
    elif cupom:
        valor_pgto -= ticket * cupom.discount

    if credito_carbono:
        valor_carbono_ida = accounting_svc.calcular_valor_neutralizacao_carbono(tc_ida.trecho_vendido)
        valor_carbono_volta = 0
        if grupo_volta:
            valor_carbono_volta = accounting_svc.calcular_valor_neutralizacao_carbono(tc_volta.trecho_vendido)
        valor_carbono_total = (valor_carbono_ida + valor_carbono_volta) * qtd_pax
        valor_pgto += valor_carbono_total

    if seguro_extra:
        valor_seguro_extra_ida = accounting_svc.calcular_valor_seguro_extra_por_passageiros(
            tc_ida, passageiros=passengers
        )
        valor_seguro_extra_volta = 0
        if grupo_volta:
            valor_seguro_extra_volta = accounting_svc.calcular_valor_seguro_extra_por_passageiros(
                tc_volta, passageiros=passengers
            )
        valor_seguro_extra_total = valor_seguro_extra_ida + valor_seguro_extra_volta
        valor_pgto += valor_seguro_extra_total

    if poltronas_selected:
        valor_pgto += accounting_svc.calcula_valor_marcacao_assento(tc_ida) * qtd_assentos_selecionados
        if grupo_volta:
            valor_pgto += accounting_svc.calcula_valor_marcacao_assento(tc_volta) * qtd_assentos_selecionados

    if quantidade_bagagem_adicional > 0:
        valor_bagagem_adicional_ida = accounting_svc.calcular_valor_bagagem_adicional(quantidade_bagagem_adicional)
        valor_bagagem_adicional_volta = 0
        if grupo_volta:
            valor_bagagem_adicional_volta = accounting_svc.calcular_valor_bagagem_adicional(
                quantidade_bagagem_adicional
            )
        valor_bagagem_adicional_total = valor_bagagem_adicional_ida + valor_bagagem_adicional_volta
        valor_pgto += valor_bagagem_adicional_total

    if categoria_especial:
        desconto_categoria_especial_marketplace = gratuidade_marketplace_svc.desconto_categoria_especial(
            categoria_especial, qtd_pax, ticket
        )
        valor_pgto -= desconto_categoria_especial_marketplace

    data = {
        "passengers": passengers,
        "groups": dgroups,
        "credito_carbono": credito_carbono,
        "seguro_extra": seguro_extra,
        "quantidade_bagagem_adicional": quantidade_bagagem_adicional,
        "poltronas_selected": poltronas_selected,
        "categoria_especial": categoria_especial,
        "user_revendedor": user_revendedor_id,
        "payment": {
            "value": valor_pgto,
            "net_value": valor_pgto,
            "parcela_count": 1,
            "payment_method": payment_method,
            "provider": provider,
            "name": comprador.get_full_name(),
            "email": comprador.email,
            "cpf": only_numbers(comprador.profile.cpf),
            "phone": comprador.profile.cell_phone,
        },
    }

    if payment_method == "credit_card":
        data["payment"].update(
            {
                "bank_identification_number": faker.numerify("%#####"),
                "card_hash": faker.lexify("??????"),
                "card_name": comprador.get_full_name().upper(),
                "street": faker.street_name(),
                "streetnum": faker.building_number(),
                "cep": faker.postcode(),
                "state": faker.estado_sigla(),
                "city": faker.city(),
                "neighborhood": faker.bairro(),
                "card_brand": "master",
            }
        )
    return comprador, data


def _fake_pax(faker, qtd):
    pax_list = []
    for _ in range(qtd):
        f_profile = faker.simple_profile()
        f_pax = {
            "name": f_profile["name"],
            "cpf": faker.cpf(),
            "rg_number": faker.rg(),
            "rg_orgao": "SSP/SP",
            "tipo_documento": "RG",
            "birthday": f_profile["birthdate"].strftime("%d/%m/2000"),
        }
        pax_list.append(f_pax)
    return pax_list


@pytest.fixture
def mocked_response():
    def factory(json_data=None, status_code=HTTPStatus.OK):
        class MockResponse(Response):
            def __init__(self, json_data=json_data, status_code=status_code):
                self.json_data = json_data or {}
                self.status_code = status_code

            def json(self, *args, **kwargs) -> Any:
                return self.json_data

        return MockResponse()

    return factory


@pytest.fixture
def passageiro_com_historico_remanejamento_e_alteracao_travel():
    def factory():
        max_split_value_original = 100
        max_split_value_downgrade_1 = max_split_value_original * 0.95
        max_split_value_downgrade_2 = max_split_value_downgrade_1 * 0.95
        max_split_value_downgrade_3 = max_split_value_downgrade_2 * 0.95
        max_split_value_atual = max_split_value_downgrade_3 * 0.95
        # Remanejamento 1 - Downgrade
        grupo_original = baker.make("core.Grupo")
        grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
        travel_original = baker.make(
            "core.Travel",
            grupo_classe=grupo_classe_original,
            max_split_value=max_split_value_original,
            count_seats=1,
            grupo=grupo_original,
        )
        grupo_1 = baker.make("core.Grupo")
        grupo_classe_1 = baker.make("core.GrupoClasse", tipo_assento="cama premium")
        travel_1 = baker.make(
            "core.Travel",
            grupo_classe=grupo_classe_1,
            max_split_value=max_split_value_downgrade_1,
            count_seats=1,
            grupo=grupo_1,
            movido_de=grupo_original,
        )
        historico_1 = baker.make(
            "core.HistoricoRemanejamento",
            travel_nova=travel_1,
            travel_antiga=travel_original,
            antigo_tipo_assento="cama premium individual",
            novo_tipo_assento="cama premium",
        )

        # Alteração de classe 1 - Downgrade
        grupo_classe_2 = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
        travel_1.grupo_classe = grupo_classe_2
        travel_1.max_split_value = max_split_value_downgrade_2
        travel_1.save(update_fields=["grupo_classe", "max_split_value"])
        baker.make(
            "core.AlteracaoTravel",
            travel=travel_1,
            antigo_tipo_assento="cama premium",
            novo_tipo_assento="leito cama individual",
            tipo="change_class",
        )

        # Remanejamento 2 - Downgrade
        grupo_atual = baker.make("core.Grupo")
        grupo_classe_3 = baker.make("core.GrupoClasse", tipo_assento="leito cama")
        trecho_classe = baker.make(
            "core.TrechoClasse",
            datetime_ida=dateutils.to_default_tz_required(dateutils.now()),
            duracao_ida=timedelta(hours=3),
        )
        travel_atual = baker.make(
            "core.Travel",
            grupo_classe=grupo_classe_3,
            trecho_classe=trecho_classe,
            max_split_value=max_split_value_downgrade_3,
            count_seats=1,
            grupo=grupo_atual,
            movido_de=grupo_1,
        )
        baker.make(
            "core.HistoricoRemanejamento",
            travel_nova=travel_atual,
            travel_antiga=travel_1,
            antigo_tipo_assento="leito cama individual",
            novo_tipo_assento="leito cama",
        )

        # Alteração de classe 2 - Downgrade
        grupo_classe_4 = baker.make("core.GrupoClasse", tipo_assento="leito individual")
        travel_atual.grupo_classe = grupo_classe_4
        travel_atual.max_split_value = max_split_value_atual
        travel_atual.save(update_fields=["grupo_classe", "max_split_value"])
        baker.make(
            "core.AlteracaoTravel",
            travel=travel_atual,
            antigo_tipo_assento="leito cama",
            novo_tipo_assento="leito individual",
            tipo="change_class",
        )

        passageiro = baker.make("core.Passageiro", travel=travel_atual)
        baker.make(
            "core.AccountingOperation",
            travel=travel_original,
            passageiro=passageiro,
            source="RESERVA",
            value_real=-D("100"),
        )
        baker.make(
            "core.AccountingOperation",
            travel=travel_original,
            passageiro=passageiro,
            source="RESERVA_CANCELADA_PARCIAL",
            value_real=D("100"),
        )
        baker.make(
            "core.AccountingOperation",
            travel=travel_1,
            passageiro=passageiro,
            source="RESERVA",
            value_real=-D("100"),
        )
        baker.make(
            "core.AccountingOperation",
            travel=travel_1,
            passageiro=passageiro,
            source="RESERVA_CANCELADA_PARCIAL",
            value_real=D("100"),
        )
        baker.make(
            "core.AccountingOperation",
            travel=travel_atual,
            passageiro=passageiro,
            source="RESERVA",
            value_real=-D("100"),
        )
        return passageiro, historico_1

    return factory
