from decimal import Decimal as D

import pytest
from model_bakery import baker
from pytest_mock import MockerFixture

from accounting.models import AccountingOperation as AccopV2
from accounting.service import accounting_svc, credit_accounting_svc
from core.models_contabil import AccountingOperation as AccOpV1
from core.models_credito import CreditAccountingOperation
from core.service.reserva import reserva_svc


def test_pax_in_reserva_cancelada_parcial(reserva, user_pax, db):
    travel = reserva(pax=2, user=user_pax)[0]
    ultimo_pax = travel.passageiro_set.last()
    reserva_svc.remove_passenger(ultimo_pax, by_admin=True)
    assert AccOpV1.objects.filter(source="RESERVA_CANCELADA_PARCIAL", travel=travel, passageiro=ultimo_pax).exists()


def test_cancela_travel_sem_reserva(user_pax, db):
    # Agora temos a figura da travel sem nenhuma accounting operation
    # https://sentry.io/organizations/buser/issues/**********/?project=5258692&query=is%3Aunresolved
    travel = baker.make("core.Travel", user=user_pax)
    assert not travel.accountingoperation_set.exists()
    accounting_svc.cancela_registros_contabeis(travel, dry_run=False)


@pytest.mark.parametrize(
    "source",
    [
        "RESERVA",
        "RESERVA_DOWNGRADE",
        "PASSAGEIRO_COMPENSADO",
        "NEUTRALIZACAO_CARBONO",
        "SEGURO_EXTRA",
        "ADMIN_REAIS",
        "PROMOCAO",
        "PAGAMENTO_CREDITO",
        "RESERVA_REMANEJADA",
    ],
)
def test_cancela_accop(source: AccopV2.Source, mocker: MockerFixture):
    user = baker.make("auth.User")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    accop = baker.make(
        "accounting.AccountingOperation",
        source=source,
        value_real=D("10"),
        value=D("10"),
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
    )

    accounting_svc.cancela_accops([accop])

    accop_cancelamento = AccopV2.objects.all().exclude(source=source, value_real=accop.value_real)[0]
    assert accop_cancelamento.value_real == -accop.value_real
    assert accop_cancelamento.source == accounting_svc.MAP_SOURCE_CANCELAMENTO_SOURCE[source]
    assert accop_cancelamento.passageiro == passageiro
    assert accop_cancelamento.travel == passageiro.travel
    assert accop_cancelamento.user == user


@pytest.mark.parametrize(
    "source",
    [
        "RESERVA",
        "RESERVA_DOWNGRADE",
        "PASSAGEIRO_COMPENSADO",
        "NEUTRALIZACAO_CARBONO",
        "SEGURO_EXTRA",
        "ADMIN_REAIS",
        "PAGAMENTO_CREDITO",
        "RESERVA_REMANEJADA",
    ],
)
@pytest.mark.parametrize(
    "existe_cancelamento",
    [True, False],
)
def test_accops_to_cancel_retorna_vazio_pq_ja_existe_cancelamento(
    source: AccopV2.Source, mocker: MockerFixture, existe_cancelamento: bool
):
    user = baker.make("auth.User")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    baker.make(
        "accounting.AccountingOperation",
        source=source,
        value_real=D("10"),
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
    )
    mocker.patch(
        "accounting.service.accounting_svc._cancelamento_existe",
        return_value=existe_cancelamento,
    )

    accops = accounting_svc._accops_to_cancel(passageiro)

    assert existe_cancelamento == bool(not accops)


@pytest.mark.parametrize("is_cancelado", [True, False])
def test_cancelamento_existe(is_cancelado: bool):
    source = "RESERVA_DOWNGRADE"
    source_cancelamento = "RESERVA_DOWNGRADE_CANCELADO"
    value = D("10")

    user = baker.make("auth.User")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    accop = baker.make(
        "accounting.AccountingOperation",
        source=source,
        value_real=value,
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
    )

    if is_cancelado:
        baker.make(
            "accounting.AccountingOperation",
            source=source_cancelamento,
            value_real=-value,
            passageiro=passageiro,
            user=user,
            travel=passageiro.travel,
        )

    cancelamento_existe = accounting_svc._cancelamento_existe(accop, accop.promocao)

    assert cancelamento_existe == is_cancelado


@pytest.mark.parametrize("use_travel", [True, False])
def test_cancela_registros_contabeis_flow(use_travel: bool, mocker: MockerFixture):
    user = baker.make("auth.User")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    valor_reserva = -D("10")
    valor_compensacao = D("5")
    baker.make(
        "accounting.AccountingOperation",
        source="RESERVA",
        value_real=valor_reserva,
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
    )
    baker.make(
        "accounting.AccountingOperation",
        source="PASSAGEIRO_COMPENSADO",
        value_real=valor_compensacao,
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
    )
    cancela_accops = mocker.patch("accounting.service.accounting_svc.cancela_accops")

    entity = passageiro
    if use_travel:
        entity = passageiro.travel
    saldo = accounting_svc.cancela_registros_contabeis(entity, dry_run=False)

    assert cancela_accops.called
    assert len(cancela_accops.call_args.args[0]) == 2
    assert saldo == -(valor_reserva + valor_compensacao)


def test_cancela_registros_creditos():
    source = "PAGAMENTO_USADO"
    user = baker.make("auth.User")
    gift_card = baker.make("core.GiftCard")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    credit_sequence = 1
    credit_accop = baker.make(
        "core.CreditAccountingOperation",
        source=source,
        value=D("20"),
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
        credit_sequence=credit_sequence,
        gift_card=gift_card,
    )

    credit_accounting_svc.cancela_registros_creditos(passageiro.travel, False)

    accop_cancelamento = CreditAccountingOperation.objects.all().exclude(source=source, value=credit_accop.value)[0]
    assert accop_cancelamento.value == -credit_accop.value
    assert accop_cancelamento.source == credit_accounting_svc.MAP_SOURCE_CREDITO_CANCELAMENTO[source]
    assert accop_cancelamento.passageiro == passageiro
    assert accop_cancelamento.travel == passageiro.travel
    assert accop_cancelamento.user == user
    assert accop_cancelamento.credit_sequence == credit_sequence
    assert accop_cancelamento.gift_card == gift_card


def test_credit_accops_to_cancel(mocker: MockerFixture):
    source = "PAGAMENTO_USADO"
    user = baker.make("auth.User")
    passageiro = baker.make("core.Passageiro", travel=baker.make("core.Travel", user=user))
    value = D("10")
    credit_accop = baker.make(
        "core.CreditAccountingOperation",
        source=source,
        value=value,
        passageiro=passageiro,
        user=user,
        travel=passageiro.travel,
        credit_sequence=1,
        gift_card=baker.make("core.GiftCard"),
    )
    credit_accops_cancelar, saldo_creditos_utilizado = credit_accounting_svc._credit_accops_to_cancel([passageiro])

    assert credit_accops_cancelar == [credit_accop]
    assert saldo_creditos_utilizado == value
