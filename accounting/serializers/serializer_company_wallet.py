import json
from dataclasses import asdict, dataclass
from datetime import timedelta
from decimal import Decimal as D

from django.db.models import Prefetch, Sum, prefetch_related_objects

from accounting.models import CompanyAccountingOperation, CompanyWallet
from commons.dateutils import now, to_default_tz
from core.models_rota import Checkpoint


@dataclass
class Saldo:
    saldo_frete: D
    saldo_dias_parados: D
    saldo_bonus: D
    saldo_emprestimo: D
    saldo_locadora: D
    saldo_pedagio: D
    motivo_bloqueio: str

    @property
    def bloqueado(self):
        return bool(self.motivo_bloqueio)


@dataclass
class Extrato:
    frete: list[CompanyAccountingOperation]
    dias_parados: list[CompanyAccountingOperation]
    bonus: list[CompanyAccountingOperation]
    emprestimo: list[CompanyAccountingOperation]
    pedagio: list[CompanyAccountingOperation]
    locadora: list[CompanyAccountingOperation]
    saldo: Sal<PERSON>


def extrato(company, days=30):
    wallet = CompanyWallet(company)

    frete = _extrato(wallet.frete, days)
    dias_parados = _extrato(wallet.dias_parados, days)
    bonus = _extrato(wallet.bonus, days)
    emprestimo = _extrato(wallet.emprestimo, days)
    pedagio = _extrato(wallet.pedagio, days)
    locadora = _extrato(wallet.locadora, days)

    return asdict(Extrato(frete, dias_parados, bonus, emprestimo, pedagio, locadora, _saldo(wallet)))


def _extrato(account, days):
    data_inicial = to_default_tz(now() - timedelta(days=days)).date()
    data_final = to_default_tz(now()).date()

    extrato_conta = serialize(
        account.operacoes.filter(created_at__date__range=[data_inicial, data_final]).order_by("created_at", "id")
    )

    if not extrato_conta:
        return []

    primeira_op = CompanyAccountingOperation.objects.get(pk=extrato_conta[0]["id"])

    saldo_ate_primeira_op = account.operacoes.filter(created_at__lt=to_default_tz(primeira_op.created_at)).aggregate(
        sum=Sum("value")
    )["sum"] or D("0")
    extrato_conta[0].update(saldo=saldo_ate_primeira_op + primeira_op.value)

    for index, op in enumerate(extrato_conta[1:]):
        op.update(saldo=extrato_conta[index]["saldo"] + op["value"])

    return extrato_conta[::-1]


def _saldo(wallet: CompanyWallet):
    saldo = Saldo(
        saldo_frete=wallet.frete.saldo(),
        saldo_dias_parados=wallet.dias_parados.saldo(),
        saldo_bonus=wallet.bonus.saldo(),
        saldo_emprestimo=wallet.emprestimo.saldo() + wallet.saldo_emprestimo_inadimplente,
        motivo_bloqueio=wallet.saque_bloqueado[1],
        saldo_pedagio=wallet.pedagio.saldo(),
        saldo_locadora=wallet.locadora.saldo(),
    )
    dsaldo = asdict(saldo)
    dsaldo.update(bloqueado=saldo.bloqueado)
    return dsaldo


def _friendly_description(operacao):
    if operacao.source == "SAQUE":
        msg = "Saque realizado"
        if operacao.fromuser:
            msg += " por %s" % operacao.fromuser.first_name
        bank_account = json.loads(operacao.bank_account_json)
        msg += ". ag: %s cc: %s" % (
            bank_account.get("agencia"),
            bank_account.get("conta"),
        )
        return msg
    if operacao.source == "SAQUE_FALHOU":
        msg = operacao.reason or "Saque devolvido pelo banco."
        bank_account = json.loads(operacao.bank_account_json)
        msg += " ag: %s cc: %s" % (
            bank_account.get("agencia"),
            bank_account.get("conta"),
        )
        return msg
    if operacao.source == "DESCONTO_PAGAMENTO_VIAGEM":
        return "%s. Descontado do grupo #%s %s, %s" % (
            operacao.reason,
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
        )
    if operacao.source == "DESCONTO_PAGAMENTO_VIAGEM_CANCELADO":
        description = "Desconto cancelado do grupo #%s %s, %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
        )
        if operacao.reason:
            description = "%s. %s" % (operacao.reason, description)
        return description
    if operacao.source == "PAGAMENTO_VIAGEM":
        if operacao.reason_key == "MODELO_HIBRIDO":
            return "Pagamento referente a repasse do grupo #%s %s, %s / Transbrasil" % (
                operacao.grupo.id,
                operacao.grupo.rota,
                to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
            )

        return "Pagamento do grupo #%s %s, %s %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
            "/ Transbrasil" if operacao.grupo.modelo_venda == "hibrido" else "",
        )
    if operacao.source == "PAGAMENTO_VIAGEM_CANCELADO":
        return "Pagamento cancelado. Grupo #%s %s, %s. %s %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
            operacao.reason or "",
            "/ Transbrasil" if operacao.grupo.modelo_venda == "hibrido" else "",
        )
    if operacao.source == "PAGAMENTO_EMPRESTIMO":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Pagamento {description}"
    if operacao.source == "REPASSE_VIAGEM_SUBTRAIDO":
        return "Repasse subtraido por cancelamento de passageiros referente ao grupo #%s %s, %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
        )
    if operacao.source == "REPASSE_VIAGEM_SUBTRAIDO_CANCELADO":
        return "Cancelamento subtração no repasse por cancelamento de passageiros referente ao grupo #%s %s, %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
        )
    if operacao.source == "DESCONTO_EMPRESTIMO":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Desconto {description}"
    if operacao.source == "DESCONTO_EMPRESTIMO_IOF":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Desconto IOF {description}"
    if operacao.source == "DESCONTO_EMPRESTIMO_IOF_CANCELADO":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Desconto IOF cancelado {description}"
    if operacao.source == "RETENCAO_EMPRESTIMO_IRRF":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Pagamento IRRF {description}"
    if operacao.source == "DESCONTO_EMPRESTIMO_CANCELADO":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Desconto cancelado {description}"
    if operacao.source == CompanyAccountingOperation.Source.DESCONTO_DIVIDA:
        return "Desconta dívida de parceria estratégica com a company #%s, grupo #%s" % (
            operacao.grupo.company.id,
            operacao.grupo.id,
        )
    if operacao.source == CompanyAccountingOperation.Source.DESCONTO_DIVIDA_CANCELADO:
        return "Cancela desconto de dívida de parceria estratégica com a company #%s, grupo #%s" % (
            operacao.grupo.company.id,
            operacao.grupo.id,
        )
    if operacao.source == "RETENCAO_EMPRESTIMO_IRRF_CANCELADO":
        description = _descricao_emprestimo(operacao.lending_operation_v2)
        return f"Pagamento IRRF cancelado {description}"
    if operacao.source == "TRANSF_RECEBIDA_CFRETE":
        return "Transferência recebida da conta de frete"
    if operacao.source == "TRANSF_RECEBIDA_CDIAS_PARADOS":
        return "Transferência recebida da conta de dias parados"
    if operacao.source == CompanyAccountingOperation.Source.TRANSF_RECEBIDA_CBONUS:
        return "Transferência recebida da conta de bônus"
    if operacao.source == "TRANSF_RECEBIDA_CEMPRESTIMO":
        return "Transferência recebida da conta de empréstimo"
    if operacao.source == "TRANSF_RECEBIDA_CPEDAGIO":
        return "Transferência recebida da conta de pedágio"
    if operacao.source == "TRANSF_PARA_CFRETE":
        return "Transferência para conta de frete"
    if operacao.source == "TRANSF_PARA_CDIAS_PARADOS":
        return "Transferência para conta de dias parados"
    if operacao.source == "TRANSF_PARA_CPEDAGIO":
        return "Transferência para conta de pedágio"
    if operacao.source == CompanyAccountingOperation.Source.TRANSF_PARA_CBONUS:
        return "Transferência para conta de bônus"
    if operacao.source == "TRANSF_PARA_CEMPRESTIMO":
        return "Transferência para conta de empréstimo"
    if operacao.source == "PAGAMENTO_DIA_PARADO":
        data = to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M")
        description = f"[Dia Parado] Pagamento do grupo #{operacao.grupo.id} {operacao.grupo.rota}, {data}"
        if operacao.grupo.onibus and operacao.grupo.onibus.placa:
            description += f" Ônibus {operacao.grupo.onibus.placa}"
        if operacao.grupo.observation:
            description += f" {operacao.grupo.observation}"
        return description
    if operacao.source == "PAGAMENTO_DIA_PARADO_CANCELADO":
        data = to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M")
        description = f"[Dia Parado] Pagamento cancelado do grupo #{operacao.grupo.id} {operacao.grupo.rota}, {data}"
        if operacao.grupo.onibus and operacao.grupo.onibus.placa:
            description += f" Ônibus {operacao.grupo.onibus.placa}"
        if operacao.grupo.observation:
            description += f" {operacao.grupo.observation}"
        return description
    if operacao.source == CompanyAccountingOperation.Source.PAGAMENTO_BONUS:
        return "Pagamento de bônus"
    if operacao.source == CompanyAccountingOperation.Source.PAGAMENTO_BONUS_CANCELADO:
        return "Pagamento de bônus cancelado"
    if operacao.source == "ARRENDAMENTO_ONIBUS":
        return "Pagamento do arrendamento mensal de ônibus."
    if operacao.source == "ARRENDAMENTO_ONIBUS_CANCELADO":
        return "Cancelamento do pagamento do arrendamento mensal de ônibus."
    if operacao.source == "PAGAMENTO_PEDAGIO":
        data_formatada = to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M")
        return f"Pagamento de pedágio do grupo {operacao.grupo.id} {operacao.grupo.rota}, {data_formatada}"
    if operacao.source == "PAGAMENTO_PEDAGIO_CANCELADO":
        data_formatada = to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M")
        return (
            f"Cancelamento do pagamento de pedágio do grupo {operacao.grupo.id} {operacao.grupo.rota}, {data_formatada}"
        )
    if operacao.source == "PAGAMENTO_AJUSTE":
        return "Pagamento ajustado do grupo #%s %s, %s %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
            "/ Transbrasil" if operacao.grupo.modelo_venda == "hibrido" else "",
        )
    if operacao.source == "PAGAMENTO_AJUSTE_CANCELADO":
        return "Cancelamento de pagamento ajustado do grupo #%s %s, %s %s" % (
            operacao.grupo.id,
            operacao.grupo.rota,
            to_default_tz(operacao.grupo.datetime_ida).strftime("%d/%m/%Y %H:%M"),
            "/ Transbrasil" if operacao.grupo.modelo_venda == "hibrido" else "",
        )
    if operacao.source == "RETENCAO_LOCADORA":
        return "Valor retido da operação de locação de ônibus"
    if operacao.source == "RETENCAO_LOCADORA_CANCELADA":
        return "Cancelamento do valor retido da operação de locação de ônibus"
    if operacao.source == "DESCONTO_ANTECIPACAO_CANCELADO":
        return "Antecipação Pagamento do grupo #%s cancelado." % (operacao.grupo.id,)
    if operacao.source == "DESCONTO_ANTECIPACAO":
        return "Antecipação de pagamento do grupo #%s." % (operacao.grupo.id,)
    if operacao.source == CompanyAccountingOperation.Source.SALDO_BLOQUEADO:
        return "Saldo bloqueado"
    if operacao.source == CompanyAccountingOperation.Source.SALDO_BLOQUEADO_CANCELADO:
        return "Saldo desbloqueado"
    return operacao.source


def _descricao_emprestimo(lending_operation):
    if not lending_operation:
        return ""
    return f"{lending_operation.description} - Empréstimo {lending_operation.lending.id}"


def serialize(operacoes):
    _prepare(operacoes)
    return [serialize_object(o) for o in operacoes]


def serialize_object(operacao):
    d = operacao.to_dict_json()
    d.update(description=_friendly_description(operacao))
    return d


def _prepare(operacoes):
    prefetch_related = [
        Prefetch(
            "grupo__rota__itinerario",
            queryset=Checkpoint.objects.to_serialize(),
        ),
        "lending_operation",
        "fromuser",
        "grupo__rota",
        "grupo__onibus",
        "account",
    ]

    prefetch_related_objects(operacoes, *prefetch_related)
