import json
from decimal import Decimal as D
from functools import cached_property
from typing import ClassVar, Literal

from django.contrib.auth.models import User
from django.db import models, transaction
from django.db.models import Sum
from django.db.models.fields import Decimal<PERSON>ield
from django_fsm import FSMField, can_proceed, transition
from django_qserializer.serialization import SerializableManager

from accounting import accoputils
from commons.dateutils import now
from commons.redis import lock
from core.models_commons import BankAccount
from core.models_company import Company, CompanyLending, CompanyLendingOperationV2, NotaFiscal
from core.models_contabil import AccountingOperation as V1Op
from core.models_contabil import AccountingTransaction
from core.models_grupo import Grupo
from core.models_travel import Cupom, Pagamento, Passageiro, Travel
from core.service.company_lending_svc_utils import adia_parcela, paga_parcela

from . import models_expira_saldo  # noqa: F401


class Account(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    removed = models.BooleanField(default=False)

    class Meta:
        abstract = True


class AccountingOperation(models.Model):
    objects: ClassVar[SerializableManager] = SerializableManager()

    class Source(models.TextChoices):
        ADMIN = "ADMIN"
        ADMIN_REAIS = "ADMIN_REAIS"  # deprecated
        BONUS = "BONUS"  # deprecated
        BONUS_REAIS = "BONUS_REAIS"  # deprecated
        DESCONTO_RATEIO_FECHADO = "DESCONTO_RATEIO_FECHADO"  # deprecated
        ESTORNO = "ESTORNO"
        ESTORNO_CANCELADO = "ESTORNO_CANCELADO"
        PAGAMENTO = "PAGAMENTO"
        PAGAMENTO_ATRASADO = "PAGAMENTO_ATRASADO"
        PAGAMENTO_CANCELADO = "PAGAMENTO_CANCELADO"
        PAGAMENTO_CHARGEBACK = "PAGAMENTO_CHARGEBACK"
        PAGAMENTO_CHARGEBACK_DEVOLVIDO = "PAGAMENTO_CHARGEBACK_DEVOLVIDO"
        PAGAMENTO_CORRECAO = "PAGAMENTO_CORRECAO"
        PAGAMENTO_EXTRA = "PAGAMENTO_EXTRA"  # deprecated
        PAGAMENTO_PARCIAL = "PAGAMENTO_PARCIAL"  # deprecated
        PASSAGEIRO_COMPENSADO = "PASSAGEIRO_COMPENSADO"
        PASSAGEIRO_COMPENSADO_CANCELADO = "PASSAGEIRO_COMPENSADO_CANCELADO"
        PASSAGEIRO_RESSARCIDO = "PASSAGEIRO_RESSARCIDO"  # deprecated
        PROMOCAO = "PROMOCAO"
        PROMOCAO_CANCELADA = "PROMOCAO_CANCELADA"
        PROMOCAO_CORRECAO = "PROMOCAO_CORRECAO"
        REALOCAR_BUEDAS = "REALOCAR_BUEDAS"  # deprecated
        RESERVA = "RESERVA"
        RESERVA_CORRECAO = "RESERVA_CORRECAO"
        SALDO_EXPIRADO = "SALDO_EXPIRADO"
        SALDO_EXPIRADO_CANCELADO = "SALDO_EXPIRADO_CANCELADO"
        RESERVA_CANCELADA = "RESERVA_CANCELADA"
        RESERVA_CANCELADA_PARCIAL = "RESERVA_CANCELADA_PARCIAL"
        RESERVA_DOWNGRADE = "RESERVA_DOWNGRADE"
        RESERVA_DOWNGRADE_CANCELADO = "RESERVA_DOWNGRADE_CANCELADO"
        RESERVA_RESSARCIDA = "RESERVA_RESSARCIDA"  # deprecated
        SAQUE = "SAQUE"
        SAQUE_FALHOU = "SAQUE_FALHOU"
        NEUTRALIZACAO_CARBONO = "NEUTRALIZACAO_CARBONO"
        NEUTRALIZACAO_CARBONO_CANCELADA = "NEUTRALIZACAO_CARBONO_CANCELADA"
        RESERVA_REMANEJADA = "RESERVA_REMANEJADA"
        RESERVA_REMANEJADA_CANCELADA = "RESERVA_REMANEJADA_CANCELADA"
        SEGURO_EXTRA = "SEGURO_EXTRA"
        SEGURO_EXTRA_CANCELADO = "SEGURO_EXTRA_CANCELADO"
        MARCACAO_ASSENTO = "MARCACAO_ASSENTO"
        MARCACAO_ASSENTO_CANCELADA = "MARCACAO_ASSENTO_CANCELADA"
        BAGAGEM_ADICIONAL = "BAGAGEM_ADICIONAL"
        BAGAGEM_ADICIONAL_CANCELADA = "BAGAGEM_ADICIONAL_CANCELADA"
        TAXA_SERVICO_RESERVA = "TAXA_SERVICO_RESERVA"
        TAXA_SERVICO_RESERVA_CANCELADA = "TAXA_SERVICO_RESERVA_CANCELADA"
        TAXA_CANCELAMENTO = "TAXA_CANCELAMENTO"
        PAGAMENTO_CREDITO = "PAGAMENTO_CREDITO"
        PAGAMENTO_CREDITO_CANCELADO = "PAGAMENTO_CREDITO_CANCELADO"

    class Status(models.TextChoices):
        CONFIRMED = "confirmed"
        PENDING = "pending"

    id: int
    accounting_transaction = models.ForeignKey(
        AccountingTransaction,
        blank=True,
        null=True,
        related_name="+",
        on_delete=models.PROTECT,
    )
    cupom_id: int
    cupom = models.ForeignKey(Cupom, blank=True, null=True, related_name="+", on_delete=models.PROTECT)
    fromuser_id: int
    fromuser = models.ForeignKey(User, blank=True, null=True, related_name="+", on_delete=models.PROTECT)
    origin_id: int
    origin = models.ForeignKey(V1Op, null=True, related_name="+", on_delete=models.PROTECT)
    pagamento_id: int
    pagamento = models.ForeignKey(Pagamento, blank=True, null=True, related_name="+", on_delete=models.PROTECT)
    passageiro_id: int
    passageiro = models.ForeignKey(Passageiro, blank=True, null=True, related_name="+", on_delete=models.PROTECT)
    travel_id: int
    travel = models.ForeignKey(Travel, blank=True, null=True, related_name="+", on_delete=models.PROTECT)
    user_id: int
    user = models.ForeignKey(User, related_name="+", on_delete=models.PROTECT)

    date = models.DateTimeField(default=now)
    data_conciliacao = models.DateTimeField(null=True)
    jsondata = models.JSONField(null=True, blank=True)
    promo_description = models.CharField(max_length=128, null=True, blank=True)
    promocao = models.CharField(max_length=64, null=True, blank=True)
    reason = models.CharField(max_length=256, null=True, blank=True)
    reason_key = models.CharField(max_length=256, null=True, blank=True)
    source = models.CharField(max_length=32, choices=Source.choices)
    status = models.CharField(default=Status.CONFIRMED, max_length=32, choices=Status.choices)
    updated_on = models.DateTimeField(auto_now=True)

    value = DecimalField(max_digits=12, decimal_places=2)
    value_bueda = DecimalField(max_digits=12, decimal_places=2, default=D(0))
    value_real = DecimalField(max_digits=12, decimal_places=2, default=D(0))

    related_to = models.ForeignKey(
        "self",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="related_ops",
    )
    internal_note = models.TextField(null=True, blank=True)

    def __str__(self) -> str:
        return f"[V2] {self.source} (R$ {self.value_real}, C$ {self.value_bueda})"

    def __repr__(self) -> str:
        return (
            f"V2(source={self.source}, R$={self.value_real}, C$={self.value_bueda},"
            f" travel_id={self.travel_id}, pax_id={self.passageiro_id})"
        )

    def _friendly_description(self):
        return V1Op._friendly_description(self)

    @property
    def friendly_source(self):
        return accoputils.get_friendly_source(self.source, self.pagamento, self.accounting_transaction)

    @property
    def codigo_op(self):
        return accoputils.get_codigo_op(self.pagamento, self.source, self.travel)

    @property
    def pax(self):
        return accoputils.get_pax(self.passageiro)

    @property
    def descricao_extrato(self):
        return accoputils.get_descricao_extrato(
            self.source,
            self.pagamento,
            self.travel,
            self.accounting_transaction,
            self.passageiro,
        )

    @property
    def metodo_pagamento(self):
        return accoputils.get_metodo_pagamento(self.pagamento)

    @property
    def friendly_status(self):
        return accoputils.get_friendly_status(self.pagamento)

    @property
    def transaction_status(self):
        return accoputils.get_transaction_status(self.accounting_transaction)

    @property
    def info_op(self):
        return accoputils.get_info_op(
            self.pagamento,
            self.fromuser,
            self.travel,
            self.accounting_transaction,
            self.reason,
            self.promo_description,
        )

    @property
    def grupo(self):
        return accoputils.get_grupo(self.travel)


lock_account = lock("acertar_saldos_company_{self.company.id}", max_wait_time=700)


class CompanyWallet:
    def __init__(self, company):
        if not company:
            raise ValueError("CompanyWallet precisa de uma empresa")
        self.company = company

    def _get_account(self, model):
        account, _ = model.objects.get_or_create(company=self.company)
        return account

    @cached_property
    def bonus(self) -> "BonusAccount":
        return self._get_account(BonusAccount)

    @cached_property
    def dias_parados(self) -> "DiasParadosAccount":
        return self._get_account(DiasParadosAccount)

    @cached_property
    def frete(self) -> "FreteAccount":
        return self._get_account(FreteAccount)

    @cached_property
    def emprestimo(self) -> "EmprestimoAccount":
        return self._get_account(EmprestimoAccount)

    @cached_property
    def locadora(self) -> "LocadoraAccount":
        return self._get_account(LocadoraAccount)

    @cached_property
    def pedagio(self) -> "PedagioAccount":
        return self._get_account(PedagioAccount)

    def pagar_parcela(self, parcela):
        if not self.saldo_suficiente(parcela.total_value):
            return

        _, adiada_in = adia_parcela(parcela, now().date(), atualiza_impostos=True)

        # a parcela adiada é mais cara que a original por causa dos impostos
        if adiada_in and not self.saldo_suficiente(adiada_in.total_value):
            return

        parcela_paga = paga_parcela(parcela if not adiada_in else adiada_in)
        self.emprestimo.cobrar(parcela_paga)

    def saldo_suficiente(self, value):
        saldo_emprestimo = self.emprestimo.saldo()
        saldo_frete = self.frete.saldo()
        saldo_dias_parados = self.dias_parados.saldo()
        saldo_pedagio = self.pedagio.saldo()

        return saldo_emprestimo + saldo_frete + saldo_dias_parados + saldo_pedagio >= abs(value)

    @property
    def tem_conta_negativa(self):
        return (
            self.bonus.saldo() < D("0")
            or self.dias_parados.saldo() < D("0")
            or self.frete.saldo() < D("0")
            or self.emprestimo.saldo() < D("0")
            or self.pedagio.saldo() < D("0")
        )

    @property
    def emprestimos(self):
        status = CompanyLending.Status.ATIVO
        return CompanyLending.objects.filter(status=status, company=self.company)

    @property
    def saldo_emprestimo_inadimplente(self):
        saldo_emprestimo_inadimplente = D("0")
        for lending in self.emprestimos:
            saldo_emprestimo_inadimplente += lending.saldo_inadimplente
        return saldo_emprestimo_inadimplente

    @property
    def tem_emprestimo_inadimplente(self):
        for lending in self.emprestimos:
            if lending.parcelas_inadimplentes:
                return True
        return False

    @property
    def tem_refinanciamento_nao_aceito(self):
        statuses = [
            CompanyLending.Status.AGUARDANDO_ACEITE,
            CompanyLending.Status.NAO_ACEITO,
        ]
        lendings = CompanyLending.objects.filter(status__in=statuses, company=self.company).exclude(accepted=True)
        for lending in lendings:
            if lending.is_refinancing:
                return True
        return False

    @property
    def tem_contrato_ccb_nao_aceito(self):
        statuses = [
            CompanyLending.Status.AGUARDANDO_ACEITE,
            CompanyLending.Status.NAO_ACEITO,
        ]
        lendings = CompanyLending.objects.filter(status__in=statuses, company=self.company).exclude(accepted=True)
        for lending in lendings:
            if lending.is_ccb:
                return True
        return False

    @property
    def saque_bloqueado(self):
        if self.tem_conta_negativa or self.tem_emprestimo_inadimplente:
            return True, "conta negativa"
        if self.tem_refinanciamento_nao_aceito:
            return True, "refinanciamento não aceito"
        if self.tem_contrato_ccb_nao_aceito:
            return True, "contrato CCB não aceito"
        return False, ""

    @property
    def saldo(self):
        return (
            self.bonus.saldo()
            + self.dias_parados.saldo()
            + self.frete.saldo()
            + self.emprestimo.saldo()
            + self.pedagio.saldo()
            + self.locadora.saldo()
        )

    def descontar_parcelas_inadimplentes(self):
        for emprestimo in self.emprestimos:
            for parcela in emprestimo.parcelas_inadimplentes:
                self.pagar_parcela(parcela)
            if can_proceed(emprestimo.quitar):
                emprestimo.quitar()
                emprestimo.save()

    @lock_account
    def acertar_saldo_contas(self, descontar_emprestimo=False):
        if descontar_emprestimo:
            self.descontar_parcelas_inadimplentes()
        self.acertar_saldo_emprestimo()
        self.acertar_saldo_frete()
        self.acertar_saldo_dparados()
        self.acertar_saldo_pedagio()
        self.acertar_saldo_bonus()

    def retorna_saldo_emprestimo(self):
        retornou_tudo = self.emprestimo.retornar_saldo(self.frete)
        if retornou_tudo:
            return

        retornou_tudo = self.emprestimo.retornar_saldo(self.dias_parados)
        if retornou_tudo:
            return

        retornou_tudo = self.emprestimo.retornar_saldo(self.pedagio)
        if retornou_tudo:
            return

        self.emprestimo.retornar_saldo(self.bonus)

    def acertar_saldo_emprestimo(self):
        pagou_tudo = self.frete.pagar(self.emprestimo)
        if pagou_tudo:
            return

        pagou_tudo = self.dias_parados.pagar(self.emprestimo)
        if pagou_tudo:
            return

        pagou_tudo = self.pedagio.pagar(self.emprestimo)
        if pagou_tudo:
            return

        self.bonus.pagar(self.emprestimo)

    def acertar_saldo_frete(self):
        pagou_tudo = self.emprestimo.pagar(self.frete)
        if pagou_tudo:
            return

        pagou_tudo = self.dias_parados.pagar(self.frete)
        if pagou_tudo:
            return

        pagou_tudo = self.pedagio.pagar(self.frete)
        if pagou_tudo:
            return

        self.bonus.pagar(self.frete)

    def acertar_saldo_dparados(self):
        pagou_tudo = self.emprestimo.pagar(self.dias_parados)
        if pagou_tudo:
            return

        pagou_tudo = self.frete.pagar(self.dias_parados)
        if pagou_tudo:
            return

        pagou_tudo = self.pedagio.pagar(self.dias_parados)
        if pagou_tudo:
            return

        self.bonus.pagar(self.dias_parados)

    def acertar_saldo_bonus(self):
        pagou_tudo = self.emprestimo.pagar(self.bonus)
        if pagou_tudo:
            return

        pagou_tudo = self.frete.pagar(self.bonus)
        if pagou_tudo:
            return

        pagou_tudo = self.dias_parados.pagar(self.bonus)
        if pagou_tudo:
            return

        self.pedagio.pagar(self.bonus)

    def acertar_saldo_pedagio(self):
        pagou_tudo = self.emprestimo.pagar(self.pedagio)
        if pagou_tudo:
            return

        pagou_tudo = self.frete.pagar(self.pedagio)
        if pagou_tudo:
            return

        pagou_tudo = self.dias_parados.pagar(self.pedagio)
        if pagou_tudo:
            return

        self.bonus.pagar(self.pedagio)


class CompanyAccount(Account):
    class TipoConta(models.TextChoices):
        BONUS = "bonus"
        DIAS_PARADOS = "dias_parados"
        FRETE = "frete"
        EMPRESTIMO = "emprestimo"
        LOCADORA = "locadora"
        PEDAGIO = "pedagio"

    class Meta:
        unique_together = ("company", "tipo")

    company = models.ForeignKey(
        Company,
        null=False,
        blank=False,
        related_name="contas",
        on_delete=models.PROTECT,
    )
    tipo = models.CharField(max_length=15, null=False, blank=False, choices=TipoConta.choices)

    @property
    def wallet(self):
        return CompanyWallet(self.company)

    def bloquear(self, valor: D, motivo: str) -> "CompanyAccountingOperation":
        if not motivo:
            raise ValueError("motivo não informado")

        if not valor or valor <= 0:
            raise ValueError("valor deve ser > 0")

        operacao = CompanyAccountingOperation(
            source=CompanyAccountingOperation.Source.SALDO_BLOQUEADO,
            account=self,
            value=-valor,
            reason=motivo,
        )
        operacao.save()
        return operacao

    def desbloquear(self, lancamento_id: int) -> "CompanyAccountingOperation":
        with transaction.atomic():
            try:
                bloqueio = (
                    CompanyAccountingOperation.objects.select_for_update(no_key=True)
                    .filter(
                        id=lancamento_id,
                        source=CompanyAccountingOperation.Source.SALDO_BLOQUEADO,
                    )
                    .get()
                )
            except CompanyAccountingOperation.DoesNotExist as err:
                raise ValueError(f"{lancamento_id} não é um bloqueio de saldo") from err

            ja_revertido = bloqueio.related_to is not None
            if ja_revertido:
                raise ValueError(f"{bloqueio} já revertido")

            desbloqueio = CompanyAccountingOperation(
                source=CompanyAccountingOperation.Source.SALDO_BLOQUEADO_CANCELADO,
                account=bloqueio.account,
                value=bloqueio.value * -1,
                related_to=bloqueio,
            )
            desbloqueio.save()

            bloqueio.related_to = desbloqueio
            bloqueio.save()
        return desbloqueio

    def depositar(self, grupo, user, operacao_em_lote, valor_frete=None, desconto_antecipacao: D | None = None):
        if self.tipo == CompanyAccount.TipoConta.FRETE:
            return FreteAccount.depositar(self, grupo, user, operacao_em_lote, valor_frete, desconto_antecipacao)
        if self.tipo == CompanyAccount.TipoConta.DIAS_PARADOS:
            return DiasParadosAccount.depositar(self, grupo, user, operacao_em_lote)
        if self.tipo == CompanyAccount.TipoConta.PEDAGIO:
            return PedagioAccount.depositar(self, grupo, user, operacao_em_lote)

    def estornar_deposito(self, grupo, user, reason=None, valor_estorno=None):
        if self.tipo == CompanyAccount.TipoConta.FRETE:
            return FreteAccount.estornar_deposito(self, grupo, user, reason, valor_estorno)
        if self.tipo == CompanyAccount.TipoConta.DIAS_PARADOS:
            return DiasParadosAccount.estornar_deposito(self, grupo, user, reason)
        if self.tipo == CompanyAccount.TipoConta.PEDAGIO:
            return PedagioAccount.estornar_deposito(self, grupo, user)

    def subtrair_repasse(self, amount, repasse):
        return CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.REPASSE_VIAGEM_SUBTRAIDO,
            value=amount,
            grupo=repasse.grupo,
        )

    def estornar_repasse_subtraido(self, amount, repasse):
        return CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.REPASSE_VIAGEM_SUBTRAIDO_CANCELADO,
            value=-amount,
            grupo=repasse.grupo,
        )

    def saldo(self, account=None):
        operacoes = self.operacoes
        if account:
            operacoes = operacoes.filter(account_origem_operacao=account)
        return operacoes.aggregate(sum=Sum("value"))["sum"] or D("0")

    def retornar_saldo(self, to_account):
        # saldo que a conta A já movimentou com relação a conta B
        # (já enviou ou recebeu da conta B)
        saldo_restante = self.saldo()

        if saldo_restante <= D("0"):
            return True

        saldo_movimentado = self.saldo(to_account)

        if saldo_movimentado > D("0"):
            valor = min(saldo_restante, saldo_movimentado)
            self.transferir(to_account, valor)

        return saldo_movimentado >= saldo_restante

    def pagar(self, to_account):
        saldo_a_pagar = to_account.saldo()

        if saldo_a_pagar >= D("0"):
            return True

        saldo_disponivel = self.saldo()

        if saldo_disponivel > D("0"):
            value = min(saldo_disponivel, abs(saldo_a_pagar))
            self.transferir(to_account, value)

        return saldo_disponivel >= abs(saldo_a_pagar)

    def transferir(self, destino, valor, lending_operation=None):
        def get_source_origem(tipo_conta):
            return {
                CompanyAccount.TipoConta.FRETE: "TRANSF_PARA_CFRETE",
                CompanyAccount.TipoConta.DIAS_PARADOS: "TRANSF_PARA_CDIAS_PARADOS",
                CompanyAccount.TipoConta.BONUS: CompanyAccountingOperation.Source.TRANSF_PARA_CBONUS,
                CompanyAccount.TipoConta.EMPRESTIMO: "TRANSF_PARA_CEMPRESTIMO",
                CompanyAccount.TipoConta.PEDAGIO: "TRANSF_PARA_CPEDAGIO",
            }[tipo_conta]

        def get_source_destino(tipo_conta):
            return {
                CompanyAccount.TipoConta.FRETE: "TRANSF_RECEBIDA_CFRETE",
                CompanyAccount.TipoConta.DIAS_PARADOS: "TRANSF_RECEBIDA_CDIAS_PARADOS",
                CompanyAccount.TipoConta.BONUS: CompanyAccountingOperation.Source.TRANSF_RECEBIDA_CBONUS,
                CompanyAccount.TipoConta.EMPRESTIMO: "TRANSF_RECEBIDA_CEMPRESTIMO",
                CompanyAccount.TipoConta.PEDAGIO: "TRANSF_RECEBIDA_CPEDAGIO",
            }[tipo_conta]

        CompanyAccountingOperation.objects.create(
            account=self,
            source=get_source_origem(destino.tipo),
            lending_operation_v2=lending_operation,
            value=-valor,
            account_origem_operacao=destino,
        )

        CompanyAccountingOperation.objects.create(
            account=destino,
            source=get_source_destino(self.tipo),
            lending_operation_v2=lending_operation,
            value=valor,
            account_origem_operacao=self,
        )

    def saque(self, fromuser, bank_account, value):
        return CompanyAccountingOperation.objects.create(
            account=self,
            source="SAQUE",
            bank_account=bank_account,
            bank_account_json=json.dumps(bank_account),
            fromuser=fromuser,
            value=-value,
        )

    def saque_falhou(self, transacao, reason=None, bank_provider: Literal["stark", "santander"] = "stark"):
        if not transacao.bank_request_id or (
            transacao.bank_request_id
            and not CompanyAccountingOperation.objects.filter(
                source="SAQUE_FALHOU", bank_request_id=transacao.bank_request_id
            ).exists()
        ):
            accop = CompanyAccountingOperation.objects.create(
                account=self,
                source="SAQUE_FALHOU",
                bank_account=transacao.bank_account,
                bank_account_json=transacao.bank_account_json,
                fromuser=transacao.fromuser,
                value=-transacao.value,
                bank_request_id=transacao.bank_request_id,
                bank_transaction_id=transacao.bank_transaction_id,
                bank_transaction_status=transacao.bank_transaction_status,
                reason=reason,
                bank_provider=bank_provider,
            )
            self.wallet.acertar_saldo_contas()
            return accop


class BonusManager(models.Manager):
    def get_queryset(self):
        return super(BonusManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.BONUS)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.BONUS})
        return super(BonusManager, self).get_or_create(**kwargs)


class DiasParadosManager(models.Manager):
    def get_queryset(self):
        return super(DiasParadosManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.DIAS_PARADOS)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.DIAS_PARADOS})
        return super(DiasParadosManager, self).get_or_create(**kwargs)


class FreteManager(models.Manager):
    def get_queryset(self):
        return super(FreteManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.FRETE)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.FRETE})
        return super(FreteManager, self).get_or_create(**kwargs)


class EmprestimoManager(models.Manager):
    def get_queryset(self):
        return super(EmprestimoManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.EMPRESTIMO)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.EMPRESTIMO})
        return super(EmprestimoManager, self).get_or_create(**kwargs)


class LocadoraManager(models.Manager):
    def get_queryset(self):
        return super(LocadoraManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.LOCADORA)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.LOCADORA})
        return super(LocadoraManager, self).get_or_create(**kwargs)


class PedagioManager(models.Manager):
    def get_queryset(self):
        return super(PedagioManager, self).get_queryset().filter(tipo=CompanyAccount.TipoConta.PEDAGIO)

    def get_or_create(self, **kwargs):
        kwargs.update({"tipo": CompanyAccount.TipoConta.PEDAGIO})
        return super(PedagioManager, self).get_or_create(**kwargs)


class LocadoraAccount(CompanyAccount):
    objects = LocadoraManager()

    class Meta:
        proxy = True

    def cobrar(self, valor: D, fromuser: User, internal_note: str | None = None) -> None:
        if not valor:
            raise ValueError("Valor inválido")

        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.RETENCAO_LOCADORA,
            fromuser=fromuser,
            value=-abs(valor),
            internal_note=internal_note,
        )

    def estornar_cobranca(self, accop_id: int, fromuser: User, internal_note: str | None = None) -> None:
        retencao = CompanyAccountingOperation.objects.get(
            id=accop_id, source=CompanyAccountingOperation.Source.RETENCAO_LOCADORA
        )

        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.RETENCAO_LOCADORA_CANCELADA,
            fromuser=fromuser,
            value=abs(retencao.value),
            related_to=retencao,
            internal_note=internal_note,
        )


class PedagioAccount(CompanyAccount):
    objects = PedagioManager()

    class Meta:
        proxy = True

    def depositar(self, grupo, fromuser, operacao_em_lote=False) -> None:
        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.PAGAMENTO_PEDAGIO,
            fromuser=fromuser,
            value=grupo.pedagio.valor_parceiro,
            grupo=grupo,
        )
        if not operacao_em_lote:
            self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def estornar_deposito(self, grupo, fromuser) -> None:
        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.PAGAMENTO_PEDAGIO_CANCELADO,
            fromuser=fromuser,
            value=-grupo.pedagio.valor_parceiro,
            grupo=grupo,
        )
        self.wallet.acertar_saldo_contas()


class BonusAccount(CompanyAccount):
    objects = BonusManager()

    class Meta:
        proxy = True

    def depositar(self, valor, fromuser):
        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.PAGAMENTO_BONUS,
            fromuser=fromuser,
            value=valor,
        )
        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def estornar_deposito(self, valor, fromuser):
        CompanyAccountingOperation.objects.create(
            account=self,
            source=CompanyAccountingOperation.Source.PAGAMENTO_BONUS_CANCELADO,
            fromuser=fromuser,
            value=-valor,
        )
        self.wallet.acertar_saldo_contas()


class DiasParadosAccount(CompanyAccount):
    objects = DiasParadosManager()

    class Meta:
        proxy = True

    def depositar(self, grupo, fromuser, operacao_em_lote=False):
        CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_DIA_PARADO",
            fromuser=fromuser,
            grupo=grupo,
            notafiscal=grupo.notafiscal,
            value=grupo.valor_frete,
        )

        if not operacao_em_lote:
            self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def estornar_deposito(self, grupo, fromuser, reason):
        CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_DIA_PARADO_CANCELADO",
            fromuser=fromuser,
            grupo=grupo,
            reason=reason,
            notafiscal=grupo.notafiscal,
            value=-grupo.valor_frete,
        )
        self.wallet.acertar_saldo_contas()


class FreteAccount(CompanyAccount):
    objects = FreteManager()

    class Meta:
        proxy = True

    def cobrar(self, grupo, fromuser, value, reason_key, reason_description):
        accoutting_operation = CompanyAccountingOperation.objects.create(
            account=self,
            source="DESCONTO_PAGAMENTO_VIAGEM",
            fromuser=fromuser,
            grupo=grupo,
            reason=reason_description,
            reason_key=reason_key,
            value=-value,
        )
        self.wallet.acertar_saldo_contas()
        return accoutting_operation

    def estornar_cobranca(self, grupo, fromuser, value, reason_key, reason_description):
        accoutting_operation = CompanyAccountingOperation.objects.create(
            account=self,
            source="DESCONTO_PAGAMENTO_VIAGEM_CANCELADO",
            fromuser=fromuser,
            grupo=grupo,
            reason=reason_description,
            reason_key=reason_key,
            value=value,
        )
        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)
        return accoutting_operation

    def depositar(self, grupo, user, operacao_em_lote=False, valor_frete=None, desconto_antecipacao: D | None = None):
        accop = CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_VIAGEM",
            fromuser=user,
            grupo=grupo,
            notafiscal=grupo.notafiscal if grupo.notafiscal else None,
            value=valor_frete or grupo.valor_frete,
        )

        if desconto_antecipacao:
            CompanyAccountingOperation.objects.create(
                account=self,
                source="DESCONTO_ANTECIPACAO",
                fromuser=user,
                grupo=grupo,
                notafiscal=grupo.notafiscal if grupo.notafiscal else None,
                value=-desconto_antecipacao,
            )

        if not operacao_em_lote:
            self.wallet.acertar_saldo_contas(descontar_emprestimo=True)
        return accop

    def depositar_arrendamento(self, fromuser, valor):
        CompanyAccountingOperation.objects.create(
            account=self, source="ARRENDAMENTO_ONIBUS", fromuser=fromuser, value=valor
        )

        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def cancelar_arrendamento(self, fromuser, valor):
        CompanyAccountingOperation.objects.create(
            account=self,
            source="ARRENDAMENTO_ONIBUS_CANCELADO",
            fromuser=fromuser,
            value=valor,
        )

        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def depositar_compensacao_impostos(self, grupo, fromuser, valor, operacao_em_lote=False):
        CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_VIAGEM",
            fromuser=fromuser,
            grupo=grupo,
            reason_key="MODELO_HIBRIDO",
            reason="Pagamento dos impostos da emissao de passagem",
            value=valor,
        )
        if not operacao_em_lote:
            self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def estornar_deposito(self, grupo, fromuser, reason, valor):
        accop = CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_VIAGEM_CANCELADO",
            fromuser=fromuser,
            grupo=grupo,
            reason=reason,
            notafiscal=grupo.notafiscal if grupo.notafiscal else None,
            value=-valor,
        )
        self.wallet.acertar_saldo_contas()
        return accop

    def estornar_antecipacao(self, grupo, fromuser, reason, valor):
        accop = CompanyAccountingOperation.objects.create(
            account=self,
            source="DESCONTO_ANTECIPACAO_CANCELADO",
            fromuser=fromuser,
            grupo=grupo,
            reason=reason,
            notafiscal=grupo.notafiscal if grupo.notafiscal else None,
            value=valor,
        )
        self.wallet.acertar_saldo_contas()
        return accop

    def ajuste(self, grupo: Grupo, valor_ajuste: D, fromuser: User) -> None:
        if not valor_ajuste or valor_ajuste < 0:
            raise ValueError("valor_ajuste deve ser um número positivo")
        CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_AJUSTE",
            fromuser=fromuser,
            grupo=grupo,
            value=valor_ajuste,
        )
        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)

    def ajuste_cancelado(self, op_ajuste: "CompanyAccountingOperation", fromuser: User) -> None:
        if not op_ajuste or op_ajuste.source != "PAGAMENTO_AJUSTE" or op_ajuste.account != self:
            raise ValueError("op_ajuste deve ser um PAGAMENTO_AJUSTE desta conta.")
        CompanyAccountingOperation.objects.create(
            account=self,
            source="PAGAMENTO_AJUSTE_CANCELADO",
            fromuser=fromuser,
            grupo=op_ajuste.grupo,
            value=-op_ajuste.value,
        )
        self.wallet.acertar_saldo_contas()


class EmprestimoAccount(CompanyAccount):
    objects = EmprestimoManager()

    class Meta:
        proxy = True

    def cobrar(self, lending_operation):
        self._operacao_emprestimo(lending_operation, -lending_operation.value, "DESCONTO_EMPRESTIMO")
        if lending_operation.irrf_value:
            self._operacao_emprestimo(
                lending_operation,
                -lending_operation.irrf_value,
                "RETENCAO_EMPRESTIMO_IRRF",
                irrf=True,
            )
        if lending_operation.iof_value:
            self._operacao_emprestimo(
                lending_operation,
                -lending_operation.iof_value,
                "DESCONTO_EMPRESTIMO_IOF",
                iof=True,
            )

    def _operacao_emprestimo(self, lending_operation, value, source, irrf=False, iof=False):
        _, created = CompanyAccountingOperation.objects.get_or_create(
            source=source,
            account=self,
            lending_operation_v2=lending_operation,
            defaults={
                "fromuser": lending_operation.lending.created_by,
                "value": value,
            },
        )
        if created:
            lending_operation.pay(irrf=irrf, iof=iof)

    def estornar_cobranca(self, lending_operation_estornada, user):
        self._estornar_emprestimo(
            lending_operation_estornada,
            -lending_operation_estornada.value,
            "DESCONTO_EMPRESTIMO_CANCELADO",
            user,
        )
        if lending_operation_estornada.irrf_value:
            self._estornar_emprestimo(
                lending_operation_estornada,
                -lending_operation_estornada.irrf_value,
                "RETENCAO_EMPRESTIMO_IRRF_CANCELADO",
                user,
                irrf=True,
            )
        if lending_operation_estornada.iof_value:
            self._estornar_emprestimo(
                lending_operation_estornada,
                -lending_operation_estornada.iof_value,
                "DESCONTO_EMPRESTIMO_IOF_CANCELADO",
                user,
                iof=True,
            )

        self.wallet.retorna_saldo_emprestimo()
        self.wallet.acertar_saldo_contas()

    def _estornar_emprestimo(self, lending_operation_estornada, value, source, user, irrf=False, iof=False):
        _, created = CompanyAccountingOperation.objects.get_or_create(
            source=source,
            account=self,
            lending_operation_v2=lending_operation_estornada,
            defaults={
                "fromuser": user,
                "value": value,
            },
        )
        if created:
            lending_operation_estornada.pay(irrf=irrf, iof=iof)

    def depositar(self, lending_operation):
        if lending_operation.source != CompanyLendingOperationV2.Source.TRANSFERENCIA_CREDITO_REFINANCIADO:
            self._operacao_emprestimo(lending_operation, -lending_operation.value, "PAGAMENTO_EMPRESTIMO")
        if lending_operation.iof_value:
            self._operacao_emprestimo(
                lending_operation,
                -lending_operation.iof_value,
                "DESCONTO_EMPRESTIMO_IOF",
                iof=True,
            )
        self.wallet.acertar_saldo_contas(descontar_emprestimo=True)


class CompanyAccountingOperation(models.Model):
    class Source(models.TextChoices):
        SAQUE = "SAQUE"
        SAQUE_FALHOU = "SAQUE_FALHOU"
        PAGAMENTO_VIAGEM = "PAGAMENTO_VIAGEM"
        PAGAMENTO_VIAGEM_CANCELADO = "PAGAMENTO_VIAGEM_CANCELADO"
        DESCONTO_PAGAMENTO_VIAGEM = "DESCONTO_PAGAMENTO_VIAGEM"
        DESCONTO_PAGAMENTO_VIAGEM_CANCELADO = "DESCONTO_PAGAMENTO_VIAGEM_CANCELADO"
        PAGAMENTO_BONUS = "PAGAMENTO_BONUS"
        PAGAMENTO_BONUS_CANCELADO = "PAGAMENTO_BONUS_CANCELADO"
        PAGAMENTO_DIA_PARADO = "PAGAMENTO_DIA_PARADO"
        REPASSE_VIAGEM_SUBTRAIDO = "REPASSE_VIAGEM_SUBTRAIDO"
        REPASSE_VIAGEM_SUBTRAIDO_CANCELADO = "REPASSE_VIAGEM_SUBTRAIDO_CANCELADO"
        PAGAMENTO_DIA_PARADO_CANCELADO = "PAGAMENTO_DIA_PARADO_CANCELADO"
        PAGAMENTO_EMPRESTIMO = "PAGAMENTO_EMPRESTIMO"
        RETENCAO_EMPRESTIMO_IRRF = "RETENCAO_EMPRESTIMO_IRRF"
        DESCONTO_EMPRESTIMO = "DESCONTO_EMPRESTIMO"
        DESCONTO_EMPRESTIMO_CANCELADO = "DESCONTO_EMPRESTIMO_CANCELADO"
        RETENCAO_EMPRESTIMO_IRRF_CANCELADO = "RETENCAO_EMPRESTIMO_IRRF_CANCELADO"
        TRANSF_RECEBIDA_CFRETE = "TRANSF_RECEBIDA_CFRETE"
        TRANSF_PARA_CFRETE = "TRANSF_PARA_CFRETE"
        TRANSF_RECEBIDA_CDIAS_PARADOS = "TRANSF_RECEBIDA_CDIAS_PARADOS"
        TRANSF_PARA_CDIAS_PARADOS = "TRANSF_PARA_CDIAS_PARADOS"
        TRANSF_RECEBIDA_CBONUS = "TRANSF_RECEBIDA_CBONUS"
        TRANSF_PARA_CBONUS = "TRANSF_PARA_CBONUS"
        TRANSF_RECEBIDA_CEMPRESTIMO = "TRANSF_RECEBIDA_CEMPRESTIMO"
        TRANSF_PARA_CEMPRESTIMO = "TRANSF_PARA_CEMPRESTIMO"
        DESCONTO_EMPRESTIMO_IOF = "DESCONTO_EMPRESTIMO_IOF"
        DESCONTO_EMPRESTIMO_IOF_CANCELADO = "DESCONTO_EMPRESTIMO_IOF_CANCELADO"
        ARRENDAMENTO_ONIBUS = "ARRENDAMENTO_ONIBUS"
        ARRENDAMENTO_ONIBUS_CANCELADO = "ARRENDAMENTO_ONIBUS_CANCELADO"
        RETENCAO_LOCADORA = "RETENCAO_LOCADORA"
        RETENCAO_LOCADORA_CANCELADA = "RETENCAO_LOCADORA_CANCELADA"
        PAGAMENTO_PEDAGIO = "PAGAMENTO_PEDAGIO"
        PAGAMENTO_PEDAGIO_CANCELADO = "PAGAMENTO_PEDAGIO_CANCELADO"
        TRANSF_PARA_CPEDAGIO = "TRANSF_PARA_CPEDAGIO"
        TRANSF_RECEBIDA_CPEDAGIO = "TRANSF_RECEBIDA_CPEDAGIO"
        DESCONTO_ANTECIPACAO = "DESCONTO_ANTECIPACAO"
        DESCONTO_ANTECIPACAO_CANCELADO = "DESCONTO_ANTECIPACAO_CANCELADO"
        DESCONTO_DIVIDA = "DESCONTO_DIVIDA"
        DESCONTO_DIVIDA_CANCELADO = "DESCONTO_DIVIDA_CANCELADO"
        SALDO_BLOQUEADO = "SALDO_BLOQUEADO"
        SALDO_BLOQUEADO_CANCELADO = "SALDO_BLOQUEADO_CANCELADO"

    class BankProvider(models.TextChoices):
        STARK = "stark"
        SANTANDER = "santander"

    source = models.CharField(max_length=64, choices=Source.choices)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(auto_now=True)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    fromuser = models.ForeignKey(User, null=True, related_name="+", on_delete=models.PROTECT)
    reason = models.CharField(max_length=1024, null=True, blank=True)
    reason_key = models.CharField(max_length=256, null=True, blank=True)
    notafiscal = models.ForeignKey(NotaFiscal, null=True, blank=True, related_name="+", on_delete=models.PROTECT)
    grupo = models.ForeignKey(Grupo, null=True, blank=True, on_delete=models.PROTECT)
    multa = models.ForeignKey(
        "core.Multa", null=True, blank=True, related_name="company_accounting_operations", on_delete=models.PROTECT
    )
    lending_operation = models.ForeignKey(
        "core.CompanyLendingOperation", null=True, blank=True, on_delete=models.PROTECT
    )
    lending_operation_v2 = models.ForeignKey(
        "core.CompanyLendingOperationV2",
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    bank_account = models.ForeignKey(BankAccount, null=True, blank=True, related_name="+", on_delete=models.PROTECT)
    bank_account_json = models.TextField(default="{}", blank=True)
    bank_request_id = models.TextField(null=True, blank=True)
    bank_transaction_id = models.TextField(null=True, blank=True)
    bank_transaction_status = models.CharField(max_length=32, null=True, blank=True)
    account = models.ForeignKey(CompanyAccount, null=True, related_name="operacoes", on_delete=models.PROTECT)
    account_origem_operacao = models.ForeignKey(CompanyAccount, null=True, related_name="+", on_delete=models.PROTECT)
    bank_provider = models.TextField(choices=BankProvider.choices, null=True, blank=True)
    internal_note = models.TextField(null=True, blank=True)
    related_to = models.ForeignKey(
        "accounting.CompanyAccountingOperation",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="related_ops",
    )

    def to_dict_json(self) -> dict:
        return {
            "id": self.pk,
            "date": self.created_at.isoformat(),
            "value": round(self.value, 2),
            "source": self.source,
        }

    def __str__(self):
        return f"{self.source}#{self.pk} R${self.value}"


class AccountingEvent(models.Model):
    class TipoEvento(models.TextChoices):
        SAQUE = "saque"
        SAQUE_FALHOU = "saque_falhou"

    class Meta:
        indexes = [
            models.Index(fields=["tipo", "company_accounting_operation"]),
        ]

    tipo = models.CharField(max_length=20, null=False, blank=False, choices=TipoEvento.choices)
    company_accounting_operation = models.ForeignKey(
        CompanyAccountingOperation,
        null=True,
        related_name="+",
        on_delete=models.PROTECT,
    )
    event_data = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class PaymentSchedule(models.Model):
    class Status(models.TextChoices):
        PAID_EARLY = "paid_early"
        PAID = "paid"
        WAITING_PAYMENT = "waiting_payment"
        CANCELED = "canceled"

    id: int
    objects: ClassVar[SerializableManager] = SerializableManager()

    group = models.ForeignKey(Grupo, on_delete=models.CASCADE)
    value = models.DecimalField(max_digits=12, decimal_places=2)
    account = models.ForeignKey(CompanyAccount, on_delete=models.CASCADE)
    scheduled_by = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    status = FSMField(choices=Status.choices, default=Status.WAITING_PAYMENT)
    paid_at = models.DateTimeField(auto_now_add=False, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    due_date = models.DateField(null=True, default=None)

    @transition(field=status, source=Status.WAITING_PAYMENT, target=Status.PAID)
    def pay(self, valor_pago):
        self.value = valor_pago

    @transition(field=status, source=Status.WAITING_PAYMENT, target=Status.CANCELED)
    def cancel(self):
        pass

    @transition(field=status, source=Status.WAITING_PAYMENT, target=Status.PAID_EARLY)
    def pay_early(self):
        self.paid_at = now()


class PaymentScheduleLog(models.Model):
    schedule = models.ForeignKey(PaymentSchedule, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
