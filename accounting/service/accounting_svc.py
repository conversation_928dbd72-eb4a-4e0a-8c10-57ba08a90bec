from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from functools import partial
from typing import Any, Callable, Iterable, cast
from zoneinfo import ZoneInfo

from beeline import traced
from dateutil.relativedelta import relativedelta
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.paginator import Paginator
from django.db.models import Case, DecimalField, Q, Sum, When
from django.db.models.functions import Coalesce

from accounting import accoputils
from accounting import feature_flags as flags
from accounting.evento.accounting_evento import (
    EventoCreditoReserva,
    EventoRemanejamentoContabil,
    EventoRemanejamentoDeprecado,
    EventoReserva,
    PaxInfo,
    Promocao,
)
from accounting.models import AccountingOperation as AccOpV2
from accounting.repo import AccopRepository
from accounting.repo.errors import AccopRepositoryException
from accounting.repo.local import ORMRepository, create_op
from accounting.repo.remote import RemoteRepository
from accounting.service import credit_accounting_svc
from accounting.service.accounting_utils import (
    EMPTY_BALANCE,
    SOURCES_BAGAGEM_ADICIONAL,
    SOURCES_CANCELAMENTO_MARCACAO_ASSENTO,
    SOURCES_CANCELAMENTO_NEUTRALIZACAO_CARBONO,
    SOURCES_CANCELAMENTO_PROMOCAO,
    SOURCES_CANCELAMENTO_RESERVA,
    SOURCES_CANCELAMENTO_RESERVA_REMANEJADA,
    SOURCES_CANCELAMENTO_SEGURO_EXTRA,
    SOURCES_CANCELAMENTO_TAXA_SERVICO,
    SOURCES_CARBONO,
    SOURCES_CREDITO,
    SOURCES_CUSTO,
    SOURCES_MARCACAO_ASSENTO,
    SOURCES_PROMOCAO,
    SOURCES_RESSARCIMENTO,
    SOURCES_SEGURO_EXTRA,
    SOURCES_TAXA_SERVICO,
    TAXA_CARBONO,
    D,
)
from commons import feature_flags as ff
from commons import utils
from commons.dateutils import now, to_default_tz
from commons.django_model_utils import dictfetchall
from commons.guard import is_afiliado
from commons.logging import buserlogger
from commons.redis import lock
from commons.utils import filter_list_of_dicts, hashint
from core.models_commons import ValorRessarcimento
from core.models_contabil import AccountingOperation
from core.models_eventos import SaldoExpirado
from core.models_grupo import Grupo, TrechoClasse
from core.models_travel import PROMOCOES_PRICING, ItemAdicional, Pagamento, Passageiro, Reserva, Travel
from core.serializers import serializer_accounting_ops, serializer_travel
from core.serializers.serializer_accounting_ops import AccountingOpsSerializer
from core.service import globalsettings_svc, rotas_svc

# Tirar esse import causa esse erro: ImportError: cannot import name 'passageiro_ressarcido'
# from partially initialized module 'core.service.notifications.user_notification_svc'
# (most likely due to a circular import) (/django/core/service/notifications/user_notification_svc.py)
from core.service.reserva import reserva_extrato_svc  # noqa
from core.service.reserva.reserva_validations_svc import PAGAMENTO_MINIMO

VALOR_SEGURO_EXTRA_DEFAULT = {"percentage": "0.04", "max": "11.90", "min": "4.90"}
VALOR_SEGURO_EXTRA_VARIANT = "valor_seguro_extra"

MARCACAO_ASSENTO_PERCENTAGE = D("0.05")
MARCACAO_ASSENTO_MIN_VALUE = D("3.90")
MARCACAO_ASSENTO_MAX_VALUE = D("9.90")

orm_repo: AccopRepository = ORMRepository()
remote_repo: AccopRepository = RemoteRepository()


@dataclass
class AddonsPriceConfig:
    seguro_extra: dict


def _enable_remote(user_id: int, feature: str) -> bool:
    return ff.is_user_enabled(feature, user_id)


def get_repository(user_id: int, feature: str = "") -> AccopRepository:
    repository = orm_repo
    if _enable_remote(user_id, feature) and remote_repo.is_healthy():
        repository = remote_repo
    return repository


def calcular_valores_addons(trecho_classe_ids: list[int], user: User | None) -> dict[str, Any]:
    trecho_classes = list(TrechoClasse.objects.select_related("trecho_vendido").filter(pk__in=trecho_classe_ids))

    valor_carbono = sum(calcular_valor_neutralizacao_carbono(tc.trecho_vendido) for tc in trecho_classes)
    allowed_seguro_extra = all(is_allowed_seguro_extra(tc) for tc in trecho_classes)
    valores_seguro_extra_tcs = [calcular_valor_seguro_extra(tc, user) for tc in trecho_classes]
    addons_price_config = valores_seguro_extra_tcs[0][1]
    valor_seguro_extra = sum(v[0] for v in valores_seguro_extra_tcs)
    valores_marcacao_assento = {hashint(tc.id): calcula_valor_marcacao_assento(tc) for tc in trecho_classes}
    valor_bagagem_adicional = _get_valor_bagagem_adicional()

    return {
        "valorCarbono": valor_carbono,
        "valorSeguroExtra": valor_seguro_extra,
        "isSeguroExtraPermitido": allowed_seguro_extra,
        "valoresMarcacaoAssento": valores_marcacao_assento,
        "valorBagagemAdicional": valor_bagagem_adicional,
        "bagagemAdicionalLiberada": user.is_authenticated and bagagem_liberada(user) if user else False,
        "addonsPriceConfig": asdict(addons_price_config),
    }


def calcular_valor_neutralizacao_carbono(trecho_vendido):
    distancia_viagem = rotas_svc.calcula_distancia_entre_checkpoints(trecho_vendido.id)
    valor_neutralizacao_carbono = distancia_viagem * TAXA_CARBONO

    return D(str(valor_neutralizacao_carbono)).quantize(D("0.01"))


def bagagem_liberada(user: User) -> bool:
    saldo_usuario = get_saldo(user)["creditos"]
    return saldo_usuario == D(0)


def is_allowed_seguro_extra(trechos_classe: TrechoClasse | list[TrechoClasse]):
    if not isinstance(trechos_classe, list):
        trechos_classe = [trechos_classe]
    for tc in trechos_classe:
        tv = tc.trecho_vendido
        if tv.origem.cidade.is_exterior or tv.destino.cidade.is_exterior:
            return False
    return True


def get_variant_seguro_extra(user=None):
    base_configs = globalsettings_svc.get(
        "seguro_extra_config",
        {"ab_active": False, "valor_seguro_default": VALOR_SEGURO_EXTRA_DEFAULT, "variant": VALOR_SEGURO_EXTRA_VARIANT},
    )

    valor_seguro = base_configs["valor_seguro_default"]
    if not (user and user.is_authenticated and base_configs["ab_active"]):
        return valor_seguro

    return ff.get_variant_by_user(base_configs["variant"], user_id=user.id, default_value=valor_seguro)


def calcular_valor_seguro_extra(trecho_classe: TrechoClasse, user=None) -> tuple[D, AddonsPriceConfig]:
    valor_seguro = get_variant_seguro_extra(user)
    min_value, max_value, percentage = D(valor_seguro["min"]), D(valor_seguro["max"]), D(valor_seguro["percentage"])

    valor_seguro_extra = calcula_valor_addon(trecho_classe.max_split_value_bucket, min_value, max_value, percentage)
    # Define o limite de tempo para a primeira cobrança
    partida = cast(datetime, trecho_classe.get_horario_partida())
    chegada = trecho_classe.get_horario_chegada()
    limite_cobranca_simples = (partida + timedelta(days=1)).replace(hour=23, minute=59, second=59)

    if chegada and chegada > limite_cobranca_simples:
        return valor_seguro_extra * 2, AddonsPriceConfig(seguro_extra=valor_seguro)

    return valor_seguro_extra, AddonsPriceConfig(seguro_extra=valor_seguro)


def calcular_valor_bagagem_adicional(quantidade_bagagem: int) -> D:
    return D(str(quantidade_bagagem * _get_valor_bagagem_adicional()))


def calcula_valor_addon(max_split_value: D, valor_minimo: D, valor_max: D, percentage: D) -> D:
    valor = _trunca_com_final_x(max_split_value * percentage)
    valor = max(valor, valor_minimo)
    valor = min(valor, valor_max)
    return valor


def _get_valor_bagagem_adicional() -> D:
    global_setting_do_valor = globalsettings_svc.get("valor_bagagem_adicional")
    valor = global_setting_do_valor if global_setting_do_valor else D("0")
    return valor


def _trunca_com_final_x(valor: D, x: D = D("0.9")) -> D:
    value = valor // D("1") + x
    return D(str(value)).quantize(D("0.01"))


def calcula_valor_marcacao_assento(trecho_classe: TrechoClasse) -> D:
    valor = calcula_valor_addon(
        trecho_classe.max_split_value_bucket,
        MARCACAO_ASSENTO_MIN_VALUE,
        MARCACAO_ASSENTO_MAX_VALUE,
        MARCACAO_ASSENTO_PERCENTAGE,
    )
    return valor


def get_qtd_pax_elegiveis_seguro_extra(
    trecho_classe: TrechoClasse,
    passageiros: list[Passageiro] | list[dict],
):
    # Caso tenha um pax com idade > 85 anos ninguem pode contratar seguro.
    data_ida = cast(datetime, trecho_classe.datetime_ida)

    for pax in passageiros:
        nascimento = None

        if isinstance(pax, Passageiro):
            nascimento = pax.buseiro.birthday
        elif isinstance(pax, dict) and pax.get("birthday"):
            try:
                nascimento = datetime.strptime(pax["birthday"], "%d/%m/%Y").replace(
                    tzinfo=ZoneInfo("America/Sao_Paulo")
                )
            except ValueError:
                nascimento = datetime.strptime(pax["birthday"], "%d%m%Y").replace(tzinfo=ZoneInfo("America/Sao_Paulo"))

        if not nascimento:
            continue

        idade = data_ida.year - nascimento.year - ((data_ida.month, data_ida.day) < (nascimento.month, nascimento.day))

        if idade >= 85:
            return D(0)

    return len(passageiros)


def calcular_valor_seguro_extra_por_passageiros(
    trecho_classe: TrechoClasse, passageiros: list[Passageiro] | list[dict], user=None
) -> D:
    valor_seguro_extra, _ = calcular_valor_seguro_extra(trecho_classe, user)
    return get_qtd_pax_elegiveis_seguro_extra(trecho_classe, passageiros) * valor_seguro_extra


def get_extrato(user, page=1, items_per_page=50):  # TODO: tony revisar esse codigo aqui
    if items_per_page > 100:
        raise Exception("Número de páginas muito alto!")

    select_related = [
        "user__profile",
        "fromuser",
        "pagamento",
        "travel__grupo",
        "cupom",
    ]
    prefetch_related = [
        "pagamento__travels",
    ]
    operations = (
        AccountingOperation.objects.select_related(*select_related)
        .prefetch_related(*prefetch_related)
        .filter(user=user)
        .order_by("-date")
    )

    page_operations = Paginator(operations, items_per_page).page(page)
    doperations = [op.to_dict_json() for op in page_operations]

    dtravels = serializer_travel.serialize_accounting_extrato_travels(
        [op.travel for op in page_operations if op.source in ["RESERVA_CANCELADA"]]
    )
    dtravels_map = {t["id"]: t for t in dtravels}

    for dop in doperations:
        if dop["source"] == "RESERVA_CANCELADA":
            dop["travel"] = dtravels_map[dop["travel_id"]]

    return {
        "doperations": doperations,
        "total_length": page_operations.paginator.count,
    }


def get_new_extrato(user_id, filter):
    qs = AccountingOperation.objects.to_serialize(AccountingOpsSerializer).order_by("date")
    filter_user = Q(user=user_id)

    if filter == "all":
        operations = qs.filter(filter_user)
    elif filter == "reais":
        sources_reais = [
            "RESERVA",
            "RESERVA_CANCELADA",
            "RESERVA_CANCELADA_PARCIAL",
            "PROMOCAO",
            "PROMOCAO_CANCELADA",
            "PAGAMENTO",
            "PAGAMENTO_PARCIAL",
            "PAGAMENTO_EXTRA",
            "PAGAMENTO_CANCELADO",
            "PAGAMENTO_ATRASADO",
            "ESTORNO",
            "ESTORNO_CANCELADO",
            "PAGAMENTO_CHARGEBACK_DEVOLVIDO",
            "PAGAMENTO_CORRECAO",
            "SAQUE",
            "SAQUE_FALHOU",
            "RECOMPENSA_MGM",
        ]
        operations = qs.filter(filter_user & (Q(source__in=sources_reais) | Q(source="ADMIN", value_real__gte=0)))
    else:
        operations = qs.filter(
            filter_user & (Q(reason="INVITE_BONUS") | Q(source="PROMOCAO") | Q(source="ADMIN", value_bueda__gte=0))
        )

    _attach_travel_gratis(operations)
    doperations = []
    checked_ids = set()
    for op in operations:
        if op.id in checked_ids or op.source == "PROMOCAO":
            continue

        if op.source == "PAGAMENTO":
            viagem, viagem_ops_ids = _check_pagamento_operation(op, operations)
            doperations.append(viagem)
            checked_ids.update(viagem_ops_ids)

        elif op.source == "RESERVA":
            viagem, viagem_ops_ids = _check_reserva_operation(op, operations)
            doperations.append(viagem)
            checked_ids.update(viagem_ops_ids)

        elif op.source == "BONUS" and op.reason == "INVITE_BONUS":
            dop = _check_bonus_operation(op)
            doperations.append(dop)

        else:
            doperations.append(op)
    return serializer_accounting_ops.serialize_extrato(doperations)[::-1]


def _attach_travel_gratis(operations):
    deferred_ops = []
    deferred_travel_ids = []

    for op in operations:
        if op.source == "PAGAMENTO":
            travel = None
            op.travel_gratis_id = None
            pagamento_travels = list(op.pagamento.travels.all())
            if pagamento_travels:
                travel = pagamento_travels[0]
                if len(pagamento_travels) == 1:
                    op.travel_gratis_id = travel.travel_ida_id
        elif op.source == "RESERVA":
            travel = op.travel
            op.travel_gratis_id = travel.travel_ida_id if travel else None
        else:
            continue

        if not op.travel_gratis_id and travel:
            deferred_ops.append(op)
            deferred_travel_ids.append(travel.id)

    data = dict(Travel.objects.filter(travel_ida_id__in=deferred_travel_ids).values_list("travel_ida_id", "id"))

    for op, travel_id in zip(deferred_ops, deferred_travel_ids):
        op.travel_gratis_id = data.get(travel_id)


@traced("accounting_svc._check_pagamento_operation")
def _check_pagamento_operation(op, operations):
    pagamento_travels = list(op.pagamento.travels.all())
    travels_ids = [t.id for t in pagamento_travels]

    if op.travel_gratis_id:
        travels_ids.append(op.travel_gratis_id)

    viagem_operations = _get_viagem_operations_pagamento(operations, op, travels_ids)
    viagem = _get_travels_from_accops(viagem_operations)

    dviagem_operations = [op, *viagem_operations][::-1]
    viagem.update({"tipo": "viagem", "operations": dviagem_operations})
    return viagem, [o.id for o in viagem_operations]


def _get_viagem_operations_pagamento(operations, op, travels_ids):
    return [
        o for o in operations if (o.id != op.id and (o.pagamento_id == op.pagamento_id or o.travel_id in travels_ids))
    ]


@traced("accounting_svc._check_reserva_operation")
def _check_reserva_operation(op, operations):
    travels_ids = [op.travel.id]
    if op.travel_gratis_id:
        travels_ids.append(op.travel_gratis_id)

    viagem_operations = _get_viagem_operations_reserva(operations, travels_ids)
    viagem = _get_travels_from_accops(viagem_operations)

    dviagem_operations = viagem_operations[::-1]
    viagem.update({"tipo": "viagem", "operations": dviagem_operations})
    return viagem, [o.id for o in viagem_operations]


def _get_viagem_operations_reserva(operations, travels_ids):
    return sorted(
        [o for o in operations if (o.travel and o.travel.id in travels_ids)],
        key=lambda o: o.date,
    )


@traced("accounting_svc._check_bonus_operation")
def _check_bonus_operation(op):
    amigo = op.fromuser.get_full_name() if op.fromuser else "Alguém"
    op.amigo = amigo
    return op


def get_saldo(user) -> dict:
    try:
        return get_repository(user.id, flags.READONLY).get_balance(user.id)
    except AccopRepositoryException as err:
        buserlogger.error("accounting.invalid_balance", extra={"err": str(err)})
        return EMPTY_BALANCE.copy()


def admin_reais(pax, fromuser, value, reason_key, reason):
    fromuser_id = fromuser.id if fromuser else None
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_admin_reais(
        pax_id=pax.id,
        travel_id=pax.travel_id,
        user_id=pax.travel.user_id,
        fromuser_id=fromuser_id,
        value=value,
        reason=reason,
        reason_key=reason_key,
    )


def _passageiro_compensado_cancelado(pax, fromuser, value, reason):
    value = round(value, 2)
    fromuser_id = fromuser.id if fromuser else None
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_passageiro_compensado_cancelado(
        pax_id=pax.id,
        travel_id=pax.travel_id,
        user_id=pax.travel.user_id,
        fromuser_id=fromuser_id,
        value=value,
        reason=reason,
    )


def _reserva_downgrade_cancelado(pax, fromuser, value, reason):
    value = round(value, 2)
    fromuser_id = fromuser.id if fromuser else None
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_reserva_downgrade_cancelado(
        pax_id=pax.id,
        travel_id=pax.travel_id,
        user_id=pax.travel.user_id,
        fromuser_id=fromuser_id,
        value=value,
        reason=reason,
    )


def _valida_cancelamento_staff(pax: Passageiro, value: D, source: AccOpV2.Source) -> None:
    sum_ops = AccountingOperation.objects.filter(passageiro=pax, source=source).aggregate(
        sum=Coalesce(Sum("value_real"), D(0))
    )["sum"]
    if abs(value) > sum_ops:
        raise ValidationError(f"Só pode ser cancelado {sum_ops} reais de operações {source} para o passageiro {pax}")


def remover_reais(pax, fromuser, value, reason_key, reason):
    if reason_key == "COMPENSADO_CANCELADO":
        _valida_cancelamento_staff(pax, value, AccOpV2.Source.PASSAGEIRO_COMPENSADO)
        _passageiro_compensado_cancelado(pax, fromuser, value, reason)
    elif reason_key == "DOWNGRADE_CANCELADO":
        _valida_cancelamento_staff(pax, value, AccOpV2.Source.RESERVA_DOWNGRADE)
        _reserva_downgrade_cancelado(pax, fromuser, value, reason)
    else:
        admin_reais(pax, fromuser, value, reason_key, reason)


def afiliado_ganhar_reais(user, value, reason="reais pelo programa de afiliação"):
    if not is_afiliado(user):
        raise ValidationError("Operação válida apenas para afiliados")
    value = round(value, 2)
    create_op(user=user, source="BONUS_REAIS", value=value, value_real=value, reason=reason)


def pagamento(pagamento):
    value = round(pagamento.net_value, 2)

    if AccountingOperation.objects.filter(
        user=pagamento.user,
        source="PAGAMENTO",
        value=value,
        value_real=value,
        pagamento=pagamento,
    ).exists():
        return

    get_repository(pagamento.user_id, flags.CREATE_OPS).post_pagamento(
        user_id=pagamento.user_id,
        value=value,
        pagamento_id=pagamento.id,
        status=_get_status_operation(pagamento),
    )


def _get_status_operation(pagamento: Pagamento) -> AccOpV2.Status:
    return AccOpV2.Status.CONFIRMED if pagamento.status == Pagamento.Status.PAID else AccOpV2.Status.PENDING


def _valid_addon(valor_addon: D, valor_total_reserva: D = PAGAMENTO_MINIMO) -> bool:
    return valor_addon > 0 and valor_total_reserva > PAGAMENTO_MINIMO


def cria_evento_reserva(travel, dtravel, valor_total_reserva):
    if AccountingOperation.objects.filter(user=travel.user, source="RESERVA", travel=travel).exists():
        return
    promocao, extrato = dtravel.get("promocao"), dtravel["extrato"]
    valor_pago = extrato["valor_pagamento"] + extrato["reais_utilizados"] + extrato["creditos_utilizados"]
    if abs(valor_pago - extrato["value"]) > D("0.01"):
        raise Exception("Erro na contabilidade da reserva")

    promo_pricing = dtravel.get("promo_pricing")
    _valid_addon_partital = partial(_valid_addon, valor_total_reserva=valor_total_reserva)
    credito_carbono = _valid_addon_partital(dtravel["valor_credito_carbono"])
    seguro_extra = _valid_addon_partital(dtravel["valor_seguro_extra"])
    bagagem_adicional = _valid_addon_partital(dtravel["valor_bagagem_adicional"])
    marcacao_assento = _valid_addon_partital(dtravel["valor_marcacao_assento"])

    valor_taxa_servico = dtravel.get("valor_taxa_servico", 0)
    passageiros = list(travel.passageiro_set.all().filter(removed=False))
    promocoes = []
    pax_ids = [passenger.id for passenger in passageiros]
    if promocao:
        promo_pax_ids = list(travel.get_promo_passengers().values_list("id", flat=True))
        promocao.update(pax_ids=promo_pax_ids, cupom_id=travel.cupom_id)
        promocoes.append(promocao)

    if promo_pricing:
        # TODO: Aqui está alterando o PromoPricingDTO. Parece errado.
        promo_pricing.pax_ids = pax_ids
        promocoes.append(promo_pricing)

    valor_carbono = D("0")
    if credito_carbono:
        valor_carbono = calcular_valor_neutralizacao_carbono(travel.trecho_vendido) * len(pax_ids)

    valor_seguro_extra = D("0")
    if seguro_extra:
        valor_seguro_extra = dtravel["valor_seguro_extra"]

    valor_bagagem_adicional = D("0")
    quantidade_bagagem_adicional = 0
    if bagagem_adicional:
        valor_bagagem_adicional = dtravel["valor_bagagem_adicional"]
        quantidade_bagagem_adicional = dtravel["quantidade_bagagem_adicional"]

    valor_marcacao_assento = D("0")
    if marcacao_assento:
        valor_marcacao_assento = dtravel["valor_marcacao_assento"]
    pax_ids_com_marcacao_assento = {passageiro.id for passageiro in passageiros if passageiro.poltrona is not None}
    valor_promocoes = sum(_get_promocao_attr(promocao, "value", D("0")) for promocao in promocoes)
    valor_reserva = (
        valor_pago
        - valor_carbono
        - valor_marcacao_assento
        - valor_seguro_extra
        - valor_bagagem_adicional
        + valor_promocoes
        - valor_taxa_servico
    )
    pax_info_list = constroi_pax_info(
        valor_reserva,
        valor_carbono,
        valor_seguro_extra,
        valor_taxa_servico,
        valor_bagagem_adicional,
        quantidade_bagagem_adicional,
        valor_marcacao_assento,
        promocoes,
        pax_ids,
        pax_ids_com_marcacao_assento,
    )
    return EventoReserva(travel_id=travel.id, user_id=travel.user_id, pax_info=pax_info_list)


def reserva(evento_reserva: EventoReserva):
    if (
        evento_reserva.type == "RESERVA"
        and AccountingOperation.objects.filter(source="RESERVA", travel_id=evento_reserva.travel_id).exists()
    ):
        return

    get_repository(evento_reserva.user_id, flags.EVENTS).post_reserva(evento_reserva=evento_reserva)


def _neutralizacao_carbono_cancelada(pax: Passageiro, value: D):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_neutralizacao_carbono_cancelada(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value
    )


def _reserva_remanejada_cancelada(pax: Passageiro, value: D):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_reserva_remanejada_cancelada(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value
    )


def _seguro_extra_cancelado(pax: Passageiro, value: D):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_seguro_extra_cancelado(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value
    )


def _bagagem_adicional_cancelada(pax: Passageiro, value: D, jsondata: str | None):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_bagagem_adicional_cancelada(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value, jsondata=jsondata
    )


def _marcacao_assento_cancelada(pax: Passageiro, value: D):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_marcacao_assento_cancelada(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value
    )


def _taxa_servico_cancelada(pax: Passageiro, value: D):
    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_taxa_servico_cancelada(
        pax_id=pax.id, travel_id=pax.travel_id, user_id=pax.travel.user_id, value=value
    )


def taxa_cancelamento(pax, taxa_cancelamento_percentual):
    value_a_ser_retido = _calcula_taxa_cancelamento(pax, taxa_cancelamento_percentual)
    if value_a_ser_retido:
        get_repository(pax.travel.user_id, flags.CREATE_OPS).post_taxa_cancelamento(
            pax_id=pax.id,
            travel_id=pax.travel_id,
            user_id=pax.travel.user_id,
            value=-value_a_ser_retido,
        )


def _calcula_taxa_cancelamento(pax, taxa_cancelamento_percentual):
    sources = (
        SOURCES_CANCELAMENTO_RESERVA
        + SOURCES_CANCELAMENTO_PROMOCAO
        + SOURCES_CANCELAMENTO_NEUTRALIZACAO_CARBONO
        + SOURCES_CANCELAMENTO_MARCACAO_ASSENTO
        + SOURCES_CANCELAMENTO_SEGURO_EXTRA
        + SOURCES_CANCELAMENTO_TAXA_SERVICO
        + SOURCES_CANCELAMENTO_RESERVA_REMANEJADA
    )
    value_pago_pax = AccountingOperation.objects.filter(passageiro=pax, source__in=sources).aggregate(
        sum=Coalesce(Sum("value"), D("0"))
    )["sum"]
    return D(taxa_cancelamento_percentual) * value_pago_pax


def _reserva_cancelada_parcial(value: D, passageiro: Passageiro) -> None:
    get_repository(passageiro.travel.user_id, flags.CREATE_OPS).post_reserva_cancelada_parcial(
        pax_id=passageiro.id,
        travel_id=passageiro.travel_id,
        user_id=passageiro.travel.user_id,
        value=value,
    )


def _promocao_cancelada(pax: Passageiro, value: D, cupom_id: int | None, promocao: str | None) -> None:
    if abs(value) < D("0.01"):
        return

    get_repository(pax.travel.user_id, flags.CREATE_OPS).post_promocao_cancelada(
        pax_id=pax.id,
        travel_id=pax.travel_id,
        user_id=pax.travel.user_id,
        value=value,
        promocao=promocao,
        cupom_id=cupom_id,
    )


def passageiro_compensado(pax, value, reason_key, reason_description, fromuser) -> AccountingOperation | None:
    return get_repository(pax.travel.user_id, flags.CREATE_OPS).post_passageiro_compensado(
        pax_id=pax.id,
        travel_id=pax.travel_id,
        user_id=pax.travel.user_id,
        fromuser_id=fromuser.id,
        value=value,
        reason=reason_description,
        reason_key=reason_key,
    )


def reserva_downgrade(devolucao, pax, travel, reason_key=None):
    downgrade_value = devolucao
    if downgrade_value > D("0.01"):
        get_repository(pax.travel.user_id, flags.CREATE_OPS).post_reserva_downgrade(
            pax_id=pax.id, travel_id=travel.id, user_id=travel.user_id, value=downgrade_value, reason_key=reason_key
        )


def expira_saldo(saldo_expirado: SaldoExpirado) -> list[int]:
    accop_ids_geradas = []
    for evento in saldo_expirado.eventos_expirados:
        if not evento.valor_expirado:
            continue
        id = _saldo_expirado(evento.valor_expirado, evento.travel_id, saldo_expirado.user_id)
        AccountingOperation.objects.filter(id__in=evento.accop_ids).update(related_to_id=id)
        accop_ids_geradas.append(id)

    return accop_ids_geradas


def expira_saldo_cancelado(accop_ids: list[int]) -> list[int]:
    accops = AccountingOperation.objects.filter(id__in=accop_ids, source="SALDO_EXPIRADO")

    if len(accops) != len(accop_ids):
        raise ValidationError(f"Nem todos os ids contábeis {accop_ids} geraram accops")

    accop_ids_revertido = []
    for accop in accops:
        accop_ids_revertido.append(_cancela_saldo_expirado(accop))

    return accop_ids_revertido


def _saldo_expirado(valor: D, travel_id: int | None, user_id: int) -> int:
    id = get_repository(user_id, flags.CREATE_OPS).post_saldo_expirado(
        travel_id=travel_id,
        user_id=user_id,
        value=-valor,
    )

    return id


def _cancela_saldo_expirado(accop: AccountingOperation) -> int:
    id = get_repository(accop.user_id, flags.CREATE_OPS).post_saldo_expirado_cancelado(
        travel_id=accop.travel_id, user_id=accop.user_id, value=-accop.value_real
    )

    accop.related_to_id = id
    accop.save(update_fields=["related_to"])

    return id


def estorno(pagamento, value=None, travel=None):
    if value is None:
        value = pagamento.net_value

    travel_id = travel.id if travel else None
    value = -round(value, 2)

    if value > 0:
        raise ValidationError("Só valores negativos pro estorno")

    get_repository(pagamento.user_id, flags.CREATE_OPS).post_estorno(
        travel_id=travel_id,
        user_id=pagamento.user_id,
        value=value,
        pagamento_id=pagamento.id,
    )


def estorno_falhou(pagamento, value):
    if value < 0:
        raise ValidationError("Só valores positivos pro estorno falhou")

    return get_repository(pagamento.user_id, flags.CREATE_OPS).post_estorno_falhou(
        user_id=pagamento.user_id, value=value, pagamento_id=pagamento.id
    )


def pagamento_confirmado(pagamento: Pagamento, value: D) -> None:
    pay_op = AccountingOperation.objects.filter(user=pagamento.user, source="PAGAMENTO", pagamento=pagamento).first()

    if not pay_op:
        return

    if pay_op and pay_op.status != "confirmed":
        pay_op.status = "confirmed"
        pay_op.save(update_fields=["status", "updated_on"])

    pay_opv2 = AccOpV2.objects.filter(user=pagamento.user, source="PAGAMENTO", pagamento=pagamento).first()
    if pay_opv2 and pay_opv2 != "confirmed":
        pay_opv2.status = "confirmed"
        pay_opv2.save(update_fields=["status", "updated_on"])

    is_payment_canceled = AccountingOperation.objects.filter(
        user=pagamento.user, source="PAGAMENTO_CANCELADO", pagamento=pagamento
    ).exists()
    if is_payment_canceled and value > D("0.01"):
        _pagamento_atrasado(pagamento, round(value, 2))


def pagamento_parcial(pagamento, value):
    create_op(
        user=pagamento.user,
        source="PAGAMENTO_PARCIAL",
        value=round(value, 2),
        value_real=round(value, 2),
        pagamento=pagamento,
        status="confirmed",
    )


def pagamento_extra(pagamento, value):
    get_repository(pagamento.user_id, flags.CREATE_OPS).post_pagamento_extra(
        user_id=pagamento.user_id, value=value, pagamento_id=pagamento.id
    )


def _pagamento_atrasado(pagamento, value):
    get_repository(pagamento.user_id, flags.CREATE_OPS).post_pagamento_atrasado(
        user_id=pagamento.user_id, value=value, pagamento_id=pagamento.id
    )


def pagamento_cancelado(pagamento: Pagamento, value=None):
    buserlogger.info("accounting_svc.pagamento_cancelado", extra={"pagamento_id": pagamento.id})
    existing_ops = AccountingOperation.objects.filter(pagamento=pagamento, user=pagamento.user).values_list(
        "source", flat=True
    )

    if "PAGAMENTO" not in existing_ops:
        return

    cancelou = "PAGAMENTO_CANCELADO" in existing_ops

    if not cancelou and (
        pagamento.status in Pagamento.STATUSES_WAIT | {Pagamento.Status.REFUSED, Pagamento.Status.CANCELED}
    ):
        _pagamento_cancelado(pagamento, value)
    elif pagamento.status == Pagamento.Status.CHARGEDBACK:
        if not cancelou or (cancelou and "PAGAMENTO_CHARGEBACK_DEVOLVIDO" in existing_ops):
            _pagamento_cancelado(pagamento, value, is_chargeback=True)
    elif not cancelou and pagamento.method == Pagamento.Method.DINHEIRO:
        _pagamento_cancelado(pagamento, value)


def _pagamento_cancelado(pagamento: Pagamento, value: D | None = None, is_chargeback: bool = False):
    if value is None:
        value = cast(D, pagamento.net_value)

    if value <= D("0"):
        return
    get_repository(pagamento.user_id, flags.CREATE_OPS).post_pagamento_cancelado(
        user_id=pagamento.user_id,
        value=-value,
        pagamento_id=pagamento.id,
        is_chargeback=is_chargeback,
    )


def _pagamento_chargeback_devolvido(pagamento, value):
    get_repository(pagamento.user_id, flags.CREATE_OPS).post_pagamento_chargeback_devolvido(
        user_id=pagamento.user_id, value=value, pagamento_id=pagamento.id
    )


def chargeback_refund_value(pagamento: Pagamento):
    base = Q(user=pagamento.user, pagamento=pagamento)

    if not AccountingOperation.objects.filter(base, source="PAGAMENTO_CHARGEBACK_DEVOLVIDO").exists():
        return abs(
            AccountingOperation.objects.filter(
                base,
                source__in={"PAGAMENTO_CANCELADO", "PAGAMENTO_CORRECAO"},
                is_chargeback=True,
            ).aggregate(sum=Sum("value_real"))["sum"]
            or D("0")
        )
    else:
        accop = (
            AccountingOperation.objects.filter(base, source="PAGAMENTO_CANCELADO", is_chargeback=True)
            .order_by("date")
            .last()
        )
        if accop:
            return abs(accop.value_real)
        return D("0")


def chargeback_refund(pagamento, value=None):
    if value is None or value <= D("0") or pagamento.status != "chargeback_refund":
        return

    sources = _get_sources_chargeback(pagamento)

    tem_chargeback_devolvido = "PAGAMENTO_CHARGEBACK_DEVOLVIDO" in sources
    tem_pagamento_cancelado = "PAGAMENTO_CANCELADO" in sources

    if not tem_chargeback_devolvido or (tem_pagamento_cancelado and tem_chargeback_devolvido):
        _pagamento_chargeback_devolvido(pagamento, value)


def _get_sources_chargeback(pagamento):
    base = Q(user=pagamento.user, pagamento=pagamento)
    chargebacks = Q(source="PAGAMENTO_CANCELADO", is_chargeback=True)
    chargebacks_refund = Q(source="PAGAMENTO_CHARGEBACK_DEVOLVIDO")

    return AccountingOperation.objects.filter(base & Q(chargebacks | chargebacks_refund)).values_list(
        "source", flat=True
    )


def correcao(pagamento, reason, value=None):
    if value:
        create_op(
            user=pagamento.user,
            value=round(value, 2),
            value_real=round(value, 2),
            source="PAGAMENTO_CORRECAO",
            reason=reason,
            pagamento=pagamento,
        )


def saque(transacao):
    if not AccountingOperation.objects.filter(accounting_transaction=transacao, source="SAQUE").exists():
        get_repository(transacao.user_id, flags.CREATE_OPS).post_saque(
            user_id=transacao.user_id,
            value=round(-transacao.value, 2),
            accounting_transaction_id=transacao.id,
        )


def saque_falhou(transacao):
    if not AccountingOperation.objects.filter(accounting_transaction=transacao, source="SAQUE_FALHOU").exists():
        get_repository(transacao.user_id, flags.CREATE_OPS).post_saque_falhou(
            user_id=transacao.user_id,
            value=round(transacao.value, 2),
            accounting_transaction_id=transacao.id,
        )


def finalizar_contabilidade_grupo(grupo: Grupo) -> None:
    grupo_viajou = grupo.status == Grupo.Status.DONE and not grupo.nao_teve_frete
    if not grupo_viajou:
        return
    ops = []
    for travel in grupo.travel_set.exclude(status="canceled").prefetch_related("passageiro_set"):
        for pax in travel.passageiro_set.all():
            if pax.removed:
                continue
            viajada_pax = AccOpV2(
                travel=travel,
                user_id=travel.user_id,
                passageiro=pax,
                value=0,
                value_real=0,
                value_bueda=0,
                date=now(),
                source="RESERVA_VIAJADA",
            )
            ops.append(viajada_pax)
    AccOpV2.objects.bulk_create(ops)


def _get_travels_from_accops(viagem_operations):
    op = None
    for viagem_operation in viagem_operations:
        # Usa a segunda RESERVA se existir ou a primeira.
        if viagem_operation.source == "RESERVA":
            if op:
                op = viagem_operation
                break
            else:
                op = viagem_operation
    return _get_travels_from_accop(op)


def _get_travels_from_accop(accounting_operation):
    ida: Travel | None = None
    volta: Travel | None = None

    if accounting_operation and accounting_operation.travel:
        if accounting_operation.travel.travel_ida:
            ida = accounting_operation.travel.travel_ida
            volta = accounting_operation.travel
        else:
            ida = accounting_operation.travel
            volta = None

    # TODO: fazer esse to_dict_json ao fim de todas as operações
    d_ida = ida.to_dict_json(include_deleted_grupo_classe=True) if ida else None
    if volta is not None:
        d_volta = volta.to_dict_json()
        return {"ida": d_ida, "volta": d_volta}

    return {"ida": d_ida}


def sum_ops_today(*, touser=None, fromuser=None):
    if touser:
        qs = AccountingOperation.objects.filter(user=touser)
    elif fromuser:
        qs = AccountingOperation.objects.filter(fromuser=fromuser)
    else:
        qs = AccountingOperation.objects.none()
    return qs.filter(
        source__in=["ADMIN_REAIS", "PASSAGEIRO_COMPENSADO"],
        date__gte=cast(datetime, to_default_tz(now())).replace(hour=0, minute=0, second=0),
    ).aggregate(sum=Coalesce(Sum("value_real"), D(0)))["sum"]


def get_custo_travels(travels):
    custos_por_viagem = (
        AccountingOperation.objects.filter(
            source__in=["RESERVA", "PROMOCAO", "BAGAGEM_ADICIONAL", "SEGURO_EXTRA", "MARCACAO_ASSENTO"],
            travel__in=travels,
        )
        .values("travel_id")
        .annotate(
            custo=Sum(
                Case(
                    When(source__in=["RESERVA", "PROMOCAO"], then="value_real"), default=0, output_field=DecimalField()
                )
            ),
            adicionais=Sum(
                Case(
                    When(source__in=["BAGAGEM_ADICIONAL", "SEGURO_EXTRA", "MARCACAO_ASSENTO"], then="value_real"),
                    default=0,
                    output_field=DecimalField(),
                )
            ),
        )
    )

    return {c["travel_id"]: {"custo": c["custo"], "adicionais": c["adicionais"]} for c in custos_por_viagem}


def cria_evento_reserva_remanejamento(
    old_travel: Travel,
    new_travel: Travel,
    itens_adicionais: list[ItemAdicional],
    manteve_marcacao_assento: dict[int, int] | None = None,
) -> tuple[EventoReserva, list[Any]]:
    # TODO: Paramos aqui para fazer o tratamento de bagagem adicional
    if not old_travel.status == "canceled":
        raise ValidationError("Travel de origem deve ser cancelada antes de ajustar a contabilidade.")

    if globalsettings_svc.get("novo_remanejamento_contabil") and not _travel_paid_with_credit(old_travel):
        evento = EventoRemanejamentoContabil(old_travel, new_travel, itens_adicionais, manteve_marcacao_assento)
        pax_info = evento.create_pax_info()
        if pax_ids := EventoRemanejamentoContabil.get_pax_ids_promo_move_travel(pax_info):
            new_travel.apply_promocao(pax_ids, "MOVE_TRAVEL", new_travel.cupom, new_travel.voucher)
        return EventoReserva(travel_id=new_travel.id, user_id=new_travel.user_id, pax_info=pax_info), []

    return _cria_evento_reserva_remanejamento__deprecado(old_travel, new_travel)


def _travel_paid_with_credit(travel: Travel) -> bool:
    if travel.reserva and travel.reserva.evento_credito:
        return True

    return travel.accountingoperation_set.all().filter(source__in=SOURCES_CREDITO).exists()


def _cria_evento_reserva_remanejamento__deprecado(old_travel: Travel, new_travel: Travel):
    evt = EventoRemanejamentoDeprecado(old_travel, new_travel)
    promocoes, new_preco = evt.get_new_values()
    old_valor_pago_credito, old_valor_pago_reais = evt.get_valores_pagos_old_travel()
    valor_a_pagar_promo = sum(promo["value"] for promo in promocoes)
    valor_falta_pagar = new_preco - valor_a_pagar_promo
    valor_a_pagar_credito = D("0")

    if valor_falta_pagar > D("0"):
        valor_falta_pagar = valor_falta_pagar - old_valor_pago_reais
        if valor_falta_pagar > D("0") and old_valor_pago_credito >= valor_falta_pagar:
            valor_a_pagar_credito = valor_falta_pagar
        elif valor_falta_pagar > D("0") and old_valor_pago_credito < valor_falta_pagar:
            ValidationError("Erro na contabilidade: falta valor a pagar e não tem crédito suficiente.")

    if promocoes and promocoes[0]["name"] == "MOVE_TRAVEL":
        new_travel.apply_promocao(promocoes[0]["pax_ids"], promocoes[0]["name"], new_travel.cupom, new_travel.voucher)

    if valor_a_pagar_credito > D("0"):
        evt_credito = credit_accounting_svc.cria_eventos_creditos_remanejamento(valor_a_pagar_credito, new_travel)
    else:
        evt_credito = []

    pax_info_list = constroi_pax_info(
        new_preco, D("0"), D("0"), D("0"), D("0"), 0, D("0"), promocoes, evt.get_pax_remanejados_ids(), set()
    )
    evt_reserva = EventoReserva(travel_id=new_travel.id, user_id=new_travel.user_id, pax_info=pax_info_list)
    return evt_reserva, evt_credito


def get_extrato_v2(user_id: int, filter_str: str | None = None, show_all: bool = False) -> list[dict]:
    date = now() - relativedelta(months=6)
    max_iterations = 5
    if show_all:
        date = datetime(2018, 1, 1, tzinfo=ZoneInfo("America/Sao_Paulo"))
        max_iterations = 1000
    statement = get_repository(user_id, flags.READONLY).get_statement(
        user_id, start_date=date, max_iterations=max_iterations
    )
    saldo = get_repository(user_id, flags.READONLY).get_balance(user_id)
    statement["lines"].reverse()
    response = accoputils.serialize_lines(statement["lines"], saldo["reais"])
    if filter_str:
        response = filter_list_of_dicts(response, filter_str)
    return response


def cancela_registros_contabeis(entity: Travel | Passageiro, dry_run: bool) -> D:
    passageiros = None
    if isinstance(entity, Passageiro):
        passageiros = [entity]
    elif isinstance(entity, Travel):
        passageiros = entity.passageiro_set.filter(removed=False).order_by("id")
    else:
        raise ValueError("Invalid travel or pax")

    accops_to_cancel = []
    reasons_nao_estornaveis = ValorRessarcimento.objects.filter(estornar_no_cancelamento=False).values_list(
        "key", flat=True
    )
    for pax in passageiros:
        accops_to_cancel += _accops_to_cancel(pax, cast(list, reasons_nao_estornaveis))

    if accops_to_cancel and not dry_run:
        cancela_accops(accops_to_cancel)

    return -_saldo(accops_to_cancel)


def _accops_to_cancel(pax: Passageiro, reasons_nao_estornaveis: list[str] | None = None) -> list[AccountingOperation]:
    if reasons_nao_estornaveis is None:
        reasons_nao_estornaveis = []
    sources = MAP_SOURCE_CANCELAMENTO_FUNC.keys()
    accops_pax = (
        AccOpV2.objects.filter(passageiro=pax, source__in=sources)
        .exclude(reason_key__in=reasons_nao_estornaveis)
        .order_by("id")
    )

    accops_to_cancel = []

    for accop in accops_pax:
        if not _cancelamento_existe(accop, promocao=accop.promocao):
            accops_to_cancel.append(accop)

    return accops_to_cancel


def cancela_accops(accops: list[AccOpV2]) -> None:
    for accop in accops:
        if accop.source is None:
            raise ValueError(f"Erro ao cancelar accop {accop.id} - source não informada")
        func = _get_cancelamento_func(AccOpV2.Source(accop.source))
        if func is None:
            raise ValueError(f"Erro ao cancelar accop - {accop.source} sem função de cancelamento")
        func(accop)
        buserlogger.info(f"Cancelando accop com {accop.source=}, {accop.travel_id=} e {accop.passageiro_id=}")


def _saldo(accops: list[AccountingOperation]) -> D:
    return cast(D, sum(op.value_real for op in accops if op.value_real))


def _get_cancelamento_func(source: AccOpV2.Source) -> Callable | None:
    try:
        return MAP_SOURCE_CANCELAMENTO_FUNC[source]
    except KeyError:
        raise ValidationError(f"Cancelamento não implementado para source {source}")


def _cancela_reserva(accop: AccOpV2) -> None:
    assert accop.passageiro
    _reserva_cancelada_parcial(-accop.value_real, accop.passageiro)


def _cancela_downgrade(accop: AccOpV2) -> None:
    _reserva_downgrade_cancelado(accop.passageiro, None, -accop.value_real, None)


def _cancela_neutralizacao_carbono(accop: AccOpV2) -> None:
    assert accop.passageiro
    _neutralizacao_carbono_cancelada(accop.passageiro, -accop.value_real)


def _cancela_marcacao_assento(accop: AccOpV2) -> None:
    assert accop.passageiro
    _marcacao_assento_cancelada(accop.passageiro, -accop.value_real)


def _cancela_reserva_remanejada(accop: AccOpV2) -> None:
    assert accop.passageiro
    _reserva_remanejada_cancelada(accop.passageiro, -accop.value_real)


def _cancela_seguro_extra(accop: AccOpV2) -> None:
    assert accop.passageiro
    _seguro_extra_cancelado(accop.passageiro, -accop.value_real)


def _cancela_bagagem_adicional(accop: AccOpV2) -> None:
    if not accop.passageiro:
        buserlogger.info(
            f"Erro ao cancelar a bagagem adicional - pax não informado - accop {accop.id}, travel  {accop.travel_id=}"
        )
        raise Exception("Erro ao cancelar a bagagem adicional - pax não informado")
    _bagagem_adicional_cancelada(accop.passageiro, -accop.value_real, accop.jsondata)


def _cancela_taxa_servico(accop: AccOpV2) -> None:
    assert accop.passageiro
    _taxa_servico_cancelada(accop.passageiro, -accop.value_real)


def _cancela_passageiro_compensado(accop: AccOpV2) -> None:
    _passageiro_compensado_cancelado(accop.passageiro, None, -accop.value_real, None)


def _cancela_admin_reais(accop: AccOpV2) -> None:
    admin_reais(accop.passageiro, None, -accop.value_real, None, None)


def _cancela_promocao(accop: AccOpV2) -> None:
    assert accop.passageiro
    _promocao_cancelada(accop.passageiro, -accop.value_real, accop.cupom_id, accop.promocao)


def _cancela_credito(accop: AccOpV2) -> None:
    assert accop.passageiro
    source_cancelamento = MAP_SOURCE_CANCELAMENTO_SOURCE.get(accop.source)
    assert source_cancelamento
    get_repository(accop.passageiro.travel.user_id, flags.CREATE_OPS).post_credito_cancelado(
        accop.passageiro_id,
        accop.travel_id,
        accop.user_id,
        -accop.value,
        source_cancelamento,
        accop.reason,
    )


def _cancelamento_existe(accop: AccOpV2, promocao: str | None) -> bool:
    source_cancelamento = MAP_SOURCE_CANCELAMENTO_SOURCE.get(accop.source)

    # Cancelamentos de crédito podem conter acops similares na mesma travel.
    # Isso ocorre pois o pax pode utilizar saldo de dois gift cards diferentes
    # para compor o pagamento, por exemplo. Como as acops são a nível de passageiro,
    # em função de possíveis cancelamentos, os dados das acops podem ser iguais,
    # porém não é uma duplicidade.
    if source_cancelamento == "PAGAMENTO_CREDITO_CANCELADO":
        return False

    return AccOpV2.objects.filter(
        source=source_cancelamento,
        value_real=-accop.value_real,
        passageiro=accop.passageiro,
        promocao=promocao,
    ).exists()


MAP_SOURCE_CANCELAMENTO_FUNC = {
    "RESERVA": _cancela_reserva,
    "RESERVA_DOWNGRADE": _cancela_downgrade,
    "NEUTRALIZACAO_CARBONO": _cancela_neutralizacao_carbono,
    "SEGURO_EXTRA": _cancela_seguro_extra,
    "BAGAGEM_ADICIONAL": _cancela_bagagem_adicional,
    "TAXA_SERVICO_RESERVA": _cancela_taxa_servico,
    "PASSAGEIRO_COMPENSADO": _cancela_passageiro_compensado,
    "ADMIN_REAIS": _cancela_admin_reais,
    "PROMOCAO": _cancela_promocao,
    "PAGAMENTO_CREDITO": _cancela_credito,
    "MARCACAO_ASSENTO": _cancela_marcacao_assento,
    "RESERVA_REMANEJADA": _cancela_reserva_remanejada,
}

MAP_SOURCE_CANCELAMENTO_SOURCE = {
    "RESERVA": "RESERVA_CANCELADA",
    "RESERVA_DOWNGRADE": "RESERVA_DOWNGRADE_CANCELADO",
    "NEUTRALIZACAO_CARBONO": "NEUTRALIZACAO_CARBONO_CANCELADA",
    "MARCACAO_ASSENTO": "MARCACAO_ASSENTO_CANCELADA",
    "SEGURO_EXTRA": "SEGURO_EXTRA_CANCELADO",
    "BAGAGEM_ADICIONAL": "BAGAGEM_ADICIONAL_CANCELADA",
    "TAXA_SERVICO_RESERVA": "TAXA_SERVICO_RESERVA_CANCELADA",
    "PASSAGEIRO_COMPENSADO": "PASSAGEIRO_COMPENSADO_CANCELADO",
    "ADMIN_REAIS": "ADMIN_REAIS",
    "PROMOCAO": "PROMOCAO_CANCELADA",
    "PAGAMENTO_CREDITO": "PAGAMENTO_CREDITO_CANCELADO",
    "RESERVA_REMANEJADA": "RESERVA_REMANEJADA_CANCELADA",
}


def extrato_travel(travel_id: int) -> dict[str, D]:
    sql = """
    select
        coalesce(sum(value_real) filter (where source in %(sources_custo)s), 0) as custo_reais,
        coalesce(sum(value_bueda) filter (where source in %(sources_custo)s), 0) as custo_buedas,
        coalesce(sum(value_real) filter (where source in %(sources_carbono)s), 0) as neutralizacao_carbono,
        coalesce(sum(value_real) filter (where source in %(sources_seguro_extra)s), 0) as seguro_extra,
        coalesce(sum(value_real) filter (where source in %(sources_bagagem_adicional)s), 0) as bagagem_adicional,
        coalesce(sum(value_real) filter (where source in %(sources_marcacao_assento)s), 0) as marcacao_assento,
        coalesce(sum(value_real) filter (where source in %(sources_taxa_servico)s), 0) as taxa_servico,
        coalesce(sum(value_real) filter (where source in %(sources_ressarcimento)s), 0) as ressarcimento_reais,
        coalesce(sum(value_bueda) filter (where source in %(sources_ressarcimento)s), 0) as ressarcimento_buedas,
        coalesce(sum(value_real) filter (where source in %(sources_promocao)s and (promocao not in %(promocoes_pricing)s or promocao is null)), 0) as promo_reais,
        coalesce(sum(value_real) filter (where source in %(sources_promocao)s and promocao in %(promocoes_pricing)s), 0) as promo_pricing,
        coalesce(sum(value_bueda) filter (where source in %(sources_promocao)s), 0) as promo_buedas
    from core_accountingoperation
    where travel_id = %(travel_id)s
    """
    result = dictfetchall(
        sql,
        {
            "sources_custo": SOURCES_CUSTO,
            "sources_carbono": SOURCES_CARBONO,
            "sources_seguro_extra": SOURCES_SEGURO_EXTRA,
            "sources_bagagem_adicional": SOURCES_BAGAGEM_ADICIONAL,
            "sources_taxa_servico": SOURCES_TAXA_SERVICO,
            "sources_ressarcimento": SOURCES_RESSARCIMENTO,
            "sources_marcacao_assento": SOURCES_MARCACAO_ASSENTO,
            "sources_promocao": SOURCES_PROMOCAO,
            "promocoes_pricing": PROMOCOES_PRICING,
            "travel_id": travel_id,
        },
    )[0]
    return result


def bulk_extrato_travel(travels_id: list[int]) -> dict[int, dict[str, D]]:
    sql = """
    select
        travel_id,
        coalesce(sum(value_real) filter (where source in %(sources_custo)s ), 0) as custo_reais,
        coalesce(sum(value_bueda) filter (where source in %(sources_custo)s), 0) as custo_buedas,
        coalesce(sum(value_real) filter (where source in %(sources_carbono)s), 0) as neutralizacao_carbono,
        coalesce(sum(value_real) filter (where source in %(sources_seguro_extra)s), 0) as seguro_extra,
        coalesce(sum(value_real) filter (where source in %(sources_bagagem_adicional)s), 0) as bagagem_adicional,
        coalesce(sum(value_real) filter (where source in %(sources_marcacao_assento)s), 0) as marcacao_assento,
        coalesce(sum(value_real) filter (where source in %(sources_ressarcimento)s), 0) as ressarcimento_reais,
        coalesce(sum(value_bueda) filter (where source in %(sources_ressarcimento)s), 0) as ressarcimento_buedas,
        coalesce(sum(value_real) filter (where source in %(sources_promocao)s and (promocao not in %(promocoes_pricing)s or promocao is null)), 0) as promo_reais,
        coalesce(sum(value_real) filter (where source in %(sources_promocao)s and promocao in %(promocoes_pricing)s), 0) as promo_pricing,
        coalesce(sum(value_bueda) filter (where source in %(sources_promocao)s), 0) as promo_buedas
    from core_accountingoperation
    where travel_id = ANY(%(travels_id)s)
    group by travel_id
    """
    result = dictfetchall(
        sql,
        {
            "sources_custo": SOURCES_CUSTO,
            "sources_carbono": SOURCES_CARBONO,
            "sources_seguro_extra": SOURCES_SEGURO_EXTRA,
            "sources_bagagem_adicional": SOURCES_BAGAGEM_ADICIONAL,
            "sources_marcacao_assento": SOURCES_MARCACAO_ASSENTO,
            "sources_ressarcimento": SOURCES_RESSARCIMENTO,
            "sources_promocao": SOURCES_PROMOCAO,
            "promocoes_pricing": PROMOCOES_PRICING,
            "travels_id": travels_id,
        },
    )
    return {r["travel_id"]: r for r in result}


def extrato_pax(pax_id: int) -> dict[str, D]:
    sql = """
    select
        coalesce(sum(value_real) filter (where source in %(sources_ressarcimento)s), 0) as ressarcimento_reais,
        coalesce(sum(value_real) filter (where source in %(sources_promocao)s and (promocao not in  %(promocoes_pricing)s or promocao is null)), 0) as promo_reais
    from core_accountingoperation where passageiro_id = %(pax_id)s
    """
    result = dictfetchall(
        sql,
        {
            "sources_ressarcimento": SOURCES_RESSARCIMENTO,
            "sources_promocao": SOURCES_PROMOCAO,
            "promocoes_pricing": PROMOCOES_PRICING,
            "pax_id": pax_id,
        },
    )[0]
    return result


@lock("cria_registro_contabil_{reserva_id}")
def cria_registros_contabeis(reserva_id: int) -> None:
    evt_reserva = Reserva.objects.get(id=reserva_id)

    if evt_reserva.pagamento():
        pagamento(evt_reserva.pagamento())

    eventos_contabeis = evt_reserva.evento_contabil
    eventos_credito = evt_reserva.evento_credito

    for evento in eventos_contabeis:
        reserva(EventoReserva.to_obj(evento))

    for evento in eventos_credito:
        credit_accounting_svc.create_credits_account(EventoCreditoReserva.to_obj(evento))


def cria_eventos_reserva(extrato_reserva: dict, travels: list) -> list[EventoReserva]:
    extratos_ida, extratos_volta = extrato_reserva["travels_ida"], extrato_reserva["travels_volta"]
    extratos = extratos_ida + extratos_volta

    valor_total_reserva = extrato_reserva["pagamento_total"] + sum(
        extrato["extrato"].get("reais_utilizados", 0) + extrato["extrato"].get("creditos_utilizados", 0)
        for extrato in extratos
    )

    eventos_reserva = []
    for travel, _extrato_travel in zip(travels, extratos):
        evento_reserva = cria_evento_reserva(travel, _extrato_travel, valor_total_reserva)
        eventos_reserva.append(evento_reserva)

    return eventos_reserva


def constroi_pax_info(
    valor_reserva: D,
    valor_carbono: D,
    valor_seguro_extra: D,
    valor_taxa_servico: D,
    valor_bagagem_adicional: D,
    quantidade_bagagem_adicional: int,
    valor_marcacao_assento: D,
    promocoes: list[dict],
    pax_ids: list[int],
    pax_ids_marcacao_assento: set[int],
):
    carbono_ppax_list = utils.split_value(valor_carbono, len(pax_ids))
    seguro_extra_ppax_list = utils.split_value(valor_seguro_extra, len(pax_ids))
    marcacao_assento_ppax_list = utils.split_value(valor_marcacao_assento, len(pax_ids_marcacao_assento))
    reserva_ppax_list = utils.split_value(valor_reserva, len(pax_ids))
    taxa_servico_ppax_list = utils.split_value(valor_taxa_servico, len(pax_ids))

    pax_info_list = []
    first_pax = True
    count_assentos = 0
    for pax_id, reserva, carbono, seguro_extra, taxa_servico in zip(
        pax_ids, reserva_ppax_list, carbono_ppax_list, seguro_extra_ppax_list, taxa_servico_ppax_list
    ):
        quantidade_bagagem = 0
        valor_bagagem = D("0")

        if first_pax and quantidade_bagagem_adicional:
            quantidade_bagagem = quantidade_bagagem_adicional
            valor_bagagem = valor_bagagem_adicional
            pax = Passageiro.objects.get(pk=pax_id)
            pax.set_bagagem(quantidade_bagagem_adicional)
            first_pax = False

        marcacao_assento_value = D("0")
        if pax_id in pax_ids_marcacao_assento:
            marcacao_assento_value = marcacao_assento_ppax_list[count_assentos]
            count_assentos += 1

        pax_info_list.append(
            PaxInfo(
                id=pax_id,
                valor_reserva=reserva,
                valor_carbono=carbono,
                valor_seguro_extra=seguro_extra,
                valor_taxa_servico=taxa_servico,
                valor_bagagem_adicional=valor_bagagem,
                quantidade_bagagem_adicional=quantidade_bagagem,
                promocoes=[],
                valor_marcacao_assento=marcacao_assento_value,
            )
        )
    for promocao in promocoes:
        promo_pax_ids = _get_promocao_attr(promocao, "pax_ids")
        if not promo_pax_ids:
            continue
        promo_ppax_list = utils.split_value(cast(D, _get_promocao_attr(promocao, "value")), len(promo_pax_ids))
        for pax_id, promo_value in zip(promo_pax_ids, promo_ppax_list):
            info = [pax_info for pax_info in pax_info_list if pax_info.id == pax_id][0]
            info.promocoes.append(
                Promocao(
                    nome=_get_promocao_attr(promocao, "name"),
                    cupom_id=_get_promocao_attr(promocao, "cupom_id"),
                    valor=promo_value,
                    description=_get_promocao_attr(promocao, "description"),
                )
            )

    return pax_info_list


def _get_promocao_attr(promocao, attr, default=None, *, use_none=False):
    try:
        value = getattr(promocao, attr)
    except AttributeError:
        try:
            value = promocao[attr]
        except KeyError:
            value = default
        else:
            if value is None and use_none is False:
                # Em alguns casos a chave existe mas o valor é none.
                value = default

    return value


def get_valor_total_taxa_servico_checkout_por_travel(travel_id):
    source = "TAXA_SERVICO_RESERVA"
    accop_result = AccountingOperation.objects.filter(travel_id=travel_id, source=source).aggregate(
        valor_total_taxa=Coalesce(-Sum("value_real"), D("0"))
    )
    return accop_result["valor_total_taxa"]


def calcula_accops_travel_map(travels: Iterable[Travel], addons=True, promocao=False) -> dict[int, D]:
    """
    Retorna um mapa da soma de accops relacionadas a uma travel.
    Os parâmetros permitem adicionar ao cálculo os itens adicionais de compra, como SEGURO_EXTRA ou valores referentes à
    promoção, da ACCOP PROMOCAO. Esse segundo é útil para cálculo do GMV da Travel.
    """

    cash_sources = [
        "RESERVA",
        "RESERVA_CANCELADA",
        "RESERVA_PARCIAL",
        "RESERVA_CANCELADA_PARCIAL",
        "REALOCAR_BUEDAS",  # legado
        # marketplace
        "TAXA_CANCELAMENTO",
        "TAXA_SERVICO_RESERVA",
        "TAXA_SERVICO_RESERVA_CANCELADA",
    ]
    promocao_sources = ["PROMOCAO", "PROMOCAO_CANCELADA"]
    addons_sources = [
        "SEGURO_EXTRA",
        "SEGURO_EXTRA_CANCELADO",
        "MARCACAO_ASSENTO",
        "MARCACAO_ASSENTO_CANCELADA",
        "BAGAGEM_ADICIONAL",
        "BAGAGEM_ADICIONAL_CANCELADA",
        "NEUTRALIZACAO_CARBONO",
        "NEUTRALIZACAO_CARBONO_CANCELADA",
        "MARCACAO_ASSENTO",
        "MARCACAO_ASSENTO_CANCELADA",
    ]
    SOURCES = cash_sources

    if addons:
        SOURCES += addons_sources
    if promocao:
        SOURCES += promocao_sources

    accops_total_travel_map = dict(
        AccountingOperation.objects.filter(travel__in=travels, source__in=SOURCES)
        .values("travel_id")
        .annotate(value_total=Coalesce(-Sum("value_real"), D(0)))
        .values_list("travel_id", "value_total")
    )

    return accops_total_travel_map


def check_reserva_tem_seguro_extra(travel_id: int) -> bool:
    return AccountingOperation.objects.filter(travel_id=travel_id, source="SEGURO_EXTRA").exists()
