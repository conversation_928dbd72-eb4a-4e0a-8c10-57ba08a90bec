# Generated by Django 4.1.13 on 2025-07-08 20:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounting", "0018_alter_companyaccountingoperation_source"),
    ]

    operations = [
        migrations.AlterField(
            model_name="companyaccountingoperation",
            name="source",
            field=models.CharField(
                choices=[
                    ("SAQUE", "Saque"),
                    ("SAQUE_FALHOU", "Saque <PERSON>"),
                    ("PAGAMENTO_VIAGEM", "Pagamento Viagem"),
                    ("PAGAMENTO_VIAGEM_CANCELADO", "Pagamento Viagem Cancelado"),
                    ("DESCONTO_PAGAMENTO_VIAGEM", "Desconto Pagamento Viagem"),
                    ("DESCONTO_PAGAMENTO_VIAGEM_CANCELADO", "Desconto Pagamento Viagem Cancelado"),
                    ("PAGAMENTO_BONUS", "Pagamento Bonus"),
                    ("PAGAMENTO_BONUS_CANCELADO", "Pagamento Bonus Cancelado"),
                    ("PAGAMENTO_DIA_PARADO", "Pagamento Dia Parado"),
                    ("REPASSE_VIAGEM_SUBTRAIDO", "Repasse Viagem Subtraido"),
                    ("REPASSE_VIAGEM_SUBTRAIDO_CANCELADO", "Repasse Viagem Subtraido Cancelado"),
                    ("PAGAMENTO_DIA_PARADO_CANCELADO", "Pagamento Dia Parado Cancelado"),
                    ("PAGAMENTO_EMPRESTIMO", "Pagamento Emprestimo"),
                    ("RETENCAO_EMPRESTIMO_IRRF", "Retencao Emprestimo Irrf"),
                    ("DESCONTO_EMPRESTIMO", "Desconto Emprestimo"),
                    ("DESCONTO_EMPRESTIMO_CANCELADO", "Desconto Emprestimo Cancelado"),
                    ("RETENCAO_EMPRESTIMO_IRRF_CANCELADO", "Retencao Emprestimo Irrf Cancelado"),
                    ("TRANSF_RECEBIDA_CFRETE", "Transf Recebida Cfrete"),
                    ("TRANSF_PARA_CFRETE", "Transf Para Cfrete"),
                    ("TRANSF_RECEBIDA_CDIAS_PARADOS", "Transf Recebida Cdias Parados"),
                    ("TRANSF_PARA_CDIAS_PARADOS", "Transf Para Cdias Parados"),
                    ("TRANSF_RECEBIDA_CBONUS", "Transf Recebida Cbonus"),
                    ("TRANSF_PARA_CBONUS", "Transf Para Cbonus"),
                    ("TRANSF_RECEBIDA_CEMPRESTIMO", "Transf Recebida Cemprestimo"),
                    ("TRANSF_PARA_CEMPRESTIMO", "Transf Para Cemprestimo"),
                    ("DESCONTO_EMPRESTIMO_IOF", "Desconto Emprestimo Iof"),
                    ("DESCONTO_EMPRESTIMO_IOF_CANCELADO", "Desconto Emprestimo Iof Cancelado"),
                    ("ARRENDAMENTO_ONIBUS", "Arrendamento Onibus"),
                    ("ARRENDAMENTO_ONIBUS_CANCELADO", "Arrendamento Onibus Cancelado"),
                    ("RETENCAO_LOCADORA", "Retencao Locadora"),
                    ("RETENCAO_LOCADORA_CANCELADA", "Retencao Locadora Cancelada"),
                    ("PAGAMENTO_PEDAGIO", "Pagamento Pedagio"),
                    ("PAGAMENTO_PEDAGIO_CANCELADO", "Pagamento Pedagio Cancelado"),
                    ("TRANSF_PARA_CPEDAGIO", "Transf Para Cpedagio"),
                    ("TRANSF_RECEBIDA_CPEDAGIO", "Transf Recebida Cpedagio"),
                    ("DESCONTO_ANTECIPACAO", "Desconto Antecipacao"),
                    ("DESCONTO_ANTECIPACAO_CANCELADO", "Desconto Antecipacao Cancelado"),
                    ("DESCONTO_DIVIDA", "Desconto Divida"),
                    ("DESCONTO_DIVIDA_CANCELADO", "Desconto Divida Cancelado"),
                    ("SALDO_BLOQUEADO", "Saldo Bloqueado"),
                    ("SALDO_BLOQUEADO_CANCELADO", "Saldo Bloqueado Cancelado"),
                ],
                max_length=64,
            ),
        ),
    ]
