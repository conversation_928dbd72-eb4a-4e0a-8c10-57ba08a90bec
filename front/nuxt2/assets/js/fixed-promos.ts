// import paginaPromocoesImage from '~/assets/images/home/<USER>/BANNER_PROMOCOES.webp'
// import paginaDescubraImage from '~/assets/images/home/<USER>/PRODUTO_BANNER_DESCUBRA.webp'
// import paginaValeBuserImage from '~/assets/images/home/<USER>/PRODUTO_BANNER_VALE_BUSER.webp'
// import bannerBuserMais from '~/assets/images/home/<USER>/campanha/BANNER_BUSERMAIS.webp'
import bannerNaoViaja from '~/assets/images/home/<USER>/BANNER_NAO_VIAJA.webp'
import bannerIndique from '~/assets/images/home/<USER>/campanha/BANNER_INDIQUE_2.webp'
// import bannerPrimeira30 from '~/assets/images/home/<USER>/campanha/BANNER_PRIMEIRA_30_2.webp'
import bannerVale from '~/assets/images/home/<USER>/campanha/BANNER_VALE_2.webp'

interface Promo {
  code: string // Por padrão o link do cupom será /promo/{code}
  promo_image: string
  description: string
}

interface FixedPromo extends Promo {
  path?: string // O link pode ser customizado aqui
  pathsAB?: string[] // Caso queira fazer um teste AB com links diferentes, usa isso daqui
  target?: string
  options?: {
    loggedIn?: boolean // true = só logado, false = só deslogado, undefined = todos
    showEsquentaBF?: boolean
    showBF?: boolean
    hideApp?: boolean
    targetPage?: string,
  }
}

// const DESCUBRA: FixedPromo = {
//   code: 'DESCUBRA',
//   promo_image: paginaDescubraImage,
//   description: 'Descubra ofertas',
//   path: '/onibus/de/descubra'
// }

// const VALE_BUSER: FixedPromo = {
//   code: 'VALE_BUSER',
//   promo_image: paginaValeBuserImage,
//   description: 'Valer Buser.',
//   path: 'https://vale.buser.com.br',
//   target: '_blank'
// }

// const PROMOCOES: FixedPromo = {
//   code: 'PROMOCOES',
//   promo_image: paginaPromocoesImage,
//   description: 'Viagens em promoção.',
//   path: '/onibus/promo/de'
// }

// const PRIMEIRA_30: FixedPromo = {
//   code: 'NAOVIAJA',
//   promo_image: bannerPrimeira30,
//   description: 'Primeira viagem com 30% de desconto.'
// }

const VALE_BUSER: FixedPromo = {
  code: 'VALE_BUSER',
  promo_image: bannerVale,
  description: 'Valer Buser.',
  path: 'https://vale.buser.com.br',
  target: '_blank'
}

const INDIQUE: FixedPromo = {
  code: 'INDIQUE',
  promo_image: bannerIndique,
  description: 'Indique e ganhe.',
  path: '/perfil/close-friends'
}

const NAO_VIAJA: FixedPromo = {
  code: 'NAO_VIAJA',
  promo_image: bannerNaoViaja,
  description: 'Não viaja, vai de Buser.',
  path: '/onibus/de/descubra'
}

const FIXED_PROMOS: FixedPromo[] = [
  INDIQUE,
  VALE_BUSER,
  NAO_VIAJA
]

export default () => FIXED_PROMOS
