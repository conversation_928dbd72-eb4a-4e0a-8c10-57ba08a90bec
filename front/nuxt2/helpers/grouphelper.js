import dayjs from 'dayjs'
import { useSettingsStore } from '~/stores/settings.js'

export const MARKETPLACE = 'marketplace'
export const BUSER = 'buser'
export const HIBRIDO = 'hibrido'

// Map de final de slugs que representam paises validos cadastrados hoje no banco de dados
// Ex: O slug de Buenos Aires é 'buenos-aires-arg-ex' e o final da string ('arg-ex') representa consistentemente o país
export const MAP_FINAL_SLUG_PAISES_VALIDOS = {
  'buenos-aires-arg-ex': 'argentina',
  'santiago-chile-ex': 'chile',
  'uruguai-ex': 'uruguai',
  'paraguai-ex': 'paraguai',
  'peru-ex': 'peru'
}

const THRESHOLD_AVISO_VAGAS_RESTANTES = 12
const THRESHOLD_AVISO_VAGAS_RESTANTES_BUCKETIZADO = 5

export function canMakeReservation(group) {
  return (
    group.status in { pending: 1, travel_confirmed: 1 } &&
    !group.closed &&
    group.vagas > 0 &&
    dayjs().isBefore(group.datetime_ida)
  )
}

export function isMarketplace(group) {
  return group?.modelo_venda === MARKETPLACE
}

export function isHibrido(group) {
  return group?.modelo_venda === HIBRIDO
}

export function isAssentosOciosos(group) {
  return group?.modelo_operacao === 'assentos_ociosos'
}

export function atLeastOneGroupIsMarketplace(groups) {
  return groups.some((p) => p.modelo_venda === MARKETPLACE)
}

export function atLeastOneGroupIsHibrido(groups) {
  return groups.some((p) => p.modelo_venda === HIBRIDO)
}

export function atLeastOneGroupIsBuser(groups) {
  return groups.some((p) => p.modelo_venda === BUSER)
}

export function isMarketplaceOrHibrido(group) {
  return isMarketplace(group) || isHibrido(group)
}

export function getItinerarioOrigemIndex(group) {
  return group?.itinerario?.findIndex(item => group.origem_id === item.local_id)
}

export function getItinerarioDestinoIndex(group) {
  return group?.itinerario?.findIndex(item => group.destino_id === item.local_id)
}

export function getAndTrimItinerario(group, trimBeforeOrigem = true, trimAfterDestino = true, trimBetween = false) {
  const indexOrigem = getItinerarioOrigemIndex(group)
  const indexDestino = getItinerarioDestinoIndex(group)
  return group?.itinerario?.filter((itinerario, index) => {
    if (!trimBeforeOrigem && !trimAfterDestino) { return true }
    if (!trimBeforeOrigem) { return index <= indexDestino }
    if (!trimAfterDestino) { return index >= indexOrigem }
    if (trimBetween) {
      return index === indexOrigem || index === indexDestino
    }
    return index >= indexOrigem && index <= indexDestino
  }) || []
}

export function quantidadeParadas(group) {
  return getAndTrimItinerario(group).length - 2 // -2 por que o primeiro e o último não são paradas
}

export function isViagemDireta(group) {
  return quantidadeParadas(group) === 0
}

export function isViagemInternacional(group) {
  if (!group) {
    return null
  }
  const groups = Array.isArray(group) ? group : [group]
  return groups.some((g) => g.destino?.uf === 'EX' || g.origem?.uf === 'EX')
}

export function getPaisDestino(group) {
  // retorna o pais do slug se for um dos paises validos
  for (const finalSlug of Object.keys(MAP_FINAL_SLUG_PAISES_VALIDOS)) {
    if (group?.destino?.slug?.endsWith(finalSlug)) {
      return MAP_FINAL_SLUG_PAISES_VALIDOS[finalSlug]
    }
  }
  // ou retorna o Brazil-zil-zil
  return 'brasil'
}

export function isGrupoBucketizado(group) {
  // vagas_bucket vem do back como 0 se não for bucketizado ou se tiver esgotado
  return group?.vagas_bucket > 0 && group?.vagas_bucket < group?.vagas
}

export function getVagasRestantes(group) {
  const groups = Array.isArray(group) ? group : [group]
  const allVagas = groups.map((g) => isGrupoBucketizado(g) ? g?.vagas_bucket : g?.vagas)
  return Math.min(...allVagas)
}

export function getAvisoVagasRestantes(group) {
  let vagas = group?.vagas
  let mostraAviso = vagas > 0 && vagas <= THRESHOLD_AVISO_VAGAS_RESTANTES
  let texto = vagas > 1
    ? 'vagas disponíveis'
    : 'vaga disponível'

  if (isGrupoBucketizado(group)) {
    vagas = group?.vagas_bucket
    mostraAviso = vagas <= THRESHOLD_AVISO_VAGAS_RESTANTES_BUCKETIZADO
    texto = vagas > 1
      ? 'vagas neste preço'
      : 'vaga neste preço'
  }

  return mostraAviso ? `${vagas} ${texto}` : ''
}

export function getAvisoVagasRestantesV2(group) {
  let vagas = group?.vagas
  let mostraAviso = vagas > 0 && vagas <= THRESHOLD_AVISO_VAGAS_RESTANTES
  let texto = vagas > 1
    ? 'vagas disponíveis.'
    : 'vaga disponível.'

  if (isGrupoBucketizado(group)) {
    vagas = group?.vagas_bucket
    mostraAviso = vagas <= THRESHOLD_AVISO_VAGAS_RESTANTES_BUCKETIZADO
    texto = vagas > 1
      ? 'vagas neste preço.'
      : 'vaga neste preço.'
  }

  return mostraAviso ? `Apenas ${vagas} ${texto}` : ''
}

export function getTextoVagasDisponivel(group) {
  if (!group?.vagas) {
    return ''
  }
  return group?.vagas > 1
    ? `${group.vagas} vagas disponíveis`
    : `${group.vagas} vaga disponível`
}

export function getGroupInfo(group) {
  if (!group) {
    return {}
  }
  const origem = group.origem ? group.origem.slug : null
  const destino = group.destino ? group.destino.slug : null
  const datetime_ida = group.datetime_ida || group.departureDate
  const params = {
    origem,
    destino,
    trecho:
      origem && destino
        ? origem > destino
          ? `${destino}=${origem}`
          : `${origem}=${destino}`
        : '',
    grupo_id: group.grupo_id,
    grupo_classe_id: group.id, // isso aqui na verdade é o trecho_classe_id
    trecho_classe_id: group.id,
    trecho_vendido_id: group.trecho_vendido_id,
    datetime_ida,
    datetime_volta: group.returnDate || null,
    tipo_assento: group.tipo_assento,
    max_split_value: group.max_split_value,
    value: group.max_split_value, // convenção do Facebook
    vagas: group.vagas,
    preco_rodoviaria: group.preco_rodoviaria,
    status: group.status,
    onibus_plotado: group.onibus_plotado,
    origem_id: group.origem?.id || group.origem_id,
    origem_name: group.origem?.name,
    origem_city_name: group.origem?.city_name,
    origem_uf: group.origem?.uf,
    origem_nickname: group.origem?.nickname,
    destino_id: group.destino?.id || group.destino_id,
    destino_name: group.destino?.name,
    destino_city_name: group.destino?.city_name,
    destino_uf: group.destino?.uf,
    destino_nickname: group.destino?.nickname,
    difDataDeAgora: group.difDataDeAgora,
    modelo_venda: group.modelo_venda,
    is_grupo_recomendado: group.is_grupo_recomendado,
    has_parcelamento: !!group?.parcelamento,
    has_marcacao_assento: group?.has_marcacao_assento,
    isPrecoPromocional: group.isPrecoPromocional,
    results: group.results
  }
  return params
}

export function splitGroupIds(idIda, idVolta) {
  const idsIda = idIda ? idIda.split(':') : []
  const idsVolta = idVolta ? idVolta.split(':') : []
  const isConexao = idsIda.length > 1 || idsVolta.length > 1

  return { idsIda, idsVolta, isConexao }
}

export function atLeastOneGroupCompanyHasRequiredPassengerData(groups) {
  const settingsStore = useSettingsStore()
  const { empresas_marketplace_ids_data_nascimento_obrigatoria } = settingsStore.settings

  if (!empresas_marketplace_ids_data_nascimento_obrigatoria) return false

  return groups.some((p) => empresas_marketplace_ids_data_nascimento_obrigatoria.includes(p.company_id))
}

export function groupsHaveBuserPremium(groups) {
  return groups.some((grupo) => {
    return grupo?.company_name?.includes('Nena') && grupo?.tipo_assento?.includes('cama') && grupo?.modelo_venda === 'buser'
  })
}
