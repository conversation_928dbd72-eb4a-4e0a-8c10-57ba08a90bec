import autoapplyPromo from '~/helpers/autoapply-promo.ts'
import dateEventInfo from '~/helpers/date-event-info.js'
import EventBus from '~/helpers/eventbus.js'
import { dateFormat, DATE_FORMATS } from '~/helpers/formatters.js'
import { groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import localstorage from '~/helpers/localstorage.js'

export default {
  emiteEventosDeCompra(travels, extratoReserva, neverTraveled, currentUser, promo, $cookies, travelRemarcadaId, isCheckoutOffer, origem, destino, addonsPriceConfig, bagagemAdicional, incentivoRemarcacao, upgrade, idsGruposRecomendados, viuNovoCheckout, grupoDestaque) {
    travels.forEach((travel, idx) => {
      if (travel.grupo) {
        travel.is_volta = !!travel.travel_ida
        travel.cash = extratoReserva[idx].footer.value

        // TODO: O correto aqui seria pegar pelo type igual à taxa de revenda, mas não temos para todos
        travel.discount = Math.abs(extratoReserva[idx].entries?.find(e => e.title === 'Promoção')?.value || 0)
        travel.valorSeguroExtra = extratoReserva[idx].entries?.find(e => e.title === 'Seguro')?.value || 0
        travel.valorMarcacaoAssento = extratoReserva[idx].entries?.find(e => e.title === 'Seleção poltrona')?.value ?? -1
        travel.valorCarbono = extratoReserva[idx].entries?.find(e => e.title === 'Neutralização de carbono')?.value || 0
        travel.taxaRevenda = extratoReserva[idx].entries?.find(e => e.type === 'taxa_revenda')?.value || 0
        travel.hasDescontoTaxaRevenda = extratoReserva[idx].entries?.find(e => e.type === 'taxa_revenda')?.desconto_taxa_servico > 0
        travel.descontoTaxaRevenda = extratoReserva[idx].entries?.find(e => e.type === 'taxa_revenda')?.desconto_taxa_servico || 0

        travel.neverTraveled = neverTraveled
        promo = promo || autoapplyPromo.getPromo($cookies.get(autoapplyPromo.cookieKey))
        travel.promo = promo?.code || $cookies.get('promo') || ''
        travel.cupom_code = promo?.cupom_code || ''
        travel.voucher_code = promo?.voucher_code || ''
        travel.diaSemana = dateEventInfo.diaSemana(travel.grupo.datetime_ida)
        travel.periodo = dateEventInfo.periodo(travel.grupo.datetime_ida)
        travel.difDataDeAgora = dateEventInfo.difDataDeAgora(travel.grupo.datetime_ida)
        travel.horaViagem = dateEventInfo.horaViagem(travel.grupo.datetime_ida)
        travel.isRecomendado = idsGruposRecomendados.includes(travel.grupo.hashed_id)
        travel.fromGoogleTransit = grupoDestaque?.id === travel.grupo.hashed_id
        const hasBuserPremium = groupsHaveBuserPremium([travel.grupo])

        EventBus.$emit('solicitou-reserva', {
          travel, currentUser, travelRemarcadaId, isCheckoutOffer, addonsPriceConfig, bagagemAdicional, incentivoRemarcacao, hasBuserPremium, viuNovoCheckout
        })
        if (!travel.payment || travel.payment.method !== 'boleto') {
          EventBus.$emit('purchase-confirmed', travel)
        }
        if (upgrade.isUpgrade) {
          EventBus.$emit('efetuou-upgrade-poltrona', {
            travel,
            gmvUpgrade: upgrade.gmvUpgrade,
            lastPage: upgrade.lastPage
          })
        }
      }
    })
    const conexoesClicadas = localstorage.getLocalStorageWithExpiry('conexoes-clicadas')
    const origemDestino = { origem, destino }
    const conexaoValida = conexoesClicadas?.some(conexao => conexao.origem === origemDestino.origem && conexao.destino === origemDestino.destino)
    if (conexaoValida) {
      EventBus.$emit('comprou-conexao', origemDestino)
    }
  },
  getGroupInfo(group) {
    if (!group) {
      return {}
    }

    const origemSlug = group.origem?.slug || null
    const destinoSlug = group.destino?.slug || null
    const origemName = group.origem?.name || null
    const destinoName = group.destino?.name || null
    const datetime_ida = group.datetime_ida || group.departureDate
    const content_ids = origemSlug && destinoSlug ? (origemSlug > destinoSlug ? `${destinoSlug}=${origemSlug}` : `${origemSlug}=${destinoSlug}`) : ''
    const quantity = group.passengers?.length || 1
    const params = {
      // Parâmetros obrigatórios pro FB
      content_ids,
      content_name: `${origemSlug}-${destinoSlug}`,
      content_category: 'rotas',
      content_type: 'product',
      contents: [{ id: content_ids, quantity }],
      currency: 'BRL',
      value: group.max_split_value,
      // Parâmetros custom
      origem: origemName,
      destino: destinoName,
      datetime_ida: dateFormat(DATE_FORMATS.date, datetime_ida),
      tipo_assento: group.tipo_assento
    }
    return params
  },
  prepatePaymentLinkPayload(reservationPayload, reservationData, completeStatement) {
    const groups = [...reservationPayload.groups.trechos_ida, ...reservationPayload.groups.trechos_volta]
    const payload = {
      reservationDataCleaned: {
        ...reservationPayload,
        groups,
        payment: {}
      },
      paymentInfo: {
        ...completeStatement,
        possiblePassengers: reservationData.possiblePassengers,
        passengers: reservationData.passengers
      }
    }
    return payload
  }
}
