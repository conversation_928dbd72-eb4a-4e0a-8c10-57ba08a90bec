import dayjs from 'dayjs'
import { useGeolocationStore } from '~/stores/geolocation.js'
import { useSearchStore } from '~/stores/search.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import { useToastStore } from '~/stores/toast.js'

export default {
  ALLOWED_COMPANIES_TO_FILTER: ['1190'], // Catedral
  parseSearchResponse(response) {
    response.groups_by_date.forEach(item => (
      this.assignExtraData(item.grupos, response)
    ))
    this.itinerariosToMap(response)
    delete response.itinerarios
    return response
  },
  parseSearchConexaoResponse(response) {
    if (!response?.groups_by_date) {
      return null
    }
    response.groups_by_date.forEach(item => (
      this.assignExtraDataConexao(item.grupos, response)
    ))
    delete response.itinerarios
    return response
  },
  splitGroupsByNextDay(groupsByDate, departureDate) {
    const groupsByDateCopy = [...groupsByDate]
    const groupsNextDay = {}
    groupsNextDay.datetime_ida = dayjs(departureDate).add(1, 'day').format('YYYY-MM-DD')
    groupsNextDay.grupos = []
    groupsByDate.forEach((elem, idx) => {
      if (elem.datetime_ida === groupsNextDay.datetime_ida) {
        groupsNextDay.grupos = elem.grupos
        groupsByDateCopy.splice(idx, 1)
      }
    })

    return { groupsNextDay, groupsByDate: groupsByDateCopy }
  },
  assignExtraData(grupos, response) {
    grupos.forEach(grupo => {
      const extra = {
        origem: response.locais[grupo.origem_id],
        destino: response.locais[grupo.destino_id],
        itinerario: response.itinerarios[grupo.rota_id]
      }
      Object.assign(grupo, extra)
    })
  },
  itinerariosToMap(response) {
    Object.values(response.itinerarios).forEach(iti => {
      iti.forEach(checkpoint => {
        checkpoint.local = response.locais[checkpoint.local_id]
      })
    })
  },

  assignExtraDataConexao(grupo, response) {
    const mapItinerarioLocal = Object.values(response.itinerarios).reduce((acc, iti) => {
      iti.forEach(checkpoint => {
        acc[checkpoint.local_id] = checkpoint
      })
      return acc
    }, {})

    grupo.forEach(grupo => {
      const primeiraConexao = grupo.conexoes[0]
      const ultimaConexao = grupo.conexoes[grupo.conexoes.length - 1]

      const origem_id = primeiraConexao.origem_id
      const origem = response.locais[origem_id]
      const destino_id = ultimaConexao.destino_id
      const destino = response.locais[destino_id]

      let itinerario = []

      grupo.conexoes.forEach(conexao => {
        const origem = response.locais[conexao.origem_id]
        const destino = response.locais[conexao.destino_id]
        Object.assign(conexao, { origem, destino })

        const checkpoint = mapItinerarioLocal[conexao.origem_id]
        checkpoint.local = response.locais[checkpoint.local_id]

        itinerario.push(checkpoint)
      })
      const extra = {
        origem,
        destino,
        itinerario,
        origem_id,
        destino_id,
        chegada_ida: ultimaConexao.chegada_ida
      }
      Object.assign(grupo, extra)
    })
  }
}

export const search = async(route, $pinia, headers) => {
  const searchboxStore = useSearchboxStore($pinia)
  const searchStore = useSearchStore($pinia)

  const isVolta = searchStore.isVolta(route)

  const initialSearchParams = [
    searchboxStore.fromCity?.slug,
    searchboxStore.toCity?.slug
  ]
  const newSearchOrigin = route.params.origem
  const newSearchDestiny = route.params.destino

  if (isVolta && (!initialSearchParams.includes(newSearchOrigin) || !initialSearchParams.includes(newSearchDestiny))) {
    searchStore.setGrupoIda(null)
  }

  const searchParams = route.params
  const searchQuery = route.query

  const departureDate = isVolta ? searchQuery.volta : searchQuery.ida

  try {
    await searchStore.search({
      date: departureDate,
      origem: searchParams.origem,
      destino: searchParams.destino,
      weekDay: searchParams.weekDay,
      company: searchQuery.company
    }, route, headers)
  } catch (error) {
    // Se o erro for 400 ou 404, quer dizer que o slug da origem ou do destino estão errados
    // logo apenas olhamos o erro se não for um desses casos
    if (error.response?.status !== 400 && error.response?.status !== 404) {
      throw error
    }
  }
}

export const loadSuggestions = async($route, $pinia, withSEOContent = false, defaultLimit = 15) => {
  const searchStore = useSearchStore($pinia)
  const searchParams = $route.params
  const searchQuery = $route.query
  const searchMeta = Array.isArray($route.meta) ? $route.meta[0] : $route.meta

  const limit = (() => {
    if (searchMeta?.isPromo) {
      return null
    }
    if (!searchQuery.suggestions) {
      return defaultLimit
    }
    if (searchQuery.suggestions !== 'all') {
      return parseInt(searchQuery.suggestions) || null
    }

    return null
  })()

  const promo = searchMeta?.isPromo
  await searchStore.fetchSuggestions({ ...searchParams, limit, withSEOContent, promo })
}

export const getGroupInfo = (group) => {
  if (!group) {
    return {}
  }
  const origemSlug = group.origem ? group.origem.slug : null
  const destinoSlug = group.destino ? group.destino.slug : null
  if (!origemSlug || !destinoSlug) return ''
  return origemSlug > destinoSlug ? `${destinoSlug}=${origemSlug}` : `${origemSlug}=${destinoSlug}`
}

export const wrapperFestivalOptions = async($route, $pinia) => {
  const params = {}
  const query = {}

  const cidadeFallback = 'sao-paulo-sp'
  const rioFallback = 'rio-de-janeiro-rj'

  if (!$route.params.origem) {
    const geolocationStore = useGeolocationStore($pinia)
    try {
      await geolocationStore.fetchLocationFromApi()
    } catch (error) {
      console.error(error)
    }
    params.origem = geolocationStore.currentPosition?.slug || cidadeFallback
  }

  if (!$route.params.destino) {
    params.destino = $route.params.origem !== rioFallback ? rioFallback : cidadeFallback
  }

  return { params, query }
}

export const redirectTo = (route, redirect) => {
  if (route.query.promo) {
    const query = { ...route.query, promo: undefined }
    return [true, () => redirect({ ...route, name: 'promoOrigin', query })]
  }

  if (route.query.ida && dayjs().isAfter(route.query.ida, 'day')) {
    const localQuery = { ...route.query, ida: undefined }
    return [true, () => redirect({ ...route, query: localQuery, name: 'searchPageV1' })]
  }

  return [false, null]
}

export const searchFetchData = async(route, $pinia, $headers, isServer, isOnlyOrigemOrDestino = false, $config, $cookies) => {
  const isBuscaComData = route.query.ida || route.query.volta

  const initData = {
    origemSlug: route.params.origem,
    destinoSlug: route.params.destino,
    departureDate: route.query.ida,
    returnDate: route.query.volta || '',
    idGrupoDestaque: route.query.idGrupoDestaque
  }

  const searchStore = useSearchStore($pinia)
  searchStore.init(initData)

  // Nas buscas com data, nao precisamos carregar algumas coisas para SEO pois não indexamos essas páginas.
  try {
    if (!isBuscaComData || isOnlyOrigemOrDestino) {
      await loadSuggestions(route, $pinia, true)
    }

    if (isOnlyOrigemOrDestino) return

    try {
      // Incluimos os headers para pegar o usuário logado no back
      let headers
      if (isServer) {
        headers = { cookie: $headers?.cookie }
      }

      await search(route, $pinia, headers, $config, $cookies)
    } catch (error) {
      // Se o erro for 400 ou 404, quer dizer que o slug da origem ou do destino estão errados
      // logo apenas olhamos o erro se não for um desses casos
      if (error.response?.status !== 400 && error.response?.status !== 404) {
        throw error
      }
    }
  } catch (error) {
    const toastStore = useToastStore($pinia)
    toastStore.open({ message: error.message, type: 'error' })
  }
}
