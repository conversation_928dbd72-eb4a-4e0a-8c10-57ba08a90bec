<template>
  <div class="promo">
    <div class="p-image">
      <img
        :src="promoImage"
        alt="Promoção"
      >
    </div>

    <section class="p-main">
      <ada-container class="pm-section">
        <div class="pfs-card">
          <promo-secao-card
            :promocao-ativa="promocaoAtiva"
            class="my-1"
            :promo="promo"
            @ativou-promo="handlePromoAtivada"
          >
            <template #extra>
              <promo-alert
                v-if="promocaoAtiva || alertPromo?.type === 'error'"
                class="mt-3"
                :alert-promo="alertPromo"
                :logged-in="loggedIn"
                :contagem-regressiva="contagemRegressiva"
                :tempo-restante="tempoRestanteHumanized"
                :promo="promo"
              />
              <progress-bar
                v-if="maxUsos && usos"
                :total="maxUsos"
                :current-value="usos"
                :message="`${usos} pessoas já usaram esse cupom!`"
              />
            </template>
          </promo-secao-card>

          <ada-card
            v-if="showBaixaApp"
            class="pfsc-baixa-app"
            rounded
          >
            <h3 class="title-xs">
              Baixe o app e ative o seu cupom
            </h3>
            <div>
              <app-link-button
                content="promo"
                data-testid="google-play"
                platform="google"
                :width="136"
                :height="40"
                class="mr-2"
              />
              <app-link-button
                content="promo"
                data-testid="apple-store"
                platform="apple"
                :width="136"
                :height="40"
                class="mr-2"
              />
            </div>
          </ada-card>
        </div>
        <promo-regras-destaques
          v-if="!erroCupomInexistente"
          class="mb-3 mt-2"
          :promo="promo"
        />
      </ada-container>
    </section>
    <visto-recentemente
      v-if="buscasRecentesCurrent?.length > 0 && (!hasPromo || promoAlert?.type === 'error')"
      class="mb-4"
      :buscas-recentes="buscasRecentesCurrent"
    />

    <descubra-destinos
      v-show="!$andromeda.breakpoint.smAndDown || !promocaoAtiva"
      name-page="promoOrigin"
    />
  </div>
</template>

<script>
import { defineNuxtComponent } from '#app'
import { mapState, mapActions } from 'pinia'
import api from '~api'
import { saveLead, salvaPromoLeadAnon } from '~api/lead.js'
import { getPromocao } from '~api/promo.js'
import sentryhelper from '~sentryhelper'
import promoBackgroundDefault from '~/assets/images/onibus/onibus-rosa-na-ponte.png'
import cookiehelper from '~/helpers/cookiehelper.js'
import EventBus from '~/helpers/eventbus.js'
import { timer, dateFormat, DATE_FORMATS } from '~/helpers/formatters.js'
import metahelper from '~/helpers/metahelper.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import { useSessionStore } from '~/stores/session.js'
import descubraDestinos from '~/components/shared/descubra-destinos/descubra-destinos.vue'
import progressBar from '~/components/shared/progress-bar.vue'
import AppLinkButton from '~/components/app-link-button/app-link-button.vue'
import vistoRecentemente from '~/components/home/<USER>/visto-recentemente.vue'
import promoAlert from '~/components/promo/promo-alert/promo-alert.vue'
import promoRegrasDestaques from '~/components/promo/promo-regras-destaque/promo-regras-destaques.vue'
import promoSecaoCard from '~/components/promo/promo-secao-card/promo-secao-card.vue'
export default defineNuxtComponent({
  name: 'Promo',
  components: {
    AppLinkButton,
    promoAlert,
    vistoRecentemente,
    promoRegrasDestaques,
    promoSecaoCard,
    descubraDestinos,
    progressBar
  },
  async asyncData(ctx) {
    let promoCode = ctx.params.code
    const deviceToken = ctx.$cookies.get('pushtoken') || ctx.$cookies.get('apnstoken')
    const source = ctx.params.source

    if (!promoCode) {
      return { promo: {}, source }
    }

    if (promoCode && promoCode.includes('-')) {
      promoCode = promoCode.split('-').join('').toUpperCase()
    }

    try {
      const promo = await getPromocao(promoCode, true, deviceToken) || {}

      if (promo.error) {
        return {
          promo,
          alertPromo: {
            msg: promo.error,
            type: 'error'
          },
          source
        }
      }

      salvaPromoLeadAnon(promoCode)

      return {
        promo,
        source
      }
    } catch (e) {
      sentryhelper.captureException(e)
      return {
        promo: {},
        source
      }
    }
  },
  data() {
    return {
      initializing: true,
      promoBackgroundImageStyle: null,
      promocaoAtiva: false,
      alertPromo: null,
      leadCapturado: false,
      tempoRestante: null,
      contador: null,
      maxUsos: null,
      usos: null,
      buscasRecentesComRecs: []
    }
  },
  async mounted() {
    if (this.alertPromo?.type === 'error') {
      EventBus.$emit('aplica-promo-erro', { reason: this.alertPromo?.msg })
    }
    if (!this.hasPromo) {
      this.initBuscasRecentes()
      this.buscasRecentesComRecs = await api.passenger.getVistoRecentementeRecommendations(this.buscasRecentes)
    }

    this.leadCapturado = this.haslead

    if (this.leadCapturado) {
      if (this.loggedIn && this.promo) {
        this.salvaCupomNoLeadDoUser(this.user)
        EventBus.$emit('adicionou-cupom', this.promo)
      }

      this.initPromo(this.promo)
    }
  },
  destroyed() {
    if (this.contador) {
      clearInterval(this.contador)
    }
  },
  watch: {
    fromCity() {
      this.makeAlertPromo({ msg: this.getOrigemDestinoWarning(), type: 'warning' })
    },
    toCity() {
      this.makeAlertPromo({ msg: this.getOrigemDestinoWarning(), type: 'warning' })
    }
  },
  computed: {
    ...mapState(useSessionStore, ['loggedIn', 'user', 'neverTraveled']),
    ...mapState(useSearchboxStore, ['toCity', 'fromCity']),
    ...mapState(useBuscasRecentesStore, { buscasRecentes: 'buscas' }),
    contagemRegressiva() {
      return this.hasPromo && this.promo.time_to_expire > 0 && this.promo.is_valid
    },
    hasPromo() {
      return !!Object.keys(this.promo).length
    },
    hasCustomPromoImage() {
      return this.promo?.background_image && this.promo.background_image !== '/None'
    },
    promoImage() {
      return this.hasCustomPromoImage ? this.promo.background_image : promoBackgroundDefault
    },
    tempoRestanteHumanized() {
      return timer(this.tempoRestante)
    },
    dueDateFormatted() {
      return dateFormat(DATE_FORMATS.datetime, this.promo.due_date)
    },
    showBaixaApp() {
      if (!this.alertPromo) return false
      return this.alertPromo.msg?.includes('exclusivo para uso no app')
    },
    haslead() {
      return this.loggedIn || cookiehelper.getItem('lead_capturado') === 'sim' || ['crm', 'dito'].includes(this.$route.query.utm_source)
    },
    erroCupomInexistente() {
      if (!this.alertPromo) return false
      return this.alertPromo.msg?.includes('Nenhum cupom encontrado')
    },
    buscasRecentesCurrent() {
      return this.buscasRecentesComRecs?.length ? this.buscasRecentesComRecs : this.buscasRecentes
    }
  },
  methods: {
    ...mapActions(useBuscasRecentesStore, ['initBuscasRecentes']),
    makeAlertPromo(data) {
      if (!data) return null

      const { msg, type } = data
      let wrapperPayload = null
      if (this.hasPromo && !this.promo.is_valid && this.promo.due_date) {
        wrapperPayload = {
          msg: `Essa promoção encerrou em ${this.dueDateFormatted}`,
          type: 'error'
        }
      }

      const payload = wrapperPayload ?? msg ? { msg, type } : null

      if (type === 'error') {
        EventBus.$emit('aplica-promo-erro', { reason: msg })
      }

      this.alertPromo = payload
    },

    iniciaContagem() {
      if (!this.contagemRegressiva) return
      this.contador = setInterval(() => {
        if (this.tempoRestante <= 0) {
          clearInterval(this.contador)
          this.$set(this.promo, 'is_valid', false)
          return
        }

        this.tempoRestante--
      }, 1000)
    },
    initPromo(promo) {
      if (promo?.code) {
        this.promo = promo
        this.initializing = true

        if (this.promo.background_image && this.promo.background_image !== '/None') {
          this.promoBackgroundImageStyle = `background-image: url(${this.promo.background_image}); background-position: center;`
        }
        this.makeAlertPromo(this.getError())

        if (!this.alertPromo) {
          if (promo.is_nunca_viajou) {
            this.alertPromo = this.warningUserAlreadyHadFirstTravel()
          }
          cookiehelper.setItem('promo', promo.code)

          if (promo.qtde_max_uso && promo.count_usages) {
            this.maxUsos = promo.qtde_max_uso || 0
            this.usos = promo.count_usages || 1
          }

          EventBus.$emit('acessou-promocao', { code: this.promo.code, source: this.source })

          const promoCode = cookiehelper.getItem('promo')

          if (promoCode.toLowerCase() === this.promo.code.toLowerCase()) {
            this.leadCapturado = true
          }

          this.tempoRestante = this.promo.time_to_expire
        }

        this.initializing = false
        this.iniciaContagem()
      }
    },
    salvaCupomNoLeadDoUser(user) {
      const lead = {
        email: user.email,
        url: this.$route.fullPath,
        attributes: [
          { key: 'tag', value: 'promo' },
          { key: 'cupom', value: this.promo.code }
        ],
        apns_pushtoken: this.$cookies.get('apnstoken'),
        pushtoken: this.$cookies.get('pushtoken')
      }
      saveLead(lead)
    },
    getError() {
      if (this.promo.is_valid) return null
      const msg = this.promo.invalid_msg || this.promo.error || ''
      const type = msg.includes('encerrou') ? 'warning' : 'error'
      return { msg, type }
    },
    warningUserAlreadyHadFirstTravel() {
      let msg = null

      if (this.loggedIn) {
        const userHasTraveled = !this.neverTraveled
        if (userHasTraveled) {
          msg = 'Atenção: pelo menos um passageiro da sua conta já viajou antes. O cupom funcionará somente para passageiros que nunca viajaram'
        }
        return msg ? { msg, type: 'warning' } : null
      }
    },
    getOrigemDestinoWarning() {
      const restricaoViagemCopy = 'Cupom inválido para viagens '
      const restricaoEstadual = this.getRestricaoEstadoWarning()
      if (restricaoEstadual) return restricaoViagemCopy + restricaoEstadual

      const restricaoCidades = this.getRestricaoCidadesWarning()
      if (restricaoCidades) return restricaoViagemCopy + restricaoCidades
      if (!this.validTrechos()) return restricaoViagemCopy + `entre ${this.fromCity.label} e ${this.toCity.label}`
      return ''
    },
    getRestricaoEstadoWarning() {
      let restricaoEstadoOrigem = ''
      let restricaoEstadoDestino = ''
      let conectivo = ''
      if (!this.estadoOrigemPermitido()) {
        restricaoEstadoOrigem = `com origem em ${this.fromCity.state}`
      }
      if (!this.estadoDestinoPermitido()) {
        restricaoEstadoDestino = `com destino a ${this.toCity.state}`
        conectivo = this.estadoOrigemPermitido() ? '' : ' e '
      }
      return restricaoEstadoOrigem + conectivo + restricaoEstadoDestino
    },
    getRestricaoCidadesWarning() {
      let restricaoCidadeOrigem = ''
      let restricaoCidadeDestino = ''
      let conectivo = ''
      if (!this.cidadeOrigemPermitida()) {
        restricaoCidadeOrigem = `com origem em ${this.fromCity.label}`
      }
      if (!this.cidadeDestinoPermitida()) {
        restricaoCidadeDestino = `com destino a ${this.toCity.label}`
        conectivo = this.cidadeOrigemPermitida() ? '' : ' e '
      }
      return restricaoCidadeOrigem + conectivo + restricaoCidadeDestino
    },
    cidadeOrigemPermitida() {
      if (!this.fromCity || !this.promo?.cidades_origem?.length) return true
      return this.promo.cidades_origem.some(cidade => String(cidade.id) === this.fromCity.id)
    },
    cidadeDestinoPermitida() {
      if (!this.toCity || !this.promo?.cidades_destino?.length) return true
      return this.promo.cidades_destino.some(cidade => String(cidade.id) === this.toCity.id)
    },
    estadoOrigemPermitido() {
      if (!this.fromCity || !this.promo?.estados_origem?.length) return true
      return this.promo.estados_origem.some(uf => {
        return uf === this.fromCity.state || uf === this.fromCity.uf
      })
    },
    estadoDestinoPermitido() {
      if (!this.toCity || !this.promo?.estados_destino?.length) return true
      return this.promo.estados_destino.some(uf => {
        return uf === this.toCity.state || uf === this.toCity.uf
      })
    },
    validTrechos() {
      if (!this.fromCity || !this.toCity || !this.promo?.trechos?.length) {
        return true
      }
      return this.promo.trechos.some(trecho => {
        const temOrigemIgual = this.fromCity.label === trecho.nome_origem
        const temDestinoIgual = this.toCity.label === trecho.nome_destino
        if (trecho.ida_e_volta) {
          return (temOrigemIgual && temDestinoIgual) || (this.fromCity.label === trecho.nome_destino && this.toCity.label === trecho.nome_origem)
        }
        return temOrigemIgual && temDestinoIgual
      })
    },
    handlePromoAtivada(promo) {
      if (promo?.error) {
        this.makeAlertPromo({
          msg: promo.error,
          type: 'error'
        })
        return
      }
      this.initPromo(promo)
      this.leadCapturado = true
      this.promocaoAtiva = true
    }
  },
  head() {
    const title = 'Promoções'

    return {
      ...metahelper.generateMetaTags({
        title,
        url: 'https://www.buser.com.br/promo'
      })
    }
  }
})
</script>

<style lang="scss" scoped>

.promo {
  .p-image {
    width: 100%;
    position: relative;
    height: 25vh;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &::before {
      content: '';
      width: 100%;
      height: 20%;
      position: absolute;
      bottom: 0;
      background: linear-gradient(to top, $color-white 15%, rgba($color-white, 0) 100%);
    }

    @media (min-width: $screen-tablet-min) {
      height: 65vh;

      &::before {
        background: none;
      }
    }
  }

  .p-main {
    margin-bottom: $spacing-3;

    .pm-section {
      margin-top: (-$spacing-5);

      @media (min-width: $screen-tablet-min) {
        margin-top: $spacing-5;
        position: static;
      }

      .pfs-card {
        margin-bottom: $spacing-3;

        @media (min-width: $screen-tablet-min) {
          position: absolute;
          top: 10vh;
        }

        .pfsc-baixa-app {
          padding: $spacing-2;
          flex-direction: column;
          display: flex;
          align-items: center;
          gap: $spacing-1;
        }
      }
    }
  }
}
</style>
