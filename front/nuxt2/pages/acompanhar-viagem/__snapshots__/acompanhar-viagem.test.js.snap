// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Acompanhar viagem page should render correctly 1`] = `
"<div class="acompanhar-viagem">
  <div class="ada-container map-wrapper c-full-width-mobile">
    <div aria-label="Mapa do local de embarque" class="mw-map no-print" style="top: 60px;">
      <!---->
    </div>
    <div class="travel-details pb-4 av-details">
      <!---->
      <div class="">
        <div class="ada-card mb-3 mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <div class="travel-historic-current">
            <div class="mt-2 mb-2 px-2">
              <fa-stub icon="[object Object]" size="lg" class="thc-icon"></fa-stub>
            </div>
            <div class="d-flex fd-column"><strong>

    </strong></div>
          </div>
        </div>
        <!---->
        <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <div class="resumo-viagem">
            <div class="rv-wrapper">
              <div><span class="rvw-label">Partida</span>
                <p class="text-sm"><strong class="text-md">Florianópolis</strong>
                  / SC
                </p>
                <div class="rvw-horario-partida"><span class="text-sm">
          10/out, 12:30
        </span>
                  <!---->
                </div>
              </div>
              <div class="rvw-desembarque"><span class="rvw-label">Chegada</span>
                <p class="text-sm"><strong class="text-md">Porto Alegre</strong>
                  / RS
                </p>
                <p class="text-sm">
                  11/out, 12:00
                </p>
              </div>
            </div>
            <!---->
            <div class="rv-footer">
              <p class="caption">
                Duração: 6h
              </p> <span class="ada-badge bg-primary is-rounded">
          Partida em 30 minutos
        </span>
            </div>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <div class="travel-alerts mb-2">
          <!---->
        </div>
        <div class="ada-card travel-info o-hidden mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <div class="ti-company-container">
            <div class="ada-image ticc-foto-onibus mr-one-half ai-rounded" style="width: 76px; height: 76px; --object-fit: cover;">
              <!----><img width="76" height="76" alt="ônibus Cia. de Fretamento com nome muito grande" src="https://assets.volvo.com/is/image/VolvoInformationTechnologyAB/1860x1050-onibus-rodoviario-imagem-release?wid=720"> <button aria-label="Clique para ampliar a imagem" class="tifo-button">
                <fa-stub icon="[object Object]"></fa-stub>
              </button></div>
            <div>
              <p class="caption text-grey">
                Fretamento Buser
              </p>
              <p class="title-xs">
                Cia. de Fretamento com nome muito grande
              </p>
              <p class="mt-half">
                Ônibus <span>- 2 andares </span></p>
            </div>
          </div>
          <div class="ti-chips">
            <div class="tic-chip">
              <p class="caption">
                Placa
              </p>
              <p title="Copiar placa do ônibus" class="ticc-text color-primary">
                OIE1234
                <fa-stub icon="[object Object]" class="ml-1 color-grey-dark"></fa-stub>
              </p>
            </div>
            <div class="tic-chip">
              <p class="caption">
                Reserva
              </p>
              <p title="Copiar código da reserva" class="ticc-text">
                Cód. asidha
                <fa-stub icon="[object Object]" class="ml-1"></fa-stub>
              </p>
            </div>
          </div>
          <ul class="ada-list">
            <!----> <span class="ada-divider mx-2 w-auto bg-grey-light" style="height: 1px;"></span>
            <li class="ada-list-item">
              <router-link-stub to="[object Object]" class="ada-button li-wrapper is-link is-clickable">
                <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Bagagens aceitas </span>
                <!----></span></span>
            </span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></router-link-stub>
            </li>
          </ul> <span><!----></span>
        </div>
        <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <h3 class="title-sm pb-2">
            Bagagens da sua reserva
          </h3>
          <!---->
          <router-link-stub to="[object Object]" class="ada-button is-link" target="_blank">
            <!---->
            Ver dimensões das bagagens
          </router-link-stub>
        </div>
        <!---->
        <!---->
        <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <div>
            <h3 class="fw-600 pb-1 title-sm">
              Itinerário
            </h3>
            <div class="text-sm fw-500 pb-2">
              <!---->
              <!---->
            </div>
            <ul class="travel-itinerary">
              <li class="ti-local is-embarque">
                <!---->
                <div class="ti-section ti-road">
                  <div class="tir-icon">
                    <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
                  </div>
                </div>
                <div class="ti-section ti-content">
                  <p class="caption color-grey mb-half">
                    Embarque
                  </p>
                  <h3 class="title-xs mb-half">
                    Auto Posto Joiris
                  </h3>
                  <div class="d-flex fd-row ai-center"><a href="https://maps.google.com/0" target="_blank" class="text-sm">
                      Av. 43, nº 658, Alvorada. Auto Posto Joiris, Florianópolis - SC
                    </a> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small" data-testid="button">
                      <!---->
                      <fa-stub icon="[object Object]" size="lg"></fa-stub>
                    </button></div>
                  <div class="tis-description text-sm mt-1">
                    <p><strong>Instruções:</strong>
                      No terminal turístico JK, em frente à escolinha do Prof. Raimundo tem uma entrada onde os ônibus ficam estacionados. É lá que você deve esperar.
                    </p>
                    <!---->
                  </div>
                  <!---->
                </div>
              </li>
              <li class="ti-local">
                <!---->
                <div class="ti-section ti-road">
                  <div class="tir-icon">
                    <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
                  </div>
                </div>
                <div class="ti-section ti-content"><button type="button" class="ada-button w-auto is-button b-color-white b-rounded b-small">
                    <!---->
                    2 paradas no percurso
                    <fa-stub icon="[object Object]" class="ml-1"></fa-stub></button>
                  <!---->
                </div>
              </li>
              <li class="ti-local" style="display: none;">
                <!---->
                <div class="ti-section ti-road">
                  <div class="tir-icon">
                    <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
                  </div>
                </div>
                <div class="ti-section ti-content">
                  <h3 class="text-sm fw-400">
                    Pouso Alegre - MG
                  </h3>
                </div>
              </li>
              <li class="ti-local" style="display: none;">
                <!---->
                <div class="ti-section ti-road">
                  <div class="tir-icon">
                    <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
                  </div>
                </div>
                <div class="ti-section ti-content">
                  <h3 class="text-sm fw-400">
                    Campinas - SP
                  </h3>
                </div>
              </li>
              <li class="ti-local is-desembarque">
                <!---->
                <div class="ti-section ti-road">
                  <div class="tir-icon">
                    <fa-stub icon="[object Object]" class="tiri-fa"></fa-stub>
                  </div>
                </div>
                <div class="ti-section ti-content">
                  <p class="caption color-grey mb-half">
                    Desembarque
                  </p>
                  <h3 class="title-xs">
                    Shopis Centis
                  </h3>
                  <div class="d-flex fd-row jc-start ai-center">
                    <div><a href="https://maps.google.com/3" target="_blank" class="text-sm">
                        Shopping Center, Porto Alegre - RS
                      </a></div> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small">
                      <!---->
                      <fa-stub icon="[object Object]" size="lg"></fa-stub>
                    </button>
                  </div>
                  <!---->
                  <!---->
                </div>
              </li>
            </ul>
            <!---->
          </div>
        </div>
        <!---->
        <div class="ada-card boarding-documents p-2 mb-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <h3 class="title-xs">
            Documentos para viajar
          </h3>
          <div class="documentos-viagem-nacional">
            <ul class="mt-2">
              <li class="dvn-item">
                <div class="d-flex">
                  <fa-sprite-stub sprite="search" icon="far-fa-id-card" class="dvni-icon color-primary mr-1"></fa-sprite-stub>
                  <p class="text-sm">
                    Para embarcar você deve levar
                    <strong>
            Carteira de identidade, RG, CNH, Passaporte originais com foto
          </strong>
                    ou qualquer outro documento que esteja
                    <a href="/ajuda/informacoes-sobre-minha-viagem/documentos-para-embarque">aqui</a>.
                  </p>
                </div>
              </li>
              <li class="dvn-item">
                <div class="d-flex">
                  <fa-sprite-stub sprite="search" icon="fas-fa-print-slash" class="dvni-icon color-primary mr-1"></fa-sprite-stub>
                  <p class="text-sm">
                    Não é necessário imprimir a reserva, basta levar o documento.
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <!---->
        <!---->
        <h2 class="title-sm mb-1 pt-1">
          Passageiros
        </h2>
        <!---->
        <div data-sentry-mask="" class="mb-2">
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Daniel Silva Campos Junior
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <p class="color-primary caption mt-one-half">
              Deve levar cadeirinha bebê conforto
            </p>
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  Telefone
                </p>
                <p class="text-sm">
                  (12) 99911 8445
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  991.119.999-99
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  123243429
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Companheiro do Fábio 1
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <p class="color-primary caption mt-one-half">
              Deve levar cadeirinha bebê conforto
            </p>
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  Telefone
                </p>
                <p class="text-sm">
                  (12) 99118 4452
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  827.829.123-23
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  2.888.999-9
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    C2
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  987.312.631-97
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  7962439
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Companheiro do Fábio com nome muito muito grande 2
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  Telefone
                </p>
                <p class="text-sm">
                  (12) 77463 5543
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  213.321.315-98
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  2011031388761
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft bg-grey-light">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Étila Amaral
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  777.777.666-54
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  324339999
                </p>
              </div>
            </div>
            <!---->
            <!----> <span class="ada-badge mt-one-half bg-grey" small="">
    Removido
  </span>
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Tony Lâmpada
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  333.444.555-21
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  2014874562429
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    João dos Santos sem CPF
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <!---->
              <!---->
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    Maria da Silva sem CPF
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <!---->
              <!---->
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
            <!---->
            <div class="d-flex jc-between">
              <div>
                <div class="pr-1">
                  <p class="title-xs fw-wrap w-100">
                    João Noshow
                  </p>
                </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
              </div>
              <!---->
              <!---->
            </div>
            <!---->
            <div class="cp-infos">
              <div class="cpi-item">
                <p class="caption color-grey">
                  Poltrona
                </p>
                <div class="poltrona">
                  <!---->
                  <div class="p-assento">
                    <!---->
                    <div class="d-flex fd-column text-sm">
                      <p class="">
                        Executivo
                        <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                      </p>
                    </div>
                  </div>
                  <!---->
                  <!---->
                </div>
              </div>
              <!---->
              <div class="cpi-item">
                <p class="caption color-grey">
                  CPF
                </p>
                <p class="text-sm">
                  333.444.555-21
                </p>
              </div>
              <div class="cpi-item">
                <p class="caption color-grey">
                  RG:
                </p>
                <p class="text-sm">
                  2014874562429
                </p>
              </div>
            </div>
            <!---->
            <!---->
            <!---->
          </div>
          <div role="alert" class="ada-alert mt-2 a-warning">
            <fa-stub icon="[object Object]" class="a-icon"></fa-stub>
            <div class="a-content">
              <!---->
              <p class="text-sm">
                É obrigatório o uso da cadeirinha Bebê Conforto para bebês até 18
                meses.
              </p>
            </div>
            <!---->
          </div>
          <div role="alert" class="ada-alert mt-2 a-warning">
            <fa-stub icon="[object Object]" class="a-icon"></fa-stub>
            <div class="a-content">
              <!---->
              <p class="text-sm">
                Crianças e adolescentes menores de 12 anos precisam do
                <strong>RG e Certidão de nascimento</strong>
                originais para embarcar.
              </p>
              <p class="text-sm">
                Menores de 16 anos que não estiverem acompanhados dos pais devem levar documento de autorização judicial assinado.
              </p>
              <router-link-stub to="[object Object]" class="p-documents">
                Veja a regra
              </router-link-stub>
            </div>
            <!---->
          </div>
          <!---->
        </div>
        <!---->
        <div class="card-hoteis-destino mb-1"><img src="~/assets/images/parceria/decolar/QUARTO_HOTEL.webp" alt="Quarto Hotel" width="114" height="114">
          <div class="chd-content">
            <h3 class="title-xs">
              Hotéis em Porto Alegre
            </h3>
            <p class="text-sm">
              Veja os melhores preços em hospedagem e atividades para curtir em sua viagem.
            </p> <a href="https://buser.viagens.decolar.com/?utm_source=site&amp;utm_medium=detalhes-viagem&amp;utm_campaign=decolarjunho" class="ada-button chdc-btn is-button b-color-primary b-small" target="_blank">
              <!---->
              Ver preços <fa-sprite-stub sprite="motoristas" icon="far-fa-arrow-right" class="ml-1"></fa-sprite-stub></a>
          </div>
        </div>
        <h2 class="title-sm mb-1 pt-1">
          Opções de segurança
        </h2>
        <div class="ada-card mb-2 o-hidden ac-outlined ac-rounded ac-soft">
          <!---->
          <div>
            <ul class="ada-list">
              <!---->
              <!---->
              <li class="ada-list-item">
                <router-link-stub to="[object Object]" class="ada-button li-wrapper is-link is-clickable">
                  <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Adicionar contato de emergência </span>
                  <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></router-link-stub>
              </li>
              <li class="ada-list-item"><button type="button" class="ada-button li-wrapper is-link is-clickable">
                  <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Compartilhar minha viagem </span>
                  <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></button></li>
            </ul> <span><!----></span> <span class="share-travel-popup"><!----></span>
          </div>
        </div>
        <!---->
        <h3 class="title-xs mb-1">
          Não encontrou o que queria?
        </h3>
        <div class="ada-card p-2 ac-outlined ac-rounded ac-soft">
          <!---->
          <p>
            <router-link-stub to="[object Object]">
              Faça o login e acesse sua conta
            </router-link-stub>
            para mais detalhes.
          </p>
        </div>
      </div>
      <!---->
      <!---->
    </div> <span><!----></span>
  </div>
</div>"
`;

exports[`Acompanhar viagem page should show travelForm and "Nenhuma viagem encontrada" alert when travel is null 1`] = `
"<div class="acompanhar-viagem">
  <div class="ada-card acompanhar-viagem-form ac-outlined ac-rounded ac-elevation-close ac-soft">
    <!---->
    <div role="alert" class="ada-alert mb-2 a-error">
      <!---->
      <div class="a-content">
        <!---->
        <p class="text-md fw-bold mb-1">
          Nenhuma viagem encontrada
        </p>
        <p class="text-md">
          Não foi possível consultar sua viagem. Verifique o código de reserva ou a data da viagem e tente novamente.
        </p>
      </div>
      <!---->
    </div>
    <form class="ada-form">
      <div class="ada-field ada-input reservation-code mb-3"><label for="ada-input-150" class="f-label"> Código da reserva </label>
        <div data-testid="container" class="f-container fc-outlined" style="height: 44px;">
          <!----><input id="ada-input-150" placeholder="THYU43" maxlength="6" class="i-field">
          <div class="ada-float ada-tooltip">
            <div class="f-reference"><button type="button" class="ada-button is-button b-color-primary b-transparent b-icon b-rounded" aria-label="O que é o código da reserva?">
                <!---->
                <fa-stub icon="[object Object]"></fa-stub>
              </button></div>
            <!---->
          </div>
          <!---->
        </div>
        <!---->
      </div>
      <div class="ada-datepicker mb-3">
        <div tabindex="0"></div>
        <div class="ada-field ada-input"><label for="ada-input-160" class="f-label"> Data da viagem </label>
          <div data-testid="container" class="f-container fc-outlined" style="height: 44px;">
            <fa-stub icon="[object Object]" class="fc-start-icon"></fa-stub><input id="ada-input-160" placeholder="Selecione a data" readonly="readonly" class="i-field">
            <!---->
            <!---->
            <!---->
          </div>
          <!---->
        </div><span><div class="ada-float d-block"><div class="f-reference"></div><!----></div></span>
      </div> <button type="submit" class="ada-button mb-2 is-button b-color-primary b-block">
        <!---->
        Buscar reserva
      </button>
      <!---->
    </form>
    <div class="avf-divider"><span class="ada-divider bg-grey-lightest" style="height: 1px;"></span>
      <p class="avfd-text">
        Não lembra do código?
      </p>
    </div>
    <router-link-stub to="[object Object]" class="ada-button is-button b-color-primary b-transparent b-block">
      <!---->
      Acessar minha conta
    </router-link-stub>
  </div>
</div>"
`;
