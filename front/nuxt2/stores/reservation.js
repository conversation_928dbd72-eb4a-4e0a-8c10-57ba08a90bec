import { defineStore } from 'pinia'
import { unblockPoltronas, getBusLayout, getTaxaCancelamentoGruposV2, getAddonsExtratoV2 } from '~api/checkout.js'
import { getCupomLead, getGroupsCupons } from '~api/promo.js'
import { getGroupsV2 } from '~api/search.js'
import sentryhelper from '~sentryhelper'
import Eventbus from '~/helpers/eventbus.js'
import { splitGroupIds } from '~/helpers/grouphelper.js'
import passengerHelper from '~/helpers/passengerhelper.js'
import vuehelper from '~/helpers/vue.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSearchStore } from '~/stores/search.js'
import { useStatementStore } from '~/stores/statement.js'

export const MAX_PASSENGERS_PER_RESERVATION = 5
export const MAX_PASSENGERS_FOR_RENVEDEDOR = 15

export const useReservationStore = defineStore('reservation', {
  state: () => ({
    step: 1,
    promoCode: null,
    gruposIda: [],
    gruposVolta: [],
    layoutOnibusIda: undefined,
    layoutOnibusVolta: undefined,
    reservationData: {
      user: {
        email: null,
        phone: null,
        name: null
      },
      possiblePassengers: [],
      passengers: [],
      poltronasMapIda: {},
      poltronasMapVolta: {},
      countCadeirinhas: 0,
      creditoCarbono: false,
      valorCarbono: 0,
      seguroExtra: false,
      valorSeguroExtra: 0,
      bagagemAdicional: 0,
      valorBagagemAdicional: 0,
      bagagemAdicionalLiberada: false,
      isSeguroExtraPermitido: false,
      cadeirinhasAcceptance: false,
      acceptance: false,
      promoCode: null,
      payment: null,
      binCartaoSelecionado: '',
      paymentLinkCode: null,
      resellerUserId: null,
      dadosBeneficio: null,
      categoriaEspecial: null,
      rakuten_time_entered: null
    },
    validationErrors: {
      passengers: false,
      acceptance: false,
      payment: false,
      creditoCarbono: false,
      seguroExtra: false,
      promoCode: false,
      user: false,
      marcacaoPoltronas: false,
      protocoloRevendedor: false
    },
    extrato: [],
    pagamento_total: null,
    idaPreview: null,
    voltaPreview: null,
    cuponsDisponiveis: [],
    cpfDaPromo: null,
    taxaCancelamento: {},
    isCheckoutOffer: false,
    isConexao: false
  }),
  getters: {
    selectedPassengers(state) {
      return state.reservationData.possiblePassengers.filter(p => p.selected)
    },
    selectedPoltronasMapIda(state) {
      return state.reservationData.poltronasMapIda
    },
    selectedPoltronasMapVolta(state) {
      return state.reservationData.poltronasMapVolta
    },
    hasVolta(state) {
      return state.gruposVolta.length > 0
    },
    countCriancas() {
      return this.selectedPassengers.filter(p => p.type === 'CRIANCA').length
    },
    maxPassengers(state) {
      const isRevendedor = !!state.reservationData.resellerUserId
      return isRevendedor ? MAX_PASSENGERS_FOR_RENVEDEDOR : MAX_PASSENGERS_PER_RESERVATION
    },
    fullReason(state) {
      const statementStore = useStatementStore()
      const { gruposIda, gruposVolta } = state
      const countPassengers = this.selectedPassengers.length
      if (countPassengers >= this.maxPassengers) {
        return `São permitidos no máximo ${this.maxPassengers} passageiros por reserva.`
      }

      const idaIsFull = Math.min(...gruposIda.map((g) => g.vagas)) - countPassengers <= 0
      const voltaIsFull = gruposVolta && Math.min(...gruposVolta.map((g) => g.vagas)) - countPassengers <= 0
      if (idaIsFull || voltaIsFull) {
        return 'Não é possível adicionar mais passageiros pois o ônibus está cheio.'
      }

      const statement = statementStore.completeStatement
      if (statement?.alerta_buckets) {
        const message = statement.alerta_buckets
        delete statement.alerta_buckets // pra não bloquear a seleção, caso o usuário desselecione algo
        return message
      }

      return false
    },
    isUserInfoFilled(state) {
      const { email, phone, name } = state.reservationData.user
      return !!(email?.trim() && phone?.trim() && name?.trim())
    },
    allGroups(state) {
      return state.gruposIda.concat(state.gruposVolta)
    },
    allGroupsIds(state) {
      return this.allGroups.map((g) => g.id)
    }
  },
  actions: {
    initReservation() {
      this.step = 1
      this.gruposIda = []
      this.gruposVolta = []
      this.layoutOnibusIda = undefined
      this.layoutOnibusVolta = undefined
      this.taxaCancelamento = {}
      this.isCheckoutOffer = false
      this.reservationData.possiblePassengers = []
      this.reservationData.passengers = []
      this.reservationData.poltronasMapIda = {}
      this.reservationData.poltronasMapVolta = {}
      this.reservationData.countCadeirinhas = 0
      this.reservationData.creditoCarbono = false
      this.reservationData.valorCarbono = 0
      this.reservationData.seguroExtra = false
      this.reservationData.valorSeguroExtra = 0
      this.reservationData.bagagemAdicional = 0
      this.reservationData.valorBagagemAdicional = null
      this.reservationData.isSeguroExtraPermitido = false
      this.reservationData.isItemAdicional = false
      this.reservationData.cadeirinhasAcceptance = false
      this.reservationData.addonsPriceConfig = {}
      this.reservationData.acceptance = false
      this.reservationData.promoCode = null
      this.reservationData.payment = null
      this.reservationData.binCartaoSelecionado = ''
      this.reservationData.valoresMarcacaoAssento = {}
      this.reservationData.poltronasSelected = {}
      this.reservationData.user = {
        email: null,
        phone: null,
        name: null
      }
      this.reservationData.categoriaEspecial = null
      this.reservationData.dadosBeneficio = {}
    },
    next() {
      this.step = this.step > 0 ? this.step + 1 : 0
    },
    previous() {
      this.step = this.step > 0 ? this.step - 1 : 0
    },
    async setupPreviewData(idGrupoIda, idGrupoVolta) {
      const { idsIda, idsVolta } = splitGroupIds(idGrupoIda, idGrupoVolta)
      const { gruposIda, gruposVolta } = await getGroupsV2(idsIda, idsVolta)
      this.idaPreview = gruposIda
      if (gruposVolta) this.voltaPreview = gruposVolta
    },
    async setupCuponsDisponiveis(trechoClasseIds) {
      const { cupons } = await getGroupsCupons({ trechoClasseIds })
      this.cuponsDisponiveis = cupons || []
    },
    async setupCuponsDisponiveisRevendedor() {
      const isRevendedorSite = useRevendedorStore().isRevendedorSite
      const buyerId = this.reservationData.user.id
      if (isRevendedorSite && buyerId) {
        const { cupons } = await getCupomLead({ user_revenda: buyerId })
        this.cuponsDisponiveis = cupons || []
      }
    },
    async setupTaxaCancelamentoGrupos(idTravelRemarcada) {
      this.taxaCancelamento = await getTaxaCancelamentoGruposV2(this.allGroupsIds, idTravelRemarcada)
    },
    toggleCreditoCarbono() {
      this.reservationData.creditoCarbono = !this.reservationData.creditoCarbono
    },
    toggleSeguroExtra() {
      this.reservationData.seguroExtra = !this.reservationData.seguroExtra
    },
    addBagagemExtra(quantBagagem) {
      this.reservationData.bagagemAdicional = quantBagagem
    },
    initPassengers(passengers = [], ignoreSelected) {
      if (this.selectedPassengers.length === 0 || ignoreSelected) {
        this.reservationData.possiblePassengers = passengers.map(
          p => passengerHelper.updatePassenger(p, this.gruposIda[0].datetime_ida)
        )
      }
    },
    updateReservationPassengers() {
      const passengers = this.reservationData.possiblePassengers.filter(p => p.selected)
      this.reservationData.passengers = passengers
      this.allGroups.forEach((g) => { g.passengers = passengers })
    },
    setPoltronasMap(potronasMapIda = null, potronasMapVolta = null) {
      if (potronasMapIda) {
        this.reservationData.poltronasMapIda = potronasMapIda
      }
      if (potronasMapVolta) {
        this.reservationData.poltronasMapVolta = potronasMapVolta
      }

      // later we need to support connections

      if (this.gruposIda[0]?.id) {
        this.reservationData.poltronasSelected[this.gruposIda[0].id] = this.selectedPoltronasMapIda
      }
      if (this.gruposVolta[0]?.id) {
        this.reservationData.poltronasSelected[this.gruposVolta[0].id] = this.selectedPoltronasMapVolta
      }
    },
    updatePassengers(passengers = []) {
      this.reservationData.possiblePassengers = passengers.map(p => passengerHelper.updatePassenger(p, this.gruposIda[0].datetime_ida)) || []
    },
    createPassenger(passenger) {
      this.reservationData.possiblePassengers.push(passengerHelper.updatePassenger(passenger, this.gruposIda[0].datetime_ida))
    },
    editPassenger(passenger, idx) {
      vuehelper.set(this.reservationData.possiblePassengers, idx, passengerHelper.updatePassenger(passenger, this.gruposIda[0].datetime_ida))
    },
    removePossiblePassenger(idx) {
      const passenger = this.reservationData.possiblePassengers[idx]

      if (passenger.selected) {
        const selectedPassengerIdx = this.reservationData.passengers.findIndex(p => p.id === passenger.id)
        this.reservationData.passengers.splice(selectedPassengerIdx, 1)
      }

      this.reservationData.possiblePassengers.splice(idx, 1)
    },
    togglePassenger(idx) {
      const p = this.reservationData.possiblePassengers[idx]
      p.selected = !p.selected
      vuehelper.set(this.reservationData.possiblePassengers, idx, p)
    },
    unselectAllPassengers() {
      this.reservationData.possiblePassengers.forEach((p, idx) => {
        p.selected = false
        vuehelper.set(this.reservationData.possiblePassengers, idx, p)
      })
    },
    unselectAllPoltronas() {
      const poltronasIdaNumbers = Object.values(this.selectedPoltronasMapIda)
      this.setPoltronasMap({}, {})
      if (poltronasIdaNumbers.length > 0) {
        // later we need to support connections
        poltronasIdaNumbers.forEach(async(poltrona) => {
          await this.unblockPoltronas(this.gruposIda[0].id, poltrona)
        })
      }

      if (this.gruposVolta[0]) {
        const poltronasVoltaNumbers = Object.values(this.selectedPoltronasMapVolta)

        if (poltronasVoltaNumbers.length > 0) {
          // later we need to support connections
          poltronasVoltaNumbers.forEach(async(poltrona) => {
            await this.unblockPoltronas(this.gruposVolta[0].id, poltrona)
          })
        }
      }
    },
    async unblockPoltronas(grupo, poltrona) {
      try {
        await unblockPoltronas(grupo, poltrona)
      } catch (error) {
        sentryhelper.captureException(error)
      }
    },
    async fetchBusLayoutIda(grupoIda) {
      this.layoutOnibusIda = await this.fetchBusLayout(grupoIda)
      return this.layoutOnibusIda?.length > 0
    },
    async fetchBusLayoutVolta(grupoVolta) {
      this.layoutOnibusVolta = await this.fetchBusLayout(grupoVolta)
      return this.layoutOnibusVolta?.length > 0
    },
    async fetchBusLayout(grupo) {
      if (!(grupo && grupo.has_marcacao_assento)) return []

      try {
        const { layout } = await getBusLayout(grupo.id)
        return layout
      } catch (error) {
        Eventbus.$emit('carregamento-layout-erro', {
          grupo_id: grupo.id,
          company_name: grupo.company_name,
          origem_slug: grupo.origem?.slug,
          destino: grupo.destino?.slug,
          error: error.response?.data
        })
        // 408 - Request Timeout. Rodoviaria demorou demais para responder.
        // 404 - Not Found. Rodoviaria não encontrou o trecho.
        // Silent - Erros mapeados que não precisam ser capturados
        if (error.response?.data?.silent || error.response?.status === 408 || error.response?.status === 404) return
        sentryhelper.captureException(error)
        return []
      }
    },
    setGroupAttributes(gruposIda, gruposVolta = []) {
      this.gruposIda = gruposIda
      this.gruposVolta = gruposVolta
      const searchStore = useSearchStore()
      const badgeMap = searchStore.grupoBadgeMap
      if (!badgeMap) return
      this.allGroups.forEach((g) => { g.badge = badgeMap[g.id] })
    },
    async populateAddons(headers = null) {
      let addonsData = await getAddonsExtratoV2(this.allGroupsIds, headers)
      this.reservationData.valorCarbono = addonsData.valorCarbono
      this.reservationData.valorSeguroExtra = addonsData.valorSeguroExtra
      this.reservationData.isSeguroExtraPermitido = addonsData.isSeguroExtraPermitido
      this.reservationData.valorBagagemAdicional = addonsData.valorBagagemAdicional
      this.reservationData.bagagemAdicionalLiberada = addonsData.bagagemAdicionalLiberada
      this.reservationData.valoresMarcacaoAssento = addonsData.valoresMarcacaoAssento
      this.reservationData.addonsPriceConfig = addonsData.addonsPriceConfig
    }
  }
})
