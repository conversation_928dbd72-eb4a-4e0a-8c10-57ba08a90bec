<template>
  <router-link
    v-if="grupo"
    class="td-none color-inherit"
    :to="{ name: 'checkout', params: checkoutParams }"
    @click.native="sendAmplitudeEvent"
  >
    <ada-card class="p-2" outlined rounded>
      <div class="gih-top mb-2">
        <company-label :grupo="grupo" :wrap="false" />
        <p class="caption" :class="labelColor">
          {{ label }}
        </p>
      </div>

      <itinerario-resumido class="mb-2" :grupo="grupo" :is-conexao="isConexao" />

      <div class="gih-body">
        <div v-if="isConexao" class="gihb-poltrona-conexao">
          <ul>
            <li v-for="(poltrona, index) in poltronasByPerna" :key="index" class="caption">
              <span class="color-grey-dark">{{ index + 1 }}°</span>
              <span class="color-grey">{{ poltrona }}</span>
            </li>
          </ul>
        </div>
        <poltrona
          v-else
          class="gihb-poltrona"
          :tipo-assento="grupo.tipo_assento"
          :tipo-veiculo="grupo.tipo_veiculo"
          :tem-banheiro="grupo.tem_banheiro"
          :has-marcacao-assento="false"
          hide-icon
        />

        <div class="gihbo-oferta">
          <div class="gihbo-preco">
            <preco class="gihbo-valor" :preco-antigo="precoAntigo" :preco-atual="precoAtual" />

            <p v-if="showParcelamento" class="text-sm w-100 ws-nowrap">
              {{ textoParcelamento }}
            </p>
          </div>

          <ada-button class="gihb-selection" color="primary" :small="true">
            <p class="fw-bold">
              Selecionar
            </p>
          </ada-button>
        </div>
      </div>
    </ada-card>
  </router-link>
</template>

<script>
import { mapState } from 'pinia'
import EventBus from '~/helpers/eventbus.js'
import { useSearchStore } from '~/stores/search.js'
import companyLabel from '~/components/search-result/grupos/grupo/company-label.vue'
import itinerarioResumido from '~/components/search-result/grupos/grupo/itinerario-resumido.vue'
import poltrona from '~/components/search-result/grupos/grupo/poltrona.vue'
import preco from '~/components/search-result/grupos/grupo/preco/preco.vue'

export default {
  name: 'CardOfertaSimples',
  components: {
    poltrona,
    itinerarioResumido,
    companyLabel,
    preco
  },
  props: {
    label: {
      type: String,
      required: false,
      default: undefined
    },
    grupo: {
      type: Object,
      required: true
    },
    checkoutParams: {
      type: Object,
      required: true
    },
    tipo: {
      type: String,
      required: true
    },
    trechoPromoDiscount: {
      type: Number,
      default: 0
    },
    source: {
      type: String,
      required: false,
      default: 'home'
    }
  },
  computed: {
    ...mapState(useSearchStore, ['activePromo']),
    isConexao() {
      return this.grupo.id.includes(':')
    },
    labelColor() {
      return (this.label === 'Visto recentemente') ? 'color-green' : 'color-brand'
    },
    precoAntigo() {
      const maxValue = Math.max(this.grupo.max_split_value, this.grupo.ref_split_value)
      if (this.promo?.promotional_value !== undefined && this.promo.promotional_value !== maxValue) {
        return maxValue
      }

      if (maxValue > this.grupo.max_split_value) return maxValue

      return 0
    },
    precoAtual() {
      return (this.promo?.promotional_value !== undefined)
        ? this.promo.promotional_value
        : this.grupo.max_split_value
    },
    showParcelamento() {
      if (!this.grupo?.parcelamento || this.grupo.zera_preco) return false
      return !this.promotionalValueExists
    },
    textoParcelamento() {
      if (!this.showParcelamento) return
      const { valor_por_parcela, quantidade_de_parcelas } = this.grupo.parcelamento
      return `${quantidade_de_parcelas}x ${real(valor_por_parcela)}`
    },
    promo() {
      if (
        this.trechoPromoDiscount ||
        (!this.activePromo?.code || this.activePromo?.code === 'trecho-promo')
      ) {
        // ex: 100 * (1 - 0.15) = 100 * 0.85 = 85
        const promotionalValue = this.grupo.max_split_value * (1 - this.trechoPromoDiscount)
        return { promotional_value: promotionalValue }
      }

      const availableGroups = this.activePromo?.availableGroups

      const promoObj = availableGroups?.[this.grupo.id]

      return promoObj && promoObj.promocao
    },
    promotionalValueExists() {
      return typeof this.promo?.promotional_value === 'number'
    },
    poltronasByPerna() {
      return this.grupo.conexoes?.reduce((poltronas, g) => {
        // Capitaliza o primeiro caráctere de cada palavra.
        const poltrona = g.tipo_assento.replace(/\b\w/g, (c) => c.toUpperCase())
        return [...poltronas, poltrona]
      }, [])
    }
  },
  methods: {
    sendAmplitudeEvent() {
      const amplitudeParams = {
        grupo: this.grupo,
        tipo: this.tipo,
        source: this.source
      }
      EventBus.$emit('click-oferta-simples', amplitudeParams)
    }
  }
}
</script>

<style lang="scss">
.gih-top {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: $spacing-half;

  .company-label {
    margin-right: $spacing-2;
    min-width: initial;
    max-width: 300px;

    .cl-label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .cll-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .caption {
    white-space: nowrap;
  }
}

.c-container {
  a {
    .ada-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
}

.gih-body {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .gihbo-oferta {
    align-self: flex-end;
    display: flex;
    flex-direction: row;
    gap: $spacing-2;
    align-items: center;

    .gihbo-preco {
      .gihbo-valor {
        align-self: flex-end;
        align-items: center;
        line-height: 0
      }
    }

    .gihb-selection {
      flex: 0;
    }
  }
}
</style>
