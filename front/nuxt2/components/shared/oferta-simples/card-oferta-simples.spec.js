import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import userEvent from '@testing-library/user-event'
import { render, screen } from '@testing-library/vue'
import VueRouter from 'vue-router'
import EventBus from '~/helpers/eventbus.js'
import { generateRoutes } from '~/router.js'
import cardOfertaSimples from './card-oferta-simples.vue'

jest.mock('~/router.js')

const router = new VueRouter({
  routes: generateRoutes()
})

describe('CardOfertaSimples', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render correctly', async() => {
    const eventBusEmitSpy = jest.spyOn(EventBus, '$emit')
    const group = {
      id: 'ble',
      tipo_assento: 'leito',
      origem: { name: 'Vassouras', uf: 'SP' },
      destino: { name: 'Tagamandápio', uf: 'SP' },
      datetime_ida: '2023-04-11T19:15:00-03:00',
      max_split_value: 20
    }

    createTesting<PERSON>inia({})

    await render(cardOfertaSimples, {
      props: {
        label: 'Recomendado',
        tipo: 'melhor-tempo',
        grupo: group,
        checkoutParams: { idIda: 'ble' },
        trechoPromoDiscount: 0.2,
        source: 'home'
      },
      router
    })

    expect(screen.getByText(/Recomendado/i)).toBeInTheDocument()
    // 20 - (1 - 0.2) = 16
    expect(screen.getByText(/16/)).toBeInTheDocument()

    const selectBtn = screen.getByRole('link')
    await userEvent.click(selectBtn)

    expect(eventBusEmitSpy).toHaveBeenCalledWith(
      'click-oferta-simples',
      {
        grupo: group,
        tipo: 'melhor-tempo',
        source: 'home'
      }
    )
  })
})
