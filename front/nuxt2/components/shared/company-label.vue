<template>
  <div
    class="company-label"
    :class="labelSize"
  >
    <template v-if="grupo.modelo_venda === 'buser' || (isRevendedorSite && group?.modelo_venda === 'hibrido')">
      <img
        fetchpriority="low"
        src="~/assets/images/buser-icon-pink.svg"
        alt="logo Buser Brasil"
        width="13"
        height="8"
      >

      {{ isPremium ? 'Buser Premium' : 'Fretamento Buser' }}
    </template>

    <template v-else>
      <div class="cl-rodoviaria">
        <span class="d-flex ai-center">
          <fa
            :icon="faTicket"
            class="color-brand mr-half"
          />
          Viagem rodoviária
        </span>
        <span>
          {{ grupo.company_name }}
        </span>
      </div>
    </template>
  </div>
</template>

<script>
import { defineNuxtComponent } from '#app'
import { faTicket } from '@fortawesome/pro-regular-svg-icons'
import { mapState } from 'pinia'
import { groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { useRevendedorStore } from '~/stores/revendedor.js'

export default defineNuxtComponent({
  name: 'CompanyLabel',
  props: {
    grupo: {
      type: Object,
      required: true
    },
    habilitarNovoCheckout: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      faTicket
    }
  },
  computed: {
    ...mapState(useRevendedorStore, ['isRevendedorSite']),
    labelSize() {
      return this.habilitarNovoCheckout ? 'caption' : ''
    },
    isPremium() {
      return groupsHaveBuserPremium([this.grupo])
    }
  }
})
</script>

<style lang="scss" scoped>
.company-label {
  grid-area: company-label;
  color: $color-grey;
  font-size: 12px;

  .cl-rodoviaria {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
  }
}
</style>
