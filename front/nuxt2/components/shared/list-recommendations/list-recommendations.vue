<template>
  <div class="list-recommendations">
    <template v-if="gruposRecomendadosBased?.length > 0">
      <div class="lr-grupos">
        <h3 class="lrg-title">
          Recomendados para você
        </h3>
        <grupo
          v-for="recommendation in gruposRecomendadosBased"
          :key="recommendation.id"
          class="lrg-grupo"
          :grupo="recommendation"
          searchrank-api-recommended
        />
      </div>
    </template>
    <grupo-recomendado
      v-else-if="!!gruposRecomendados || Object.keys(gruposRecomendadosConexao).length > 0"
    />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { defineAsyncComponent } from 'vue'
import { useSearchStore } from '~/stores/search.js'

const grupo = defineAsyncComponent({ loader: () => import('~/components/search-result/grupos/grupo.vue') })

const grupoRecomendado = defineAsyncComponent({ loader: () => import('~/components/search-result/grupo-recomendado/grupo-recomendado.vue') })

const searchStore = useSearchStore()
const { gruposRecomendadosBased, gruposRecomendados, gruposRecomendadosConexao } = storeToRefs(searchStore)

</script>

<style lang="scss" scoped>
.list-recommendations {
  .lr-grupos {
    background-color: $color-blue-light;
    border-radius: $border-radius-md;
    padding: $spacing-2;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    gap: $spacing-1;

    @media (min-width: $screen-tablet-min) {
        gap: $spacing-2;
        border-radius: 0;
    }

    .lrg-title {
        font-size: $font-size-md;
        font-weight: $font-weight-bold;
        color: $color-blue-dark;

        @media (min-width: $screen-tablet-min) {
            font-size: $font-size-lg;
        }
    }
  }
}
</style>
