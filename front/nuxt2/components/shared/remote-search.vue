<template>
  <div class="remote-search" :class="{ 'force-mobile': forceMobileAppearance }">
    <slot />

    <form autocomplete="off" @submit.prevent="search">
      <div class="rs-cities">
        <select-origem-destino v-model="fromCity" class="rsc-city is-origem" type="origem" remote />

        <ada-button class="rsc-swap-btn" aria-label="Inverter origem e destino" small icon @click="swapOrigemDestino">
          <fa class="rscsb-desktop" :icon="faArrowRightArrowLeft" />
          <fa class="rscsb-mobile" :icon="faArrowDownArrowUp" />
        </ada-button>

        <select-origem-destino v-model="toCity" class="rsc-city is-destino" type="destino" remote />
      </div>

      <div class="rs-dates">
        <custom-datepicker
          v-model="departureDate"
          :min-date="today"
          :view-date="today"
          :legend-marks="datepickerLegendMarks"
          label="Data de ida"
          text-format="DD/MMM"
          placeholder="Ida"
          clearable
          @navigate-date="onDateNavigation"
          @open="departureOpened = true"
          @close="departureOpened = false"
        >
          <template #start>
            <fa v-if="departureDate || departureOpened" :icon="faCalendarDay" class="color-brand ml-2" />
            <fa v-else :icon="faCalendar" class="color-grey ml-2" />
          </template>
        </custom-datepicker>

        <custom-datepicker
          v-model="returnDate"
          :min-date="departureDate || today"
          :view-date="today"
          :highlight-date="departureDate"
          :legend-marks="datepickerLegendMarks"
          label="Data de volta"
          text-format="DD/MMM"
          placeholder="Volta"
          clearable
          @navigate-date="onDateNavigation"
          @open="returnOpened = true"
          @close="returnOpened = false"
        >
          <template #start>
            <fa v-if="returnDate || returnOpened" :icon="faCalendarDay" class="color-brand ml-2" />
            <fa v-else :icon="faCalendar" class="color-grey ml-2" />
          </template>
        </custom-datepicker>
      </div>

      <ada-button class="rs-submit" color="primary" aria-label="Buscar" type="submit" block>
        <fa :icon="faMagnifyingGlass" class="rss-icon" />
        <span class="rss-text" :class="{ 'show-tablet': !forceMobileAppearance }">Buscar</span>
      </ada-button>

      <slot name="btn-extra" />
    </form>

    <transition name="fade">
      <p v-if="searchError" class="text-red text-sm mt-1">
        {{ searchError }}
      </p>
    </transition>
  </div>
</template>

<script>
import {
  faArrowRightArrowLeft,
  faArrowDownArrowUp,
  faCalendarDay,
  faCalendar,
  faMagnifyingGlass
} from '@fortawesome/pro-regular-svg-icons'
import dayjs from 'dayjs'
import { mapWritableState, mapActions, mapState } from 'pinia'
import referrercomponenthelper from '~/helpers/referrercomponenthelper.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { useGeolocationStore } from '~/stores/geolocation.js'
import { useSearchStore } from '~/stores/search.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import { useToastStore } from '~/stores/toast.js'
import customDatepicker from '~/components/shared/custom-datepicker/custom-datepicker.vue'
import selectOrigemDestino from '~/components/shared/select-origem-destino.vue'

export default {
  name: 'RemoteSearchbox',
  emits: ['navigate-date', 'search'],
  components: {
    selectOrigemDestino,
    customDatepicker
  },
  props: {
    permiteBuscaSemAlterarParametros: Boolean,
    useReplaceRoute: Boolean,
    voltaRequired: Boolean,
    datepickerLegendMarks: {
      type: Array,
      default: () => []
    },
    forceMobileAppearance: Boolean,
    pageResultName: {
      type: String,
      default: null,
      validator(value) {
        const validValues = ['searchPageV1', 'searchDestinationPageV1', 'searchOriginPageV1', 'searchPageWeekDay', 'searchPageFestival', 'promoOrigin', 'parceria']
        return validValues.includes(value)
      }
    },
    extraParams: {
      type: Object,
      default: () => ({})

    }
  },
  data() {
    return {
      departureOpened: false,
      returnOpened: false,
      showError: false,
      faMagnifyingGlass,
      faArrowRightArrowLeft,
      faArrowDownArrowUp,
      faCalendarDay,
      faCalendar
    }
  },
  computed: {
    ...mapWritableState(useSearchboxStore, ['toCity', 'fromCity', 'departureDate', 'returnDate']),
    ...mapState(useGeolocationStore, ['fetchLocation', 'currentPosition']),
    ...mapState(useBuscasRecentesStore, ['ultimaBusca']),
    today() {
      return dayjs().format('YYYY-MM-DD')
    },
    isPromo() {
      return this.$route.meta?.isPromo && this.fromCity && !this.toCity && !this.departureDate && !this.returnDate
    },
    searchError() {
      const showError = this.showError && !this.fromCity && !this.toCity
      return showError && 'Informe uma cidade'
    },
    searchName() {
      if (this.pageResultName !== null) {
        return this.pageResultName
      }
      let name = 'searchPageV1'
      if (!this.fromCity) {
        name = 'searchDestinationPageV1'
      } else if (this.isPromo) {
        name = 'promoOrigin'
      } else if (!this.toCity) {
        name = 'searchOriginPageV1'
      }
      return name
    }
  },
  watch: {
    '$route.query'(newQuery, oldQuery) {
      if (newQuery.ida && newQuery.ida !== oldQuery.ida) {
        this.departureDate = newQuery.ida
      }
      if (newQuery.volta && newQuery.volta !== oldQuery.volta) {
        this.returnDate = newQuery.volta
      }
    }
  },
  async mounted() {
    this.departureDate = this.conditionalAssign(this.$route.query.ida, !this.$route.query.ida, this.departureDate)
    this.returnDate = this.conditionalAssign(this.$route.query.volta, !this.$route.query.volta, this.returnDate)
    if (!this.fromCity && !this.toCity) {
      await this.preencheSearchBox()
    }
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useSearchboxStore, ['swapOrigemDestino', 'setFromCityBySlug']),
    ...mapActions(useSearchStore, ['setGrupoIda']),
    search() {
      if (!this.fromCity && !this.toCity) {
        // não preencheu origem nem destino
        this.showError = true
        return
      }
      if (!this.hasChangedSearchParams() && !this.permiteBuscaSemAlterarParametros) {
        // não alterou os parâmetros da busca e não permite busca sem alterar
        return
      }

      if (this.voltaRequired && !this.returnDate) {
        // não informou a data de volta que era obrigatória
        this.openToast({
          message: 'Preencha todos os campos', type: 'error'
        })
        return
      }

      // Isso aqui é temporário. Tecnicamente não deveriamos mesmo aceitar buscas com origem e destino iguais.
      // Mas, estamos com uma oferta intermunicipal para o evento de Sarará, o que foge da regra.
      // TODO: Remover essa exceção após o evento e melhorar a UX para esses casos.
      const isToBH = this.toCity?.slug === 'belo-horizonte-mg'
      if (this.fromCity?.id === this.toCity?.id && !isToBH) {
        this.openToast({
          message: 'Cidade de origem e destino devem ser diferentes', type: 'error'
        })
        return
      }

      const query = {}
      if (!this.departureDate && this.returnDate) {
        // Se buscar só volta, comporta-se como fosse só ida.
        query.ida = this.returnDate
        this.departureDate = this.returnDate
        this.returnDate = null
      } else {
        if (this.departureDate) query.ida = this.departureDate
        if (this.returnDate) query.volta = this.returnDate
      }

      const params = {
        origem: this.fromCity && this.fromCity.slug,
        destino: this.toCity && this.toCity.slug,
        ...this.extraParams
      }
      if (this.searchName === 'parceria') {
        params.nickname = this.$route.params.nickname
      }
      referrercomponenthelper.setReferrerComponent(this.$options.name)

      // Limpa o grupo a cada busca, para evitar do user buscar outro destino na volta.
      this.setGrupoIda(null)

      const routeSearch = { name: this.searchName, params, query }

      this.$emit('search', routeSearch)
      const navigationMethod = this.useReplaceRoute ? 'replace' : 'push'
      this.$router[navigationMethod](routeSearch)
    },
    hasChangedSearchParams() {
      const mudouOrigem = (
        this.fromCity &&
        this.fromCity.slug &&
        this.fromCity.slug !== this.$route.params.origem
      )
      const mudouDestino = (
        this.toCity &&
        this.toCity.slug &&
        this.toCity.slug !== this.$route.params.destino
      )
      const removeuOrigem = !this.fromCity && this.$route.params.origem
      const removeuDestino = !this.toCity && this.$route.params.destino
      const mudouDataIda = this.departureDate !== this.$route.query.ida
      const mudouDataVolta = this.returnDate !== this.$route.query.volta

      return (
        mudouOrigem || mudouDestino ||
        removeuOrigem || removeuDestino ||
        mudouDataIda || mudouDataVolta
      )
    },
    onDateNavigation(newDate) {
      this.$emit('navigate-date', newDate)
    },
    conditionalAssign(variable, condition, value) {
      if (condition) {
        return value
      }
      return variable
    },
    async preencheSearchBox() {
      if (!this.ultimaBusca) {
        await this.fetchLocation()
        if (this.currentPosition) {
          this.setFromCityBySlug(this.currentPosition.slug)
        }
        return
      }

      this.fromCity = this.ultimaBusca.origem
      this.toCity = this.ultimaBusca.destino
      this.departureDate = this.conditionalAssign(
        this.departureDate,
        !this.departureDate && this.ultimaBusca.dataIda >= dayjs().format('YYYY-MM-DD'),
        this.ultimaBusca.dataIda
      )
      this.returnDate = this.conditionalAssign(
        this.returnDate,
        !this.returnDate && this.departureDate,
        this.ultimaBusca.dataVolta
      )
    }
  }
}
</script>

<style lang="scss" scoped>
/**
  Esse CSS é um pouco diferente do padrão para facilitar a implmentação do `forceMobile`.
  Todo CSS mobile está escrito separado das regras do desktop.
*/
.remote-search {
  form {
    display: flex;
    flex-flow: column wrap;
    align-items: flex-end;
    gap: $spacing-one-half;
  }

  :deep(.f-label) {
    display: none;
  }

  .rs-cities {
    position: relative;
    width: 100%;

    .rsc-city {
      &.is-origem {
        :deep(.f-container) {
          border-radius: $spacing-1 $spacing-1 0 0;
        }
      }

      &.is-destino {
        :deep(.f-container) {
          border-top-width: 0;
          border-radius: 0 0 $spacing-1 $spacing-1;
        }
      }
    }

    .rsc-swap-btn {
      position: absolute;
      top: 50%;
      right: $spacing-2;
      z-index: $z-index-main;
      transform: translateY(-50%);
      border: 1px solid $color-grey-light;
      background-color: $color-white;

      .rscsb-desktop {
        display: none;
      }
    }
  }

  .rs-dates {
    flex: 2;
    display: flex;
    gap: $spacing-1;
    width: 100%;
  }

  .rs-submit {
    justify-content: center;

    .rss-text {
      margin-left: $spacing-1;
    }
  }

  // Regras desktop que só serão aplicadas caso não tenha a classe `force-mobile`
  @media (min-width: $screen-tablet-min) {
    &:not(.force-mobile) {
      form {
        gap: $spacing-2;
        flex-direction: row;
        flex: 2;
      }

      .rs-cities {
        flex: 3;
        display: flex;
        align-items: flex-end;
        gap: $spacing-half;

        .rsc-city {
          :deep(.f-container) {
            border-radius: $spacing-1;
            border-top-width: 1px;
          }

          flex: 2;
        }

        .rsc-swap-btn {
          position: static;
          transform: none;
          margin-bottom: 6px;

          .rscsb-mobile {
            display: none;
          }

          .rscsb-desktop {
            display: block;
          }
        }
      }

      .rs-dates {
        gap: $spacing-2;
      }

      .rs-submit {
        background-color: $color-brand;
        color: $color-white;
        border-radius: $border-radius-circular;
        width: 44px;
        height: 44px;

        &:hover {
          opacity: 0.7;
        }

        .rss-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
</style>
