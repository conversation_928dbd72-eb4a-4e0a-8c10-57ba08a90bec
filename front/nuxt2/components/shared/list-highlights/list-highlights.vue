<template>
  <div class="list-highlights">
    <grupo
      v-if="grupoDestaque"
      :grupo="grupoDestaque"
      :recomendacao="grupoDestacadoComponent"
      data-testid="grupo-recomendado"
      class="mb-0 lrg-grupo"
    />
  </div>
</template>

<script setup>
import { colorBrand, colorBrandLightest } from '@andromeda/design-tokens'
import { faBus } from '@fortawesome/pro-regular-svg-icons'
import { useSearchStore } from '~/stores/search.js'
import Grupo from '~/components/search-result/grupos/grupo.vue'

const grupoDestacadoComponent = {
  title: 'Continue sua compra',
  icon: faBus,
  textColor: colorBrand,
  backgroundColor: colorBrandLightest,
  borderColor: colorBrand,
  googleTransit: true
}
const searchStore = useSearchStore()
const { grupoDestaque } = searchStore

</script>

<style lang="scss" scoped>
.list-highlights {
  .lrg-grupo {
    margin-inline: 0;
  }
}
</style>
