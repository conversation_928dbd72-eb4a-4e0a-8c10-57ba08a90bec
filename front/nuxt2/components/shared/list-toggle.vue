<template>
  <div class="list-toggle">
    <ul
      class="lt-list"
      :class="classes"
      :style="styles"
    >
      <li
        v-for="(item, idx) in items"
        :key="`item_${idx}`"
        class="lt-item"
      >
        <slot
          name="item"
          :item="item"
        />
      </li>
    </ul>
    <ada-button
      class="lt-toggle-button"
      color="primary"
      outlined
      rounded
      @click="toggle"
    >
      {{ expanded ? showLessButtonName : showMoreButtonName }}
      <fa
        class="ml-2"
        :icon="expanded ? faChevronUp : faChevronDown"
      />
    </ada-button>
  </div>
</template>

<script>
import { faChevronDown, faChevronUp } from '@fortawesome/pro-regular-svg-icons'

export default {
  props: {
    items: {
      type: Array,
      default: () => []
    },
    limitHeight: {
      type: Number,
      default: 330
    },
    showMoreButtonName: {
      type: String,
      default: 'Ver mais'
    },
    showLessButtonName: {
      type: String,
      default: 'Ver menos'
    }
  },
  data() {
    return {
      expanded: false,
      faChevronDown,
      faChevronUp
    }
  },
  computed: {
    styles() {
      const styles = {}
      if (!this.expanded) {
        styles.height = `${this.limitHeight}px`
      }
      return styles
    },
    classes() {
      return {
        'lt-list-expanded': this.expanded
      }
    }
  },
  methods: {
    toggle() {
      this.expanded = !this.expanded
    }
  }
}
</script>

<style lang="scss" scoped>
  .list-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;

    .lt-list {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-5;
      overflow: hidden;

      &:not(.lt-list-expanded) {
        position: relative;

        &::after {
          content: '';
          width: 100%;
          height: 50%;
          position: absolute;
          bottom: 0;
          left: 0;
          background-image: linear-gradient(to top, rgba($color-white, 1), rgba($color-white, 0));
        }
      }
    }

    .lt-toggle-button {
      margin-top: $spacing-3;
    }
  }
</style>
