<template>
  <div class="template-search">
    <taskbar-search />

    <slot name="page-header" />
    <client-only>
      <ada-container
        v-if="!!cupomBlackFriday || Object.keys(promoEmDestaque).length"
        class="ts-cupom-alerts"
        full-width-mobile
      >
        <cupom-alert-controller
          :cupom-black-friday="cupomBlackFriday"
          :promo-em-destaque="promoEmDestaque"
          @hide-cupom-incentivo="promoEmDestaque = {}"
          @loaded="scrollTo($refs.container)"
        />
      </ada-container>
    </client-only>

    <ada-container ref="container" class="ts-container">
      <busca-origem-ou-destino v-if="isOnlyOrigemOrDestino" />
      <resultados v-else />
      <div class="mt-3 show-phone">
        <ada-card outlined rounded class="p-2 bg-grey-lightest">
          <trecho-breadcrumbs class="color-grey-dark text-sm" />
        </ada-card>
        <search-result-resumo is-mobile />
      </div>
    </ada-container>

    <LazyHydrate v-if="!isBuscaComData" v-slot="{ hydrated }" when-visible>
      <seo-content v-if="hydrated" :is-only-origem-or-destino="isOnlyOrigemOrDestino" :trecho="trecho" />
    </LazyHydrate>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { mapActions, mapState, mapWritableState } from 'pinia'
import LazyHydrate from 'vue-lazy-hydration'
import api from '~api'
import { mapLocaisEmbarqueFoto } from '~/assets/js/ponto-de-embarque.ts'
import { useStatefulCookie } from '~/composables/utils/cookie.js'
import abpreco from '~/helpers/abpreco.js'
import amplitudehelper from '~/helpers/amplitudehelper.js'
import dateEventInfo from '~/helpers/date-event-info.js'
import EventBus from '~/helpers/eventbus.js'
import { DATE_FORMATS, dateFormat } from '~/helpers/formatters.js'
import { isViagemDireta, groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import localstorage from '~/helpers/localstorage.js'
import referrercomponenthelper from '~/helpers/referrercomponenthelper.js'
import { search } from '~/helpers/searchhelper.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { FILTRO_KEYS, QUERY_TO_FILTROS_MAP, useFiltroStore } from '~/stores/filtro.js'
import { useLeadPopupStore } from '~/stores/lead-popup.js'
import { JK_ID, useSearchStore } from '~/stores/search.js'
import { useSearchboxStore } from '~/stores/searchbox.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import { LOCAL_STORAGE_CLOSE_KEY_CARD_WPP } from '~/components/search-result/constants.js'
import resultados from '~/components/search-result/resultados.vue'
import searchResultResumo from '~/components/search-result/shared/search-result-resumo.vue'
import trechoBreadcrumbs from '~/components/search-result/shared/trecho-breadcrumbs.vue'
import taskbarSearch from '~/components/search-result/taskbar-search.vue'

let searchViews = []

export default {
  name: 'SearchTemplate',
  components: {
    searchResultResumo,
    LazyHydrate,
    trechoBreadcrumbs,
    buscaOrigemOuDestino: () => ({ component: import('~/components/search-result/busca-origem-ou-destino/busca-origem-ou-destino.vue') }),
    seoContent: () => import('~/components/search-result/seo-content/seo-content-bundle/search-seo-content-bundle.vue'),
    cupomAlertController: () => import('~/components/search-result/cupom-alert-controller.vue'),
    resultados,
    taskbarSearch
  },
  data() {
    return {
      searchViewLogInterval: undefined,
      referrerComponent: null,
      promoEmDestaque: {}
    }
  },
  watch: {
    $route: async function(route, old) {
      const mudouPath = route.path !== old.path
      const mudouIda = route.query.ida !== old.query.ida
      const mudouVolta = route.query.volta !== old.query.volta
      const mudouFiltro = FILTRO_KEYS.some(filtroKey => route.query[filtroKey] !== old.query[filtroKey])

      if (process.browser && (mudouPath || mudouIda || mudouVolta)) {
        this.gruposRecomendadosBased = []
        this.adicionaBuscaRecente()
        try {
          await search(this.$route, this.$pinia)
        } catch {
          // Se da erro aqui eu não preciso carregar os cupons (que está comentado no momento)
          return
        }

        const isSearchViewIncluded = searchViews.some(search => search.id === this.searchIdentifier)
        if ((mudouIda || mudouVolta) && !isSearchViewIncluded) {
          searchViews.push(this.getAmplitudePayload())
        }
      }
      if (mudouFiltro) {
        this.aplicaFiltrosQuerystring()
      }
    },
    completed(isCompleted) {
      if (!isCompleted) return

      let routeName = 'checkout'
      let params = { idIda: this.grupoIda.id }

      let query = null

      if (this.isVolta(this.$route)) {
        params.idVolta = this.grupoVolta.id
      }

      if (!this.permiteCheckoutDeslogado && !this.loggedIn) {
        routeName = 'login'
        params = { step: 'entrar', origin: 'via-reserva' }

        let nextRoute = `/reserva/grupos/${this.grupoIda.id}`

        query = { idIda: this.grupoIda.id }

        if (this.isVolta(this.$route)) {
          nextRoute = `/reserva/grupos/${this.grupoIda.id}/${this.grupoVolta.id}`
          query.idVolta = this.grupoVolta.id
        }

        query.next = nextRoute
      }

      this.$router.push({
        name: routeName,
        params,
        query
      })
    },
    promoCode(newCode, oldCode) {
      EventBus.$emit('atualizou-cupom',
        {
          pagina: 'search',
          cupom: newCode || oldCode,
          acao: !newCode ? 'remocao' : 'adicao'
        })
    }
  },
  props: {
    isOnlyOrigemOrDestino: {
      type: Boolean,
      default: false
    }
  },
  async mounted() {
    if (!this.isOnlyOrigemOrDestino) {
      const params = {
        origemSlug: this.$route.params.origem,
        destinoSlug: this.$route.params.destino
      }

      try {
        this.promoEmDestaque = await api.passenger.getPromoDestaqueSearchResult(params)
      } catch (error) {
        console.error(error)
      }
    }
    this.loadSettings()
    this.cleanSearchStore(this.$route)
    this.handlePopups()
    this.referrerComponent = referrercomponenthelper.getAndClearReferrerComponent()
    EventBus.$emit('open-search-result-page', {
      hasFotoLocalEmbarque: this.hasFotoLocalEmbarque,
      hasCardWhatsapp: this.hasCardWhatsapp,
      hasGrupos: this.hasGruposTrechoPesquisado,
      hasTrechoPromo: this.activePromo?.code === 'trecho-promo',
      hasOnibusPlotado: this.hasOnibusPlotado,
      hasViagensDiretas: this.hasViagensDiretas,
      hasGruposProximoDia: this.hasGruposProximoDia,
      hasPrecoPromocional: this.hasPrecoPromocional,
      searchedWithPromo: !!this.promoCode,
      promoEmDestaque: this.promoEmDestaque,
      neverTraveled: !this.loggedIn || this.neverTraveled,
      difDataDeAgora: dateEventInfo.difDataDeAgora(this.$route.query.ida),
      departureDate: this.departureDate,
      returnDate: this.returnDate,
      cidadeProxima: this.cidadeProxima,
      componenteDeOrigem: this.referrerComponent,
      trechoClasseResults: this.priceLookup,
      grupo: {
        ...this.trecho,
        departureDate: this.departureDate,
        returnDate: this.returnDate,
        results: this.resultsGroups
      },
      qtdRecomendacoesExibidas: this.gruposRecomendadosBased.length,
      results: this.resultsGroups,
      ...this.recomendacaoBusca,
      ...this.resultsGroupedByModeloVenda,
      searchResultName: this.$route.name,
      hasBuserPremium: this.hasBuserPremium,
      hasJKRecomendado: this.gruposRecomendadosBased.some(g => g.company_id === JK_ID)

    })

    window.addEventListener('visibilitychange', this.onVisibilityChange)
    const BATCH_INTERVAL = 1000 * 60 * 2
    if (!this.searchViewLogInterval) {
      this.searchViewLogInterval = setInterval(this.sendSearchResultViewsEvent, BATCH_INTERVAL)
    }

    if (this.results) {
      abpreco.set(this.$cookies, this.results.price_cookie)
    }
    this.adicionaBuscaRecente()
    this.aplicaFiltrosQuerystring()
  },
  destroyed() {
    this.destroyLeadPopup(this.$andromeda.breakpoint.smAndDown)
  },
  methods: {
    // eslint-disable-next-line vue/no-unused-properties
    beforeRouteLeave(to, from, next) {
      const restrictionRoutes = new Set(['checkout', 'searchPageV1'])

      if (!restrictionRoutes.has(to.name)) {
        const origemName = this.trecho.origem?.city_name || this.trecho.origem?.name
        const destinoName = this.trecho.destino?.city_name || this.trecho.destino?.name
        if (destinoName) {
          EventBus.$emit('abandonou-busca', {
            origem: origemName,
            destino: destinoName,
            datetimeIda: dateFormat(DATE_FORMATS.dbdatetime, from.query.ida),
            datetimeVolta: dateFormat(DATE_FORMATS.dbdatetime, from.query.volta)
          })
        }
      }

      clearInterval(this.searchViewLogInterval)
      window.removeEventListener('visibilitychange', this.onVisibilityChange)
      if (searchViews.length) {
        const search = searchViews[searchViews.length - 1]
        search.isLastSearchPreFunnelShift = true
        this.sendSearchResultViewsEvent()
      }
      amplitudehelper.setTransport('fetch')
      next()
    },
    ...mapActions(useFiltroStore, ['populaFiltrosAplicados']),
    ...mapActions(useBuscasRecentesStore, ['addBuscaRecente']),
    ...mapActions(useSettingsStore, {
      loadSettings: 'load'
    }),
    ...mapActions(useSearchStore, [
      'cleanSearchStore',
      'filtraGrupos'
    ]),
    ...mapActions(useLeadPopupStore, {
      initLeadPopup: 'init',
      destroyLeadPopup: 'destroy'
    }),
    onVisibilityChange() {
      if (document.visibilityState !== 'hidden') return

      this.sendSearchResultViewsEvent()
      amplitudehelper.setTransport('beacon')
    },
    sendSearchResultViewsEvent() {
      if (!searchViews.length) return
      EventBus.$emit('changed-search-result-params', { searchViews })
      searchViews = []
    },
    getAmplitudePayload() {
      const { origem, destino } = this.$route.params
      const { ida, volta } = this.$route.query
      const trecho = [origem, destino].filter(Boolean).join(':')

      return {
        id: this.searchIdentifier,
        isLastSearchPreFunnelShift: false,
        hasParcelamento: !!this.trecho?.parcelamento,
        hasOnibusPlotado: this.hasOnibusPlotado,
        hasViagensDiretas: this.hasViagensDiretas,
        hasGruposProximoDia: this.hasGruposProximoDia,
        hasPrecoPromocional: this.hasPrecoPromocional,
        searchedWithPromo: !!this.promoCode,
        neverTraveled: !this.loggedIn || this.neverTraveled,
        trecho,
        ida,
        volta,
        difDataDeAgora: dateEventInfo.difDataDeAgora(ida),
        componenteDeOrigem: this.referrerComponent,
        cidadeProxima: this.cidadeProxima,
        departureDate: this.departureDate,
        returnDate: this.returnDate,
        results: this.resultsGroups,
        trechoClasseResults: this.priceLookup,
        ...this.recomendacaoBusca,
        ...this.resultsGroupedByModeloVenda
      }
    },
    scrollTo(child) {
      if (!child || !child.$el) {
        return
      }

      const el = child.$el
      setTimeout(() => {
        el.scrollIntoView({ behavior: 'smooth' })
      })
    },
    handlePopups() {
      this.initLeadPopup(this.$andromeda.breakpoint.smAndDown)
    },
    adicionaBuscaRecente() {
      const numGrupos = this.resultsGroups
      if (numGrupos > 0) {
        const imgUrl = this.results.destino_picture_url
        this.addBuscaRecente(this.fromCity, this.toCity, this.departureDate, this.returnDate, imgUrl)
      }
    },
    aplicaFiltrosQuerystring() {
      // Ajudinha Filtro: é necessário adicionar o novo filtro aqui para que ele possa ser aplicado pela querystring
      const { embarque, desembarque, horario, poltrona, modelo, metro, trajeto, blackFriday, acessibilidade, trechoPromo } = this.$route.query

      const queryFiltros = { embarque, desembarque, horario, poltrona, modelo, metro, trajeto, blackFriday, acessibilidade, trechoPromo }
      const filtrosSelecionados = {
        localDeEmbarque: {},
        localDeDesembarque: {},
        faixaDeHorario: {},
        poltrona: {},
        proximoAoMetro: {},
        modeloVenda: {},
        tipoTrajeto: {},
        blackFriday: {},
        trechoPromo: {},
        acessibilidade: {}

      }

      for (const [filtroLabel, filtroValues] of Object.entries(queryFiltros)) {
        if (filtroValues) {
          const filtrosAplicadosQuery = filtroValues.split(',')
          for (const filtroAplicado of filtrosAplicadosQuery) {
            filtrosSelecionados[QUERY_TO_FILTROS_MAP[filtroLabel]][filtroAplicado] = true
          }
        }
      }

      this.populaFiltrosAplicados(filtrosSelecionados)
      this.filtraGrupos()
      this.popupFiltroAberta = false
    }
  },
  setup() {
    const promoCode = useStatefulCookie('promo')

    return { promoCode }
  },
  computed: {
    ...mapWritableState(useFiltroStore, {
      popupFiltroAberta: 'popupAberta'
    }),
    ...mapWritableState(useSearchStore, ['gruposRecomendadosBased']),
    ...mapState(useSessionStore, ['loggedIn', 'neverTraveled']),
    ...mapState(useSearchboxStore, ['fromCity', 'toCity']),
    ...mapState(useSettingsStore, ['permiteCheckoutDeslogado', 'isApp', 'permiteLayoutPreBlackfriday']),
    ...mapState(useSearchStore, [
      'results',
      'trecho',
      'trechoCidadeProxima',
      'completed',
      'grupoIda',
      'grupoVolta',
      'isVolta',
      'hasGruposTrechoPesquisado',
      'gruposRecomendados',
      'gruposRecomendadosConexao',
      'hasGruposProximoDia',
      'activePromo'
    ]),
    hasFotoLocalEmbarque() {
      return this.allGroups.some(g => g?.origem_slug in mapLocaisEmbarqueFoto || g?.destino_slug in mapLocaisEmbarqueFoto)
    },
    cupomBlackFriday() {
      if (!this.permiteLayoutPreBlackfriday) {
        return null
      }
      const validCupom = {
        FOFOCA40: () => !this.loggedIn || this.neverTraveled,
        FOFOCA30: () => this.loggedIn && this.departureDate && dayjs(this.departureDate).diff(dayjs().startOf('day'), 'day', true) >= 3
      }

      for (const [code, func] of Object.entries(validCupom)) {
        if (func()) {
          return code
        }
      }
      return null
    },
    hasCardWhatsapp() {
      const closedLocalStorage = localstorage.getLocalStorageWithExpiry(LOCAL_STORAGE_CLOSE_KEY_CARD_WPP)
      return !closedLocalStorage && !this.isApp && (!this.loggedIn || this.neverTraveled)
    },
    searchIdentifier() {
      const { name, params: { origem, destino }, query: { ida, volta } } = this.$route
      const identifierParts = [name, origem, destino, ida, volta]
      return identifierParts.map(part => part ?? 'identificador-indefinido').join(':')
    },
    resultsGroups() {
      return this.allGroups.length
    },
    priceLookup() {
      return this.allGroups.map(({
        id,
        max_split_value,
        vagas,
        trecho_vendido_id,
        tipo_assento,
        grupo_id
      }) => ({
        id,
        preco: max_split_value,
        vagas,
        trecho_vendido_id,
        tipo_assento,
        grupo_id
      }))
    },
    resultsGroupedByModeloVenda() {
      return this.allGroups.reduce((acc, group) => {
        let modeloVendaKey
        if (group.is_conexao) {
          modeloVendaKey = 'results_conexao'
        } else if (group.modelo_venda) {
          modeloVendaKey = `results_${group.modelo_venda}`
        } else {
          // se não tem modelo venda nem é conexão a gente ignora (não deve acontecer)
          return acc
        }
        acc[modeloVendaKey]
          ? acc[modeloVendaKey] += 1
          : acc[modeloVendaKey] = 1
        return acc
      }, {})
    },
    isBuscaComData() {
      return this.$route.query.ida || this.$route.query.volta
    },
    allGroups() {
      const groupsByDate = this.results?.groups_by_date || []
      const allGroups = groupsByDate.map((date) => date.grupos)
      return [].concat.apply([], allGroups) // faz a mesma coisa que o .flat(), só que funciona (denovo)
    },
    hasOnibusPlotado() {
      return this.allGroups.filter((grupo) => grupo.onibus_plotado).length > 0
    },
    hasBuserPremium() {
      return groupsHaveBuserPremium(this.allGroups)
    },
    hasViagensDiretas() {
      return this.allGroups.some((grupo) => isViagemDireta(grupo))
    },
    hasPrecoPromocional() {
      return this.allGroups.some((grupo) => grupo.ref_split_value > grupo.max_split_value)
    },
    departureDate() {
      return this.$route.query.ida
    },
    returnDate() {
      return this.$route.query.volta
    },
    cidadeProxima() {
      const { origem, destino } = this.trechoCidadeProxima
      const hasTrechoCidadeProxima = origem && destino
      if (!hasTrechoCidadeProxima) return
      return origem?.slug > destino.slug
        ? `${destino.slug}=${origem.slug}`
        : `${origem.slug}=${destino.slug}`
    },
    recomendacaoBusca() {
      if (!this.gruposRecomendados && !this.gruposRecomendadosConexao) return
      const RECOMENDACOES = ['custo_beneficio', 'menor_preco']
      for (const recomendacao of RECOMENDACOES) {
        const rec = this.gruposRecomendados?.[recomendacao] || this.gruposRecomendadosConexao?.[recomendacao]
        if (rec) {
          return {
            idRecomendacao: rec,
            tipoRecomendacao: recomendacao
          }
        }
      }
      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.template-search {
  background-color: $color-grey-lightest;

  .ts-cupom-alerts {
    @media (min-width: $screen-tablet-min) {
      margin-top: $spacing-3;
      margin-bottom: $spacing-3;
    }
  }

  .ts-container {
    padding-top: $spacing-4;
    padding-bottom: $spacing-4;
    margin-top: (-$spacing-2);

    // Offset pro `scrollIntoView`"
    scroll-margin-top: $taskbar-height-mobile;

    @media (min-width: $screen-tablet-min) {
      padding-top: 0;
      margin-top: 0;
      scroll-margin-top: $taskbar-height-desktop;
    }
  }
}
</style>
