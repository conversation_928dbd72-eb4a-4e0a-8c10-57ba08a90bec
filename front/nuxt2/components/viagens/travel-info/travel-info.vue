<template>
  <ada-card
    class="travel-info o-hidden"
    outlined
    rounded
  >
    <div class="ti-company-container">
      <ada-image
        v-if="fotoCompanyOrOnibus?.img_url"
        class="ticc-foto-onibus mr-one-half"
        :class="{'tifo-outline': fotoCompanyOrOnibus?.outline}"
        :width="`${fotoCompanyOrOnibus?.width}px`"
        :height="`${fotoCompanyOrOnibus?.height}px`"
        rounded
        :object-fit="fillImagem"
      >
        <img
          v-lazy-load
          :data-src="fotoCompanyOrOnibus.img_url"
          :width="fotoCompanyOrOnibus.width"
          :height="fotoCompanyOrOnibus.height"
          :alt="`ônibus ${fotoCompanyOrOnibus.company_name}`"
        >

        <button
          v-if="mostraBotaoAmpliar"
          class="tifo-button"
          aria-label="Clique para ampliar a imagem"
          @click="popupFotoOnibusVisible = true"
        >
          <fa :icon="faMagnifyingGlassPlus" />
        </button>
      </ada-image>

      <company-logo
        v-else-if="isMarketplace"
        :grupo="travel.grupo"
        class="mr-one-half"
      />

      <div>
        <p
          v-if="!isPremium"
          class="caption text-grey"
        >
          {{ modeloVendaLabel }}
        </p>
        <p
          v-if="empresa"
          class="title-xs"
        >
          {{ empresa }}
        </p>

        <p
          v-if="placa"
          class="mt-half"
        >
          {{ tipoVeiculoLabel }} <span v-if="tipoVeiculoLabel === 'Ônibus'">- {{ andares }} </span>
        </p>
      </div>
    </div>
    <div class="ti-chips">
      <div class="tic-chip">
        <p class="caption">
          Placa
        </p>
        <p
          v-clipboard="placa.placa"
          :class="`color-${placa.cor}`"
          class="ticc-text"
          title="Copiar placa do ônibus"
          @click="copiado('Número da placa')"
        >
          {{ placa.placa }}
          <fa
            class="ml-1 color-grey-dark"
            :icon="faCopy"
          />
        </p>
      </div>
      <div class="tic-chip">
        <p class="caption">
          Reserva
        </p>
        <p
          v-clipboard="reserva"
          class="ticc-text"
          title="Copiar código da reserva"
          @click="copiado('Código da reserva')"
        >
          Cód. {{ reserva.toLowerCase() }}
          <fa
            class="ml-1"
            :icon="faCopy"
          />
        </p>
      </div>
    </div>

    <ada-list :divided="false">
      <ada-list-item
        v-if="isMarketplace"
        title="Dados de contato da empresa"
        :start-icon="faPhone"
        :end-icon="faArrowRight"
        :to="{
          name: 'empresaRodoviariaMarketplace',
          params: {
            id: travel.id
          }
        }"
      />
      <ada-divider color="grey-light" class="mx-2 w-auto" />
      <ada-list-item
        title="Bagagens aceitas"
        :start-icon="faSuitcaseRolling"
        :end-icon="faArrowRight"
        :to="{
          name: 'ajudaQuestion',
          params: {
            topicSlug: 'bagagem',
            questionSlug: isFretamento ? 'politica-de-bagagens-fretamento-buser' : 'politica-de-bagagens-revenda-rodoviaria'
          }
        }"
      />
    </ada-list>

    <popup
      v-model="popupFotoOnibusVisible"
      :title="`${tipoVeiculoLabel} da sua viagem`"
    >
      <ada-image
        v-if="fotoCompanyOrOnibus?.img_url"
        object-fit="contain"
        rounded
      >
        <img
          v-lazy-load
          :data-src="fotoCompanyOrOnibus.img_url"
          :alt="`ônibus ${fotoCompanyOrOnibus.company_name}`"
        >
      </ada-image>
    </popup>
  </ada-card>
</template>

<script>
import { faCopy, faMagnifyingGlassPlus, faPhone, faArrowRight, faSuitcaseRolling } from '@fortawesome/pro-regular-svg-icons'
import { mapActions } from 'pinia'
import onibusPlotadoImage from '~/assets/images/onibus_plotado.jpg'
import { groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { isCloseToTravelTime } from '~/helpers/travelhelper.js'
import { useToastStore } from '~/stores/toast.js'
import companyLogo from '~/components/shared/company-logo.vue'
import popup from '~/components/shared/popup/popup.vue'

export default {
  components: {
    popup,
    companyLogo
  },
  props: {
    travel: {
      type: Object,
      required: true
    },
    isMarketplace: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      popupFotoOnibusVisible: false,
      faCopy,
      faMagnifyingGlassPlus,
      faPhone,
      faArrowRight,
      faSuitcaseRolling
    }
  },
  computed: {
    placa() {
      const temPlaca = this.travel.grupo?.onibus_placa
      return {
        placa: temPlaca || 'Em breve',
        cor: temPlaca ? 'primary' : 'color-grey-dark'
      }
    },
    reserva() {
      return this.travel?.reservation_code
    },
    empresa() {
      return this.travel.grupo?.company_name
    },
    isFretamento() {
      return this.travel.grupo?.modelo_venda === 'buser'
    },
    oneDayFromTravelTime() {
      return isCloseToTravelTime(this.travel, 1, 'days')
    },
    fotoCompanyOrOnibus() {
      const grupo = this.travel?.grupo
      if (!grupo) {
        return null
      }

      const infos = {
        company_name: grupo.company_name,
        height: 76,
        width: 76
      }

      if (this.oneDayFromTravelTime) {
        if (grupo.foto_onibus) {
          infos.img_url = grupo.foto_onibus
          return infos
        }
        if (grupo.onibus_plotado) {
          infos.img_url = onibusPlotadoImage
          return infos
        }
      }
      if (grupo.company_logo_url && !this.isMarketplace) {
        infos.img_url = grupo.company_logo_url
        infos.outline = true
      }
      return infos
    },
    mostraBotaoAmpliar() {
      return this.fotoCompanyOrOnibus?.img_url && this.fotoCompanyOrOnibus?.img_url !== this.travel.grupo?.company_logo_url
    },
    fillImagem() {
      const isLogoCompany = this.fotoCompanyOrOnibus?.img_url === this.travel.grupo?.company_logo_url
      return isLogoCompany ? 'contain' : 'cover'
    },
    tipoVeiculoLabel() {
      return (
        {
          van: 'Van',
          'micro-onibus': 'Micro-ônibus'
        }[this.travel.grupo?.tipo_veiculo] || 'Ônibus'
      )
    },
    andares() {
      return this.travel.grupo?.tipo_veiculo === 'DD' ? '2 andares' : '1 andar'
    },
    modeloVendaLabel() {
      return this.isFretamento ? 'Fretamento Buser' : 'Revenda Rodoviária'
    },
    isPremium() {
      return groupsHaveBuserPremium([this.travel.grupo])
    }
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    copiado(prefixo) {
      this.openToast({
        message: `${prefixo} copiado!`,
        type: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$zoom-button-size: 30px;

.travel-info {
  .ti-company-container {
    display: flex;
    margin: $spacing-2 $spacing-2 0 $spacing-2;
    align-items: center;

    .ticc-foto-onibus {
      position: relative;
      flex-shrink: 0;

      &.tifo-outline {
        border: 1px solid $color-grey-light;
        padding: 0 $spacing-1;
      }

      .tifo-button {
        cursor: pointer;
        border: none;
        position: absolute;
        left: 50%;
        top: 50%;
        width: $zoom-button-size;
        height: $zoom-button-size;
        text-align: center;
        line-height: $zoom-button-size;
        transform: translate(-50%, -50%);
        background-color: rgba($color-black, .5);
        border-radius: $border-radius-md;
        color: $color-white;
      }
    }
  }

  .ti-chips {
    display: flex;
    gap: $spacing-1;
    justify-content: space-between;
    margin: $spacing-2;

    .tic-chip {
      padding: $spacing-1;
      border: 1px solid $color-grey-light;
      background-color: $color-grey-lightest;
      border-radius: $border-radius-md;
      width: 100%;

      .ticc-text {
        cursor: pointer;
      }
    }
  }
}
</style>
