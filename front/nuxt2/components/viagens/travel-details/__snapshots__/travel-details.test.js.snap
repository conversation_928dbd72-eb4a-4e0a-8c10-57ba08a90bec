// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Travel Details should render correctly (com viagem internacional) 1`] = `
"<div class="travel-details pb-4">
  <!---->
  <div class="">
    <div class="ada-card ada-accordion travel-historic mb-2 ac-outlined ac-soft is-outlined">
      <!---->
      <div class="ada-toggleable">
        <div class="ada-toggleable-item ada-accordion-item">
          <div class="d-flex jc-between ai-center c-pointer">
            <div class="travel-historic-current">
              <div class="mt-2 mb-2 px-2">
                <fa-stub icon="[object Object]" size="lg" class="thc-icon"></fa-stub>
              </div>
              <div class="d-flex fd-column"><strong>
      Viagem confirmada
    </strong></div>
            </div> <button type="button" class="ada-button th-button is-button b-color-primary b-transparent b-small">
              <!---->
              Ver mais
              <fa-stub icon="[object Object]" class="ml-1"></fa-stub></button>
          </div>
          <section class="aai-content bg-white is-paddless" style="display: none;">
            <div class="travel-historic-events p-2">
              <div class="the-evento is-previous">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Reserva realizada
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-previous">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Pagamento confirmado
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-current">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Viagem confirmada
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque iniciado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque realizado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque encerrado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Viagem concluída
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
    <!---->
    <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div class="resumo-viagem">
        <div class="rv-wrapper">
          <div><span class="rvw-label">Partida</span>
            <p class="text-sm"><strong class="text-md">Florianópolis</strong>
              / SC
            </p>
            <div class="rvw-horario-partida"><span class="text-sm">
          01/jun, 12:00
        </span>
              <!---->
            </div>
          </div>
          <div class="rvw-desembarque"><span class="rvw-label">Chegada</span>
            <p class="text-sm"><strong class="text-md">Buenos Aires</strong>
              / EX
            </p>
            <p class="text-sm">
              01/jun, 12:00
            </p>
          </div>
        </div>
        <!---->
        <div class="rv-footer">
          <p class="caption">
            Duração: 6h
          </p>
          <p class="caption color-primary">
            Horários aproximados
          </p>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <div>
      <div class="card-retail-media mb-2"><img src="QUARTO_HOTEL.webp" alt="Quarto Hotel" width="114" height="114">
        <div class="crm-content">
          <h3 class="title-xs">
            Hotéis em São Paulo
          </h3>
          <p class="text-sm">
            Veja os melhores preços em hospedagem e atividades para curtir em sua viagem.
          </p> <a href="https://buser.viagens.decolar.com/?utm_source=site&amp;utm_medium=detalhes-viagem&amp;utm_campaign=decolarjunho" class="ada-button crmc-btn is-button b-color-primary b-small" target="_blank">
            <!---->
            Ver preços <fa-stub icon="[object Object]" size="1x" class="ml-1"></fa-stub></a>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
    <!---->
    <!---->
    <!---->
    <div class="ada-card travel-info o-hidden mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div class="ti-company-container">
        <div class="company-logo mr-one-half"><img width="120" height="76" src="https://i.imgur.com/y9sTq12.jpg" alt="Cia. de Fretamento com nome muito grande" class="cl-logo"></div>
        <div>
          <p class="caption text-grey">
            Revenda Rodoviária
          </p>
          <p class="title-xs">
            Cia. de Fretamento com nome muito grande
          </p>
          <p class="mt-half">
            Ônibus <span>- 2 andares </span></p>
        </div>
      </div>
      <div class="ti-chips">
        <div class="tic-chip">
          <p class="caption">
            Placa
          </p>
          <p title="Copiar placa do ônibus" class="ticc-text color-primary">
            OIE1234
            <fa-stub icon="[object Object]" class="ml-1 color-grey-dark"></fa-stub>
          </p>
        </div>
        <div class="tic-chip">
          <p class="caption">
            Reserva
          </p>
          <p title="Copiar código da reserva" class="ticc-text">
            Cód. asidha
            <fa-stub icon="[object Object]" class="ml-1"></fa-stub>
          </p>
        </div>
      </div>
      <ul class="ada-list">
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/rodoviaria/empresa-rodoviaria" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Dados de contato da empresa </span>
            <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></a></li> <span class="ada-divider mx-2 w-auto bg-grey-light" style="height: 1px;"></span>
        <li class="ada-list-item"><a href="#/ajuda/bagagem/politica-de-bagagens-revenda-rodoviaria" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Bagagens aceitas </span>
            <!----></span></span>
        </span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
      </ul> <span><!----></span>
    </div>
    <!---->
    <div role="alert" class="ada-alert mb-2 a-info">
      <!---->
      <div class="a-content">
        <!---->
        <p class="text-sm">
          O Buser Passagens é um programa de revenda de passagens de ônibus de linhas rodoviárias.
          A operação após a venda da passagem neste modelo é de responsabilidade da empresa <strong>Cia. de Fretamento com nome muito grande</strong>.
        </p>
      </div>
      <!---->
    </div>
    <!---->
    <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div>
        <h3 class="fw-600 pb-1 title-sm">
          Itinerário
        </h3>
        <div class="text-sm fw-500 pb-2">
          <!---->
          <!---->
        </div>
        <ul class="travel-itinerary">
          <li class="ti-local is-embarque">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <p class="caption color-grey mb-half">
                Embarque
              </p>
              <h3 class="title-xs mb-half">
                Auto Posto Joiris
              </h3>
              <div class="d-flex fd-row ai-center"><a href="https://maps.google.com/0" target="_blank" class="text-sm">
                  Av. 43, nº 658, Alvorada. Auto Posto Joiris, Florianópolis - SC
                </a> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small" data-testid="button">
                  <!---->
                  <fa-stub icon="[object Object]" size="lg"></fa-stub>
                </button></div>
              <div class="tis-description text-sm mt-1">
                <p><strong>Instruções:</strong>
                  No terminal turístico JK, em frente à escolinha do Prof. Raimundo tem uma entrada onde os ônibus ficam estacionados. É lá que você deve esperar.
                </p>
                <!---->
              </div>
              <!---->
            </div>
          </li>
          <li class="ti-local">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content"><button type="button" class="ada-button w-auto is-button b-color-white b-rounded b-small">
                <!---->
                2 paradas no percurso
                <fa-stub icon="[object Object]" class="ml-1"></fa-stub></button>
              <!---->
            </div>
          </li>
          <li class="ti-local" style="display: none;">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <h3 class="text-sm fw-400">
                Pouso Alegre - MG
              </h3>
            </div>
          </li>
          <li class="ti-local" style="display: none;">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <h3 class="text-sm fw-400">
                Campinas - SP
              </h3>
            </div>
          </li>
          <li class="ti-local is-desembarque">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <p class="caption color-grey mb-half">
                Desembarque
              </p>
              <h3 class="title-xs">
                Shopis Centis
              </h3>
              <div class="d-flex fd-row jc-start ai-center">
                <div><a href="https://maps.google.com/3" target="_blank" class="text-sm">
                    Shopping Center, Porto Alegre - RS
                  </a></div> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small">
                  <!---->
                  <fa-stub icon="[object Object]" size="lg"></fa-stub>
                </button>
              </div>
              <!---->
              <!---->
            </div>
          </li>
        </ul>
        <!---->
      </div>
    </div>
    <!---->
    <div class="ada-card boarding-documents p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <h3 class="title-xs">
        Documentos para viajar
      </h3>
      <div class="documentos-viagem-internacional">
        <p>
          Veja o que é necessário para você viajar para a <span class="tt-capitalize">argentina</span>.
        </p>
        <ul class="dvi-item mt-2"></ul>
        <!---->
      </div>
    </div>
    <!---->
    <!---->
    <h2 class="title-sm mb-1 pt-1">
      Passageiros
    </h2>
    <!---->
    <div data-sentry-mask="" class="mb-2">
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Daniel Silva Campos Junior
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <p class="color-primary caption mt-one-half">
          Deve levar cadeirinha bebê conforto
        </p>
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 99911 8445
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              991.119.999-99
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              123243429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Companheiro do Fábio 1
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <p class="color-primary caption mt-one-half">
          Deve levar cadeirinha bebê conforto
        </p>
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 99118 4452
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              827.829.123-23
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2.888.999-9
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                C2
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              987.312.631-97
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              7962439
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Companheiro do Fábio com nome muito muito grande 2
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 77463 5543
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              213.321.315-98
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2011031388761
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft bg-grey-light">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Étila Amaral
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              777.777.666-54
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              324339999
            </p>
          </div>
        </div>
        <!---->
        <!----> <span class="ada-badge mt-one-half bg-grey" small="">
    Removido
  </span>
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Tony Lâmpada
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              333.444.555-21
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2014874562429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                João dos Santos sem CPF
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <!---->
          <!---->
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Maria da Silva sem CPF
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <!---->
          <!---->
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                João Noshow
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              333.444.555-21
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2014874562429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <!---->
    </div>
    <h2 class="title-sm mb-1 pt-1">
      Extrato
    </h2>
    <!---->
    <!---->
    <h2 class="title-sm mb-1 pt-1">
      Opções de segurança
    </h2>
    <div class="ada-card mb-2 o-hidden ac-outlined ac-rounded ac-soft">
      <!---->
      <div>
        <ul class="ada-list">
          <!---->
          <!---->
          <li class="ada-list-item"><a href="#/perfil/editar/" class="ada-button li-wrapper is-link is-clickable">
              <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Adicionar contato de emergência </span>
              <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
          <li class="ada-list-item"><button type="button" class="ada-button li-wrapper is-link is-clickable">
              <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Compartilhar minha viagem </span>
              <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></button></li>
        </ul> <span><!----></span> <span class="share-travel-popup"><!----></span>
      </div>
    </div>
    <h2 class="title-sm mb-1 pt-1">
      Ajuda 24h
    </h2>
    <div class="ada-card travel-faq o-hidden mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <ul class="ada-list is-divided">
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/perdi-um-item" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><!----><span class="w-100"><span class="d-flex fd-column"><span> Perdi um item no ônibus </span>
            <!----></span></span></span><span class="li-end-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/problemas-com-a-bagagem" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><!----><span class="w-100"><span class="d-flex fd-column"><span> Problemas com a bagagem </span>
            <!----></span></span></span><span class="li-end-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/rodoviaria/nao-consegui-viajar" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><!----><span class="w-100"><span class="d-flex fd-column"><span> Não consegui viajar </span>
            <!----></span></span></span><span class="li-end-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/rodoviaria" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><!----><span class="w-100"><span class="d-flex fd-column"><span> Estou com outro problema </span>
            <!----></span></span></span><span class="li-end-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
        <!---->
      </ul> <span><!----></span>
      <!----> <span class="ada-divider bg-grey-light" style="height: 1px;"></span>
      <h4 class="title-xs px-2 pt-2 pb-1">
        Precisa de ajuda? Conte com nosso time 24/7!
      </h4>
      <div class="tf-company-marketplace"><a href="#/perfil/viagens/1/ajuda/rodoviaria/empresa-rodoviaria" class="ada-button is-button b-color-primary">
          <!---->
          Falar com a empresa rodoviária
        </a></div>
    </div>
    <!---->
  </div>
  <!---->
  <!---->
</div>"
`;

exports[`Travel Details should render correctly 1`] = `
"<div class="travel-details pb-4">
  <!---->
  <div class="">
    <div class="ada-card ada-accordion travel-historic mb-2 ac-outlined ac-soft is-outlined">
      <!---->
      <div class="ada-toggleable">
        <div class="ada-toggleable-item ada-accordion-item">
          <div class="d-flex jc-between ai-center c-pointer">
            <div class="travel-historic-current">
              <div class="mt-2 mb-2 px-2">
                <fa-stub icon="[object Object]" size="lg" class="thc-icon"></fa-stub>
              </div>
              <div class="d-flex fd-column"><strong>
      Viagem confirmada
    </strong></div>
            </div> <button type="button" class="ada-button th-button is-button b-color-primary b-transparent b-small">
              <!---->
              Ver mais
              <fa-stub icon="[object Object]" class="ml-1"></fa-stub></button>
          </div>
          <section class="aai-content bg-white is-paddless" style="display: none;">
            <div class="travel-historic-events p-2">
              <div class="the-evento is-previous">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Reserva realizada
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-previous">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Pagamento confirmado
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-current">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color is-done"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Viagem confirmada
                    <!---->
                  </p>
                  <p class="thes-subtitle">
                    01 de jun às 12:00
                  </p>
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque iniciado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque realizado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Embarque encerrado
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
              <div class="the-evento is-next">
                <div class="thee-icon">
                  <fa-stub icon="[object Object]" size="sm" class="theei-color"></fa-stub>
                </div>
                <div class="the-track"></div>
                <div class="the-status">
                  <p class="thes-title">
                    Viagem concluída
                    <!---->
                  </p>
                  <!---->
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
    <!---->
    <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div class="resumo-viagem">
        <div class="rv-wrapper">
          <div><span class="rvw-label">Partida</span>
            <p class="text-sm"><strong class="text-md">Florianópolis</strong>
              / SC
            </p>
            <div class="rvw-horario-partida"><span class="text-sm">
          01/jun, 12:00
        </span>
              <!---->
            </div>
          </div>
          <div class="rvw-desembarque"><span class="rvw-label">Chegada</span>
            <p class="text-sm"><strong class="text-md">Porto Alegre</strong>
              / RS
            </p>
            <p class="text-sm">
              01/jun, 12:00
            </p>
          </div>
        </div>
        <!---->
        <div class="rv-footer">
          <p class="caption">
            Duração: 6h
          </p>
          <p class="caption color-primary">
            Horários aproximados
          </p>
        </div>
      </div>
    </div>
    <!---->
    <!---->
    <div>
      <!---->
    </div>
    <!---->
    <!---->
    <!---->
    <!---->
    <!---->
    <!---->
    <div class="travel-alerts mb-2">
      <div role="alert" class="ada-alert a-warning">
        <fa-stub icon="[object Object]" class="a-icon"></fa-stub>
        <div class="a-content">
          <p class="ac-title"> Identificamos um atraso na sua viagem </p>
          <p>
            Estamos avaliando o novo horário de chegada do ônibus. Em breve te atualizaremos por <strong>SMS, email, whatsapp, site e app</strong>.
          </p>
        </div>
        <!---->
      </div>
    </div>
    <div class="ada-card travel-info o-hidden mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div class="ti-company-container">
        <div class="ada-image ticc-foto-onibus mr-one-half ai-rounded tifo-outline" style="width: 76px; height: 76px; --object-fit: contain;">
          <!----><img width="76" height="76" alt="ônibus Cia. de Fretamento com nome muito grande" src="https://i.imgur.com/y9sTq12.jpg">
          <!---->
        </div>
        <div>
          <p class="caption text-grey">
            Fretamento Buser
          </p>
          <p class="title-xs">
            Cia. de Fretamento com nome muito grande
          </p>
          <p class="mt-half">
            Ônibus <span>- 2 andares </span></p>
        </div>
      </div>
      <div class="ti-chips">
        <div class="tic-chip">
          <p class="caption">
            Placa
          </p>
          <p title="Copiar placa do ônibus" class="ticc-text color-primary">
            OIE1234
            <fa-stub icon="[object Object]" class="ml-1 color-grey-dark"></fa-stub>
          </p>
        </div>
        <div class="tic-chip">
          <p class="caption">
            Reserva
          </p>
          <p title="Copiar código da reserva" class="ticc-text">
            Cód. asidha
            <fa-stub icon="[object Object]" class="ml-1"></fa-stub>
          </p>
        </div>
      </div>
      <ul class="ada-list">
        <!----> <span class="ada-divider mx-2 w-auto bg-grey-light" style="height: 1px;"></span>
        <li class="ada-list-item"><a href="#/ajuda/bagagem/politica-de-bagagens-fretamento-buser" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Bagagens aceitas </span>
            <!----></span></span>
        </span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
      </ul> <span><!----></span>
    </div>
    <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <h3 class="title-sm pb-2">
        Bagagens da sua reserva
      </h3>
      <!----> <a href="#/ajuda/bagagem/politica-de-bagagens-fretamento-buser" class="ada-button is-link" target="_blank">
        <!---->
        Ver dimensões das bagagens
      </a>
    </div>
    <!---->
    <!---->
    <div class="ada-card p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <div>
        <h3 class="fw-600 pb-1 title-sm">
          Itinerário
        </h3>
        <div class="text-sm fw-500 pb-2">
          <!---->
          <!---->
        </div>
        <ul class="travel-itinerary">
          <li class="ti-local is-embarque">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <p class="caption color-grey mb-half">
                Embarque
              </p>
              <h3 class="title-xs mb-half">
                Auto Posto Joiris
              </h3>
              <div class="d-flex fd-row ai-center"><a href="https://maps.google.com/0" target="_blank" class="text-sm">
                  Av. 43, nº 658, Alvorada. Auto Posto Joiris, Florianópolis - SC
                </a> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small" data-testid="button">
                  <!---->
                  <fa-stub icon="[object Object]" size="lg"></fa-stub>
                </button></div>
              <div class="tis-description text-sm mt-1">
                <p><strong>Instruções:</strong>
                  No terminal turístico JK, em frente à escolinha do Prof. Raimundo tem uma entrada onde os ônibus ficam estacionados. É lá que você deve esperar.
                </p>
                <!---->
              </div>
              <!---->
            </div>
          </li>
          <li class="ti-local">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content"><button type="button" class="ada-button w-auto is-button b-color-white b-rounded b-small">
                <!---->
                2 paradas no percurso
                <fa-stub icon="[object Object]" class="ml-1"></fa-stub></button>
              <!---->
            </div>
          </li>
          <li class="ti-local" style="display: none;">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <h3 class="text-sm fw-400">
                Pouso Alegre - MG
              </h3>
            </div>
          </li>
          <li class="ti-local" style="display: none;">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" size="xs" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <h3 class="text-sm fw-400">
                Campinas - SP
              </h3>
            </div>
          </li>
          <li class="ti-local is-desembarque">
            <!---->
            <div class="ti-section ti-road">
              <div class="tir-icon">
                <fa-stub icon="[object Object]" class="tiri-fa"></fa-stub>
              </div>
            </div>
            <div class="ti-section ti-content">
              <p class="caption color-grey mb-half">
                Desembarque
              </p>
              <h3 class="title-xs">
                Shopis Centis
              </h3>
              <div class="d-flex fd-row jc-start ai-center">
                <div><a href="https://maps.google.com/3" target="_blank" class="text-sm">
                    Shopping Center, Porto Alegre - RS
                  </a></div> <button type="button" class="ada-button ml-1 is-button b-color-primary b-transparent b-icon b-rounded b-small">
                  <!---->
                  <fa-stub icon="[object Object]" size="lg"></fa-stub>
                </button>
              </div>
              <!---->
              <!---->
            </div>
          </li>
        </ul>
        <!---->
      </div>
    </div>
    <!---->
    <div class="ada-card boarding-documents p-2 mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <h3 class="title-xs">
        Documentos para viajar
      </h3>
      <div class="documentos-viagem-nacional">
        <ul class="mt-2">
          <li class="dvn-item">
            <div class="d-flex">
              <fa-sprite-stub sprite="search" icon="far-fa-id-card" class="dvni-icon color-primary mr-1"></fa-sprite-stub>
              <p class="text-sm">
                Para embarcar você deve levar
                <strong>
            Carteira de identidade, RG, CNH, Passaporte originais com foto
          </strong>
                ou qualquer outro documento que esteja
                <a href="/ajuda/informacoes-sobre-minha-viagem/documentos-para-embarque">aqui</a>.
              </p>
            </div>
          </li>
          <li class="dvn-item">
            <div class="d-flex">
              <fa-sprite-stub sprite="search" icon="fas-fa-print-slash" class="dvni-icon color-primary mr-1"></fa-sprite-stub>
              <p class="text-sm">
                Não é necessário imprimir a reserva, basta levar o documento.
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <!---->
    <!---->
    <h2 class="title-sm mb-1 pt-1">
      Passageiros
    </h2>
    <!---->
    <div data-sentry-mask="" class="mb-2">
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Daniel Silva Campos Junior
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <p class="color-primary caption mt-one-half">
          Deve levar cadeirinha bebê conforto
        </p>
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 99911 8445
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              991.119.999-99
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              123243429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Companheiro do Fábio 1
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <p class="color-primary caption mt-one-half">
          Deve levar cadeirinha bebê conforto
        </p>
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 99118 4452
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              827.829.123-23
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2.888.999-9
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                C2
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              987.312.631-97
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              7962439
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Companheiro do Fábio com nome muito muito grande 2
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              Telefone
            </p>
            <p class="text-sm">
              (12) 77463 5543
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              213.321.315-98
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2011031388761
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft bg-grey-light">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Étila Amaral
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Menor de 16 anos
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              777.777.666-54
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              324339999
            </p>
          </div>
        </div>
        <!---->
        <!----> <span class="ada-badge mt-one-half bg-grey" small="">
    Removido
  </span>
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Tony Lâmpada
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              333.444.555-21
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2014874562429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                João dos Santos sem CPF
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <!---->
          <!---->
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                Maria da Silva sem CPF
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <!---->
          <!---->
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div class="ada-card card-passenger p-2 mb-1 ac-outlined ac-rounded ac-soft">
        <!---->
        <div class="d-flex jc-between">
          <div>
            <div class="pr-1">
              <p class="title-xs fw-wrap w-100">
                João Noshow
              </p>
            </div> <span class="ada-badge mt-1 bg-blue-light color-blue-dark" small="">
        Adulto
      </span>
          </div>
          <!---->
          <!---->
        </div>
        <!---->
        <div class="cp-infos">
          <div class="cpi-item">
            <p class="caption color-grey">
              Poltrona
            </p>
            <div class="poltrona">
              <!---->
              <div class="p-assento">
                <!---->
                <div class="d-flex fd-column text-sm">
                  <p class="">
                    Executivo
                    <fa-sprite-stub sprite="search" icon="far-fa-circle-info" alt="Veja os tipos de poltrona" class="p-info-icon"></fa-sprite-stub>
                  </p>
                </div>
              </div>
              <!---->
              <!---->
            </div>
          </div>
          <!---->
          <div class="cpi-item">
            <p class="caption color-grey">
              CPF
            </p>
            <p class="text-sm">
              333.444.555-21
            </p>
          </div>
          <div class="cpi-item">
            <p class="caption color-grey">
              RG:
            </p>
            <p class="text-sm">
              2014874562429
            </p>
          </div>
        </div>
        <!---->
        <!---->
        <!---->
      </div>
      <div role="alert" class="ada-alert mt-2 a-warning">
        <fa-stub icon="[object Object]" class="a-icon"></fa-stub>
        <div class="a-content">
          <!---->
          <p class="text-sm">
            É obrigatório o uso da cadeirinha Bebê Conforto para bebês até 18
            meses.
          </p>
        </div>
        <!---->
      </div>
      <div role="alert" class="ada-alert mt-2 a-warning">
        <fa-stub icon="[object Object]" class="a-icon"></fa-stub>
        <div class="a-content">
          <!---->
          <p class="text-sm">
            Crianças e adolescentes menores de 12 anos precisam do
            <strong>RG e Certidão de nascimento</strong>
            originais para embarcar.
          </p>
          <p class="text-sm">
            Menores de 16 anos que não estiverem acompanhados dos pais devem levar documento de autorização judicial assinado.
          </p> <a href="#/ajuda/comprar-passagem/viajar-com-criancas" class="p-documents">
            Veja a regra
          </a>
        </div>
        <!---->
      </div>
      <!---->
    </div>
    <h2 class="title-sm mb-1 pt-1">
      Extrato
    </h2>
    <!---->
    <!---->
    <h2 class="title-sm mb-1 pt-1">
      Opções de segurança
    </h2>
    <div class="ada-card mb-2 o-hidden ac-outlined ac-rounded ac-soft">
      <!---->
      <div>
        <ul class="ada-list">
          <!---->
          <!---->
          <li class="ada-list-item"><a href="#/perfil/editar/" class="ada-button li-wrapper is-link is-clickable">
              <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Adicionar contato de emergência </span>
              <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
          <li class="ada-list-item"><button type="button" class="ada-button li-wrapper is-link is-clickable">
              <!----><span class="d-flex ai-center w-100"><span class="liw-start-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span><span class="w-100"><span class="d-flex fd-column"><span> Compartilhar minha viagem </span>
              <!----></span></span></span><span class="li-end-icon color-grey-dark"><fa-stub icon="[object Object]"></fa-stub></span></button></li>
        </ul> <span><!----></span> <span class="share-travel-popup"><!----></span>
      </div>
    </div>
    <h2 class="title-sm mb-1 pt-1">
      Ajuda 24h
    </h2>
    <div class="ada-card travel-faq o-hidden mb-2 ac-outlined ac-rounded ac-soft">
      <!---->
      <ul class="ada-list is-divided">
        <!---->
        <li class="ada-list-item"><a href="#/perfil/viagens/1/ajuda/problemas-com-a-bagagem" class="ada-button li-wrapper is-link is-clickable">
            <!----><span class="d-flex ai-center w-100"><!----><span class="w-100"><span class="d-flex fd-column"><span> Problemas com a bagagem </span>
            <!----></span></span></span><span class="li-end-icon color-brand"><fa-stub icon="[object Object]"></fa-stub></span></a></li>
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
        <!---->
      </ul> <span><!----></span>
      <!----> <span class="ada-divider bg-grey-light" style="height: 1px;"></span>
      <h4 class="title-xs px-2 pt-2 pb-1">
        Precisa de ajuda? Conte com nosso time 24/7!
      </h4>
      <div class="fale-conosco tf-fale-conosco">
        <div class="fc-chat"><button type="button" class="ada-button is-button b-color-primary">
            <!---->
            Central de Ajuda
          </button> <a href="https://api.whatsapp.com/send?phone=5511913590868&amp;text=Olá!%20Tenho%20uma%20dúvida,%20poderia%20me%20ajudar?" class="ada-button is-button b-color-green b-outlined" target="_blank">
            <!---->
            <fa-stub icon="[object Object]" class="mr-1"></fa-stub>
            WhatsApp
          </a> <button type="button" class="ada-button is-button b-color-primary b-outlined">
            <!---->
            Envie um e-mail
          </button></div>
      </div>
    </div>
    <!---->
  </div>
  <!---->
  <!---->
</div>"
`;
