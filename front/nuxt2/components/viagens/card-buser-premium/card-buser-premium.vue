<template>
  <ada-card
    outlined
    rounded
    class="card-buser-premium"
  >
    <caption class="caption mb-1 color-grey">
      Mais conforto e tranquilidade
    </caption>
    <h2 class="title-sx fw-600 mb-2">
      Benefícios da sua viagem com <span class="color-primary">Buser Premium</span>.
    </h2>
    <div class="cbp-itens">
      <div v-for="item, index in premiumList" :key="index">
        <fa
          class="mr-half color-brand"
          :icon="item.icon"
          size="xs"
        />
        {{ item.text }}
      </div>
    </div>
  </ada-card>
</template>

<script setup>
import { faGem, faBellConcierge, faWifi, faSeatAirline } from '@fortawesome/pro-regular-svg-icons'
import { computed } from 'vue'

const premiumList = computed(() => [
  {
    icon: faGem,
    text: 'Sala VIP de embarque'
  },
  {
    icon: faWifi,
    text: 'Internet Starlink'
  },
  {
    icon: faBellConcierge,
    text: 'Serviço de bordo'
  },
  {
    icon: faSeatAirline,
    text: 'Poltrona reclinavel até 180°'
  }
])
</script>

<style lang="scss" scoped>
.card-buser-premium {
  padding: $spacing-2;
  display: flex;
  flex-direction: column;
  font-size: $font-size-sm;
  align-items: flex-start;

  .cbp-itens {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .cbp-itens > div {
    flex-basis: 100%;
    margin-bottom: $spacing-1;

    @media (min-width: $screen-tablet-min) {
      flex-basis: 50%;
    }

    @media (min-width: $screen-desktop-min) {
      flex-basis: 40%;
    }
  }
}
</style>
