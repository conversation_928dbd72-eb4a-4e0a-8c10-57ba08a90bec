<template>
  <div class="premium-banner">
    <div class="pb-gradient">
      <img
        src="~/assets/images/buser-premium-white.webp"
        class="pbg-logo"
      >
      <p class="pbg-text caption">
        <PERSON><PERSON> para {{ props.cidadeDestino }} com mais conforto.
      </p>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  cidadeDestino: {
    type: String,
    required: true
  }
})
</script>

<style lang="scss" scoped>
.premium-banner {
  height: 68px;

  @media (min-width: $screen-phone-min) {
    height: 40px;
  }

  .pb-gradient {
    width: 100%;
    height: 100%;
    background-image: linear-gradient(90deg, rgb(6 16 21 / 0%) 0%, rgb(6 16 21 / 70%) 40%, rgb(3 15 26 / 96%) 100%),
      url('~/assets/images/buser-premium-banner.webp');
    background-color: #030F1A;
    background-repeat: no-repeat;
    background-position: left;
    background-position-y: 0, -12px;
    background-size: 100%, 70%;
    display: flex;
    border-radius: $border-radius-md;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
    padding-right: $spacing-2;
    gap: $spacing-1;

    @media (min-width: $screen-phone-min) {
      background-image: linear-gradient(90deg, rgb(6 16 21 / 0%) 0%, rgb(6 16 21 / 70%) 20%, rgb(3 15 26 / 96%) 100%),
      url('~/assets/images/buser-premium-banner.webp');
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: $spacing-4;
      background-size: 100%, 20%;
      background-position-y: 0, -15px;
    }

    .pbg-logo {
      width: 100%;
      max-width: 150px;
    }

    .pbg-text {
      color: #fff;
      font-weight: 500;
    }
  }
}
</style>
