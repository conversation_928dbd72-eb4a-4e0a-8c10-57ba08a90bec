<template>
  <div
    v-if="badge"
    class="badge-grupo"
  >
    <ada-badge
      class="bg-badge fw-normal text-sm"
      :color="badge.color"
      :class="badge.class"
      rounded
    >
      <fa-sprite
        v-if="badge.icon"
        class="bgb-icon"
        :icon="badge.icon"
        sprite="search"
      />
      {{ badge.body }}
    </ada-badge>
  </div>
</template>

<script>
import { mapActions } from 'pinia'
import { useSearchStore } from '~/stores/search.js'

export default {
  name: 'BadgeGrupo',
  props: {
    grupo: Object,
    keyBadge: String
  },
  ssrComputedCache: true,
  computed: {
    badge() {
      // A ordem dos available badges define a ordem de prioridade das tags. A primeira que for válida nessa lista será exibida
      const availableBadges = {
        'menor-preco': { body: 'Menor preço', color: 'green-light' },
        'mais-confortavel': { body: '<PERSON><PERSON> confortá<PERSON>', color: 'blue-light' },
        'mais-rapido': { body: '<PERSON><PERSON> rápido', color: 'brand-lightest' }
      }

      if (this.keyBadge) {
        const badge = availableBadges[this.keyBadge]
        this.setGrupoBadge(this.grupo, badge.body)
        return badge
      }
      return null
    }
  },
  methods: {
    ...mapActions(useSearchStore, ['setGrupoBadge'])
  }
}
</script>

  <style lang="scss" scoped>
  .badge-grupo {
    display: flex;
    gap: $spacing-half;

    .bg-badge {
      padding: 0 $spacing-1;

      .bgb-icon {
        height: 16px;
        width: 16px;
        margin-right: $spacing-half;
      }
    }
  }
  </style>
