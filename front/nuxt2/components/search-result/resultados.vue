<template>
  <div class="resultados" :style="cssVars">
    <ida-volta-tab class="ida-volta" />

    <resultados-por-dia-tab
      v-if="selectedDate"
      :selected-date="selectedDate"
      :origem-slug="origemSlug"
      :destino-slug="destinoSlug"
      @change-selected-date="changeSelectedDate"
    />

    <ada-loader v-show="loading" class="r-loader" />

    <div v-show="!loading" class="r-container">
      <template v-if="hasGruposTrechoPesquisado">
        <template v-if="hasResultadosSuficientes">
          <div class="rc-ajustes-usuario is-sticky">
            <menu-filtros class="show-tablet" :is-black-friday="permiteLayoutBlackfriday" />
            <ordenacoes class="rc-ordenacoes" />
            <div v-if="permiteLayoutBlackfriday" class="black-friday-buser-container">
              <div class="bfb-content" />
            </div>
          </div>

          <ada-divider color="grey-light" class="rc-ajustes-usuario-scroll-divider" />
        </template>

        <div class="rc-container-resultados mt-3">
          <menu-filtros-desktop
            v-if="hasResultadosSuficientes"
            class="rcrc-filtros-desktop"
          />
          <div class="rcrc-grupos">
            <premium-banner
              v-if="hasBuserPremium"
              :cidade-destino="cidadeDestino"
              class="mx-half mb-2"
            />
            <list-highlights v-if="grupoDestaque" class="mb-3" />
            <list-recommendations
              v-else-if="showRecomendations"
              class="mb-3"
              :class="gruposRecomendadosBased.length ? 'rc-list-recommendations' : 'rc-list-recommendations-only-result'"
            />
            <h3 v-if="forceSectionTitle" class="rc-section-title">
              {{ grupoDestaque ? "Mais horários disponíveis" : "Todos os horários" }}
            </h3>
            <lista-grupos
              :grupos-disponiveis="gruposTrechoPesquisado"
              :ordenacao="ordenacao"
              :ordem="ordem"
              :force-title="hasGruposProximoDia"
              :force-section-title="forceSectionTitle"
            />
            <template v-if="hasGruposProximoDiaToShow">
              <h2 class="rc-pos-meia-noite-text">
                <strong>Horários após meia-noite</strong>
              </h2>
              <grupos-por-dia :grupos-por-dia="gruposProximoDiaToShow" :force-title="true" :is-grupo-da-madrugada="true" />
            </template>

            <secao-cidade-proxima v-if="showCidadeProxima" class="rc-secao-cidade-proxima" />
            <alert-pessoas-online class="mt-5" />
          </div>
        </div>

        <client-only>
          <feedback-trechos v-if="showFeedbackTrechos" class="mt-3" />
        </client-only>
      </template>
      <nao-encontrou-grupos v-if="!hasGruposTrechoPesquisado" />
    </div>
    <poltronas-modal />
    <copia-dados-grupo v-if="isRevendedor && hasSelectedGroups" />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { mapState, mapWritableState } from 'pinia'
import { groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { FILTRO_KEYS, useFiltroStore } from '~/stores/filtro.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSearchStore } from '~/stores/search.js'
import { useSettingsStore } from '~/stores/settings.js'
import listaGrupos from '~/components/shared/grupos/lista-grupos/lista-grupos.vue'
import listRecommendations from '~/components/shared/list-recommendations/list-recommendations.vue'
import resultadosPorDiaTab from '~/components/shared/resultados-por-dia-tab/resultados-por-dia-tab.vue'
import skeletonFiltroDesktop from '~/components/search-result/menu-filtros/menu-filtros-desktop/skeleton-menu-filtros-desktop.vue'
import menuFiltros from '~/components/search-result/menu-filtros/menu-filtros.vue'
import ordenacoes from '~/components/search-result/ordenacoes/ordenacoes.vue'
import premiumBanner from '~/components/search-result/premium-banner.vue'
import alertPessoasOnline from '~/components/search-result/resultados/alert-pessoas-online.vue'
import idaVoltaTab from '~/components/search-result/resultados/ida-volta-tab.vue'
import poltronasModal from '~/components/search-result/resultados/poltronas-modal.vue'

export default {
  name: 'Resultados',
  components: {
    listRecommendations,
    alertPessoasOnline,
    resultadosPorDiaTab,
    idaVoltaTab,
    poltronasModal,
    listaGrupos,
    menuFiltros,
    ordenacoes,
    premiumBanner,
    listHighlights: () => ({ component: import('~/components/shared/list-highlights/list-highlights.vue') }),
    gruposPorDia: () => ({ component: import('~/components/shared/grupos/grupos-por-dia/grupos-por-dia.vue') }),
    naoEncontrouGrupos: () => ({ component: import('~/components/search-result/nao-encontrou-grupos/nao-encontrou-grupos.vue') }),
    menuFiltrosDesktop: () => ({ component: import('~/components/search-result/menu-filtros/menu-filtros-desktop/menu-filtros-desktop.vue'), loading: skeletonFiltroDesktop }),
    feedbackTrechos: () => import('~/components/feedback-trechos/feedback-trechos.vue'),
    secaoCidadeProxima: () => import('~/components/search-result/secao-cidade-proxima.vue'),
    copiaDadosGrupo: () => import('~/components/revendedor/copia-dados-grupo.vue')
  },
  computed: {
    ...mapState(useFiltroStore, {
      temPopupFiltrosAberta: 'popupAberta',
      temFiltrosAplicados: 'temFiltrosAplicados'
    }),
    ...mapState(useSettingsStore, ['permiteLayoutBlackfriday']),
    ...mapState(useSearchStore, [
      'loading',
      'trecho',
      'selectedDate',
      'hasGruposTrechoPesquisado',
      'hasResultadosSuficientes',
      'hasGruposProximoDia',
      'isVolta',
      'gruposTrechoPesquisado',
      'quantidadeGruposTrechoPesquisado',
      'gruposProximoDiaFiltrados',
      'gruposProximoDia',
      'gruposRecomendadosBased',
      'gruposRecomendadosConexao',
      'grupoDestaque'
    ]),
    ...mapState(useRevendedorStore, ['hasSelectedGroups', 'isRevendedor', 'isRevendedorSite']),
    ...mapWritableState(useSearchStore, ['ordenacao', 'ordem', 'gruposRecomendados']),
    origemSlug() {
      return this.trecho?.origem?.slug
    },
    destinoSlug() {
      return this.trecho?.destino?.slug
    },
    hasGruposProximoDiaToShow() {
      return this.gruposProximoDiaToShow?.grupos?.length > 0
    },
    gruposProximoDiaToShow() {
      return this.temFiltrosAplicados ? this.gruposProximoDiaFiltrados : this.gruposProximoDia
    },
    cssVars() {
      return {
        // A modal quando aberta precisa ficar acima da taskbar, no entanto ela
        // é filho de um elemento que precisa ficar abaixo quando está fechada,
        // por isso precisamos desse ajuste de z-index dinâmico.
        '--z-index-increment-popup-filtros': this.temPopupFiltrosAberta ? '0' : '-1'
      }
    },
    showCidadeProxima() {
      return this.quantidadeGruposTrechoPesquisado < 5
    },
    showFeedbackTrechos() {
      const dataIda = this.$route.query.ida
      const dataVolta = this.$route.query.volta

      const exibeIda = dataIda && dayjs(dataIda).isSameOrAfter(dayjs().add(3, 'day').startOf('day'))
      const exibeVolta = dataVolta && dayjs(dataVolta).isSameOrAfter(dayjs().add(3, 'day').startOf('day'))

      return exibeIda || (exibeVolta && this.isVolta(this.$route))
    },
    cidadeDestino() {
      return this.trecho?.destino?.name
    },
    hasBuserPremium() {
      return this.gruposTrechoPesquisado.some(item => item?.grupos ? groupsHaveBuserPremium(item.grupos) : false) || groupsHaveBuserPremium(this.gruposRecomendadosBased)
    },
    forceSectionTitle() {
      return this.grupoDestaque || this.gruposRecomendadosBased?.length > 0
    },
    showRecomendations() {
      return (this.gruposRecomendadosBased?.length > 0 || this.gruposRecomendados || Object.keys(this.gruposRecomendadosConexao).length) && !this.isRevendedorSite
    }
  },
  methods: {
    changeSelectedDate(newDate) {
      const { query: currentQuery } = this.$route

      if (this.isVolta(this.$route) && currentQuery.volta === newDate) return

      if (!this.isVolta(this.$route) && currentQuery.ida === newDate) return
      const newQuery = this.isVolta(this.$route) ? { volta: newDate } : { ida: newDate }
      const cleanedCurrentQuery = this.limpaFiltrosQuerystring(currentQuery)
      this.$router.push({
        query: {
          ...cleanedCurrentQuery,
          ...newQuery
        }
      })
    },
    limpaFiltrosQuerystring(query) {
      for (const keyFiltro of FILTRO_KEYS) {
        delete query[keyFiltro]
      }
      return query
    }
  }
}
</script>
<style lang="scss" scoped>
@mixin view-point-full-width {
  // Deixa o elemento com a largura do viewport, mesmo quando dentro de um
  // pai de largura limitada. Assim podemos colocar um background sólido que
  // ocupe a tela inteira mas sem quebrar o layout interno de 1200pxs
  width: 100vw; // Ocupa a largura do viewport, mas fica descentralizado à tela por conta da largura limitada do pai
  margin-inline: calc(50% - 50vw); // Centraliza o elemento. É redução da expressão (-100vw / 2) + (100% / 2)
}

.resultados {
  $height-ajustes-usuario: 64px;
  $padding-ada-container: $spacing-2;

  --z-index-ajustes-usuario: calc(#{ $z-index-taskbar } + var(--z-index-increment-popup-filtros));

  .r-loader {
    padding-top: $spacing-6;
    padding-bottom: $spacing-10;
    display: flex;
    justify-content: center;
  }

  .r-container {
    .rc-ajustes-usuario,
    .rc-ajustes-usuario-scroll-divider {
      @media (min-width: $screen-phone-min) {
        @include view-point-full-width;

        padding-inline: calc(-50% + 50vw); // Limita largura do conteúdo interno para 100% do pai. É redução da expressão (100vw / 2) - (100% / 2)

      }

      @media (max-width: $screen-phone-max) {
        // Hoje a técnica acima não funciona em viewport < 360px porque temos um
        // `zoom: 0.88` que buga o cálculo de unidades de viewport (`vw`) então,
        // apenas shifto o elemento no valor do padding do `ada-container`
        margin-inline: (-$padding-ada-container);
        padding-inline: $spacing-2;
      }
    }

    .rc-ordenacoes {
      @media (min-width: $screen-tablet-min) {
        font-size: $font-size-sm;
      }
    }

    .rc-container-resultados {
        display: flex;
        justify-content: space-between;

      .rcrc-filtros-desktop {
        max-width: 230px;
        padding-right: $spacing-3;

        @media (max-width: $screen-tablet-max) {
          display: none;
        }
      }

      .rcrc-grupos {
        flex-grow: 1;

        .rc-list-recommendations {
          @media (min-width: $screen-phone-min) and (max-width: $screen-tablet-max) {
            @include view-point-full-width;
          }

          @media (max-width: $screen-phone-max) {
            margin-inline: (-$spacing-2);
          }
        }

        .rc-list-recommendations-only-result {
          @media (max-width: $screen-tablet-max) {
            padding-inline: calc($spacing-1 + $border-width-hairline); // a borda faz parecer um pouco maior que os outros elementos
          }
        }

        .rc-secao-cidade-proxima {
          margin-top: $spacing-half;

          @media (min-width: $screen-tablet-min) {
            margin-top: $spacing-6;
          }
        }

        .rc-section-title {
          margin-top: $spacing-4;
          margin-bottom: $spacing-2;
          padding-left: $spacing-3;

          @media (min-width: $screen-tablet-min) {
            padding: 0;
          }
        }
      }
    }

    .rc-ajustes-usuario {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: $spacing-2;
      height: $height-ajustes-usuario;
      background-color: $color-grey-lightest;

      &.is-sticky {
        position: sticky;
        top: calc(#{$taskbar-height-mobile} + var(--safe-area-inset-top));
        z-index: var(--z-index-ajustes-usuario);

        @media (min-width: $screen-tablet-min) {
          top: calc(#{$taskbar-height-desktop} + var(--safe-area-inset-top));
        }
      }
    }

    .rc-ajustes-usuario-scroll-divider {
      $height-divider: 1px;

      margin-top: (-$height-divider); // Fica escondido atrás dos filtros
      width: auto;
      position: sticky;
      top: calc(#{$taskbar-height-mobile} + #{$height-ajustes-usuario} + var(--safe-area-inset-top)); // Mas quando triggar o `sticky`, fica visível porque cola logo abaixo dessa altura
      z-index: calc(var(--z-index-ajustes-usuario) - 1);

      @media (min-width: $screen-tablet-min) {
        top: calc(#{$taskbar-height-desktop} + #{$height-ajustes-usuario} + var(--safe-area-inset-top));
      }
    }

    .rc-pos-meia-noite-text {
      font-size: $font-size-xl;
      margin: $spacing-3 0 $spacing-1;
      font-weight: $font-weight-regular;
    }

  }
}

.ida-volta{
  margin-top: 0;
}

.bfb-content {
  width: 200vw;
  height: 16px;
  background-image: url('~/assets/images/black-friday/infinite-loop-bf.webp');
  background-repeat: repeat-x;
  animation: scroll-horizontal 6s linear infinite;
}

.black-friday-buser-container {
  width: 100%;
  position: absolute;
  top: 64px;
  left: 8px;
  overflow: hidden;

  @media (min-width: $screen-tablet-min) {
    display: none;
  }
}

@keyframes scroll-horizontal {
  0% {
    transform: translate(0);
  }

  100% {
    transform: translate(-139px); /* image width */
  }
}
</style>
