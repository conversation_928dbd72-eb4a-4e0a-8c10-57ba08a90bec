<template>
  <div>
    <ada-loader v-show="localLoading" class="r-loader" />
    <div
      v-if="!$fetchState.pending && !hasGruposTrechoPesquisado && !localLoading"
      class="nao-encontrou-grupos"
    >
      <mensagem-cabecalho :dias-com-grupo="diasComGrupos" :has-other-options="hasOtherOptions" />

      <!-- Prioritariamente exibimos os trechos alternativos -->
      <trechos-alternativos v-if="hasGruposTrechoAlternativos" />

      <!-- Caso não tenha trechos alternativos, exibimos as cidades proximas, caso existam -->
      <cidades-proximas v-else-if="hasCidadesProximas" :cidades-proximas="cidadesProximas" />

      <!-- Caso não tenha cidades próximas, exibimos as conexões, caso existam -->
      <conexoes v-else-if="temConexao" />

      <!-- Caso não existam nem trechos alternativos nem conexões ... -->
      <template v-else>
        <!-- Prioritariamente exibo viagens a partir da origem e viagens que chegam no destino -->
        <template v-if="hasSugestoesOrigemOuDestino">
          <p class="title-sm mt-4">
            Viagens que podem te interessar
          </p>
          <lista-trechos />
        </template>

        <!-- E caso não encontre nenhum caso acima, exibo um conteudo para SEO -->
        <ada-container
          v-else
          class="neg-seo mt-5"
        >
          <LazyHydrate
            v-slot="{ hydrated }"
            when-visible
          >
            <seo-bundle
              v-if="hydrated"
              :components="seoComponents"
              :articles="articles"
              :midias="midias"
            />
          </LazyHydrate>
        </ada-container>
      </template>

      <!-- Exibo o card para a captura de lead sempre -->
      <ada-card
        outlined
        rounded
        class="neg-lead"
      >
        <captura-lead />
      </ada-card>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'pinia'
import LazyHydrate from 'vue-lazy-hydration'
import { getCidadeProximaV2 } from '~api/place.js'
import { getNearbyDepartures } from '~api/search.js'
import { useBlogStore } from '~/stores/blog.js'
import { useConexaoStore } from '~/stores/conexao.js'
import { useMidiasStore } from '~/stores/midias.js'
import { useSearchStore } from '~/stores/search.js'
import { useToastStore } from '~/stores/toast.js'
import capturaLead from '~/components/search-result/captura-lead/captura-lead.vue'
import cidadesProximas from '~/components/search-result/cidades-proximas/cidades-proximas.vue'
import conexoes from '~/components/search-result/conexoes/conexoes.vue'
import mensagemCabecalho from '~/components/search-result/nao-encontrou-grupos/mensagem-cabecalho-v2.vue'
import listaTrechos from '~/components/search-result/trechos-sugeridos/lista-trechos.vue'
import trechosAlternativos from '~/components/search-result/trechos-sugeridos/trechos-alternativos.vue'

async function loadSeoContent($route, $pinia) {
  const searchMeta = Array.isArray($route.meta) ? $route.meta[0] : $route.meta
  const blogStore = useBlogStore($pinia)
  const toastStore = useToastStore($pinia)
  const midiasStore = useMidiasStore($pinia)
  const searchStore = useSearchStore($pinia)
  const promises = [
    blogStore.getArticles(6),
    midiasStore.getMidias(2),
    searchStore.fetchSuggestions({
      ...$route.params,
      limit: 3,
      withSEOContent: false,
      promo: searchMeta?.isPromo
    })
  ]

  const [articlesResult, midiasResult, suggestionsResult] = await Promise.allSettled(promises)
  const articlesError = articlesResult.status === 'rejected'
  const midiasError = midiasResult.status === 'rejected'
  const suggestionsError = suggestionsResult.status === 'rejected'

  if (articlesError) {
    console.error(articlesResult.reason?.message)
  }

  if (midiasError) {
    console.error(midiasResult.reason?.message)
  }

  if (suggestionsError) {
    toastStore.open({
      message: suggestionsResult.reason?.message || 'Desculpe, ocorreu um erro desconhecido.',
      type: 'error'
    })
  }

  return {
    articles: articlesError ? null : articlesResult.value,
    midias: midiasError ? null : midiasResult.value
  }
}

export default {
  name: 'NaoEncontrouGrupos',
  components: {
    listaTrechos,
    capturaLead,
    LazyHydrate,
    conexoes,
    mensagemCabecalho,
    cidadesProximas,
    trechosAlternativos,
    seoBundle: () => import('~/components/shared/seo/seo-bundle/seo-bundle.vue')
  },
  data() {
    return {
      midias: [],
      articles: [],
      diasComGrupos: null,
      cidadesProximas: {},
      localLoading: true
    }
  },
  async fetch() {
    this.localLoading = true

    if (this.hasGruposTrechoPesquisado) {
      this.localLoading = false
      return
    }

    // Não preciso carregar mais nada caso já existam grupos alternativos para serem exibidos
    if (this.hasGruposTrechoAlternativos) {
      this.localLoading = false
      return
    }

    const { origem, destino } = this.$route.params

    const nearbyDeparturesParams = {
      origemSlug: origem,
      destinoSlug: destino,
      date: this.$route.query.ida,
      incluirTrechoAlternativo: false
    }
    const nearbyDepartures = await getNearbyDepartures(nearbyDeparturesParams)
    this.diasComGrupos = nearbyDepartures.dias_com_grupos

    // Procuro se tem cidades próximas
    this.cidadesProximas = await this.getCidadesProximas(origem, destino, this.$route.query.ida)

    // Se não tem cidades próximas, procuro se existem conexões
    if (this.cidadesProximas && !Object.values(this.cidadesProximas)?.filter(e => e.cidade_proxima_type === 'origem' && Object.keys(e.trechos_by_date).length)) {
      await this.loadConexao(origem, destino)

      // Caso também não encontre conexões, e não tem sugestões de origem ou destino, mostra um conteudo para SEO
      if (!this.temConexao && !this.hasSugestoesOrigemOuDestino) {
        const { articles, midias } = await loadSeoContent(this.$route, this.$pinia)
        this.articles = articles
        this.midias = midias
      }
    }

    this.localLoading = false
  },
  watch: {
    '$route.query': function(newQuery, oldQuery) {
      const novaIda = newQuery?.ida
      const antigaIda = oldQuery?.ida
      if (novaIda !== antigaIda) {
        this.$fetch()
      }
    }
  },
  computed: {
    ...mapState(useConexaoStore, ['temConexao']),
    ...mapState(useSearchStore, [
      'hasGruposTrechoPesquisado',
      'hasGruposTrechoAlternativos',
      'sugestoesCidadeOrigem',
      'sugestoesCidadeDestino'
    ]),
    hasSugestoesOrigem() {
      return !!this.sugestoesCidadeOrigem?.saindo_de?.length
    },
    hasSugestoesDestino() {
      return !!this.sugestoesCidadeDestino?.indo_para?.length
    },
    hasSugestoesOrigemOuDestino() {
      return this.hasSugestoesOrigem || this.hasSugestoesDestino
    },
    hasCidadesProximas() {
      if (this.cidadesProximas) {
        return Object.keys(this.cidadesProximas).length
      }
      return 0
    },
    hasOtherOptions() {
      return this.hasGruposTrechoAlternativos || this.hasCidadesProximas || this.temConexao || this.hasSugestoesOrigemOuDestino
    },
    seoComponents() {
      const components = ['top-destinos']

      if (this.articles?.length) {
        components.push('article-section')
      }

      if (this.midias?.length) {
        components.push('midia-section')
      }

      return components
    }
  },
  methods: {
    ...mapActions(useConexaoStore, {
      loadConexao: 'load'
    }),
    async getCidadesProximas(origem, destino, data) {
      try {
        const cidadesProximas = await getCidadeProximaV2({
          slugOrigem: origem,
          slugDestino: destino,
          dateSearch: data
        })

        return cidadesProximas
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .r-loader {
    display: flex;
    justify-content: center;
    margin: $spacing-5 0 $spacing-10;
  }

  .nao-encontrou-grupos {
    .neg-lead {
      margin-top: $spacing-6;
      max-width: 480px;
      padding: $spacing-4;

      @media (min-width: $screen-tablet-min) {
        margin: $spacing-4 auto 0;
        max-width: initial;
      }
    }

    .neg-seo {
      height: auto;
      padding: 0;
    }
  }
</style>
