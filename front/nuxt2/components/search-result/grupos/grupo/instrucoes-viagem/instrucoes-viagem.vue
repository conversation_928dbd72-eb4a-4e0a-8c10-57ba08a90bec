<template>
  <ada-card
    outlined
    rounded
    class="p-2"
  >
    <section class="instrucoes-viagem">
      <div
        v-if="isMarketplace"
        class="text-md fw-bold"
      >
        Revenda rodoviária
      </div>

      <div
        v-else
      >
        <img
          :src="buserLogo"
          height="14px"
          alt="logo buser"
        >
      </div>

      <documentos-viagem-internacional-list
        v-if="isViagemInternacional && hasDocumentacaoDisponivel"
        :documentacao-viagem-internacional="documentacaoViagemInternacional[paisDestino]"
        class="iv-items"
      >
        <template #listIcon>
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
        </template>
      </documentos-viagem-internacional-list>
      <ul
        class="iv-items"
      >
        <li
          v-if="!isViagemInternacional"
          class="ivi-item"
        >
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            Embarque com documento original com foto (CIN, RG, CNH, passaporte).
          </p>
        </li>
        <li class="ivi-item">
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            {{ textBagagemInclusa }}
          </p>
        </li>
        <li class="ivi-item">
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            <strong>Seguro</strong> Buser gratuito.
          </p>
        </li>
      </ul>
      <ul
        v-if="isMarketplace"
        class="iv-items"
      >
        <li class="ivi-item">
          <fa
            :icon="faCircleInfo"
            class="ivii-icon"
          />
          <p class="text-sm">
            Remarcação até 3 horas antes do embarque direto com a empresa parceira.
          </p>
        </li>
        <li class="ivi-item">
          <fa
            :icon="faCircleInfo"
            class="ivii-icon"
          />
          <p class="text-sm">
            Cancelamento até 3 horas antes do embarque com taxa de 5% do valor pago.
          </p>
        </li>
      </ul>
      <ul
        v-else
        class="iv-items"
      >
        <li class="ivi-item">
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            <strong> Remarcação gratuita </strong> até 1 hora antes do embarque.
          </p>
        </li>
        <li class="ivi-item">
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            <strong>Cancelamento gratuito</strong> até 1 hora antes do embarque em viagens de Fretamento Buser.
          </p>
        </li>
        <li class="ivi-item">
          <fa
            :icon="faCheck"
            class="ivii-icon color-green"
          />
          <p class="text-sm">
            Cupom de 80% do valor pago se você perder sua viagem, para fazer uma nova reserva.
          </p>
        </li>
      </ul>
    </section>
  </ada-card>
</template>

<script>
import { faCircleInfo } from '@fortawesome/pro-regular-svg-icons'
import { faCheck } from '@fortawesome/pro-solid-svg-icons'
import buserPremiumLogo from '~/assets/images/buser-premium.webp'
import buserFretamentoLogo from '~/assets/images/fretamento.webp'
import { getPaisDestino, groupsHaveBuserPremium } from '~/helpers/grouphelper.js'
import { DOCUMENTACAO_VIAGEM_INTERNACIONAL } from '~/helpers/travelhelper.js'
import documentosViagemInternacionalList from '~/components/shared/list-docs-viagem-internacional.vue'

export default {
  name: 'InstrucoesViagem',
  // TODO: Ao migrar substitua por <RenderCacheable>
  serverCacheKey(props) {
    return props.grupo.id
  },
  props: {
    grupo: {
      type: Object,
      required: true
    },
    isMarketplace: {
      type: Boolean,
      default: false
    },
    isViagemInternacional: {
      type: Boolean,
      default: false
    }
  },
  components: {
    documentosViagemInternacionalList
  },
  data() {
    return {
      documentacaoViagemInternacional: DOCUMENTACAO_VIAGEM_INTERNACIONAL,
      buserFretamentoLogo,
      buserPremiumLogo,
      faCheck,
      faCircleInfo
    }
  },
  computed: {
    paisDestino() {
      return getPaisDestino(this.grupo)
    },
    hasDocumentacaoDisponivel() {
      return !!this.documentacaoViagemInternacional[this.paisDestino]
    },
    textBagagemInclusa() {
      if (this.grupo?.destino?.desembarque_rapido) {
        return 'Apenas bagagem de mão.'
      }

      return 'Bagagem de mão e bagageiro inclusos.'
    },
    isBuserPremium() {
      return groupsHaveBuserPremium([this.grupo])
    },
    buserLogo() {
      return this.isBuserPremium ? this.buserPremiumLogo : this.buserFretamentoLogo
    }
  }
}
</script>

<style lang="scss" scoped>
.instrucoes-viagem {
  .iv-items {
    .ivi-item {
      display: flex;
      font-weight: 100!important;
      font-size: 14px!important;
      margin: $spacing-2 0;

      .ivii-icon {
        margin-right: $spacing-1;
      }
    }
  }
}

</style>
