<template>
  <div class="card-buser-premium">
    <div class="cbb-content">
      <div v-for="item, index in premiumList" :key="index">
        <fa
          class="mr-half color-brand"
          :icon="item.icon"
          size="xs"
        />
        {{ item.text }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { faGem, faBellConcierge, faWifi, faSeatAirline } from '@fortawesome/pro-regular-svg-icons'
import { computed } from 'vue'

const premiumList = computed(() => [
  {
    icon: faGem,
    text: 'Sala VIP'
  },
  {
    icon: faBellConcierge,
    text: 'Serviço de bordo'
  },
  {
    icon: faSeatAirline,
    text: 'Poltrona reclina até 180°'
  },
  {
    icon: faWifi,
    text: 'Internet Starlink'
  }
])
</script>

<style lang="scss" scoped>
.card-buser-premium {
  .cbb-content {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
    padding: $spacing-2 $spacing-3;
    color:$color-white;
    width: 100%;
    height: 130px;
    border-radius: 16px;
    font-size: $font-size-xs;
    background: linear-gradient(90deg, rgb(6 16 21 / 0%) 0%, rgb(6 16 21 / 0%) 30%, rgb(0 0 0 / 96%) 100%), url('~/assets/images/buser-premium-banner.webp') center / cover no-repeat;
    backdrop-filter: blur(0) 0%, blur(2.9px) 50%, blur(0) 70%, blur(0) 100%;

    @media (min-width: $screen-phone-min) {
      background-position-y: 25%;
    }

    @media (min-width: $screen-tablet-min) {
      background: linear-gradient(90deg, rgb(6 16 21 / 0%) 0%, rgb(6 16 21 / 0%) 30%, rgb(0 0 0 / 96%) 100%), url('~/assets/images/buser-premium-banner.webp') center / cover no-repeat;
      background-position-y: -50px;
    }

    @media (min-width: $screen-desktop-min) {
      background-position-y: -30px;
    }
  }
}
</style>
