<template>
  <ada-form
    ref="form"
    class="payment-credit-card-form"
  >
    <ada-input
      ref="ccNumber"
      v-model="creditCardInfo.card"
      name="cc-number"
      autocomplete="cc-number"
      x-autocompletetype="cc-number"
      inputmode="numeric"
      :rules="[rules.required, rules.minLen(14), creditCardRules]"
      :mask="creditCardMask"
      label="Número do cartão de crédito"
      placeholder="0000 0000 0000 0000"
      maxlength="19"
      autofocus
      class="pccf-input"
    >
      <template #end>
        <brand-logo
          :brand="creditCardInfo.cardBrand"
          class="pccf-logo"
        />
      </template>
    </ada-input>

    <div class="pccf-input-group">
      <ada-input
        v-model="creditCardInfo.validade"
        name="cc-exp"
        autocomplete="cc-exp"
        inputmode="numeric"
        :rules="[rules.required, rules.mmaa]"
        mask="00/00"
        label="Valido até"
        placeholder="MM/AA"
        maxlength="5"
        class="pccf-input"
      />

      <ada-input
        v-model="creditCardInfo.cvv"
        name="cvv"
        autocomplete="cc-csc"
        :rules="[rules.required, rules.number, rules.minLen(3), cvvRules]"
        label="Código de segurança"
        placeholder="CVV"
        maxlength="4"
        inputmode="numeric"
        class="pccf-input is-cvv"
      >
        <template #end>
          <ada-tooltip
            position="top"
            strategy="fixed"
            class="mx-2"
          >
            <template #activator="{ on }">
              <fa
                :icon="faCircleInfo"
                class="text-brand"
                v-on="on"
              />
            </template>
            <p>
              {{ creditCardInfo.cardBrand === 'amex' ? '4 dígitos na frente do cartão' : '3 números no verso do cartão' }}
            </p>
          </ada-tooltip>
        </template>
      </ada-input>
    </div>

    <ada-input
      v-model="creditCardInfo.cardName"
      name="nome"
      autocomplete="cc-name"
      :rules="[rules.required, rules.creditCardHolderName]"
      label="Nome do titular"
      class="pccf-input"
    />

    <div class="pccf-input-group">
      <ada-input
        v-model="creditCardInfo.cpf"
        name="cpf"
        :rules="[rules.required, rules.cpf]"
        mask="000.000.000-00"
        label="CPF do titular do cartão"
        placeholder="000.000.000-00"
        maxlength="14"
        inputmode="numeric"
        class="pccf-input"
      />

      <ada-input
        ref="cep"
        v-model="creditCardInfo.cep"
        data-testid="input-cep"
        name="cep"
        :rules="[rules.required, rules.cep]"
        mask="00.000-000"
        label="CEP"
        placeholder="00.000-000"
        maxlength="10"
        inputmode="numeric"
        class="pccf-input is-cep"
        @blur="addAddressToCreditCardInfo"
      />
    </div>

    <div
      class="pccf-input-group"
    >
      <ada-input
        v-model="creditCardInfo.street"
        name="street"
        :rules="[rules.required]"
        label="Endereço do titular do cartão"
        placeholder="Endereço do titular do cartão"
        class="pccf-input"
      />

      <ada-input
        v-model="creditCardInfo.streetnum"
        name="streetNumber"
        :rules="[rules.required, rules.number]"
        label="Número"
        inputmode="numeric"
        class="pccf-input is-street-number"
      />
    </div>

    <ada-select
      v-model="selectedParcela"
      :items="parcelamentoOptionsCartao"
      :rules="[rules.required]"
      :loading="isLoadingParcelamentoOptions"
      :disabled="!parcelamentoOptionsCartao.length"
      item-value="parcela_count"
      label="Parcelas"
      class="pccf-select"
      return-object
      @input="setParcelas"
    />
    <ada-button
      block
      class="mb-1"
      :loading="loading"
      @click="confirm"
    >
      {{ saveAndPayMode ? 'Confirmar e Pagar' : 'Confirmar' }}
    </ada-button>
  </ada-form>
</template>

<script>
import { faCircleInfo } from '@fortawesome/pro-solid-svg-icons'
import { mapActions, mapState } from 'pinia'
import { consultacep } from '~api/checkout.js'
import { real } from '~/helpers/formatters.js'
import {
  required,
  minLen,
  mmaa,
  number,
  cpf,
  cep,
  creditCard as creditCardRule,
  creditCardHolderName
} from '~/helpers/rules.js'
import { useStatementStore } from '~/stores/statement.js'
import { useToastStore } from '~/stores/toast.js'
import brandLogo from '~/components/checkout/checkout-payment-methods/brand-logo/brand-logo.vue'

export default {
  components: {
    brandLogo
  },
  emits: ['credit-card-confirm'],
  props: {
    saveAndPayMode: Boolean,
    creditCard: Object,
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      creditCardInfo: {
        card: '',
        cardName: '',
        cardBrand: '',
        validade: '',
        cvv: '',
        cpf: '',
        cep: '',
        totalParcelado: '',
        parcelaValue: '',
        parcelaCount: '',
        lastDigits: '',
        firstDigits: '',
        parcelamentoOptions: [],
        street: '',
        streetnum: '',
        city: '',
        state: '',
        neighborhood: ''
      },
      selectedParcela: null,
      cardBrandError: false,
      rules: {
        required,
        minLen,
        mmaa,
        number,
        cpf,
        cep,
        creditCardHolderName
      },
      faCircleInfo
    }
  },
  mounted() {
    const { creditCard } = this
    if (creditCard && Object.keys(creditCard).length > 0) {
      this.creditCardInfo = { ...creditCard, cardBrand: '' }
      if (!this.creditCardInfo.card) this.creditCardInfo.card = ''
      this.cardBrandError = false
    }
  },
  watch: {
    'creditCardInfo.card': {
      handler(newValue, oldValue) {
        if (!newValue || newValue.length < 6) return
        if (newValue.slice(0, 6) !== oldValue?.slice(0, 6)) {
          this.handleGetCardBrand()
          if (!this.cardBrandError) {
            this.getParcelas(this.creditCardInfo.card.slice(0, 6))
          }
        }
      },
      deep: true
    }
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useStatementStore, ['getParcelas', 'getCardBrand']),
    creditCardRules() {
      const { cardBrandError, creditCardInfo } = this
      if (cardBrandError) {
        creditCardInfo.cardBrand = ''
        return 'Bandeira do cartão não reconhecida'
      }
      const cardBrand = creditCardRule(creditCardInfo.card)
      creditCardInfo.cardBrand = cardBrand || ''
      return cardBrand ? true : 'Digite um cartão de crédito válido'
    },
    cvvRules() {
      const { cardBrand, cvv } = this.creditCardInfo
      if (!cvv?.length) {
        return 'Digite o código de segurança'
      }
      if (cardBrand !== 'amex' && cvv.length > 3) {
        return 'O código de segurança possui apenas 3 digitos'
      }
      if (cardBrand === 'amex' && cvv.length !== 4) {
        return 'O código de segurança possui 4 digitos'
      }
      return true
    },
    async confirm() {
      if (!this.$refs.form.validate()) {
        this.$nextTick(() => {
          const firstErrorElement = this.$refs.form.$el.querySelector('.f-invalid')
          if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth' })
          }
        })
        return
      }

      this.creditCardInfo.lastDigits = this.creditCardInfo.card.slice(-4)
      this.creditCardInfo.bin = this.creditCardInfo.card.slice(0, 6)
      this.creditCardInfo.parcelamentoOptions = this.parcelamentoOptionsCartao

      if (await this.addAddressToCreditCardInfo()) {
        this.$emit('credit-card-confirm', this.creditCardInfo)
      }
    },
    setParcelas(option) {
      if (!option) return
      this.creditCardInfo.parcelaValue = option.parcela_value
      this.creditCardInfo.parcelaCount = option.parcela_count
      this.creditCardInfo.totalParcelado = option.total_parcelado
    },
    setSelectedParcela(parcelas) {
      if (this.selectedParcela) return

      this.selectedParcela = parcelas[0]
      this.setParcelas(this.selectedParcela)
    },
    async handleGetCardBrand() {
      const { creditCardInfo, $refs } = this
      const bin = creditCardInfo.card.slice(0, 6)
      if (!bin) return
      const cardBrand = await this.getCardBrand(bin)

      creditCardInfo.cardBrand = cardBrand || ''
      this.cardBrandError = !cardBrand
      if ($refs.ccNumber) {
        $refs.ccNumber.validate()
      }
    },
    async addAddressToCreditCardInfo() {
      if (!this.$refs.cep.validate()) return

      try {
        const completeAddress = await consultacep(this.creditCardInfo.cep)
        if (completeAddress.error) {
          throw new Error('Não foi possível encontrar este CEP.')
        }

        const { street, city, state } = completeAddress
        if (!street || !city || !state) {
          throw new Error('Não foi possível validar o endereço completo.')
        }
        this.creditCardInfo = { ...this.creditCardInfo, ...completeAddress }
        return this.creditCardInfo
      } catch (error) {
        this.openToast({
          message: error,
          type: 'error',
          error
        })
      }
    },
    getParcelaText(parcela) {
      return `${parcela.parcela_count}x de ${real(parcela.parcela_value)} (${real(parcela.total_parcelado)})`
    }
  },
  computed: {
    ...mapState(useStatementStore, ['parcelamentoOptions', 'isLoadingParcelamentoOptions']),
    creditCardMask() {
      // Cartão amex - Necessário colocar o '[0]', caso contrário a máscara não é aplicada coretamente para números com mais de 16 dígitos
      if (this.creditCardInfo.card.length === 15) {
        return '0000 000000 00000[0]'
      }

      return '0000 0000 0000 0000'
    },
    parcelamentoOptionsCartao() {
      const bin = this.creditCardInfo.card.slice(0, 6)
      let parcelas = this.parcelamentoOptions.find(item => item.bin === bin)?.options || []
      parcelas = parcelas.map(parcela => {
        return {
          ...parcela,
          text: this.getParcelaText(parcela)
        }
      })
      this.setSelectedParcela(parcelas)
      return parcelas
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-credit-card-form {
  --input-margin: calc( #{$spacing-2} + #{$font-size-xs} ); // margem + altura do campo de erro, sempre dá margem pra aparecer sem sobrepor outro input

  .pccf-input {
    margin-bottom: var(--input-margin);

    // `min-width`s ajudam a não quebrar linha nas labels
    &.is-cvv {
      min-width: 148px;
      flex: 0;
    }

    &.is-cep {
      min-width: 130px;
      flex: 0;
    }

    &.is-street-number {
      min-width: 130px;
      flex: 0;
    }
  }

  .pccf-input-group {
    display: flex;
    align-items: flex-end; // se quebrar linha na label, campos ficam alinhados pelo bottom

    .pccf-input + .pccf-input {
      margin-left: $spacing-2;
    }
  }

  .pccf-logo {
    margin-top: $spacing-half;
    margin-right: $spacing-1;
  }

  .pccf-select {
    margin-bottom: var(--input-margin);
  }
}
</style>
