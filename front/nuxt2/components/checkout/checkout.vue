<template>
  <div class="checkout">
    <ada-container
      full-width-mobile
      class="c-container"
      :class="habilitarNovoCheckout ? 'cp-checkout-ab-hide-mobile pt-2' : 'cp-checkout-ab-on'"
    >
      <ada-alert
        v-if="mostraAvisoVagasRestantes"
        type="warning"
        class="cc-alert-vagas-restantes"
        hide-icon
      >
        <p>
          <b class="color-red"><aviso-vagas-restantes :grupo="grupoComMenosVagas" />!</b>
          Faça logo sua reserva!
        </p>
      </ada-alert>

      <div class="cc-content">
        <div class="ccc-column">
          <div ref="user">
            <lazy-checkout-revendedor-user
              v-if="isRevendedor"
              class="cccc-checkout-card"
              @user-updated="handleUpdateStatement"
            />

            <checkout-user
              v-else-if="!loggedIn"
              class="cccc-checkout-card"
              @user-updated="handleUpdateStatement"
            />
          </div>

          <div ref="passengers">
            <checkout-passengers
              :read-only="alterarReserva"
              :is-fretamento="isFretamento"
              :company-needs-passenger-data="companyNeedsPassengerData"
              class="cccc-checkout-card"
              @select-passenger="updatePassengers"
            />
          </div>

          <ada-alert
            v-if="nonAccessibleSeats"
            class="mb-2"
            type="warning"
          >
            <p class="text-sm">
              {{ nonAccessibleSeatsText }}
            </p>
            <ada-button
              class="color-brand"
              link
              small
              @click="openPopupAcessibilidade"
            >
              <p class="text-sm color-brand">
                Saiba como viajar com acessibilidade
              </p>
            </ada-button>
          </ada-alert>

          <lazy-checkout-revendedor-gratuidade
            v-if="isRevendedor && hasMarketplace"
            ref="gratuidadeAntt"
            class="cccc-checkout-card"
            @beneficio-antt-applied="handleUpdateStatement"
          />

          <checkout-poltronas
            v-if="showCheckoutPoltronas && (!habilitarNovoCheckout || !isMobile)"
            class="cccc-checkout-card"
            :layout-onibus-ida="layoutOnibusIda"
            :layout-onibus-volta="layoutOnibusVolta"
            :passengers="selectedPassengers"
            :grupo-ida="gruposIda[0]"
            :grupo-volta="gruposVolta[0]"
            @update-poltronas="updatePoltronasSelected"
          />

          <checkout-children-warning
            v-if="countCriancas > 0"
            class="cccc-checkout-card"
          />

          <div
            v-if="!(habilitarNovoCheckout && isMobile)"
            ref="payment"
          >
            <checkout-payment-methods
              v-if="!isTravelRemarcadaGratis"
              ref="paymentMethodsSection"
              class="cccc-checkout-card"
              @purchase="purchase"
            />
          </div>

          <div ref="bagagemAdicional">
            <checkout-bagagem-adicional
              v-if="mostrarBagagemAdicional"
              class="mb-2"
              :valor-bagagem-adicional="reservationData.valorBagagemAdicional"
              @add-bagagem-extra="updateBagagemAdicional"
            />
          </div>

          <div
            v-if="!alterarReserva"
            ref="promoCode"
          >
            <checkout-promo
              class="cccc-checkout-card"
              @cupom-updated="updatePromo"
            />
          </div>
          <checkout-necessary-docs v-if="isViagemInternacional" />
        </div>

        <div class="ccc-column">
          <checkout-group
            :grupos="gruposIda"
            title="Viagem de ida"
            :is-conexao="gruposIda.length > 1"
            :loading="!gruposIda"
            :is-upgrade="isUpgrade"
            class="cccc-checkout-card is-date-ida"
          />

          <checkout-group
            v-if="gruposVolta.length > 0"
            title="Viagem de volta"
            :grupos="gruposVolta"
            :is-conexao="gruposVolta.length > 1"
            :is-upgrade="isUpgrade"
            class="cccc-checkout-card is-date-volta"
          />
          <desembarque-rapido-alert v-if="shouldShowAlertDesembarqueRapido" class="cccc-checkout-card" />
          <checkout-offer-same-group
            v-if="showOfferSameGroup && offerSameGroup && hasSelectedPassengers"
            class="cccc-checkout-offer-same-group-card"
            :tipo-assento="offerTipoAssento"
            :valor-unitario="offerSameGroup.priceDiff"
            :loading="loadingOfferSameGroup"
            @melhorar-poltrona="openCheckoutOffer"
          />

          <div ref="seguroExtra">
            <checkout-seguro-extra
              v-if="!isTravelRemarcadaGratis && reservationData.isSeguroExtraPermitido && mostrarSeguroExtraNoUpgrade"
              class="cccc-checkout-card"
              :tem-seguro-contratado="temSeguroContratado"
              @toggle-seguro-extra="updateSeguroExtra"
              @birthdays-updated="updatePassengersBirthdays"
            />
          </div>
          <div class="cccc-checkout-card">
            <checkout-statement
              v-if="!isTravelRemarcadaGratis"
              :class="{'is-statement': totalDescontos > 0}"
            />
            <checkout-total-desconto v-if="totalDescontos && !isLoadingStatement" :total-desconto="totalDescontos" class="cccccc-desconto-total" :title="textTotalDesconto" />
          </div>
          <lazy-checkout-revendedor-protocolo v-if="isRevendedorInterno" class="cccc-checkout-card" @handle-protocolo-revendedor="handleProtocoloRevendedor" />
          <checkout-carbon
            v-if="!isTravelRemarcadaGratis"
            class="cccc-checkout-card"
            @toggle-carbon="updateCarbon"
          />

          <div ref="acceptance">
            <checkout-terms
              v-if="!isRevendedor"
              class="cccc-checkout-card"
              @toggle-termos-de-uso="handleAceitouCheckoutTerms"
            />
          </div>

          <ada-alert
            v-if="reservationData.user.email && aceitouCheckoutTerms"
            type="info"
            hide-icon
            class="mb-2"
          >
            <p class="cccc-termos-alert">
              Sua reserva será ligada ao email <strong>{{ reservationData.user.email }}</strong>
            </p>
          </ada-alert>

          <lazy-checkout-revendedor-finalization
            v-if="isRevendedor"
            class="cccc-checkout-card"
            :is-loading="processingPurchase || isLoadingStatement"
            @purchase="purchase"
            @create-payment-link="createPaymentLink"
          />

          <ada-button
            v-else-if="!isTravelRemarcadaGratis"
            block
            :loading="processingPurchase || atualizandoPreco || isLoadingStatement"
            class="cccc-pay"
            @click="purchase"
          >
            {{ processingPurchase ? 'Processando...' : 'Confirmar e pagar' }}
          </ada-button>

          <ada-button
            v-if="isTravelRemarcadaGratis"
            block
            :loading="loadingRemanejamento"
            class="cccc-pay"
            @click="remarcarTravelGratis"
          >
            Confirmar
          </ada-button>

          <checkout-beneficios
            :grupos-ida="gruposIda"
            :grupos-volta="gruposVolta"
          />
        </div>
      </div>
    </ada-container>

    <ada-container
      class="c-container"
      :class="habilitarNovoCheckout ? 'cp-checkout-ab-only-mobile' : 'cp-checkout-ab-off'"
    >
      <div class="cc-content">
        <div class="ccc-column">
          <div ref="user" :class="loggedIn ? '' : 'novo-checkout-1'">
            <lazy-checkout-revendedor-user
              v-if="isRevendedor"
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @user-updated="handleUpdateStatement"
            />

            <checkout-user
              v-else-if="!loggedIn"
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @user-updated="handleUpdateStatement"
            />
          </div>

          <ada-alert
            v-if="nonAccessibleSeats"
            class="mb-2"
            type="warning"
          >
            <p class="text-sm">
              {{ nonAccessibleSeatsText }}
            </p>
            <ada-button
              class="color-brand"
              link
              small
              @click="openPopupAcessibilidade"
            >
              <p class="text-sm color-brand">
                Saiba como viajar com acessibilidade
              </p>
            </ada-button>
          </ada-alert>

          <lazy-checkout-revendedor-gratuidade
            v-if="isRevendedor && hasMarketplace"
            ref="gratuidadeAntt"
            class="cccc-checkout-card"
            :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
            @beneficio-antt-applied="handleUpdateStatement"
          />

          <checkout-children-warning
            v-if="countCriancas > 0"
            class="cccc-checkout-card"
            :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
          />

          <div
            v-if="habilitarNovoCheckout && isMobile"
            ref="payment"
          >
            <checkout-payment-methods
              v-if="!isTravelRemarcadaGratis"
              ref="paymentMethodsSection"
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @purchase="purchase"
            />
          </div>

          <div
            v-if="!alterarReserva"
            ref="promoCode"
          >
            <checkout-promo
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @cupom-updated="updatePromo"
            />
          </div>
          <checkout-necessary-docs v-if="isViagemInternacional" />
        </div>

        <div class="ccc-column">
          <div class="cccc-novo-checkout">
            <h2 class="color-white title-xs mb-1">
              Viagem de ida
            </h2>
            <checkout-group
              title=""
              :grupos="gruposIda"
              :is-conexao="gruposIda.length > 1"
              :loading="!gruposIda"
              :is-upgrade="isUpgrade"
              :habilitar-novo-checkout="habilitarNovoCheckout"
              class="cccc-checkout-card is-date-ida"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @popup-itinerario="handlePopupItinerario"
            />

            <template v-if="gruposVolta.length > 0">
              <h2 class="color-white title-xs mb-1">
                Viagem de volta
              </h2>
              <checkout-group
                title=""
                :grupos="gruposVolta"
                :is-conexao="gruposVolta.length > 1"
                :is-upgrade="isUpgrade"
                :habilitar-novo-checkout="habilitarNovoCheckout"
                class="cccc-checkout-card is-date-volta"
                :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
                @popup-itinerario="handlePopupItinerario"
              />
            </template>
            <ada-alert
              v-if="mostraAvisoVagasRestantes"
              type="warning"
              class="cc-alert-vagas-restantes mb-3"
              hide-icon
            >
              <p>
                <aviso-vagas-restantes-v2 :grupo="grupoComMenosVagas" />
              </p>
            </ada-alert>
          </div>

          <div ref="passengers" :class="!loggedIn ? 'novo-checkout-1' : 'novo-checkout-5'">
            <checkout-passengers
              :read-only="alterarReserva"
              :is-fretamento="isFretamento"
              :company-needs-passenger-data="companyNeedsPassengerData"
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @select-passenger="updatePassengers"
            />
          </div>

          <checkout-poltronas
            v-if="showCheckoutPoltronas && (habilitarNovoCheckout && isMobile)"
            class="cccc-checkout-card novo-checkout-2"
            :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
            :layout-onibus-ida="layoutOnibusIda"
            :layout-onibus-volta="layoutOnibusVolta"
            :passengers="selectedPassengers"
            :grupo-ida="gruposIda[0]"
            :grupo-volta="gruposVolta[0]"
            @update-poltronas="updatePoltronasSelected"
          />

          <div ref="seguroExtra" class="novo-checkout-3">
            <checkout-seguro-extra
              v-if="!isTravelRemarcadaGratis && reservationData.isSeguroExtraPermitido && mostrarSeguroExtraNoUpgrade"
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              :tem-seguro-contratado="temSeguroContratado"
              @toggle-seguro-extra="updateSeguroExtra"
              @birthdays-updated="updatePassengersBirthdays"
            />
          </div>

          <div ref="bagagemAdicional" class="novo-checkout-4">
            <checkout-bagagem-adicional
              v-if="mostrarBagagemAdicional"
              class="mb-2"
              :valor-bagagem-adicional="reservationData.valorBagagemAdicional"
              @add-bagagem-extra="updateBagagemAdicional"
            />
          </div>

          <desembarque-rapido-alert
            v-if="shouldShowAlertDesembarqueRapido"
            class="cccc-checkout-card"
            :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
          />
          <checkout-offer-same-group
            v-if="showOfferSameGroup && offerSameGroup && hasSelectedPassengers"
            class="cccc-checkout-offer-same-group-card"
            :tipo-assento="offerTipoAssento"
            :valor-unitario="offerSameGroup.priceDiff"
            :loading="loadingOfferSameGroup"
            @melhorar-poltrona="openCheckoutOffer"
          />

          <checkout-statement
            v-if="!isTravelRemarcadaGratis"
            class="cccc-checkout-card"
            :class="{'is-statement': totalDescontos > 0, 'checkout-card-ab-on': habilitarNovoCheckout}"
          />

          <checkout-total-desconto
            v-if="totalDescontos && !isLoadingStatement"
            :total-desconto="totalDescontos"
            :title="textTotalDesconto"
            class="cccccc-desconto-total"
          />

          <lazy-checkout-revendedor-protocolo v-if="isRevendedorInterno" class="cccc-checkout-card" @handle-protocolo-revendedor="handleProtocoloRevendedor" />
          <checkout-carbon
            v-if="!isTravelRemarcadaGratis"
            class="cccc-checkout-card"
            :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
            @toggle-carbon="updateCarbon"
          />

          <div ref="acceptance">
            <checkout-terms
              class="cccc-checkout-card"
              :class="{'checkout-card-ab-on': habilitarNovoCheckout}"
              @toggle-termos-de-uso="handleAceitouCheckoutTerms"
            />
          </div>

          <ada-alert
            v-if="reservationData.user.email && aceitouCheckoutTerms"
            type="info"
            hide-icon
            class="mb-2"
          >
            <p class="cccc-termos-alert">
              Sua reserva será ligada ao email <strong>{{ reservationData.user.email }}</strong>
            </p>
          </ada-alert>

          <lazy-checkout-revendedor-finalization
            v-if="isRevendedor"
            class="cccc-checkout-card"
            :is-loading="processingPurchase || isLoadingStatement"
            @purchase="purchase"
            @create-payment-link="createPaymentLink"
          />

          <ada-button
            v-else-if="!isTravelRemarcadaGratis"
            block
            :loading="processingPurchase || atualizandoPreco || isLoadingStatement"
            class="cccc-pay"
            @click="purchase"
          >
            {{ processingPurchase ? 'Processando...' : 'Confirmar e pagar' }}
          </ada-button>

          <ada-button
            v-if="isTravelRemarcadaGratis"
            block
            :loading="loadingRemanejamento"
            class="cccc-pay"
            @click="remarcarTravelGratis"
          >
            Confirmar
          </ada-button>

          <checkout-beneficios
            :grupos-ida="gruposIda"
            :grupos-volta="gruposVolta"
          />
        </div>
      </div>
    </ada-container>

    <popup-acessibilidade
      v-if="showPopupAcessibilidade"
      @close="closePopupAcessibilidade"
    />

    <popup
      v-model="hasStatementError"
      :modal-options="{ maxWidth: '550px' }"
      :persistent="persistentPopup"
      :title="errorTitle"
      @close="resetSelectedPassengers"
    >
      <checkout-error
        :statement="completeStatement"
        :idas="gruposIda"
        :voltas="gruposVolta"
        @close="resetSelectedPassengers"
      />
    </popup>

    <popup
      v-model="hasRodoviariaError"
      :modal-options="{ maxWidth: '550px' }"
      :persistent="true"
      :title="rodoviariaErrorTitle"
      @close="resetSelectedPassengers"
    >
      <checkout-error
        :statement="erroRodoviaria"
        :idas="gruposIda"
        :voltas="gruposVolta"
        :alterar-reserva="alterarReserva"
        @close="resetSelectedPassengers"
      />
    </popup>

    <popup
      v-model="hasPaymentErrorAfterPurchase"
      :modal-options="{ maxWidth: '550px' }"
      :persistent="false"
    >
      <checkout-rejected-error
        ref="RejectedPopup"
        :statement="completeStatement"
        @pix-selected="purchaseWithPix"
        @retype-cc="retypeCreditCard"
        @confirm-and-pay="purchaseWithUpdatedCreditCardInfo"
        @credit-card-selected="showSelectCreditCardPopup"
        @authorized="openCurrentCreditCard"
        @change-method-clicked="handleChangePaymentMethod"
      />
    </popup>

    <popup-checkout-atualiza-preco
      :visible="atualizaPreco"
      :informacoes-sobre-precos="informacoesSobrePrecos"
      @close="closeAtualizaPreco"
    />

    <popup-reserva-in-progress
      v-if="hasInProgressPurchase"
    />

    <popup-itinerario
      title=""
      :visible="popupItinerarioVisible"
      :grupos="popupItinerarioGrupos"
      :is-conexao="popupItinerarioGrupos.length > 1"
      :is-upgrade="isUpgrade"
      @close="handlePopupItinerario({value: false, grupos: []})"
    />

    <input
      id="CS-SessionId"
      ref="clearSaleInput"
      type="hidden"
      value=""
    >
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { mapActions, mapState, mapWritableState } from 'pinia'
import { $axios } from '~/api/utils/axios.js'
import {
  checkReservationStatus,
  ensureCsrf,
  purchaseV3 as purchaseApi,
  remarcarV3 as remarcarApi,
  remarcarTravelGratis,
  updateTrechoV2 as updateTrecho,
  verifyUpdateTrechoV2 as verifyUpdateTrecho,
  createLinkPagamento
} from '~api/checkout.js'
import { searchSameGroup } from '~api/search.js'
import sentryhelper from '~sentryhelper'
import ajaxhandler from '~/helpers/ajaxhandler.js'
import { injetaScriptClearsale } from '~/helpers/clearsale.js'
import EventBus from '~/helpers/eventbus.js'
import { DATE_FORMATS, dateFormat, pluralize } from '~/helpers/formatters.js'
import {
  getVagasRestantes,
  getAvisoVagasRestantes,
  isViagemInternacional,
  atLeastOneGroupIsBuser,
  atLeastOneGroupCompanyHasRequiredPassengerData,
  atLeastOneGroupIsHibrido,
  atLeastOneGroupIsMarketplace,
  groupsHaveBuserPremium
} from '~/helpers/grouphelper.js'
import localstorage from '~/helpers/localstorage.js'
import passengerHelper from '~/helpers/passengerhelper.js'
import PAYMENT_ERROR_MAP from '~/helpers/payment-error.js'
import reservationhelper from '~/helpers/reservationhelper.js'
import { deleteTrechoSign, getTrechoSign, setTrechoSign } from '~/helpers/signedtrecho.js'
import { labels, prioridade } from '~/helpers/tipoAssento.js'
import { useAbandonedCartStore } from '~/stores/abandoned-cart.js'
import { useAlterarReservaStore } from '~/stores/alterar-reserva.js'
import { useBuscasRecentesStore } from '~/stores/buscas-recentes.js'
import { usePaymentStore } from '~/stores/payment.ts'
import { useReservationStore } from '~/stores/reservation.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSearchStore } from '~/stores/search.js'
import { useSessionStore } from '~/stores/session.js'
import { useSettingsStore } from '~/stores/settings.js'
import { useStatementStore } from '~/stores/statement.js'
import { useToastStore } from '~/stores/toast.js'
import avisoVagasRestantesV2 from '~/components/shared/aviso-vagas-restantes/aviso-vagas-restantes-v2.vue'
import avisoVagasRestantes from '~/components/shared/aviso-vagas-restantes/aviso-vagas-restantes.vue'
import checkoutGroup from '~/components/shared/checkout-group.vue'
import popup from '~/components/shared/popup/popup.vue'
import popupItinerario from '~/components/shared/popup-itinerario/popup-itinerario.vue'
import checkoutBagagemAdicional from '~/components/checkout/checkout-bagagem/checkout-bagagem-adicional.vue'
import checkoutBeneficios from '~/components/checkout/checkout-beneficios/checkout-beneficios.vue'
import checkoutCarbon from '~/components/checkout/checkout-carbon/checkout-carbon.vue'
import checkoutChildrenWarning from '~/components/checkout/checkout-children-warning/checkout-children-warning.vue'
import checkoutError from '~/components/checkout/checkout-error/checkout-error.vue'
import checkoutNecessaryDocs from '~/components/checkout/checkout-necessary-docs/checkout-necessary-docs.vue'
import checkoutOfferSameGroup from '~/components/checkout/checkout-offer-same-group/checkout-offer-same-group.vue'
import checkoutPassengers from '~/components/checkout/checkout-passengers/checkout-passengers.vue'
import popupAcessibilidade from '~/components/checkout/checkout-passengers/popup-acessibilidade.vue'
import checkoutPaymentMethods from '~/components/checkout/checkout-payment-methods/checkout-payment-methods.vue'
import checkoutPoltronas from '~/components/checkout/checkout-poltronas/checkout-poltronas.vue'
import checkoutPromo from '~/components/checkout/checkout-promo/checkout-promo.vue'
import checkoutRejectedError from '~/components/checkout/checkout-rejected-error/checkout-rejected-error.vue'
import checkoutSeguroExtra from '~/components/checkout/checkout-seguro-extra/checkout-seguro-extra.vue'
import checkoutStatement from '~/components/checkout/checkout-statement/checkout-statement.vue'
import checkoutTotalDesconto from '~/components/checkout/checkout-statement/checkout-total-desconto.vue'
import checkoutTerms from '~/components/checkout/checkout-terms/checkout-terms.vue'
import checkoutUser from '~/components/checkout/checkout-user/checkout-user.vue'
import popupCheckoutAtualizaPreco from '~/components/checkout/popup-checkout-atualiza-preco/popup-checkout-atualiza-preco.vue'
import popupReservaInProgress from '~/components/checkout/popup-reserva-in-progress/popup-reserva-in-progress.vue'

const REJECTED_POPUP_ERRORS = [
  // NUPAY
  'CANCELLED_BY_INSTITUTION',
  'CANCELLED_BY_SELLER',
  'CANCELLED_BY_TIMEOUT',
  'CANCELLED_BY_USER',
  'SYSTEM_ERROR',
  // CC MERCADO PAGO
  'cc_rejected_bad_filled_card_number',
  'cc_rejected_bad_filled_date',
  'cc_rejected_bad_filled_other',
  'cc_rejected_bad_filled_security_code',
  'cc_rejected_blacklist',
  'cc_rejected_call_for_authorize',
  'cc_rejected_card_disabled',
  'cc_rejected_card_error',
  'cc_rejected_card_type_not_allowed',
  'cc_rejected_duplicated_payment',
  'cc_rejected_high_risk',
  'cc_rejected_insufficient_amount',
  'cc_rejected_max_attempts',
  'cc_rejected_other_reason',
  // CUSTOM MERCADO PAGO ERROR
  'cc_rejected_invalid_amount'
]

const ERROR_BUCKET_ESGOTADO = 'Este grupo não possui mais vagas com este preço'

const CACHE_PURCHASE_KEY = 'in_progress_purchase'
const CACHE_PURCHASE_TIMEOUT = 50

export default {
  name: 'Checkout',
  components: {
    popup,
    checkoutUser,
    checkoutPassengers,
    checkoutChildrenWarning,
    checkoutStatement,
    checkoutPromo,
    checkoutOfferSameGroup,
    checkoutNecessaryDocs,
    checkoutCarbon,
    checkoutTotalDesconto,
    checkoutSeguroExtra,
    checkoutBeneficios,
    checkoutPaymentMethods,
    popupCheckoutAtualizaPreco,
    checkoutTerms,
    checkoutPoltronas,
    checkoutError,
    checkoutBagagemAdicional,
    avisoVagasRestantes,
    avisoVagasRestantesV2,
    checkoutRejectedError,
    checkoutGroup,
    popupReservaInProgress,
    popupAcessibilidade,
    popupItinerario,
    desembarqueRapidoAlert: () => import('~/components/shared/desembarque-rapido-alert.vue'),
    LazyCheckoutRevendedorUser: () => import('~/components/checkout/checkout-revendedor-user/checkout-revendedor-user.vue'),
    LazyCheckoutRevendedorFinalization: () => import('~/components/checkout/checkout-revendedor-finalization/checkout-revendedor-finalization.vue'),
    LazyCheckoutRevendedorProtocolo: () => import('~/components/revendedor/checkout-revendedor-protocolo.vue'),
    LazyCheckoutRevendedorGratuidade: () => import('~/components/checkout/checkout-revendedor-gratuidade/checkout-revendedor-gratuidade.vue')
  },
  emits: ['purchase-finished'],
  data() {
    return {
      signedGroups: null,
      processingPurchase: false,
      hasStatementError: false,
      aceitouCheckoutTerms: false,
      atualizaPreco: false,
      informacoesSobrePrecos: {},
      atualizandoPreco: false,
      persistentPopup: true,
      sameGroupData: null,
      offerSameGroup: null,
      showOfferSameGroup: false,
      valorIdaUnitario: null,
      loadingOfferSameGroup: false,
      hasPaymentErrorAfterPurchase: false,
      errorTitle: '',
      hasRodoviariaError: false,
      rodoviariaErrorTitle: '',
      erroRodoviaria: {},
      loadingRemanejamento: false,
      houveAtualizacaoPreco: false,
      passengersBirthdays: {},
      hasInProgressPurchase: false,
      showPopupAcessibilidade: false,
      protocoloRevendedor: '',
      hasBuserPremium: false,
      popupItinerarioVisible: false,
      popupItinerarioGrupos: []
    }
  },
  props: {
    previousPage: String,
    habilitarNovoCheckout: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const paymentStore = usePaymentStore()
    paymentStore.initProviders()
    return { paymentStore }
  },
  async mounted() {
    const groups = [...this.gruposIda, ...this.gruposVolta]
    this.hasBuserPremium = groupsHaveBuserPremium(groups)

    if (!this.loggedIn) {
      await ensureCsrf()
      EventBus.$emit('started-purchase-as-visitor')
    }
    this.refreshTrechoSign()
    await this.updateTrecho()
    this.sendPageEntryEvent()

    this.reservationData.rakuten_time_entered = this.$cookies.get('rakuten_time_entered')

    // remove informacoes de compra dos itens adicionais
    this.reservationData.isItemAdicional = false
    this.reservationData.extrato_item_adicional = null
    this.reservationData.promoCode = this.$cookies.get('promo')

    if (this.alterarReserva && this.bagagemAdicionalLiberada) {
      const paxComBagagem = this.reservationData.passengers.find(pax => pax.quantidade_bagagem > 0)
      this.reservationData.bagagemAdicional = paxComBagagem ? paxComBagagem.quantidade_bagagem : 0
    }

    if (this.temSeguroContratado) {
      this.reservationData.seguroExtra = true
    }

    await this.handleUpdateStatement()
    this.updatePaymentValues()

    if (this.isCheckoutOffer) {
      this.openToast({
        message: `As poltronas foram melhoradas para o tipo ${labels[this.gruposIda[0].tipo_assento]}.`,
        type: 'success'
      })
    }

    if (!this.alterarReserva) {
      this.addCart(this.gruposIda, this.gruposVolta)
    }

    await this.setupTaxaCancelamentoGrupos(this.travelRemarcada?.id)
    injetaScriptClearsale()

    const inProgressPurchase = this.getInProgressPurchase()
    if (inProgressPurchase) {
      this.continueInProgressPurchase(inProgressPurchase)
    }
  },
  watch: {
    binCartaoSelecionado() {
      this.handleUpdateStatement()
    }
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(useAbandonedCartStore, ['addCart']),
    ...mapActions(useBuscasRecentesStore, ['limpaDataComprada']),
    ...mapActions(useStatementStore, ['updateStatement']),
    ...mapActions(useReservationStore, [
      'setupTaxaCancelamentoGrupos',
      'unselectAllPassengers',
      'unselectAllPoltronas'
    ]),
    retypeCreditCard() {
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.retypeCurrentCreditCard()
    },
    async remarcarTravelGratis() {
      if (!this.validateRemarcacaoGratis()) {
        return
      }
      this.loadingRemanejamento = true
      const params = {
        travel_id: this.travelRemarcada?.id,
        trecho_classe_destino: this.gruposIda[0].id
      }
      try {
        const travelId = await remarcarTravelGratis(params)
        this.$router.push({
          name: 'reserva',
          params: {
            status: 'confirmed',
            travelIdaId: travelId.travel_id
          }
        })
      } catch (error) {
        this.openToast({
          message: 'Não foi possível fazer a remarcação da viagem.',
          type: 'error',
          error
        })
      } finally {
        this.loadingRemanejamento = false
      }
    },
    validateRemarcacaoGratis() {
      const refName = 'acceptance'
      const validations = {
        acceptance: (valid) => { this.validationErrors.acceptance = valid }
      }

      const validProp = !!this.reservationData.acceptance

      if (!validProp) {
        validations[refName](!validProp)
        this.scrollTo(refName)
      }
      return validProp
    },
    openCurrentCreditCard() {
      EventBus.$emit('clicou-btn-ja-autorizei')
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.regerarHash(false)
    },
    handleChangePaymentMethod() {
      this.hasPaymentErrorAfterPurchase = false
    },
    async updatePassengers(actionType) {
      this.unselectAllPoltronas()
      await this.handleUpdateStatement()
      this.updatePaymentValues()
      EventBus.$emit('atualizou-lista-de-passageiros', { acao: actionType, valorReserva: this.valorTotal })
      await this.setOfferSameGroup()
      if (!this.isCreditCard) {
        return
      }

      if (!this.hasSelectedPassengers) {
        this.validationErrors.payment = true
        return
      }

      this.validationErrors.payment = false
    },
    async updatePoltronasSelected() {
      await this.handleUpdateStatement()
      this.updatePaymentValues()
    },
    async updatePromo(promo) {
      await this.handleUpdateStatement()
      this.updatePaymentValues()
      this.promo = promo
    },
    async updateCarbon(carbonValue) {
      EventBus.$emit('atualizou-neutralizacao-carbono', {
        valorBase: this.valorTotal,
        valorAcrescimo: carbonValue,
        marcado: this.reservationData?.creditoCarbono
      })
      await this.handleUpdateStatement()
      this.updatePaymentValues()
    },
    async updateBagagemAdicional(quantBagagemExtra) {
      await this.handleUpdateStatement()
      this.updatePaymentValues()
    },
    async updateSeguroExtra(seguroExtraValue) {
      EventBus.$emit('atualizou-seguro-extra', {
        valorBase: this.valorTotal,
        valorAcrescimo: seguroExtraValue,
        marcado: this.reservationData?.seguroExtra,
        porcentagem: this.reservationData?.addonsPriceConfig?.seguro_extra?.percentage
      })
      await this.handleUpdateStatement()
      this.updatePaymentValues()
    },
    refreshTrechoSign() {
      this.signedGroups = this.allGroups.reduce((acc, g) => {
        acc[g.id] = getTrechoSign(g.id) || ''
        return acc
      }, {})
    },
    async handleUpdateStatement() {
      if (!this.shouldLoadStatement) return
      try {
        this.refreshTrechoSign()

        if (this.isRevendedor) {
          this.reservationData.paymentLinkCode = null
          this.reservationData.resellerUserId = this.user.id
        }

        await this.updateStatement({
          ...this.reservationData,
          groups: this.groupsMap,
          email: this.reservationData.user.email,
          deviceToken: this.deviceToken,
          isUpgrade: this.isUpgrade,
          bank_identification_number: this.binCartaoSelecionado
        }, this.$cookies, this.$route?.query)
      } catch (error) {
        this.openToast({ message: error, type: 'error', error })
      } finally {
        this.hasStatementError = !this.completeStatement || !!this.completeStatement?.error
      }
    },
    resetSelectedPassengers() {
      this.hasStatementError = false
      this.hasRodoviariaError = false
      // deletando a seleção de pagamento pois esse fluxo não vai
      // recalcular os numeros, quebrando os valores anteriores do card
      this.$refs.paymentMethodsSection?.deletePaymentInfo()
      this.unselectAllPassengers()
      this.handleUpdateStatement()
    },
    updatePaymentValues() {
      const pagamentoTotal = this.completeStatement?.pagamento_total || 0
      const params = {
        gruposIda: this.gruposIda.map(p => ({
          ...reservationhelper.getGroupInfo(p),
          origem_slug: p.origem?.slug,
          destino_slug: p.destino?.slug,
          isPromocional: !!p?.is_trecho_promocional,
          datetime_ida: dateFormat(DATE_FORMATS.dbdatetime, p.datetime_ida)
        })),
        gruposVolta: this.gruposVolta.map(p => ({
          ...reservationhelper.getGroupInfo(p),
          isPromocional: !!p?.is_trecho_promocional,
          datetime_ida: dateFormat(DATE_FORMATS.dbdatetime, p.datetime_ida)
        })),
        preco_total: pagamentoTotal
      }

      EventBus.$emit('adicionou-no-carrinho', params)
      if (!this.reservationData.payment || !this.isUserInfoValid) return

      if (this.isCreditCard) {
        const totalParcelado = this.parcelamentoOptionsCartaoSelecionado[this.reservationData.payment.parcelaCount - 1]?.total_parcelado || 0
        const parcelaValue = this.parcelamentoOptionsCartaoSelecionado[this.reservationData.payment.parcelaCount - 1]?.parcela_value || 0
        this.reservationData.payment.value = totalParcelado
        this.reservationData.payment.net_value = pagamentoTotal
        this.reservationData.payment.totalParcelado = totalParcelado
        this.reservationData.payment.parcelaValue = parcelaValue
        return
      }

      this.reservationData.payment.value = pagamentoTotal
      this.reservationData.payment.net_value = pagamentoTotal
    },
    getInfoAfiliado() {
      const infoAfiliado = {}

      try {
        infoAfiliado.tags = JSON.parse(this.$cookies.get('tags'))
      } catch { }

      const source = this.$cookies.get('source')
      const source_id = this.$cookies.get('source_id')

      if (source) infoAfiliado.source = source
      if (source_id) infoAfiliado.source_id = source_id

      return infoAfiliado
    },
    createReservationPayload() {
      function _getPassengerBirthday(ctx, passenger) {
        // Atualiza a data de nascimento dos passageiros sem data de nascimento, caso o seguro extra esteja selecionado

        if (!ctx.reservationData.seguroExtra) {
          return
        }

        const uniqueId = passengerHelper.getUniqueID(passenger)
        return ctx.passengersBirthdays[uniqueId]
      }

      // Passengers
      const passengers = this.reservationData.passengers.map(p => ({
        id: p.id,
        name: p.name,
        social_name: p.social_name,
        cpf: p.cpf,
        rg_number: p.rg_number,
        rg_orgao: p.rg_orgao,
        tipo_documento: p.tipo_documento,
        needs_cadeirinha: p.needs_cadeirinha,
        birthday: p.birthday || _getPassengerBirthday(this, p),
        phone: p.phone,
        tipos_deficiencia: p.tipos_deficiencia
      }))

      const shouldIncludeBenefitData = this.isRevendedor &&
          this.reservationData.categoriaEspecial &&
          this.reservationData.dadosBeneficio &&
          passengers.length === 1

      if (shouldIncludeBenefitData) {
        passengers[0].categoria_especial = this.reservationData.categoriaEspecial
        passengers[0].dadosBeneficio = {
          data_expedicao: this.reservationData.dadosBeneficio.dataExpedicao,
          data_expiracao: this.reservationData.dadosBeneficio.dataExpiracao,
          numero_beneficio: this.reservationData.dadosBeneficio.numeroBeneficio,
          renda: this.reservationData.dadosBeneficio.renda,
          tipo_passe_livre: this.reservationData.dadosBeneficio.tipoPasseLivre,
          auxilio_embarque: this.reservationData.dadosBeneficio.auxilioEmbarque
        }
      }

      const payload = {
        passengers,
        groups: this.groupsMap,
        credito_carbono: this.reservationData.creditoCarbono,
        seguro_extra: this.reservationData.seguroExtra,
        quantidade_bagagem_adicional: this.reservationData.bagagemAdicional,
        valor_bagagem_adicional: this.reservationData.valorBagagemAdicional,
        reserva_assentos: this.poltronas,
        is_ab_checkout_offer: this.isCheckoutOffer,
        is_upgrade: this.isUpgrade,
        valor_upgrade_pos_vendas: this.gmvUpgrade,
        acceptance: this.reservationData.acceptance,
        cpf_da_promo: this.cpfDaPromo,
        email: this.reservationData.user.email,
        name: this.reservationData.user.name,
        phone: this.reservationData.user.phone,
        device_token: this.deviceToken,
        valor_total: this.valorTotal,
        // Logando para erro pagamento diferente do esperado
        valor_grupo_ida: this.valorGrupoIda,
        valor_ida_unitario: this.valorIdaUnitario,
        valor_grupo_volta: this.valorGrupoVolta,
        poltronas_selected: this.reservationData.poltronasSelected,
        valor_volta_unitario: this.valorVoltaUnitario,
        tabid: $axios.defaults.headers.common.tabid
      }

      if (this.isRevendedor) {
        payload.user_revenda = this.reservationData.user.id
        payload.user_revendedor = this.user.id
        payload.protocolo = this.protocoloRevendedor
      }

      if (shouldIncludeBenefitData) {
        payload.categoria_especial = this.reservationData.categoriaEspecial
      }

      if (this.userPrecisaPagar) {
        if (this.isCreditCard) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { card, validade, cvv, ...paymentInfo } = this.reservationData.payment
          payload.payment = paymentInfo
          payload.payment.cs_session_id = this.$refs.clearSaleInput.value

          if (payload.payment.provider !== this.paymentStore.currentProvider) {
            payload.payment = this.paymentStore.swapCardToken(payload.payment)
          }

          payload.payment.card_cvv = this.paymentStore.isCVVRequired() ? payload.payment.card_cvv : ''
        } else {
          payload.payment = this.reservationData.payment
        }
      }

      if (this.alterarReserva) {
        payload.travel_id = this.travelRemarcada.id
      } else {
        const infoAfiliado = this.getInfoAfiliado()
        if (infoAfiliado.tags) payload.tags = infoAfiliado.tags
        if (infoAfiliado.source) payload.source = infoAfiliado.source
        if (infoAfiliado.source_id) payload.source_id = infoAfiliado.source_id
      }
      return payload
    },
    purchaseWithUpdatedCreditCardInfo(updatedFields) {
      EventBus.$emit('viu-popup-adjustable-errors-cc-reject')
      this.$refs.paymentMethodsSection.updateCurrentCreditCard(updatedFields)
      this.hasPaymentErrorAfterPurchase = false
    },
    purchaseWithPix() {
      this.$refs.paymentMethodsSection.onSelectPaymentMethod('pix')
      this.purchase()
      this.hasPaymentErrorAfterPurchase = false
    },
    showSelectCreditCardPopup() {
      this.hasPaymentErrorAfterPurchase = false
      this.$refs.paymentMethodsSection.showSelectCreditCardPopup = true
    },
    async purchase() {
      if (this.processingPurchase) {
        return
      }

      const payload = this.createReservationPayload()

      let validatePayload = this.validatePayload(payload)

      // valida se o cpf foi inserido para promoções do tipo parceria
      if (this.completeStatement.needs_travel_cpf && !this.cpfDaPromo) {
        this.validationErrors.promoCode = true
        validatePayload = false
      }
      if (this.userPrecisaPagar && this.isCreditCard && !this.paymentHasHash) {
        validatePayload = false
        const needsNewHash = this.completeStatement?.errorCode === 'cc_rejected_bad_filled_security_code' || this.completeStatement?.errorCode === 'bad_filled_card_code'
        this.$refs.paymentMethodsSection.regerarHash(needsNewHash)
      }

      if (!validatePayload) {
        this.processingPurchase = false
        return
      }

      this.showInProgressPurchaseMessage()

      let purchase = null
      try {
        let callApi = remarcarApi
        if (!this.alterarReserva) {
          callApi = purchaseApi
          if (!this.loggedIn) {
            payload.grtoken = await this.$recaptcha.execute('purchase')
          }
        }

        purchase = await callApi(payload)
      } catch (error) {
        ajaxhandler.onUnhandledRejection(error, this.$pinia)
        this.processingPurchase = false
        this.clearPurchaseCache()
        return
      }

      if (!this.assinaPreco && purchase?.error === 'Pagamento diferente do esperado') {
        await this.handleUpdateStatement()
        // Caso os buckets tenham se esgotado entre a entrada na página e a compra
        if (this.isPaymentValueInconsistent) {
          this.completeStatement.error = ERROR_BUCKET_ESGOTADO
          this.hasStatementError = true
          this.processingPurchase = false
          return
        }
      }

      // Caso nao esteja com o pagamento assincrono ativo, já trata as travels direto
      if (!purchase?.async_payment_flag) {
        return this.handleReservation(purchase)
      }

      // Por enquanto cacheia somente reservas assincronas
      this.cachePurchase(purchase)
      this.handleAsyncPayment(purchase)
      if (this.gruposIda.length > 0) {
        this.limpaDataComprada(this.gruposIda[0].datetime_ida)
      }
    },
    async createPaymentLink() {
      try {
        this.processingPurchase = true
        let reservationPayload = this.createReservationPayload()
        this.validationErrors.promoCode = this.completeStatement.needs_travel_cpf && !this.cpfDaPromo
        if (!this.validatePayload(reservationPayload) || this.validationErrors.promoCode) {
          this.processingPurchase = false
          return
        }

        const payload = reservationhelper.prepatePaymentLinkPayload(
          reservationPayload,
          this.reservationData,
          this.completeStatement
        )
        const response = await createLinkPagamento(payload)

        if (response?.error) throw new Error(response.error)
        this.reservationData.paymentLinkCode = response?.code
      } catch (error) {
        this.openToast({ message: error, type: 'error', error })
      } finally {
        this.processingPurchase = false
      }
    },
    handleReservation(reservationResult) {
      if (reservationResult?.error && this.isCreditCard && !this.paymentStore.isFixableError(reservationResult?.error_code)) {
        this.paymentStore.markProviderAsUsed()

        if (!this.paymentStore.isAllProvidersUsed) {
          this.processingPurchase = false
          this.purchase()
          return
        }

        this.paymentStore.resetProvidersAttempts()
      }

      if (reservationResult?.error) {
        this.handleStatementError(reservationResult)
        return
      }

      // checkout deslogado precisa enviar o email e o cell_phone também para o evento purchase do GTM
      let userDeslogado = null
      if (!this.loggedIn) {
        userDeslogado = {
          email: this.reservationData.user.email,
          cell_phone: this.reservationData.user.phone
        }
      }
      const user = userDeslogado || this.user

      try {
        // Emite os eventos de compra
        // Isto está dentro de um try pois não pode quebrar a compra caso algo aqui falhe, mas mesmo assim capturamos o erro
        const origem = this.allGroups[0].origem?.slug
        const lastGroup = this.allGroups[this.allGroups.length - 1]
        const destino = lastGroup.destino?.slug

        let bagagemAdicional = {
          quantidadeBagagem: this.reservationData.bagagemAdicional,
          valorBagagemAdicional: this.reservationData.valorBagagemAdicional,
          valorTotalBagagemAdicional: this.reservationData.valorBagagemAdicional * this.reservationData.bagagemAdicional
        }

        let upgrade = {
          isUpgrade: this.isUpgrade,
          gmvUpgrade: this.gmvUpgrade,
          lastPage: this.previousPage
        }

        const viuNovoCheckout = this.habilitarNovoCheckout && this.isMobile

        reservationhelper.emiteEventosDeCompra(
          reservationResult.travels,
          this.completeStatement?.extrato_reserva,
          this.neverTraveled,
          user,
          this.promo,
          this.$cookies,
          this.travelRemarcada?.id,
          this.isCheckoutOffer,
          origem,
          destino,
          this.reservationData?.addonsPriceConfig,
          bagagemAdicional,
          this.incentivoRemarcacao,
          upgrade,
          this.idsGruposRecomendados,
          viuNovoCheckout,
          this.grupoDestaque
        )
      } catch (error) {
        sentryhelper.captureException(error)
      }

      const travels = reservationResult.travels
      const travelsIda = travels.filter(t => !t.travel_ida)
      const travelsVolta = travels.filter(t => !!t.travel_ida)
      this.$cookies.remove('promo')

      // Params
      const params = {
        status: 'confirmada',
        travelIdaId: travelsIda.map(t => t.id).join(':')
      }

      if (travelsVolta.length > 0) {
        params.travelVoltaId = travelsVolta.map(t => t.id).join(':')
      }

      if (this.alterarReserva) {
        params.status = 'alterada'
      }

      // Query
      const infoAfiliados = this.getInfoAfiliado()
      let query = Object.keys(infoAfiliados).length === 0 ? null : infoAfiliados

      // Assinamos os reservation codes, caso o usuário esteja deslogado, para conseguir buscar as travels.
      if (!this.loggedIn) {
        query = query || {}
        const signedsIda = travelsIda.reduce((acc, x, i) => {
          acc[`signed_ida${i + 1}`] = x.signed
          return acc
        }, {})

        const signedsVolta = travelsVolta.reduce((acc, x, i) => {
          acc[`signed_volta${i + 1}`] = x.signed
          return acc
        }, {})

        query = { ...query, ...signedsIda, ...signedsVolta }
      }

      // No Desafio 3-DS mandamos a pessoa resolver o desafio, antes da reserva confirmada.
      if (travelsIda[0].payment?.status === 'pending_challenge') {
        params.status = 'validacao-seguranca'
        query = query || {}
        query.reservation_id = reservationResult.reservation_id
        if (!this.loggedIn) {
          query.reservation_signed = reservationResult.reservation_signed
        }
      }

      this.clearPurchaseCache()
      if (this.gruposIda.length > 0) {
        this.limpaDataComprada(this.gruposIda[0].datetime_ida)
      }

      // usado apenas para não desbloquear as poltronas na saida da navegação; não deve atrapalhar o fluxo
      try {
        this.$emit('purchase-finished')
      } catch (error) {
        sentryhelper.captureException(error)
      }

      this.$router.push({
        name: 'reserva',
        params,
        query
      })
    },
    handleStatementError(reservationResult) {
      this.clearPurchaseCache()

      const { error, error_code, title, payment_method } = reservationResult

      this.errorTitle = title

      if (this.userPrecisaPagar && this.isCreditCard && this.paymentHasHash) {
        this.reservationData.payment.card_hash = null
      }

      this.processingPurchase = false

      const isRodoviariaError = error_code === 'rodoviaria_error'
      if (isRodoviariaError) {
        this.hasRodoviariaError = isRodoviariaError
        this.erroRodoviaria = { error }
        return
      }

      if (PAYMENT_ERROR_MAP[error_code]) {
        this.hasPaymentErrorAfterPurchase = true
        this.completeStatement.error = PAYMENT_ERROR_MAP[error_code].error
        this.completeStatement.errorCode = error_code
        this.completeStatement.title = PAYMENT_ERROR_MAP[error_code].title
        this.completeStatement.paymentMethod = this.paymentMethod

        if (error && error !== error_code) {
          this.completeStatement.error = error
        }
        return
      }

      const hasRejectedPaymentError = REJECTED_POPUP_ERRORS.includes(error_code)

      this.hasPaymentErrorAfterPurchase = hasRejectedPaymentError
      this.hasStatementError = !hasRejectedPaymentError
      if (!this.completeStatement) {
        this.completeStatement = {}
      }
      this.completeStatement.error = error
      this.completeStatement.errorCode = error_code
      this.completeStatement.title = title
      this.completeStatement.paymentMethod = payment_method
    },
    handleAsyncPayment(purchase) {
      const interval = 2500
      const defaultErrorMessage = { error: 'Aconteceu um erro inesperado', error_code: 'unknown_error' }
      let countDown = 20

      const checkStatusPolling = () => {
        if (countDown === 0) {
          this.handleReservation(defaultErrorMessage)
          return
        }

        countDown -= 1

        setTimeout(async() => {
          let reservationResult = null

          try {
            reservationResult = await checkReservationStatus(purchase.reservation_id, purchase.reservation_signed)
          } catch {
            this.hideInProgessMessage()
            this.handleReservation(defaultErrorMessage)
            return
          }

          const status = reservationResult?.reservation_status

          const handleReservationStatus = {
            concluida: true,
            timeout: true,
            erro_no_processamento: true
          }

          if (status in handleReservationStatus) {
            await this.handleReservation(reservationResult)
            return
          }

          checkStatusPolling()
        }, interval)
      }

      checkStatusPolling()
    },
    validatePayload(payload) {
      let valid = true
      let refName = ''

      const validations = {
        passengers: (valid) => (this.validationErrors.passengers = valid)
      }

      if (this.userPrecisaPagar) {
        validations.payment = (valid) => (this.validationErrors.payment = valid)
      }

      if (this.isRevendedor) {
        validations.user_revenda = (valid) => (this.validationErrors.user = valid)
      } else {
        validations.acceptance = (valid) => (this.validationErrors.acceptance = valid)
      }

      if (this.isRevendedorInterno) {
        validations.protocolo = (valid) => (this.validationErrors.protocoloRevendedor = valid)
      }

      if (!this.loggedIn) {
        validations.email = (valid) => (this.validationErrors.user = valid)
        validations.name = (valid) => (this.validationErrors.user = valid)
        validations.phone = (valid) => (this.validationErrors.user = valid)
      }

      for (const prop in validations) {
        let validProp = Array.isArray(payload[prop]) ? !!payload[prop].length : !!payload[prop]

        if (validProp && prop === 'payment') {
          // Se o método é cartão de crédito e não envia o card_hash o payload não é válido
          if (payload[prop].payment_method === 'credit_card' && !(payload[prop].card_hash)) {
            validProp = false
          }
        }

        if (!validProp) {
          refName = refName || prop
          valid = false

          if (prop === 'email' || prop === 'name' || prop === 'phone' || prop === 'user_revenda') {
            refName = 'user'
          }
        }

        validations[prop](!validProp)
      }

      // Regra específica para validar a data de nascimento no seguro extra
      if (payload.seguro_extra) {
        const temPaxSemDataNascimento = payload.passengers.some(p => !p.birthday)
        if (temPaxSemDataNascimento) {
          this.validationErrors.seguroExtra = true
          refName = refName || 'seguroExtra'
          valid = false
        }
      }

      this.scrollTo(refName)
      return valid
    },
    scrollTo(refName) {
      if (refName === '' || !this.isMobile) return

      const element = this.$refs[refName]
      setTimeout(() => {
        element.scrollIntoView({ behavior: 'smooth' })
      })
    },
    handleProtocoloRevendedor(protocolo) {
      this.protocoloRevendedor = protocolo
      this.handleUpdateStatement()
    },
    handleAceitouCheckoutTerms(isAccepted) {
      this.aceitouCheckoutTerms = isAccepted
    },
    calculateDateDiff() {
      const dateIda = dayjs(this.gruposIda[0].datetime_ida)
      const today = dayjs()
      return dateIda.diff(today, 'hour')
    },
    closeAtualizaPreco() {
      this.atualizaPreco = false
      this.houveAtualizacaoPreco = false
      this.handleUpdateStatement()
    },
    openCheckoutOffer() {
      EventBus.$emit('melhorou-poltrona', {
        reservaBase: {
          valor: this.valorTotal,
          assento: this.gruposIda[0].tipo_assento
        },
        reservaOfertada: {
          acrescimo: this.offerSameGroup.priceDiff.toFixed(2),
          assento: this.offerTipoAssento.toLowerCase()
        }
      })
      const params = { idIda: this.offerSameGroup.id }
      const query = { offer: true }

      setTrechoSign(this.offerSameGroup.id, this.offerSameGroup.signed, 40 * 60)

      this.$router.push({
        name: 'checkout',
        params,
        query
      })
    },
    async updateTrecho() {
      let trechosMarketplace = this.allGroups.filter((g) => g.modelo_venda === 'marketplace')
      trechosMarketplace = trechosMarketplace.map((g) => g.id)
      if (!trechosMarketplace) {
        // só chama se tiver marketplace
        return
      }
      this.atualizandoPreco = true
      if (this.processingPurchase) return
      try {
        await ensureCsrf()
        const trechos = await updateTrecho(trechosMarketplace)
        if (this.processingPurchase) return
        const asyncUpdate = trechos.atualizacao_async_iniciada
        if (asyncUpdate) {
          this.handleAsyncUpdateTrecho(trechosMarketplace)
          return
        }
        this.checkUpdateTrecho(trechos)
      } catch (e) {
        this.atualizandoPreco = false
      }
    },
    checkUpdateTrecho(trechos) {
      const updateTypes = {
        ATUALIZAOU_PRECO: 'atualizou_preco',
        DIMINUIU_PRECO: 'diminuiu_preco',
        ACABARAM_VAGAS: 'acabaram_vagas',
        TRECHO_INDISPONIVEL: 'trecho_indisponivel'
      }
      let popupType = null
      if (trechos.sem_vagas) {
        this.erroRodoviaria = { error: 'Este grupo não possui mais vagas' }
        this.hasRodoviariaError = true
        popupType = updateTypes.ACABARAM_VAGAS
      } else if (trechos.nao_encontrada) {
        this.erroRodoviaria = { error: 'Essa viagem não está mais disponível' }
        this.hasRodoviariaError = true
        popupType = updateTypes.TRECHO_INDISPONIVEL
      } else {
        this.informacoesSobrePrecos = { precoAntigo: trechos.preco_antigo, precoNovo: trechos.preco_novo, abaixouPreco: trechos.abaixou_preco }
        this.atualizaPreco = trechos.updated
        this.abaixouPreco = trechos.abaixou_preco
        popupType = this.abaixouPreco ? updateTypes.DIMINUIU_PRECO : updateTypes.ATUALIZAOU_PRECO
      }
      this.emiteEventoAtualizacaoTrechoMarketplace(trechos, popupType)
      this.atualizandoPreco = false
    },
    emiteEventoAtualizacaoTrechoMarketplace(trechos, popupType) {
      if (!this.atualizaPreco && !this.hasRodoviariaError) {
        return
      }
      const event_params = {
        updated: trechos.updated,
        percentual_divergencia: trechos.percentual_divergencia,
        trecho_classe_ids: trechos.trecho_classe_ids,
        tipo_atualizacao: popupType
      }
      trechos.trecho_classe_ids.forEach((tc) => deleteTrechoSign(tc))

      EventBus.$emit('viu-divergencia-checkout', event_params)
    },
    handleAsyncUpdateTrecho(trechos) {
      const intervals = [1, 2, 3, 4] // espera no máximo 10 segundos
      let countDown = 4
      const checkStatusAtualizacao = () => {
        if (countDown === 0) {
          this.atualizandoPreco = false
          return
        }
        const nextInterval = intervals[4 - countDown]
        countDown -= 1
        const isUltimaVerificacao = countDown === 0
        setTimeout(async() => {
          let atualizacoes = null
          try {
            atualizacoes = await verifyUpdateTrecho(trechos, isUltimaVerificacao, this.signedGroups)
          } catch (e) {
            this.atualizandoPreco = false
            return
          }

          const atualizouNaUltima = (isUltimaVerificacao && atualizacoes.pelo_menos_uma_concluida)

          if (atualizouNaUltima || atualizacoes.atualizacao_concluida) {
            await this.checkUpdateTrecho(atualizacoes)
            return
          }
          checkStatusAtualizacao()
        }, nextInterval * 1000)
      }
      checkStatusAtualizacao()
    },
    chooseOfferSameGroup() {
      const groups = this.sameGroupData || []
      const currentGroup = this.gruposIda[0]
      let ofertaGroup = null

      for (const group of groups) {
        if (group.vagas < this.selectedPassengers.length || prioridade[currentGroup.tipo_assento] >= prioridade[group.tipo_assento] || this.valorIdaUnitario >= group.max_split_value) {
          continue
        }

        ofertaGroup = ofertaGroup && ofertaGroup.vagas > group.vagas ? ofertaGroup : group
      }

      if (ofertaGroup) ofertaGroup.priceDiff = ofertaGroup.max_split_value - this.valorIdaUnitario

      return ofertaGroup
    },
    async setOfferSameGroup() {
      // TODO checar se devemos mostrar para conexões ou volta
      if (this.allGroups.length > 1 || this.isCheckoutOffer) return

      if (!this.sameGroupData && !this.loadingOfferSameGroup) {
        this.loadingOfferSameGroup = true
        try {
          const response = await searchSameGroup(this.gruposIda[0].id)
          this.sameGroupData = response?.groups_by_date?.[0]?.grupos || []
        } catch {
          return
        } finally {
          this.loadingOfferSameGroup = false
        }
      }

      this.setValorIdaUnitario()
      this.showOfferSameGroup = true

      const groupOferta = this.chooseOfferSameGroup()

      this.offerSameGroup = groupOferta
    },
    setValorIdaUnitario() {
      if (this.valorIdaUnitario == null && this.hasSelectedPassengers && this.completeStatement?.extrato_reserva?.[0]?.entries?.[0]?.value >= 0) {
        this.valorIdaUnitario = this.completeStatement.extrato_reserva[0].entries[0].value / this.selectedPassengers.length
      }
    },
    eventDataFromGroup(group) {
      return {
        id: group.id,
        grupoClasseId: group.grupo_classe_id,
        trecho: group.trecho,
        preco: group.max_split_value,
        assento: group.tipo_assento,
        modeloVenda: group.modelo_venda,
        company_id: group.company_id,
        has_marcacao_assento: group.has_marcacao_assento,
        datetime_ida: group.datetime_ida,
        vagas: group.vagas,
        trecho_vendido_id: group.trecho_vendido_id,
        grupo_id: group.grupo_id
      }
    },
    sendPageEntryEvent() {
      const gruposIda = this.gruposIda.map(this.eventDataFromGroup)
      const gruposVolta = this.gruposVolta.map(this.eventDataFromGroup)

      const dateDiff = this.calculateDateDiff()
      const routeParams = this.$route.params

      const viuNovoCheckout = this.habilitarNovoCheckout && this.isMobile

      EventBus.$emit('acessou-pagina-pagamento', {
        gruposIda,
        gruposVolta,
        dateDiff,
        porcentagemSeguroExtra: this.reservationData?.addonsPriceConfig?.seguro_extra?.percentage,
        seguroVariant: this.reservationData?.addonsPriceConfig?.seguro_extra?.max,
        isCheckoutOffer: this.isCheckoutOffer,
        valorBagagemAdicional: this.reservationData.valorBagagemAdicional,
        isUpgradePoltronaIncentivo: routeParams?.isUpgrade && routeParams?.isRedirect,
        hasBuserPremium: this.hasBuserPremium,
        viuNovoCheckout
      })
    },
    updatePassengersBirthdays(birthdays) {
      this.passengersBirthdays = birthdays
    },
    cachePurchase(purchase) {
      if (!purchase) return

      try {
        localstorage.setLocalStorageWithExpiry(CACHE_PURCHASE_KEY, purchase, CACHE_PURCHASE_TIMEOUT)
      } catch (error) {}
    },
    getInProgressPurchase() {
      return localstorage.getLocalStorageWithExpiry(CACHE_PURCHASE_KEY)
    },
    clearPurchaseCache() {
      localstorage.del(CACHE_PURCHASE_KEY)
      this.hideInProgessMessage()
    },
    showInProgressPurchaseMessage() {
      this.hasInProgressPurchase = true
      this.processingPurchase = true
    },
    hideInProgessMessage() {
      this.hasInProgressPurchase = false
    },
    continueInProgressPurchase(purchase) {
      this.showInProgressPurchaseMessage()

      if (purchase.async_payment_flag) {
        this.handleAsyncPayment(purchase)
      }
    },
    openPopupAcessibilidade() {
      this.showPopupAcessibilidade = true
    },
    closePopupAcessibilidade() {
      this.showPopupAcessibilidade = false
    },
    handlePopupItinerario({ value, grupos }) {
      this.popupItinerarioGrupos = grupos
      this.popupItinerarioVisible = value
    }
  },
  computed: {
    ...mapState(useAlterarReservaStore, {
      travelRemarcada: 'travel',
      alterarReserva: 'alterarReserva'
    }),
    ...mapState(useSettingsStore, ['assinaPreco', 'permiteLayoutBlackfriday', 'permiteLayoutPreBlackfriday']),
    ...mapState(useSessionStore, ['loggedIn', 'user', 'neverTraveled']),
    ...mapState(useRevendedorStore, ['isRevendedor', 'isRevendedorInterno']),
    ...mapState(useStatementStore, ['completeStatement', 'parcelamentoOptions', 'isLoadingStatement']),
    ...mapWritableState(useReservationStore, ['reservationData', 'validationErrors']),
    ...mapState(useReservationStore, [
      'gruposIda',
      'gruposVolta',
      'allGroups',
      'countCriancas',
      'selectedPassengers',
      'isUserInfoFilled',
      'isCheckoutOffer',
      'cpfDaPromo',
      'layoutOnibusIda',
      'layoutOnibusVolta'
    ]),
    ...mapState(useSearchStore, ['idsGruposRecomendados', 'grupoDestaque']),
    isBuserUser() {
      return this.user?.email.includes('@buser.com.br')
    },
    shouldShowAlertDesembarqueRapido() {
      return this.gruposIda[0]?.destino?.desembarque_rapido
    },
    pcdPassengerSelected() {
      return this.selectedPassengers.some(
        (pax) => pax.tipos_deficiencia && pax.tipos_deficiencia.length >= 1
      )
    },
    mostrarSeguroExtraNoUpgrade() {
      if (this.isUpgrade && this.temSeguroContratado) {
        return false
      }
      return true
    },
    nonAccessibleSeats() {
      if (!this.pcdPassengerSelected) return null

      let seats = []
      const groups = [...this.gruposIda, ...this.gruposVolta]
      groups.forEach((group) => {
        if (group.tem_acessibilidade) return
        seats.push(group.tipo_assento)
      })
      return (seats.length === 0) ? null : seats
    },
    nonAccessibleSeatsText() {
      const seats = this.nonAccessibleSeats
      if (!seats) return null
      const startText = pluralize(seats.length, 'A poltrona', 'As poltronas', true)
      const possuiText = pluralize(seats.length, 'possui', 'possuem', true)

      const uniqueSeats = [...new Set(seats)]
      const typeText = pluralize(uniqueSeats.length, 'do tipo', 'dos tipos', true)

      let seatsText = ''
      if (uniqueSeats.length === 1) {
        seatsText = `${uniqueSeats[0]}`
      } else if (uniqueSeats.length === 2) {
        seatsText = `${uniqueSeats[0]} e ${uniqueSeats[1]}`
      } else {
        seatsText = `${uniqueSeats.slice(0, -2).join(', ')} e ${uniqueSeats[-1]}`
      }

      return `${startText} ${typeText} ${seatsText} dessa reserva não ${possuiText} acessibilidade para pessoas com dificuldade motora ou cadeira de rodas.`
    },
    idaHasMarcacaoAssento() {
      if (this.gruposIda.length > 1) return false
      if (this.gruposIda[0]?.modelo_venda === 'buser' && !this.isBuserUser) return false
      return this.gruposIda[0]?.has_marcacao_assento // later we need to support connections
    },
    voltaHasMarcacaoAssento() {
      if (this.gruposVolta && this.gruposVolta.length > 1) return false
      if (this.gruposVolta[0]?.modelo_venda === 'buser' && !this.isBuserUser) return false
      return this.gruposVolta[0]?.has_marcacao_assento // later we need to support connections
    },
    showCheckoutPoltronas() {
      return ((this.idaHasMarcacaoAssento || this.voltaHasMarcacaoAssento))
    },
    isPaymentValueInconsistent() {
      return this.reservationData?.payment?.net_value !== this.valorTotal
    },
    isInDateRange() {
      const departureDate = dayjs(this.gruposIda[0].datetime_ida)
      if (!this.travelRemarcada) return false
      if (!this.travelRemarcada.grupo) return false
      const originalTravelDepartureDate = dayjs(this.travelRemarcada.grupo.datetime_ida)
      const departureDatePlus10Days = originalTravelDepartureDate.add(11, 'days')
      const departureDateMinus10Days = originalTravelDepartureDate.subtract(11, 'days')
      return departureDate.isAfter(departureDateMinus10Days) && departureDate.isBefore(departureDatePlus10Days)
    },
    isTravelRemarcadaGratis() {
      return (
        this.$route.params.isTravelRemarcadaGratis &&
        this.gruposIda[0].modelo_venda === 'buser' &&
        this.isInDateRange
      )
    },
    incentivoRemarcacao() {
      return this.$route.params?.incentivoRemarcacao
    },
    parcelamentoOptionsCartaoSelecionado() {
      return this.parcelamentoOptions.find(item => item.bin === this.reservationData.binCartaoSelecionado)?.options || []
    },
    binCartaoSelecionado() {
      const item = this.parcelamentoOptions.find(item => item.bin === this.reservationData.binCartaoSelecionado)
      return item?.bin || ''
    },
    grupoComMenosVagas() {
      const grupoComMenos = this.allGroups.reduce((menorGrupo, group) => {
        const vagasRestantes = getVagasRestantes(group)
        return (!menorGrupo || vagasRestantes < menorGrupo.vagas)
          ? { group, vagas: vagasRestantes }
          : menorGrupo
      }, null)

      return grupoComMenos?.group
    },
    mostraAvisoVagasRestantes() {
      return !this.permiteLayoutBlackfriday && !!getAvisoVagasRestantes(this.grupoComMenosVagas)
    },
    totalDescontos() {
      if (!this.hasSelectedPassengers) return 0
      const idaTaxaRevenda = this.completeStatement?.extrato_reserva?.[0]?.entries?.find(e => e.type === 'taxa_revenda')?.desconto_taxa_servico || 0
      const idaDiscount = Math.abs(this.completeStatement?.extrato_reserva?.[0]?.entries?.find(e => e.title === 'Promoção')?.value || 0)
      const voltaTaxaRevenda = this.completeStatement?.extrato_reserva?.[1]?.entries?.find(e => e.type === 'taxa_revenda')?.desconto_taxa_servico || 0
      const voltaDiscount = Math.abs(this.completeStatement?.extrato_reserva?.[1]?.entries?.find(e => e.title === 'Promoção')?.value || 0)
      return idaTaxaRevenda + idaDiscount + voltaTaxaRevenda + voltaDiscount
    },
    textTotalDesconto() {
      if (this.permiteLayoutPreBlackfriday) {
        return 'Esquenta Black Friday'
      }
      return null
    },
    valorGrupoIda() {
      return this.gruposIda.reduce((partial, g) => partial + g.max_split_value, 0)
    },
    valorGrupoVolta() {
      return this.gruposVolta.reduce((partial, g) => partial + g.max_split_value, 0)
    },
    valorVoltaUnitario() {
      if (this.hasSelectedPassengers && this.completeStatement?.extrato_reserva?.[1]?.entries?.[0]?.value >= 0) {
        return this.completeStatement.extrato_reserva[1].entries[0].value / this.selectedPassengers.length
      }
      return null
    },
    valorTotal() {
      return this.completeStatement?.pagamento_total ?? null
    },
    userPrecisaPagar() {
      return this.valorTotal >= 2
    },
    isUserInfoValid() {
      return this.loggedIn || this.isUserInfoFilled
    },
    shouldLoadStatement() {
      return this.isUserInfoValid && this.hasSelectedPassengers
    },
    isViagemInternacional() {
      return isViagemInternacional(this.gruposIda)
    },
    offerTipoAssento() {
      if (!this.offerSameGroup) return
      return labels[this.offerSameGroup.tipo_assento]
    },
    hasSelectedPassengers() {
      return this.selectedPassengers?.length > 0
    },
    deviceToken() {
      return this.$cookies.get('apnstoken') || this.$cookies.get('pushtoken')
    },
    isCreditCard() {
      return this.reservationData.payment?.payment_method === 'credit_card'
    },
    paymentHasHash() {
      return !!this.reservationData.payment?.card_hash
    },
    isFretamento() {
      return atLeastOneGroupIsBuser(this.allGroups)
    },
    companyNeedsPassengerData() {
      return atLeastOneGroupCompanyHasRequiredPassengerData(this.allGroups)
    },
    paymentMethod() {
      return this.reservationData.payment?.payment_method
    },
    groupsMap() {
      const { gruposIda, gruposVolta } = this
      const { promoCode } = this.reservationData

      const groups = {}
      groups.trechos_ida = gruposIda.map(g => ({
        id: g.id,
        promoCode,
        signed: this.signedGroups[g.id]
      }))
      groups.trechos_volta = gruposVolta.map(g => ({
        id: g.id,
        promoCode,
        signed: this.signedGroups[g.id]
      }))

      return groups
    },
    poltronas() {
      return {
        ida: this.reservationData.poltronasMapIda,
        ...(this.grupoVolta && { volta: this.reservationData.poltronasMapVolta })
      }
    },
    hasMarketplaceOrHibrido() {
      const hasHibrido = atLeastOneGroupIsHibrido(this.allGroups)
      const hasMarketplace = atLeastOneGroupIsMarketplace(this.allGroups)
      return hasHibrido || hasMarketplace
    },
    hasMarketplace() {
      return atLeastOneGroupIsMarketplace(this.allGroups)
    },
    bagagemAdicionalLiberada() {
      const hasConexao = this.gruposIda.length > 1 || this.gruposVolta.length > 1
      const valorBagagem = this.reservationData?.valorBagagemAdicional
      const bagagemAdicionalLiberada = this.reservationData?.bagagemAdicionalLiberada && valorBagagem
      const isNotDesembarqueRapido = !this.gruposIda[0]?.destino?.desembarque_rapido
      return bagagemAdicionalLiberada && !hasConexao && !this.hasMarketplaceOrHibrido && !this.isTravelRemarcadaGratis && isNotDesembarqueRapido
    },
    mostrarBagagemAdicional() {
      return this.bagagemAdicionalLiberada && !this.alterarReserva
    },
    isUpgrade() {
      return Object.keys(this.$route.params?.newTrechoClasseInfo || {}).length > 0
    },
    gmvUpgrade() {
      if (!this.isUpgrade || !this.travelRemarcada) return 0
      const reservaAnteriorExtrato = this.travelRemarcada?.extrato?.extrato_reserva
      let valorReservaAnterior = 0
      if (this.travelRemarcada?.travel_ida) {
        valorReservaAnterior = (reservaAnteriorExtrato?.[1].entries?.find(e => e.title === 'Reserva volta')?.value || 0)
      } else {
        valorReservaAnterior = (reservaAnteriorExtrato?.[0].entries?.find(e => e.title === 'Reserva ida')?.value || 0)
      }
      const valorReservaNovo = this.completeStatement?.extrato_reserva?.[0].entries?.find(e => e.title === 'Reserva ida')?.value || 0
      return (valorReservaNovo - valorReservaAnterior).toFixed(2)
    },
    temSeguroContratado() {
      return this.travelRemarcada?.extrato?.extrato_reserva[0]?.entries?.some((entry) => {
        return entry.title === 'Seguro'
      }) ?? false
    },
    isMobile() {
      return this.$andromeda.breakpoint.smAndDown
    }
  }
}
</script>

<style lang="scss" scoped>
/* TODO: AB novo checkout, remover dpeois */
.cp-checkout-ab-on {
  display: block;
}

.cp-checkout-ab-off {
  display: none;
}

.cp-checkout-ab-only-mobile {
  display: none;

  @media (max-width: $screen-tablet-max) {
    display: block;
  }
}

.cp-checkout-ab-hide-mobile {
  display: block;

  @media (max-width: $screen-tablet-max) {
    display: none;
  }
}

.checkout {
  $column-width: 432px;
  $column-gap: $spacing-3;

  min-height: calc(100vh - #{$taskbar-height-mobile});

  @media (min-width: $screen-tablet-min) {
    min-height: calc(100vh - #{$taskbar-height-desktop});
    padding-bottom: $spacing-2;
  }

  .c-container {
    max-width: $screen-desktop-min;

    .cc-alert-vagas-restantes {
      padding: $spacing-2;
      margin-bottom: $spacing-2;
      font-size: $font-size-sm;

      @media (min-width: $screen-tablet-min) {
        font-size: $font-size-md;
        margin-inline: unset;
      }
    }

    .cc-content {
      display: flex;
      flex-direction: column;

      @media (min-width: $screen-tablet-min) {
        flex-direction: row;
        justify-content: center;
      }

      .ccc-column {
        display: contents; // Ignora este wrapper no mobile

        @media (min-width: $screen-tablet-min) {
          display: block;
          flex: 1;

          &:first-of-type {
            margin-right: $column-gap;
          }
        }

        .novo-checkout-1 {
          order: -4;
        }

        .novo-checkout-2 {
          order: -3;
        }

        .novo-checkout-3 {
          order: -2;
        }

        .novo-checkout-4 {
          order: -1;
        }

        .novo-checkout-5 {
          order: -5;
        }

        .cccc-novo-checkout {
          order: -5;
          position: relative;
          width: 100vw;
          background: linear-gradient(180deg, $color-brand calc(100% - $spacing-4), transparent $spacing-4);
          margin-inline: -#{$spacing-2};
          padding: $spacing-2 $spacing-2 0;
        }

        .cccc-checkout-card {
          margin-bottom: $spacing-2;

          &.checkout-card-ab-on {
            border-radius: $border-radius-md;
            border: 1px solid $color-grey-light;
          }

          .is-statement {
            box-shadow: none;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
          }

          .cccccc-desconto-total {
            @media (min-width: $screen-tablet-min) {
              border: $border-width-hairline solid $color-grey-light;
              border-bottom-left-radius: $border-radius-md;
              border-bottom-right-radius: $border-radius-md;
            }
          }

          &.is-date-ida {
            order: -2;
          }

          &.is-date-volta {
            order: -1;
          }
        }

        .cccc-checkout-offer-same-group-card {
          margin-bottom: $spacing-2;
        }

        .cccc-termos-alert {
          font-size: $font-size-sm;
          word-break: break-all;
        }

        .cccc-pay {
          @media (max-width: $screen-tablet-max) {
            border-radius: 0;
          }
        }
      }
    }
  }
}
</style>
