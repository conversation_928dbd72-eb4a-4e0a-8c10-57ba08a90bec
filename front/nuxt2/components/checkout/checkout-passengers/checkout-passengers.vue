<template>
  <card-travel-info
    :show-content="hasLoaded"
    :title="readOnly ? 'Passageiros' : 'Adicionar passageiro'"
    class="passengers"
    data-sentry-mask
  >
    <template #skeleton>
      <skeleton-passengers data-testid="checkout-passengers-loader" />
    </template>

    <ada-alert
      v-if="validationErrors.passengers"
      type="warning"
      class="mb-2"
    >
      Adicione um ou mais passageiros
    </ada-alert>

    <ul v-if="passengers.length > 0">
      <li
        v-for="(passenger, index) in passengers"
        :key="passenger.id"
        class="p-passenger-item"
      >
        <ada-checkbox
          v-if="!readOnly"
          :disabled="passengerSelectionDisabled(passenger)"
          :value="passenger.selected"
          :label="passenger.name"
          @input="togglePassengerCheckbox(index)"
        >
          <p
            class="fw-500 text-grey-dark"
            :class="{'text-grey': passengerSelectionDisabled(passenger)}"
          >
            {{ passenger.name }}
          </p>
          <p
            v-if="passenger.type === 'ADULTO'"
            class="text-sm text-grey"
            :class="{'text-grey': passengerSelectionDisabled(passenger)}"
          >
            Adulto | CPF: {{ passenger.cpf }}
          </p>
          <p
            v-if="passenger.type === 'CRIANCA'"
            class="text-sm text-grey"
            :class="{'text-grey': passengerSelectionDisabled(passenger)}"
          >
            Criança | RG: {{ passenger.rg_number }}
          </p>
          <p
            v-if="passenger.type === 'ESTRANGEIRO'"
            class="text-sm text-grey"
            :class="{'text-grey': passengerSelectionDisabled(passenger)}"
          >
            Estrangeiro | Passaporte: {{ passenger.rg_number }}
          </p>
        </ada-checkbox>

        <div v-else>
          <p class="fw-bold text-grey-dark">
            {{ passenger.name }}
          </p>
          <p
            v-if="passenger.type === 'ADULTO'"
            class="text-sm text-grey"
          >
            Adulto | CPF: {{ passenger.cpf }}
          </p>
          <p
            v-if="passenger.type === 'CRIANCA'"
            class="text-sm text-grey"
          >
            Criança | RG: {{ passenger.rg_number }}
          </p>
          <p
            v-if="passenger.type === 'ESTRANGEIRO'"
            class="text-sm text-grey"
          >
            Estrangeiro | Passaporte: {{ passenger.rg_number }}
          </p>
        </div>

        <ada-button
          v-if="!readOnly"
          class="ppi-btn"
          transparent
          @click="setEditablePassenger(passenger, index)"
        >
          <span class="text-grey text-sm fw-500">Editar</span>
          <fa
            class="ml-1"
            :icon="faPenToSquare"
          />
        </ada-button>
      </li>
    </ul>

    <template v-if="!readOnly">
      <template v-if="passengers.length > 0 && !fullReason">
        <ada-button
          transparent
          class="p-add-passenger"
          @click="togglePassengerDetail()"
        >
          <strong class="text-md text-brand">Adicionar mais passageiros</strong>
          <fa
            class="color-brand ml-2"
            :icon="faChevronRight"
          />
        </ada-button>
      </template>

      <template v-else-if="!fullReason">
        <ada-button
          v-if="passengers.length < maxPassengersPerReservation"
          transparent
          class="px-1 w-100"
          @click="togglePassengerDetail"
        >
          <fa
            class="color-grey mr-1"
            :icon="faUserPlus"
          />
          <span class="text-sm fw-600">Informe um ou mais passageiros</span>
          <fa
            class="p-button-chevron"
            :icon="faChevronRight"
          />
        </ada-button>
      </template>

      <ada-alert
        v-else
        type="warning"
        class="mt-2"
      >
        {{ fullReason }}
      </ada-alert>

      <ada-alert
        v-if="isFretamento"
        class="mt-1"
        :icon="faBabyCarriage"
      >
        <p>
          É obrigatório o cadastro de crianças como passageiros.
          <ada-button link @click="toggleRegrasViagem">
            Ver regras de viagem
          </ada-button>
        </p>
      </ada-alert>
    </template>

    <popup
      v-model="showPassengerDetails"
      title="Dados do passageiro"
      :modal-options="{ maxWidth: '500px' }"
      @close="clearEditablePassenger"
    >
      <ada-alert
        v-if="showPassengerInvalidDataRequiredMessage"
        type="warning"
        class="mb-2"
      >
        Para continuar com a compra, por favor insira a data de nascimento do passageiro.
      </ada-alert>
      <passenger-form
        :editable-passenger="editablePassenger"
        :logged-in="loggedIn"
        :has-passengers="hasPassengers"
        :dados-comprador="dadosComprador"
        :is-revendedor="isRevendedor"
        @add-passenger="handleCreatePassenger"
        @edit-passenger="handleEditPassenger"
        @delete-passenger="deletePassenger"
      />
    </popup>

    <popup
      v-model="showRegrasViagem"
      title="Viagem com crianças"
    >
      <p class="title-xs mb-1">
        Viagem tipo fretamento Buser
      </p>
      <p class="text-md mb-3">
        É necessário realizar a reserva para menores de idade em viagens do tipo fretamento,
        e eles pagam o valor integral da passagem
        independente da idade, bebês até 1 ano e 5 meses é
        obrigatório o uso da cadeirinha Bebê conforto.
      </p>

      <p class="title-xs mb-1">
        Revenda de viagem rodoviária
      </p>
      <p class="text-md mb-1">
        Viagens rodoviárias não exigem a reserva para crianças de colo,
        que poderão viajar sentadas no colo do pai ou mãe.
        Consulte a regra de viagem no site da empresa.
      </p>
    </popup>
  </card-travel-info>
</template>

<script>
import { faUserPlus, faChevronRight, faBabyCarriage } from '@fortawesome/pro-regular-svg-icons'
import { faPenToSquare } from '@fortawesome/pro-solid-svg-icons'
import { mapActions, mapState, mapWritableState } from 'pinia'
import { editarPassenger, excluirPassenger, adicionarPassenger } from '~api/user.js'
import EventBus from '~/helpers/eventbus.js'
import { useAlterarReservaStore } from '~/stores/alterar-reserva.js'
import { usePassengersStore } from '~/stores/passengers.js'
import { MAX_PASSENGERS_PER_RESERVATION, useReservationStore } from '~/stores/reservation.js'
import { useRevendedorStore } from '~/stores/revendedor.js'
import { useSessionStore } from '~/stores/session.js'
import { useToastStore } from '~/stores/toast.js'
import cardTravelInfo from '~/components/shared/card-travel-info/card-travel-info.vue'
import popup from '~/components/shared/popup/popup.vue'
import passengerForm from '~/components/checkout/checkout-passengers/passenger-form/passenger-form.vue'
import skeletonPassengers from './skeleton-passengers.vue'

export default {
  components: {
    popup,
    skeletonPassengers,
    cardTravelInfo,
    passengerForm
  },
  props: {
    readOnly: {
      type: Boolean,
      default: false
    },
    isFretamento: {
      type: Boolean,
      default: false
    },
    companyNeedsPassengerData: {
      type: Boolean,
      default: false
    },
    linkPagamento: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select-passenger'],
  data() {
    return {
      maxPassengersPerReservation: MAX_PASSENGERS_PER_RESERVATION,
      showPassengerDetails: false,
      showPassengerInvalidDataRequiredMessage: false,
      editablePassenger: null,
      editablePassengerIndex: null,
      hasLoaded: false,
      showRegrasViagem: false,
      faUserPlus,
      faChevronRight,
      faBabyCarriage,
      faPenToSquare
    }
  },
  mounted() {
    this.handleInitPassengers()
  },
  methods: {
    ...mapActions(useToastStore, { openToast: 'open' }),
    ...mapActions(usePassengersStore, ['fetchPassengers', 'fetchPassengersFromUser']),
    ...mapActions(useRevendedorStore, [
      'handleBuyerPassengersAdded',
      'handleBuyerPassengerEdited',
      'handleBuyerPassengerRemoved',
      'handleInitBuyerPassengers',
      'newBuyerPassengersOnly'
    ]),
    ...mapActions(useReservationStore, [
      'initPassengers',
      'updateReservationPassengers',
      'updatePassengers',
      'createPassenger',
      'editPassenger',
      'removePossiblePassenger',
      'togglePassenger'
    ]),
    async handleInitPassengers() {
      if (!this.loggedIn) {
        this.hasLoaded = true
        return
      }
      if (!this.alterarReserva && !this.linkPagamento) {
        if (this.isRevendedor) {
          await this.fetchPassengersFromUser(this.reservationData.user)
          this.initPassengers(this.userPassengers, this.isRevendedor)
          this.handleInitBuyerPassengers(this.userPassengers)
          const newPassengers = this.newBuyerPassengersOnly()
          newPassengers.forEach(pax => (this.createPassenger(pax)))
        } else {
          await this.fetchPassengers()
          this.initPassengers(this.userPassengers)
        }
      }
      this.hasLoaded = true
    },
    async handleUpdatePassengers() {
      if (!this.loggedIn) {
        this.updateReservationPassengers()
        return
      }
      if (this.isRevendedor) {
        await this.fetchPassengersFromUser(this.reservationData.user)
        this.updateReservationPassengers()
        this.maxPassengersPerReservation = this.maxPassengers
        return
      }
      await this.fetchPassengers()
      this.updatePassengers(this.userPassengers)
      this.$emit('select-passenger', 'remocao')
    },
    async handleCreatePassenger(passenger) {
      const persistPassenger = this.loggedIn && !this.isRevendedor
      let pax = persistPassenger ? null : passenger

      if (persistPassenger) {
        try {
          pax = await adicionarPassenger(passenger)
        } catch (error) {
          this.openToast({
            message: 'Ocorreu um erro ao adicionar este passageiro',
            color: 'error',
            error
          })
          return
        }
      }
      this.togglePassengerDetail()

      this.createPassenger(pax)
      if (this.isRevendedor) {
        this.handleBuyerPassengersAdded(passenger)
      }
      EventBus.$emit('adicionou-novo-passageiro')

      const index = this.loggedIn ? this.passengers.findIndex(p => p.id === pax.id) : this.passengers.length - 1

      if (index >= 0) {
        this.togglePassengerCheckbox(index)
      }
    },
    async handleEditPassenger(passenger) {
      const idx = this.editablePassengerIndex
      await this.editPassenger(passenger, idx)

      if (this.loggedIn && !this.isRevendedor) {
        try {
          await editarPassenger(passenger)
        } catch (error) {
          this.openToast({
            message: 'Ocorreu um erro ao editar este passageiro',
            color: 'error',
            error
          })
        }
      }

      if (this.isRevendedor) {
        this.handleBuyerPassengerEdited(passenger, idx)
      }
      this.handleUpdatePassengers()
      this.closeEditPassenger()
    },
    async deletePassenger() {
      if (!this.loggedIn || this.isRevendedor) {
        const idx = this.editablePassengerIndex
        this.togglePassengerCheckbox(idx)
        this.removePossiblePassenger(idx)
        if (this.isRevendedor) {
          this.handleBuyerPassengerRemoved(idx)
        }
        this.handleUpdatePassengers()
        this.closeEditPassenger()
        return
      }

      const passenger = { ...this.editablePassenger }

      try {
        if (!this.isRevendedor) {
          await excluirPassenger(passenger.id)
        }
      } catch (error) {
        this.openToast({
          message: 'Ocorreu um erro ao excluir este passageiro',
          color: 'error',
          error
        })
      }

      this.handleUpdatePassengers()
      this.closeEditPassenger()
    },
    togglePassengerDetail() {
      this.showPassengerDetails = !this.showPassengerDetails
    },
    toggleRegrasViagem() {
      this.showRegrasViagem = true
    },
    togglePassengerCheckbox(index) {
      this.togglePassenger(index)
      this.updateReservationPassengers()
      this.validationErrors.passengers = false
      const actionType = this.passengers[index].selected ? 'adicao' : 'remocao'
      this.$emit('select-passenger', actionType)

      this.showPassengerInvalidDataRequiredMessage = this.paxHasMissingDataRequiredByCompany(index)
      if (this.showPassengerInvalidDataRequiredMessage) {
        this.setEditablePassenger(this.passengers[index], index)
      }
    },
    paxHasMissingDataRequiredByCompany(index_selected_pax) {
      if (!this.companyNeedsPassengerData) return false

      const current_pax = this.passengers[index_selected_pax]

      if (!current_pax) return false

      return current_pax.selected && !current_pax.birthday
    },
    setEditablePassenger(passenger, index) {
      this.editablePassengerIndex = index
      this.editablePassenger = passenger
      this.togglePassengerDetail()
    },
    clearEditablePassenger() {
      if (this.paxHasMissingDataRequiredByCompany(this.editablePassengerIndex)) {
        this.togglePassengerCheckbox(this.editablePassengerIndex)
      }
      this.editablePassenger = null
      this.editablePassengerIndex = null
    },
    closeEditPassenger() {
      this.clearEditablePassenger()
      this.togglePassengerDetail()
    },
    passengerSelectionDisabled(passenger) {
      return !passenger.selected && !!this.fullReason
    }
  },
  computed: {
    ...mapState(useSessionStore, ['loggedIn']),
    ...mapState(useRevendedorStore, ['isRevendedor']),
    ...mapState(useAlterarReservaStore, ['alterarReserva']),
    ...mapState(usePassengersStore, {
      userPassengers: 'passengers'
    }),
    ...mapWritableState(useReservationStore, ['validationErrors']),
    ...mapState(useReservationStore, [
      'reservationData',
      'fullReason',
      'maxPassengers'
    ]),
    passengers() {
      return this.reservationData.possiblePassengers
    },
    hasPassengers() {
      return this.passengers.length > 0
    },
    dadosComprador() {
      if (this.loggedIn && !this.isRevendedor) return
      const dados = {
        name: this.reservationData.user.name,
        type: 'ADULTO'
      }
      if (this.isRevendedor && !this.hasPassengers) {
        dados.phone = this.reservationData.user.phone
      }
      return dados
    }
  },
  watch: {
    'reservationData.user': {
      handler(newValue, oldValue) {
        if (this.isRevendedor && newValue?.id !== oldValue?.id) {
          this.handleInitPassengers()
          this.handleUpdatePassengers()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.passengers {
  .p-add-passenger {
    padding: 0 $spacing-1;
    width: auto;
    margin-top: $spacing-1;
  }

  .p-passenger-item {
    display:flex;
    justify-content: space-between;

    &:not(:first-of-type) {
      margin-top: $spacing-3;
    }

    .ppi-btn {
      flex: 0;
      padding: $spacing-1;
    }
  }

  .p-button-chevron {
    margin-left: auto;
    color: $color-brand;
  }
}
</style>
