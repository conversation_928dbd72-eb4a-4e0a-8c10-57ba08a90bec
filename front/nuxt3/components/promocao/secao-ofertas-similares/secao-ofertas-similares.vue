<template>
  <section class="secao-ofertas-similares">
    <ada-container class="sos-container">
      <h3 class="sosc-title title-sm text-bolder">
        Garanta sua próxima viagem para {{ destino.name }}
      </h3>
      <p class="sosc-subtitle text-sm">
        Compre sua viagem com antecedência e viaje com os melhores preços!
      </p>

      <ada-carousel
        class="sosc-carousel mt-2"
        columns-sm="1"
        columns-md="2"
        columns-lg="3"
      >
        <card-oferta-simples
          v-for="oferta in ofertas"
          :key="oferta.ida.id"
          :source="source"
          :grupo="oferta.ida"
          :tipo="oferta.tipo"
          :checkout-params="{
            idIda: oferta.ida.id,
            idVolta: oferta.volta?.id
          }"
          :trecho-promo-discount="oferta.trechoPromoDiscount"
        />
      </ada-carousel>
    </ada-container>
  </section>
</template>

<script>
import cardOfertaSimples from '~/components/shared/oferta-simples/card-oferta-simples.vue'

export default {
  name: 'SecaoOfertasSimilares',
  components: {
    cardOfertaSimples
  },
  props: {
    origem: {
      type: Object,
      required: true
    },
    destino: {
      type: Object,
      required: true
    },
    ofertas: {
      type: Array,
      required: true
    },
    source: {
      type: String,
      default: 'home'
    }
  }
}
</script>

<style lang="scss">
.secao-ofertas-similares {
  .sos-container {
    padding: 0;

    .sosc-title {
      padding: 0 $spacing-2;
    }

    .sosc-subtitle {
      padding: 0 $spacing-2;
      margin-top: 5px;
    }

    .sosc-carousel {
      .c-container {
        grid-auto-flow: row;

        @media (min-width: $screen-tablet-min) {
          grid-auto-flow: column;
        }
      }
    }
  }
}
</style>
