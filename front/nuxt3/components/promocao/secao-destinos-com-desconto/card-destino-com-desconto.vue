<template>
  <router-link
    class="card-destino-com-desconto"
    :to="searchRoute"
    @click="sendAmplitudeEvent"
  >
    <ada-card class="cdcd-card" rounded>
      <ada-image class="cdcdc-image">
        <img
          v-lazy-load
          :data-src="destino.picture_url"
          :alt="'Foto da cidade de ' + destino.name"
          :aria-hidden="true"
          width="263"
          height="294"
        />
      </ada-image>
      <div class="cdcdc-oferta p-2 text-sm">
        <div class="cdcdco-itinerario">
          <span class="cdcdcoi-hug">
            <span class="cdcdcoih-parada" />
            <span class="cdcdcoih-line" />
            <span class="cdcdcoih-parada" />
          </span>
          <div class="cdcdcoi-cidades ml-1">
            <p>{{ origem.name }} - {{ origem.uf }}</p>
            <p class="text-bolder fw-bold">
              {{ destino.name }} - {{ destino.uf }}
            </p>
          </div>
        </div>

        <div class="cdcdco-valor mt-2">
          <div class="cdcdcov-preco">
            <p class="caption color-grey">A partir de</p>
            <p class="title-xs">{{ melhorPreco }}</p>
          </div>
          <ada-button
            class="cdcdcov-cta"
            color="primary"
            transparent
            outlined
            rounded
          >
            <fa class="color-brand" :icon="faArrowRight" />
          </ada-button>
        </div>
      </div>
    </ada-card>
  </router-link>
</template>

<script>
import { faArrowRight } from '@fortawesome/pro-regular-svg-icons'
import EventBus from '~/helpers/eventbus.js'
import { real } from '~/helpers/formatters.js'

export default {
  name: 'CardDestinoComDesconto',
  props: {
    origem: {
      type: Object,
      required: true
    },
    destino: {
      type: Object,
      required: true
    },
    idx: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      faArrowRight
    }
  },
  computed: {
    melhorPreco() {
      return real(this.destino.best_price)
    },
    searchRoute() {
      return {
        name: 'searchPageV1',
        params: {
          origem: this.origem.slug,
          destino: this.destino.slug
        }
      }
    }
  },
  methods: {
    sendAmplitudeEvent() {
      EventBus.$emit('click-destino-com-desconto', {
        origem: this.origem.slug,
        destino: this.destino.slug,
        idx: this.idx
      })
    }
  }
}
</script>

<style lang="scss">
.card-destino-com-desconto {
  text-decoration: none;
  height: 294px;

  .cdcd-card {
    overflow: hidden;
    position: relative;
    border-radius: $border-radius-lg;

    .cdcdc-oferta {
      position: absolute;
      bottom: $spacing-2;
      left: $spacing-2;
      right: $spacing-2;
      display: flex;
      flex-direction: column;
      background-color: $color-white;
      border-radius: $border-radius-lg;
      color: $color-grey-dark;

      .cdcdco-itinerario {
        display: flex;
        flex-direction: row;

        .cdcdcoi-hug {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          padding-top: 5px;
          padding-bottom: 5px;

          .cdcdcoih-parada {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: $color-grey;
          }

          .cdcdcoih-line {
            width: 2px;
            flex-grow: 1;
            background-color: $color-grey;
          }
        }
      }

      .cdcdco-valor {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .cdcdcov-cta {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}
</style>
