<template>
  <section class="secao-destinos-com-desconto">
    <ada-container class="sdcd-container p-0">
      <h3 class="sdcdc-title title-sm text-bolder ml-2">
        Destinos com desconto em
        <span class="tt-capitalize">{{ mesStr }}</span>
      </h3>

      <ada-carousel
        class="mt-2"
        columns-sm="2"
        columns-md="3"
        columns-lg="4"
        columns-xl="4"
      >
        <card-destino-com-desconto
          v-for="(destino, idx) in destinos"
          :key="destino.slug"
          :idx="idx"
          :origem="origem"
          :destino="destino"
        />
      </ada-carousel>
    </ada-container>
  </section>
</template>

<script>
import cardDestinoComDesconto from '~/components/promocao/secao-destinos-com-desconto/card-destino-com-desconto'

export default {
  name: 'SecaoDestinosComDesconto',
  components: {
    cardDestinoComDesconto
  },
  props: {
    origem: {
      type: Object,
      required: true
    },
    destinos: {
      type: Array,
      required: true
    }
  },
  computed: {
    mesStr() {
      return new Date().toLocaleString('pt-BR', { month: 'long' })
    }
  }
}
</script>
