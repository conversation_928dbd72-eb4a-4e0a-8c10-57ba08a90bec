<template>
  <section v-if="cupons.length > 0" class="secao-meus-cupons">
    <ada-container class="smc-container p-0">
      <h3 class="smcc-title title-sm text-bolder ml-2">Meus cupons</h3>
      <p class="smcc-subtitle text-sm ml-2">
        Para ver a lista completa de cupons acesse a
        <a class="color-brand" href="/perfil/promocoes">página de Cupons</a>
      </p>

      <cupom-list
        class="px-2 mt-2"
        :cupons="cupons"
        @seleciona-cupom="selecionaCupom"
      />
    </ada-container>
  </section>
</template>

<script>
import cupomList from '~/components/promo/cupom-list.vue'

export default {
  name: 'SecaoMeusCupons',
  component: {
    cupomList
  },
  props: {
    cupons: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    selecionaCupom(cupom) {
      this.$router.push({
        name: 'promocaoCode-novo',
        params: {
          code: cupom.code,
          source: 'promo-origin'
        }
      })
    }
  }
}
</script>

<style lang="scss">
.secao-meus-cupons {
  .smc-container {
    .smcc-subtitle {
      margin-top: 5px;
    }
  }
}
</style>
