<template>
  <div
    :class="{
      'is-disabled': disabled
    }"
    class="company-label"
  >
    <span v-if="labelConexao">
      <fa :icon="faArrowProgress" class="color-brand" />

      {{ labelConexao }}
    </span>
    <div v-else-if="!isMarketplaceOrHibrido || (isRevendedorSite && isHibrido)">
      <img alt="logo buser" :src="buserLogo" class="cl-logo" />
      <p v-if="isRevendedorSite && companyLabel" class="cll-name">
        Hibrido - {{ companyLabel }}
      </p>
    </div>

    <p v-else class="cl-label" :class="{ 'mx-auto': !companyLabel }">
      <span> Revenda rodoviária </span>
      <b v-if="companyLabel" class="cll-name">{{ companyLabel }}</b>
    </p>
  </div>
</template>

<script>
import { faArrowProgress } from '@fortawesome/pro-regular-svg-icons'
import { mapState } from 'pinia'
import buserPremiumLogo from '~/assets/images/buser-premium.webp'
import buserFretamentoLogo from '~/assets/images/fretamento.webp'
import * as grouphelper from '~/helpers/grouphelper.js'
import viagensproibidas from '~/helpers/viagensproibidas.js'
import { useRevendedorStore } from '~/stores/revendedor.js'

export default {
  name: 'CompanyLogo',
  props: {
    grupo: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hasInfosHibridoRestringidas: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      buserFretamentoLogo,
      buserPremiumLogo,
      faArrowProgress
    }
  },
  computed: {
    ...mapState(useRevendedorStore, ['isRevendedorSite']),
    isMarketplaceOrHibrido() {
      return grouphelper.isMarketplaceOrHibrido(this.grupo)
    },
    isHibrido() {
      return grouphelper.isHibrido(this.grupo)
    },
    isPremium() {
      return grouphelper.groupsHaveBuserPremium([this.grupo])
    },
    // Usado para casos de UFs onde o fretamento foi proibido e o grupo é
    // híbrido. Nome e Logo devem ser da empresa licenciadora (parent_company)
    isHibridoRestringido() {
      return (
        this.isHibrido &&
        this.hasInfosHibridoRestringidas &&
        viagensproibidas.checkHasInfosHibridoRestringidas(this.grupo)
      )
    },
    companyLabel() {
      let text

      if (this.isHibridoRestringido) {
        text = this.grupo?.parent_company_name // Melhor não mostrar nada se não tiver nome do que arriscar ter problemas legais mostrando nome da empresa do busão
      } else if (this.isMarketplaceOrHibrido) {
        text = this.grupo?.company_name
      }
      return text
    },
    labelConexao() {
      return this.grupo.is_conexao ? 'Viagem com conexão' : null
    },
    buserLogo() {
      return this.isPremium ? this.buserPremiumLogo : this.buserFretamentoLogo
    }
  }
}
</script>

<style lang="scss" scoped>
.company-label {
  min-width: 150px;
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  line-height: 1.2;

  @media (min-width: $screen-desktop-min) {
    max-width: 160px;
  }

  &.is-disabled {
    background-color: inherit;
    border-color: $color-grey;
  }

  .cl-logo {
    object-fit: contain;
    width: 100%;
    max-width: 150px;
    height: 14px;
    mix-blend-mode: multiply;
  }

  .cl-label {
    font-weight: $font-weight-regular;
    display: flex;
    flex-direction: column;

    .cll-name {
      font-weight: 500;
      max-width: 180px;

      @media (min-width: $screen-desktop-min) {
        display: block;
        margin-top: 2px;
      }
    }
  }
}
</style>
