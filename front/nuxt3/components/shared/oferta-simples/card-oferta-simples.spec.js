import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import userEvent from '@testing-library/user-event'
import { render, screen } from '@testing-library/vue'
import EventBus from '~/helpers/eventbus.js'
import cardOfertaSimples from './card-oferta-simples.vue'

const $router = {
  replace: vi.fn()
}

describe('CardOfertaSimples', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render correctly', async () => {
    const eventBusEmitSpy = vi.spyOn(EventBus, '$emit')
    const group = {
      id: 'ble',
      tipo_assento: 'leito',
      origem: { name: '<PERSON>assour<PERSON>', uf: 'SP' },
      destino: { name: 'Tagamandá<PERSON>', uf: 'SP' },
      datetime_ida: '2023-04-11T19:15:00-03:00',
      max_split_value: 20
    }

    const $pinia = createTestingPinia({})
    render(cardOfertaSimples, {
      global: {
        stubs: ['router-link'],
        plugins: [$pinia],
        mocks: [$router]
      },
      props: {
        label: 'Recomendado',
        tipo: 'melhor-tempo',
        grupo: group,
        checkoutParams: { idIda: 'ble' },
        trechoPromoDiscount: 0.2,
        source: 'promo-origin'
      }
    })

    expect(screen.getByText(/Recomendado/i)).toBeInTheDocument()
    // 20 - (1 - 0.2) = 16
    expect(screen.getByText(/16/)).toBeInTheDocument()

    const selectBtn = screen.getByText('Selecionar')
    await userEvent.click(selectBtn)

    expect(eventBusEmitSpy).toHaveBeenCalledWith('click-oferta-simples', {
      grupo: group,
      tipo: 'melhor-tempo',
      source: 'promo-origin'
    })
  })
})
