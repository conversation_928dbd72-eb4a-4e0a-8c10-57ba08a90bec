<template>
  <div class="menu-links" :class="getClass">
    <router-link :to="{ name: 'home' }" class="ml-item"><PERSON><PERSON><PERSON></router-link>

    <router-link :to="{ name: 'promoOrigin' }" class="ml-item">
      Promoções
    </router-link>

    <router-link
      v-if="isEsquentaOrBlackFriday"
      :to="{ name: 'blackFriday' }"
      class="ml-item"
    >
      Black Friday
    </router-link>

    <router-link v-else :to="{ name: 'descubra' }" class="ml-item">
      Descubra
    </router-link>

    <router-link :to="{ name: 'viagens' }" class="ml-item">
      Viagens
    </router-link>

    <router-link :to="{ name: 'ajuda' }" class="ml-item"> Ajuda </router-link>

    <slot />
  </div>
</template>

<script setup>
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { useSettingsStore } from '~/stores/settings.js'

const props = defineProps({
  transparent: {
    type: Boolean,
    default: false
  },
  dark: {
    type: Boolean,
    default: false
  }
})

const { permiteLayoutBlackfriday, permiteLayoutPreBlackfriday } =
  storeToRefs(useSettingsStore())
const isEsquentaOrBlackFriday = computed(
  () => permiteLayoutBlackfriday.value || permiteLayoutPreBlackfriday.value
)
const getClass = computed(() => {
  return {
    'is-transparent': props.transparent,
    'is-dark': props.dark
  }
})
</script>

<style lang="scss" scoped>
.menu-links {
  display: flex;
  align-items: center;
  gap: $spacing-1;

  &.is-dark {
    .ml-item {
      color: $color-white;

      &:hover {
        &::before {
          background-color: $color-white;
        }
      }
    }
  }

  &.is-transparent {
    .ml-item {
      color: $color-white;
      background-color: rgb(0 0 0 / 50%);
    }
  }

  .ml-item {
    color: $color-grey-dark;
    border-radius: $border-radius-pill;
    padding: calc(#{$spacing-1} - 2px) $spacing-2;
    font-weight: $font-weight-medium;
    transition: background-color $duration-faster ease-out;

    &:hover {
      background-color: $color-brand;
      text-decoration: none;
      color: $color-white;
    }

    &--with-badge {
      position: relative;

      .ml-badge {
        position: absolute;
        top: -8px;
        right: -8px;
        z-index: 1;
        border-radius: 100px;
        height: 17px;
        width: 35px;
        font-size: 10px;
      }
    }
  }
}
</style>
