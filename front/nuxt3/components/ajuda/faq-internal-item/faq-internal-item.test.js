import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render, screen } from '@testing-library/vue'
import { questions } from '~/api/mock/data/db_questions.js'
import faqinternalItem from './faq-internal-item.vue'

// RENDER COMPONENT
const renderFaqInternalItem = (props, permissions) => {
  const pinia = createTestingPinia({
    initialState: {
      session: {
        currentSession: {
          authenticated: true,
          user: {
            permissions
          }
        }
      }
    }
  })

  return render(faqinternalItem, {
    props,
    global: {
      plugins: [pinia],
      stubs: ['router-link']
    }
  })
}

describe('<faq-internal-item />', () => {
  it('should render properly', () => {
    const { html } = renderFaqInternalItem({
      topicSlug: 'test-topic',
      question: questions[0]
    })
    expect(html()).toMatchSnapshot()
  })

  it.each([
    { isStaff: true, expectedCount: 1 },
    { isStaff: false, expectedCount: 0 }
  ])(
    'should show "Copiar link" button when user is loggedIn and STAFF profile is $isStaff',
    ({ isStaff, expectedCount }) => {
      renderFaqInternalItem(
        {
          question: questions[0],
          topicSlug: 'test-topic'
        },
        { STAFF: isStaff }
      )
      expect(screen.queryAllByText('Copiar link')).toHaveLength(expectedCount)
    }
  )
})
