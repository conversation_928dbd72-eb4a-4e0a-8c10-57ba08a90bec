/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineNuxtPlugin } from '#app'
import dayjs from 'dayjs'
import amplitudehelper from '~/helpers/amplitudehelper.js'
import EventBus from '~/helpers/eventbus.js'
import { dateFormat, DATE_FORMATS } from '~/helpers/formatters.js'
import { getGroupInfo } from '~/helpers/grouphelper.js'
import leadhelper from '~/helpers/leadhelper.js'
import privacyhelper from '~/helpers/privacyhelper.js'
import useracquisitionhelper from '~/helpers/useracquisitionhelper.js'

const shouldTrackData = {
  googleEvents: privacyhelper.shouldTrackData('googleEvents'),
  hotjar: privacyhelper.shouldTrackData('hotjar')
}

const getTravelInfo = (travel: any) => {
  const info: any = getGroupInfo(travel.grupo)
  info.promocao = travel.promocao
  info.is_volta = !!travel.is_volta
  info.probability = travel.probability_on_reservation
  info.reservation_code = travel.reservation_code // Não remover porque isso é inserido no dataLayer e vai pra os eventos de conversão de afiliados
  info.valor_pagamento = travel.cash || 0
  info.travel_id = travel.id
  info.max_split_value = travel.max_split_value
  info.travel_status = travel.status
  info.user_id = travel.user_id
  info.isFirstTravel = travel.isFirstTravel
  info.neverTraveled = travel.neverTraveled
  info.difDataDeAgora = travel.difDataDeAgora

  if (travel.payment) {
    const p = travel.payment
    info.value = p.value
    info.valor_integral = travel.max_split_value
    info.payment_method = p.method
    info.parcelas = p.parcela_count
    info.parcela_value = p.parcela_value
  }

  return info
}

const getTrechoInfo = (trecho: any) => {
  const origem = trecho.origem ? trecho.origem.slug : null
  const destino = trecho.destino ? trecho.destino.slug : null
  const params = {
    origem,
    destino,
    trecho:
      origem && destino
        ? origem > destino
          ? `${destino}=${origem}`
          : `${origem}=${destino}`
        : '',
    photo: trecho.photo,
    photo_cc: trecho.photo_cc,
    preco_rodoviaria: trecho.preco_rodoviaria,
    rateio: trecho.rateio,
    tipo_assento: trecho.tipo_assento
  }
  return params
}

function _google_event(o: any) {
  if (shouldTrackData.googleEvents) {
    if (!window.dataLayer) {
      window.dataLayer = []
    }
    window.dataLayer.push(o)
  }
}

async function _ampUserLoggedIn() {
  await amplitudehelper.setUserId()
}

async function _ampUserLoggedOut() {
  await amplitudehelper.resetTrack()
}

async function _amplitude_event(eventType: any, eventData: any) {
  await amplitudehelper.track(eventType, eventData)
}

// function _hotjar_event(eventName: any) {
//   if (shouldTrackData.hotjar && window.hj) {
//     window.hj('event', eventName)
//   }
// }

async function _send(
  type: any,
  data: any,
  { amplitude = false, google = false } = {}
) {
  const grupo = data?.grupo
  const travel = data?.travel
  const reserva = data?.reserva
  const trecho = data?.trecho
  let eventData = {}
  if (travel) {
    eventData = { ...getTravelInfo(travel) }
  } else if (grupo) {
    eventData = { ...getGroupInfo(grupo) }
  } else if (reserva) {
    eventData = {
      grupo_ida: reserva.grupoIda ? getGroupInfo(reserva.grupoIda) : null,
      grupo_volta: reserva.grupoVolta ? getGroupInfo(reserva.grupoVolta) : null
    }
  } else if (trecho) {
    eventData = { ...getTrechoInfo(trecho) }
  }
  if (amplitude) {
    const ampData = {
      ...eventData,
      ...amplitudehelper.cleanData(data),
      ...amplitudehelper.metaData()
    }
    await _amplitude_event(type, ampData)
  }
  if (google) {
    const metaData = useracquisitionhelper.loadMetaData()
    const googleData = { event: type, ...metaData }
    if (travel) {
      _google_event({
        id: travel.id,
        ...googleData,
        ...getGroupInfo(travel.grupo)
      })
    } else {
      _google_event({ ...googleData, ...data })
    }
  }
  // if (hotjar) {
  //   _hotjar_event(type)
  // }
}

EventBus.$on('click-search', async (grupo) => {
  if (!grupo) {
    return {}
  }
  await _send(
    'click-search',
    { grupo },
    {
      amplitude: false,
      google: true
    }
  )
})

EventBus.$on('viu-cidades-proximas', async (params) => {
  await _send('viu-cidades-proximas', params, { amplitude: true })
})

EventBus.$on('acessou-pagina-black-friday', async (params) => {
  await _send('acessou-pagina-black-friday', params, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('viu-ofertas-bf', async (params) => {
  await _send('viu-ofertas-bf', params, { amplitude: true })
})

EventBus.$on('acessou-pagina-feriado', async (params) => {
  await _send('acessou-pagina-feriado', params, { amplitude: true })
})

EventBus.$on('acessou-pagamento-pendente', async (saldoDevedor) => {
  await _send(
    'acessou-pagamento-pendente',
    { saldoDevedor },
    { amplitude: true }
  )
})

EventBus.$on('redirecionado-pagamento-pendente', async (reason) => {
  await _send(
    'redirecionado-pagamento-pendente',
    { reason },
    { amplitude: true }
  )
})

EventBus.$on('erro-pagamento-pendente', async (message) => {
  await _send(
    'erro-pagamento-pendente',
    { error: message },
    { amplitude: true }
  )
})

EventBus.$on('gerou-pagamento-pendente', async (saldoDevedor) => {
  await _send('gerou-pagamento-pendente', { saldoDevedor }, { amplitude: true })
})

EventBus.$on('view-search-content', async (grupo) => {
  await _send('view-search-content', { grupo }, { google: true })
})

EventBus.$on('click-oferta-simples', async (params) => {
  await _send('click-oferta-simples', params, { amplitude: true })
})

EventBus.$on('open-search-result-page', async (data) => {
  await _send('open-search-result-page', data, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('changed-search-result-params', async (params) => {
  await _send('changed-search-result-params', params, { amplitude: true })
})

EventBus.$on('acessou-promo-origin', async (params) => {
  await _send('acessou-promo-origin', params, { amplitude: true, google: true })
})

EventBus.$on('acessou-promo-destination', async (params) => {
  await _send('acessou-promo-destination', params, { amplitude: true })
})

EventBus.$on('solicitou-reserva', async (params: any) => {
  const travel: any = params.travel
  await _send(
    'solicitou-reserva',
    {
      travel,
      grupo: travel.grupo,
      value: travel.max_split_value,
      valorCarbono: travel.valorCarbono,
      seguroExtra: !!travel.valorSeguroExtra,
      porcentagemSeguroExtra:
        params.addonsPriceConfig?.seguro_extra?.percentage,
      seguroVariant: params.addonsPriceConfig?.seguro_extra?.max,
      valorSeguroExtra: travel.valorSeguroExtra,
      valorMarcacaoAssento: travel.valorMarcacaoAssento,
      hasDescontoTaxaRevenda: travel.hasDescontoTaxaRevenda,
      descontoTaxaRevenda: travel.descontoTaxaRevenda,
      gmv: travel.max_split_value * travel.count_seats,
      isCheckoutOffer: params.isCheckoutOffer,
      bagagemAdicional: params?.bagagemAdicional,
      incentivoRemarcacao: params?.incentivoRemarcacao,
      isRecomendado: travel.isRecomendado,
      hasBuserPremium: params?.hasBuserPremium,
      viuNovoCheckout: params?.viuNovoCheckout,
      fromGoogleTransit: travel.fromGoogleTransit
    },
    { amplitude: true }
  )

  // evento do google analytics ecommerce
  const metaData: any = useracquisitionhelper.loadMetaData()
  const travelInfo: any = getTravelInfo(travel)
  const groupInfo: any = getGroupInfo(travel.grupo)

  _google_event({
    ...groupInfo,
    ...metaData,
    // Propriedades usadas no purchase do gtm (não consideramos o valor da neutralização do carbono)
    event: 'purchase',
    paidValue: travelInfo.valor_pagamento - travel.valorCarbono,
    transactionId: travel.id,
    promo: travel.promo,
    transactionProducts: [
      {
        item_id: `${groupInfo.origem}---${groupInfo.destino}`,
        item_category: travel.grupo.modelo_venda,
        item_brand: groupInfo.origem,
        discount: travel.discount / travel.count_seats,
        price:
          travel.max_split_value +
          (travel.taxaRevenda + travel.valorSeguroExtra - travel.discount) /
            travel.count_seats,
        quantity: travel.count_seats
      }
    ],
    // Fim
    id: travel.id,
    purchaseSource: travel.source,
    purchaseSourceId: travel.source_id,
    typeSale: travel.grupo.modelo_venda,
    transactionAffiliation: travelInfo.trecho,
    transactionTotal: travel.max_split_value,
    conversionValue: travel.max_split_value,
    purchaseTime: dayjs().format('YYYYMMDDTHHmmss[Z]'),
    paymentMethod: travelInfo.payment_method || 'viagem_gratis',
    reservationCode: travelInfo.reservation_code,
    neverTraveled: travel.neverTraveled,
    phone: params?.currentUser?.cell_phone,
    email: params?.currentUser?.email,
    travelRemarcadaId: params?.travelRemarcadaId,
    dataIda: dateFormat(DATE_FORMATS.dbdatetime, travel.grupo.datetime_ida),
    isVolta: travel.is_volta
  })
})

EventBus.$on('clicou-upgrade-poltrona', async (diffValue) => {
  await _send('clicou-upgrade-poltrona', { diffValue }, { amplitude: true })
})

EventBus.$on('efetuou-upgrade-poltrona', async (params) => {
  await _send('efetuou-upgrade-poltrona', params, { amplitude: true })
})

EventBus.$on('purchase-confirmed', async (travel: any) => {
  await _send('purchase-confirmed', {
    travel,
    grupo: travel.grupo,
    value: travel.max_split_value
  })
})

EventBus.$on('clicou-btn-ja-autorizei', async () => {
  await _send('clicou-btn-ja-autorizei', {}, { amplitude: true })
})

EventBus.$on('click-share-facebook', async (params) => {
  await _send('click-share-facebook', params, { amplitude: true })
})

EventBus.$on('click-share-whatsapp', async (source) => {
  await _send('click-share-whatsapp', { source }, { amplitude: true })
})

EventBus.$on('click-share-link', async (source) => {
  await _send('click-share-link', { source }, { amplitude: true })
})

EventBus.$on('registration-completed', async () => {
  await _send('registration-completed', {}, { amplitude: true })
})

EventBus.$on('acessou-mgm-lead-page', async (params) => {
  await _send('acessou-mgm-lead-page', params, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('phone-confirmed', async (params) => {
  await _send('phone-confirmed', params)
})

EventBus.$on('acessou-home', async (params) => {
  await _send('acessou-home', params, { amplitude: true, google: true })
})
EventBus.$on('abandonou-carrinho', async (params) => {
  await _send('abandonou-carrinho', params, { google: true })
})
EventBus.$on('acessou-item-carousel-novos-negocios', async (data) => {
  await _send(
    'acessou-item-carousel-novos-negocios',
    { data },
    { amplitude: true }
  )
})

EventBus.$on('acessou-ajuda', async (params) => {
  await _send('acessou-ajuda', params)
})

EventBus.$on('enviou-email-ajuda', async (params) => {
  await _send('enviou-email-ajuda', params, { amplitude: true })
})

EventBus.$on('entrou-fale-conosco', async (params) => {
  await _send('entrou-fale-conosco', params, { amplitude: true })
})

EventBus.$on('acessou-promocoes', async (params) => {
  await _send('acessou-promocoes', params, { amplitude: true })
})

EventBus.$on('acessou-parceiros', async () => {
  await _send('acessou-parceiros', {}, { amplitude: true })
})

EventBus.$on('acessou-form-fretamento-colaborativo', async () => {
  await _send('acessou-form-fretamento-colaborativo', {}, { amplitude: true })
})

EventBus.$on('acessou-form-marketplace', async () => {
  await _send('acessou-form-marketplace', {}, { amplitude: true })
})

EventBus.$on('acessou-viagens-proximas', async (params) => {
  await _send('acessou-viagens-proximas', params, { amplitude: true })
})

EventBus.$on('acessou-viagens-anteriores', async (params) => {
  await _send('acessou-viagens-anteriores', params, {
    amplitude: true
  })
})

EventBus.$on('opened-help-chat', async (params) => {
  await _send('opened-help-chat', params, { amplitude: true })
})

EventBus.$on('fale-pelo-chat', async (params) => {
  await _send('fale-pelo-chat', params, { amplitude: true })
})

EventBus.$on('acessou-travel', async (travel) => {
  await _send('acessou-travel', { travel }, { amplitude: true })
})

EventBus.$on('acessou-cadastro', async () => {
  await _send('acessou-cadastro', {}, { amplitude: true })
})

EventBus.$on('cadastro', async (params) => {
  await _send('cadastro', params, { amplitude: true, google: true })
  await _ampUserLoggedIn()
})

EventBus.$on('login', async (data) => {
  await _send('login', leadhelper.parseDataUser(data), {
    amplitude: true,
    google: true
  })
  await _ampUserLoggedIn()
})

EventBus.$on('logout', async (data) => {
  await _send('logout', data, { amplitude: true, google: true })
  await _ampUserLoggedOut()
})

EventBus.$on('acessou-pagamento', async (reserva) => {
  await _send('acessou-pagamento', { reserva }, { amplitude: true })
})

EventBus.$on('acessou-compra-confirmada', async (reserva) => {
  await _send('acessou-compra-confirmada', { reserva }, { amplitude: true })
})

EventBus.$on('started-purchase-as-visitor', async () => {
  await _send('started-purchase-as-visitor', {}, { amplitude: false })
})

EventBus.$on('lead-inscreveu-campanha-sul', async (email) => {
  await _send('lead-inscreveu-campanha-sul', { email })
})

EventBus.$on('acessou-promocao', async (data) => {
  await _send('acessou-promocao', data, { amplitude: true })
})

EventBus.$on('update-amplitude-user', async (props: any) => {
  await amplitudehelper.setProperty(props.label, props.value)
})

EventBus.$on('clicou-em-feedback-trechos', async (data) => {
  await _send('clicou-em-feedback-trechos', data, { amplitude: true })
})

EventBus.$on('ativou-promocao', async (data) => {
  await _send('ativou-promocao', data, { amplitude: true })
})

EventBus.$on('acessou-promocao-form', async (data) => {
  await _send('acessou-promocao-form', data, { amplitude: true })
})

EventBus.$on('campanha-artesp-acessou', async () => {
  await _send('campanha-artesp-acessou', {}, { amplitude: true })
})

EventBus.$on('campanha-artesp-enviou', async () => {
  await _send('campanha-artesp-enviou', {}, { amplitude: true })
})

EventBus.$on('campanha-artesp-compartilhou', async (data) => {
  await _send('campanha-artesp-compartilhou', data, {
    amplitude: true
  })
})

EventBus.$on('pix-visualizou', async () => {
  await _send('pix-visualizou', {}, { amplitude: true })
})

EventBus.$on('wtp-visualizou', async () => {
  await _send('wtp-visualizou', {}, { amplitude: true })
})

EventBus.$on('pix-compartilhou', async () => {
  await _send('pix-compartilhou', {}, { amplitude: true })
})

EventBus.$on('popup-lead-cadastrou', async (lead) => {
  await _send('popup-lead-cadastrou', lead, { amplitude: true })
})

EventBus.$on('clicou-vendas-wpp', async () => {
  await _send('clicou-vendas-wpp', {}, { amplitude: true })
})

EventBus.$on('question-util', async (data) => {
  await _send('question-util', data, { amplitude: true })
})

EventBus.$on('selecionou-grupo', async (params) => {
  await _send('selecionou-grupo', params, { amplitude: true, google: true })
})

EventBus.$on('viu-grupo-test-ab-delay-preco-search', async (params) => {
  await _send('viu-grupo-test-ab-delay-preco-search', params, {
    amplitude: true
  })
})

EventBus.$on('ligou-emergencia', async (params) => {
  await _send('ligou-emergencia', params, { amplitude: true })
})

EventBus.$on('clicou-badge-modelo-venda', async () => {
  await _send('clicou-badge-modelo-venda', {}, { amplitude: true })
})

EventBus.$on('clicou-local-retirada', async () => {
  await _send('clicou-local-retirada', {}, { amplitude: true })
})

EventBus.$on('acessou-landing-page', async (data) => {
  await _send('acessou-landing-page', data, { amplitude: true })
})

EventBus.$on('acessou-landing-page-destinos-inverno', async (data) => {
  await _send('acessou-landing-page-destinos-inverno', data, {
    amplitude: true
  })
})

EventBus.$on('acessou-url-promo-pricing', async (data) => {
  await _send('acessou-url-promo-pricing', data, { amplitude: true })
})

EventBus.$on('clicou-card-destino-inverno', async (data) => {
  await _send('clicou-card-destino-inverno', data, { amplitude: true })
})

EventBus.$on('click-outra-data-sugerida', async () => {
  await _send('click-outra-data-sugerida', {}, { amplitude: true })
})

EventBus.$on('lead-gerado', async (params) => {
  await _send('lead-gerado', params, { amplitude: true, google: true })
})

EventBus.$on('lead-atualizado', async (params) => {
  await _send('lead-atualizado', params, { amplitude: true, google: true })
})

EventBus.$on('acessou-definicao-senha-social', async () => {
  await _send('acessou-definicao-senha-social', {}, { amplitude: true })
})

EventBus.$on('acessou-login', async (params) => {
  await _send('acessou-login', params, { amplitude: true })
})

EventBus.$on('acessou-colocar-senha', async () => {
  await _send('acessou-colocar-senha', {}, { amplitude: true })
})

EventBus.$on('acessou-token', async () => {
  await _send('acessou-token', {}, { amplitude: true })
})

EventBus.$on('clicou-carrossel-promocoes', async (params) => {
  await _send('clicou-carrossel-promocoes', params, { amplitude: true })
})

EventBus.$on('clicou-botao-ajuda', async (params) => {
  await _send('clicou-botao-ajuda', params, { amplitude: true })
})

EventBus.$on('clicou-botao-atendimento-libras', async (params) => {
  await _send('clicou-botao-atendimento-libras', params, { amplitude: true })
})

EventBus.$on('clicou-ajuda-travel', async (params) => {
  await _send('clicou-ajuda-travel', params, { amplitude: true })
})

EventBus.$on('acessou-ajuda-travel', async (params) => {
  await _send('acessou-ajuda-travel', params, { amplitude: true })
})

EventBus.$on('acessou-ajuda-por', async (data) => {
  await _send('acessou-ajuda-por', data, { amplitude: true })
})

EventBus.$on('clicou-telefone-parceiro', async (params) => {
  await _send('clicou-telefone-parceiro', params, { amplitude: true })
})

EventBus.$on('clicou-reportar-falha-mecanica', async (params) => {
  await _send('clicou-reportar-falha-mecanica', params, { amplitude: true })
})

EventBus.$on('clicou-fale-buser-marketplace', async (params) => {
  await _send('clicou-fale-buser-marketplace', params, { amplitude: true })
})

EventBus.$on('acessou-first-lead-page', async (params) => {
  await _send('acessou-first-lead-page', params, { amplitude: true })
})

EventBus.$on('clicou-avaliar-app', async () => {
  await _send('clicou-avaliar-app', {}, { amplitude: true })
})

EventBus.$on('clicou-login', async (page) => {
  await _send('clicou-login', { page }, { amplitude: true })
})

EventBus.$on('clicou-opcao-ordenacao', async (opcao) => {
  await _send('clicou-opcao-ordenacao', { opcao }, { amplitude: true })
})

EventBus.$on('viu-todos-horarios', async (params) => {
  await _send('viu-todos-horarios', params, { amplitude: true })
})

EventBus.$on('abriu-menu-filtros', async (params) => {
  await _send('abriu-menu-filtros', params, { amplitude: true })
})

EventBus.$on('aplicou-filtro', async (params) => {
  await _send('aplicou-filtro', params, { amplitude: true })
})

EventBus.$on('clicou-aceite-termos-uso', async (aceitou) => {
  await _send('clicou-aceite-termos-uso', { aceitou }, { amplitude: true })
})

EventBus.$on('clicou-ver-detalhes', async (params) => {
  await _send('clicou-ver-detalhes', params, { amplitude: true })
})

EventBus.$on('abriu-poltronas-modal', async (params) => {
  await _send('abriu-poltronas-modal', params, { amplitude: true })
})

EventBus.$on('abriu-poltronas-marcadas-modal', async (params) => {
  await _send('abriu-poltronas-marcadas-modal', params, { amplitude: true })
})

EventBus.$on('clicou-banner-whatsapp', async () => {
  await _send('clicou-banner-whatsapp', {}, { amplitude: true })
})

EventBus.$on('clicou-login-social', async (params) => {
  await _send('clicou-login-social', params, { amplitude: true })
})

EventBus.$on('clicou-instalar-app', async (params) => {
  await _send('clicou-instalar-app', params, { amplitude: true, google: true })
})

// RETROSPECTIVA
EventBus.$on('acessou-pagina-retrospectiva', async (params) => {
  await _send('acessou-pagina-retrospectiva', params, { amplitude: true })
})

EventBus.$on('clicou-compartilhar-card', async (params) => {
  await _send('clicou-compartilhar-card', params, { amplitude: true })
})

EventBus.$on('clicou-ver-ofertas-retrospectiva', async () => {
  await _send('clicou-ver-ofertas-retrospectiva', {}, { amplitude: true })
})

EventBus.$on('clicou-copiar-link-mgm', async (params) => {
  await _send('clicou-copiar-link-mgm', params, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('clicou-zap-link-mgm', async (params) => {
  await _send('clicou-zap-link-mgm', params, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('acessou-busermais', async () => {
  await _send('acessou-busermais', {}, { amplitude: true })
})

// PESQUISA PÓS-EMBARQUE
EventBus.$on('erro-exibir-avaliacao-pos-embarque', async (params) => {
  await _send('erro-exibir-avaliacao-pos-embarque', params, { amplitude: true })
})

EventBus.$on('erro-enviar-avaliacao-pos-embarque', async (params) => {
  await _send('erro-enviar-avaliacao-pos-embarque', params, { amplitude: true })
})

// PESQUISA PÓS-VIAGEM
EventBus.$on('acessou-pagina-nova-pesquisa-da-viagem', async () => {
  await _send('acessou-pagina-nova-pesquisa-da-viagem', {}, { amplitude: true })
})

EventBus.$on('clicou-avaliar-app', async (params) => {
  await _send('clicou-avaliar-app', params, { amplitude: true })
})

EventBus.$on('clicou-compre-sua-volta', async (params) => {
  await _send('clicou-compre-sua-volta', params, { amplitude: true })
})

// FLUXO DE CANCELAMENTO DE RESERVA
EventBus.$on('erro-cancelamento', async () => {
  await _send('erro-cancelamento', {}, { amplitude: true })
})

EventBus.$on('viu-popup-erro-passagem-impressa', async () => {
  await _send('viu-popup-erro-passagem-impressa', {}, { amplitude: true })
})

EventBus.$on('tentar-novamente-cancelamento', async () => {
  await _send('tentar-novamente-cancelamento', {}, { amplitude: true })
})

EventBus.$on('clicou-cancelar-reserva', async (params) => {
  await _send('clicou-cancelar-reserva', params, { amplitude: true })
})

EventBus.$on('escolha-popup-cancelamento', async (params) => {
  await _send('escolha-popup-cancelamento', params, { amplitude: true })
})

EventBus.$on('reserva-cancelada', async (params) => {
  await _send('reserva-cancelada', params, { amplitude: true, google: true })
})

// CHECKOUT
EventBus.$on('acessou-pagina-pagamento', async (params) => {
  await _send('acessou-pagina-pagamento', params, {
    amplitude: true,
    google: true
  })
})

EventBus.$on('clicou-aba-forma-pagamento', async (method) => {
  await _send('clicou-aba-forma-pagamento', { method }, { amplitude: true })
})

EventBus.$on('adicionou-novo-passageiro', async () => {
  await _send('adicionou-novo-passageiro', {}, { amplitude: true })
})

EventBus.$on('atualizou-cupom', async (params) => {
  await _send('atualizou-cupom', params, { amplitude: true })
})

EventBus.$on('erro-cupom', async (params) => {
  await _send('erro-cupom', params, { amplitude: true })
})

EventBus.$on('erro-estorno-cancelamento', async () => {
  await _send('erro-estorno-cancelamento', {}, { amplitude: true })
})

EventBus.$on('adicionou-no-carrinho', async (params) => {
  await _send('adicionou-no-carrinho', params, { google: true })
})

EventBus.$on('atualizou-lista-de-passageiros', async (params) => {
  await _send('atualizou-lista-de-passageiros', params, { amplitude: true })
})

EventBus.$on('melhorou-poltrona', async (params) => {
  await _send('melhorou-poltrona', params, { amplitude: true })
})

EventBus.$on('acessou-selecionar-poltrona', async () => {
  await _send('acessou-selecionar-poltrona', {}, { amplitude: true })
})

EventBus.$on('selecionou-poltronas', async () => {
  await _send('selecionou-poltronas', {}, { amplitude: true })
})

EventBus.$on('erro-selecao-poltrona', async (params) => {
  await _send('erro-selecao-poltrona', params, { amplitude: true })
})

EventBus.$on('atualizou-neutralizacao-carbono', async (params) => {
  await _send('atualizou-neutralizacao-carbono', params, { amplitude: true })
})

EventBus.$on('atualizou-seguro-extra', async (params) => {
  await _send('atualizou-seguro-extra', params, { amplitude: true })
})

EventBus.$on('acessou-url-pontos-turisticos', async (params) => {
  await _send('acessou-url-pontos-turisticos', params, { amplitude: true })
})

EventBus.$on('acessou-url-pontos-de-embarque', async (params) => {
  await _send('acessou-url-pontos-de-embarque', params, { amplitude: true })
})

EventBus.$on('clicou-popup-saida-whatsapp', async (params) => {
  await _send('clicou-popup-saida-whatsapp', params, { amplitude: true })
})

EventBus.$on('comprou-conexao', async (params) => {
  await _send('comprou-conexao', params, { amplitude: true })
})

EventBus.$on('acessou-url-descubra', async (params) => {
  await _send('acessou-url-descubra', params, { amplitude: true, google: true })
})

EventBus.$on('clicou-banner-vivo-easy-reserva-confirmada', async (params) => {
  await _send(
    'clicou-banner-vivo-easy-reserva-confirmada',
    { params },
    { amplitude: true }
  )
})

EventBus.$on('alterou-cidade-localização', async (params) => {
  await _send('alterou-cidade-localização', params, { amplitude: true })
})

EventBus.$on('clicou-baixar-app-novo-menu', async () => {
  await _send('clicou-baixar-app-novo-menu', {}, { amplitude: true })
})

EventBus.$on('acessou-pagina-dados-compartilhados', async () => {
  await _send('acessou-pagina-dados-compartilhados', {}, { amplitude: true })
})

EventBus.$on('viu-divergencia-checkout', async (params) => {
  await _send('viu-divergencia-checkout', params, { amplitude: true })
})

EventBus.$on('feedback-trechos', async (payload) => {
  await _send('feedback-trechos', payload, { amplitude: true })
})

EventBus.$on('abandonou-busca', async (params) => {
  await _send('abandonou-busca', params, { google: true })
})

EventBus.$on('adicionou-cupom', async (cupom: any) => {
  let tipoDesconto, desconto
  if (cupom.discount) {
    tipoDesconto = 'porcentagem'
    desconto = cupom.discount * 100
  } else if (cupom.value) {
    tipoDesconto = 'valor'
    desconto = cupom.value
  } else if (cupom.fixed_value) {
    tipoDesconto = 'valor_fixo'
    desconto = cupom.fixed_value
  }

  const params = {
    desconto,
    tipoDesconto,
    cupomCode: cupom.code,
    validade: dateFormat(DATE_FORMATS.dbdatetime, cupom.due_date)
  }
  await _send('adicionou-cupom', params, { google: true })
})

EventBus.$on('acessou-pagina-aniversario', async (params) => {
  await _send('acessou-pagina-aniversario', params, { amplitude: true })
})

EventBus.$on('viu-ofertas-aniversario', async (params) => {
  await _send('viu-ofertas-aniversario', params, { amplitude: true })
})

// Checkout de Item adicional
EventBus.$on('acessou-checkout-adicional', async (params) => {
  await _send('acessou-checkout-adicional', params, { amplitude: true })
})

EventBus.$on('sem-upsell-disponivel', async (params) => {
  await _send('sem-upsell-disponivel', params, { amplitude: true })
})

EventBus.$on('add-bagagem-adicional', async (params) => {
  await _send('add-bagagem-adicional', params, { amplitude: true })
})

EventBus.$on('clicou-upsell-bagagem', async (params) => {
  await _send('clicou-upsell-bagagem', params, { amplitude: true })
})

EventBus.$on('clicou-upsell-seguro', async (params) => {
  await _send('clicou-upsell-seguro', params, { amplitude: true })
})

EventBus.$on('clicou-upsell-marcacao-assento', async (params) => {
  await _send('clicou-upsell-marcacao-assento', params, { amplitude: true })
})
// SEO - empresas/concorrentes
EventBus.$on('clicou-rota-empresa', async (params) => {
  await _send('clicou-rota-empresa', params, { amplitude: true })
})

EventBus.$on('buscou-widget-empresa', async (params) => {
  await _send('buscou-widget-empresa', params, { amplitude: true })
})

EventBus.$on('acessou-glossario', async (params) => {
  await _send('acessou-glossario', params, { amplitude: true })
})

EventBus.$on('acessou-festival', async (params) => {
  await _send('acessou-festival', params, { amplitude: true })
})

EventBus.$on('clicou-popup-rock', async (params) => {
  await _send('clicou-popup-rock', params, { amplitude: true })
})

EventBus.$on('clicou-popup-verao-rio', async (params) => {
  await _send('clicou-popup-verao-rio', params, { amplitude: true })
})

EventBus.$on('dismiss-popup-verao-rio', async (params) => {
  await _send('dismiss-popup-verao-rio', params, { amplitude: true })
})

EventBus.$on('aplica-promo-erro', async (params) => {
  await _send('aplica-promo-erro', params, { amplitude: true })
})

EventBus.$on('cadastro-pagina-bf', async () => {
  await _send('cadastro-pagina-bf', {}, { amplitude: true, google: true })
})

EventBus.$on('carregamento-layout-erro', async (params) => {
  await _send('carregamento-layout-erro', params, { amplitude: true })
})

EventBus.$on('clicou-remover-passageiro-travel', async (params) => {
  await _send('clicou-remover-passageiro-travel', params, { amplitude: true })
})

EventBus.$on('removeu-passageiro-travel', async (params) => {
  await _send('removeu-passageiro-travel', params, { amplitude: true })
})

EventBus.$on('identify-lead', async (params) => {
  await _send('identify-lead', params, { google: true })
})

EventBus.$on('interacao-decolar', async (params) => {
  await _send('interacao-decolar', params, { amplitude: true })
})

EventBus.$on('acessou-lp-parceria', async (params) => {
  await _send('acessou-lp-parceria', params, { amplitude: true })
})

// TODO: alterar os outros acessos.
EventBus.$on('acessou-pagina', async (params: any) => {
  const { pagina, amplitude, google, ...data } = params
  if (!pagina) {
    console.error('Evento `acessou-pagina` chamado sem página definida')
    return
  }

  await _send(
    'acessou-pagina',
    { pagina, ...data },
    {
      // Pelo menos 1.
      amplitude: amplitude || !google,
      google
    }
  )
})

EventBus.$on('clicou-upsell-card', async (params) => {
  await _send('clicou-upsell-card', params, { amplitude: true })
})

EventBus.$on('solicitou-item-adicional', async (item) => {
  await _send('solicitou-item-adicional', item, { amplitude: true })
})

EventBus.$on('click-destino-com-desconto', async (params) => {
  await _send('click-destino-com-desconto', params, { amplitude: true })
})

// Outros
export default defineNuxtPlugin(() => {
  amplitudehelper.init()
})
