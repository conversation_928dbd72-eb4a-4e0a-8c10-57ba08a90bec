// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Layout narrow > Pax > should renders properly 1`] = `
"<div data-v-8f4a0123="">
  <nav data-v-24aa6fda="" data-v-8f4a0123="" class="ada-taskbar is-fixed taskbar">
    <div class="ada-container t-content">
      <!-- @slot Conteúdo do container -->
      <!-- @slot <PERSON><PERSON><PERSON><PERSON> da taskbar -->
      <div data-v-714e2f76="" data-v-24aa6fda="" class="taskbar-desktop t-desktop hide-tablet">
        <router-link-stub data-v-95bda246="" data-v-714e2f76="" to="[object Object]" title="Ir para página inicial"><svg data-v-95bda246="" width="122" height="24" viewBox="0 0 122 24" class="" xmlns="http://www.w3.org/2000/svg" alt="Logo Buser Brasil">
            <path data-v-95bda246="" d="M15.6299 11.9915C14.6646 10.4937 13.7047 9.0067 12.7394 7.51022C12.765 7.48173 12.7892 7.45324 12.8162 7.42746C13.4153 6.84813 14.0817 6.36242 14.8127 5.9649C15.6353 5.51717 16.505 5.19834 17.4204 5.00975C17.9482 4.90121 18.4813 4.83745 19.0198 4.82523C21.3529 4.77368 23.4423 5.46833 25.2571 6.94717C26.8901 8.27678 27.9537 9.98627 28.4814 12.0336C28.6039 12.5071 28.6807 12.9887 28.7265 13.4772C28.7776 14.0253 28.7843 14.5734 28.7386 15.1202C28.5757 17.0549 27.9106 18.7956 26.7084 20.3165C25.4281 21.9351 23.7883 23.0177 21.8133 23.5808C21.3219 23.7205 20.8225 23.8128 20.3149 23.8671C19.7683 23.9268 19.2218 23.9417 18.6738 23.9023C17.1189 23.7897 15.6703 23.3312 14.3415 22.5063C13.1164 21.7465 12.1228 20.7479 11.3393 19.5323C9.38451 16.4959 7.42703 13.4609 5.47089 10.4245C4.11519 8.32698 2.76219 6.22539 1.40649 4.12516C1.1655 3.75205 1.00934 3.3491 0.974333 2.90409C0.921828 2.22572 1.0955 1.60976 1.50746 1.06707C1.91269 0.533871 2.44985 0.20554 3.10683 0.122779C4.19193 -0.0128944 5.0697 0.372419 5.67687 1.29364C6.4806 2.50793 7.25471 3.73849 8.04228 4.95955C9.92706 7.88332 11.8145 10.8071 13.7007 13.7295C14.3509 14.7376 15.0012 15.7443 15.6555 16.7496C16.3259 17.7821 17.2602 18.4469 18.4598 18.6857C19.9393 18.9801 21.2425 18.5853 22.3411 17.5447C23.0209 16.9002 23.4369 16.0984 23.6052 15.1744C24.0293 12.8517 22.5969 10.6646 20.3863 10.1043C19.9016 9.98084 19.4089 9.932 18.9121 9.98627C17.5524 10.1355 16.4753 10.7745 15.6918 11.9088C15.6797 11.9264 15.6689 11.944 15.6568 11.9603C15.6514 11.9671 15.6474 11.9712 15.6299 11.9915Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M29.067 7.27818C29.0859 7.24562 29.0993 7.2212 29.1155 7.19813C30.3864 5.22272 31.6559 3.2446 32.9295 1.27055C33.3603 0.601673 33.9769 0.210933 34.7577 0.101037C35.859 -0.0549881 36.9104 0.522983 37.3789 1.54732C37.7734 2.41021 37.7236 3.26088 37.2147 4.06543C36.5631 5.09655 35.8967 6.11953 35.237 7.14522C33.9473 9.15048 32.6575 11.1557 31.3665 13.161C31.3463 13.1922 31.3247 13.2234 31.287 13.2763C31.0784 11.0621 30.3366 9.07315 29.067 7.27818Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M52.7732 23.9215C52.5981 23.8971 52.4231 23.8713 52.2481 23.8496C51.4148 23.7438 50.6151 23.5213 49.8585 23.1495C49.3657 22.908 48.9013 22.6218 48.461 22.2948C47.5483 21.6164 46.9088 20.725 46.4443 19.6939C46.0714 18.8649 45.8493 17.9953 45.7429 17.093C45.6675 16.4595 45.6406 15.8245 45.6406 15.1868C45.6406 11.5264 45.6392 7.86587 45.6433 4.20539C45.6433 3.95439 45.6702 3.69797 45.7173 3.45104C45.786 3.09422 45.9785 2.80524 46.2693 2.58002C46.7647 2.19606 47.3342 2.09973 47.9373 2.12415C48.4207 2.14315 48.8824 2.2639 49.3321 2.43213C49.3496 2.43892 49.3671 2.44434 49.3846 2.45113C49.6552 2.56374 49.6552 2.56374 49.6565 2.85408C49.6606 6.59461 49.6646 10.3365 49.6687 14.077C49.6687 14.6143 49.6579 15.1529 49.6565 15.6902C49.6552 16.2546 49.7279 16.8108 49.8612 17.3603C50.0348 18.074 50.4172 18.6587 50.9355 19.1621C50.9395 19.1661 50.9449 19.1702 50.949 19.1743C51.7904 20.0073 52.823 20.2705 53.9592 20.1471C55.5963 19.9693 56.878 18.7618 57.193 17.1229C57.3855 16.1243 57.3357 15.1339 56.9412 14.191C56.4391 12.993 55.5169 12.2821 54.2662 12.0107C53.6267 11.8723 52.9845 11.8995 52.3545 12.0799C51.8698 12.2183 51.4323 12.4476 51.0526 12.784C51.0176 12.8153 50.9786 12.8424 50.9207 12.8872C50.9166 12.8044 50.9126 12.7447 50.9126 12.685C50.9126 11.5101 50.9153 10.3351 50.9086 9.1602C50.9072 9.01367 50.9517 8.94312 51.0849 8.88342C51.8792 8.52931 52.7085 8.33259 53.5769 8.29867C54.6243 8.25932 55.6515 8.37465 56.6289 8.76403C58.6537 9.56993 60.0713 10.9999 60.8683 13.0432C61.3287 14.2249 61.4782 15.4555 61.3987 16.7186C61.3368 17.7104 61.116 18.6601 60.7027 19.5623C59.9973 21.1022 58.8704 22.2324 57.3922 23.0206C56.4687 23.5131 55.4832 23.7886 54.4452 23.8849C54.3914 23.8903 54.3389 23.9079 54.285 23.9202C53.7802 23.9215 53.2767 23.9215 52.7732 23.9215Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M69.6716 23.9214C69.5019 23.8984 69.3336 23.8699 69.164 23.8522C68.5205 23.7844 67.8918 23.6433 67.286 23.4221C65.1871 22.6569 63.8583 21.189 63.3589 18.9856C63.2323 18.4293 63.2108 17.8595 63.2108 17.2897C63.2108 14.9832 63.196 12.6768 63.2202 10.3717C63.2269 9.7313 63.5097 9.19403 64.1303 8.89962C64.4817 8.73274 64.8532 8.66626 65.2369 8.66083C65.8926 8.65133 66.5132 8.79922 67.1029 9.08549C67.1244 9.09634 67.1459 9.10855 67.1675 9.12077C67.1769 9.12755 67.185 9.13569 67.2079 9.15604C67.2079 9.22388 67.2079 9.30393 67.2079 9.38397C67.2079 11.8139 67.2079 14.2438 67.2079 16.6737C67.2079 17.2259 67.2536 17.7727 67.4192 18.3032C67.4529 18.409 67.4906 18.5135 67.531 18.6166C67.8312 19.3587 68.3697 19.8417 69.133 20.0534C69.9287 20.2745 70.7338 20.2813 71.5348 20.0846C72.4395 19.8634 73.0157 19.276 73.3159 18.4009C73.4559 17.9952 73.5219 17.5746 73.5421 17.1472C73.5515 16.9437 73.5515 16.7389 73.5515 16.5353C73.5515 14.5884 73.5502 12.6401 73.5542 10.6932C73.5542 10.4897 73.569 10.2848 73.6054 10.0854C73.7306 9.41382 74.1371 8.99187 74.7793 8.78972C75.1482 8.67304 75.5279 8.65676 75.9088 8.67983C76.4272 8.71103 76.9239 8.84128 77.4046 9.03936C77.5351 9.09363 77.5836 9.16689 77.5823 9.31614C77.5769 12.0242 77.5796 14.7322 77.5769 17.4403C77.5755 18.3005 77.4301 19.1362 77.134 19.9448C76.6224 21.3409 75.6827 22.3463 74.3862 23.0314C73.6229 23.4344 72.8084 23.6745 71.9616 23.8075C71.726 23.8441 71.4863 23.8617 71.2494 23.8902C71.2023 23.8956 71.1551 23.9119 71.108 23.9241C70.6288 23.9214 70.1508 23.9214 69.6716 23.9214Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M121.031 10.8997C121.017 10.9458 121.004 10.9919 120.993 11.0394C120.953 11.1995 120.937 11.3718 120.869 11.5197C120.666 11.9688 120.395 12.3704 119.981 12.6743C119.92 12.6499 119.853 12.6227 119.788 12.5929C119.186 12.3202 118.567 12.1126 117.91 12.0258C117.377 11.9566 116.845 11.9471 116.317 12.0529C115.313 12.2523 114.709 12.8669 114.478 13.8655C114.384 14.278 114.361 14.6958 114.361 15.1178C114.363 17.838 114.363 20.6288 114.363 23.3478C114.363 23.4278 114.363 23.5065 114.363 23.5825C114.217 23.6232 110.686 23.6367 110.37 23.6001C110.366 23.5499 110.357 23.497 110.357 23.4441C110.357 20.31 110.355 17.1054 110.36 13.9713C110.362 13.0257 110.535 12.114 110.962 11.2646C111.579 10.0341 112.561 9.22139 113.835 8.75196C114.763 8.4087 115.725 8.29067 116.708 8.29338C117.489 8.29474 118.264 8.35036 119.021 8.56473C119.411 8.67598 119.789 8.81844 120.135 9.03823C120.557 9.30686 120.85 9.67454 120.973 10.167C121 10.2742 121.015 10.3828 121.035 10.4913C121.031 10.627 121.031 10.7626 121.031 10.8997Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M98.9142 18.0644C98.9424 18.1472 98.9613 18.2177 98.9909 18.2828C99.3598 19.1023 99.9818 19.6518 100.798 19.9883C101.285 20.189 101.799 20.28 102.32 20.3179C103.276 20.3885 104.218 20.3166 105.141 20.0303C105.559 19.9001 105.944 19.6979 106.336 19.5053C106.533 19.4076 106.732 19.314 106.947 19.2095C107.464 20.2013 107.973 21.1795 108.486 22.1645C108.454 22.193 108.431 22.2214 108.401 22.2404C107.771 22.6624 107.116 23.0382 106.389 23.2648C105.58 23.5171 104.76 23.722 103.914 23.8129C103.174 23.8929 102.433 23.9269 101.692 23.8753C100.318 23.7803 99.0178 23.433 97.8506 22.6719C96.3455 21.691 95.3438 20.3207 94.8713 18.58C94.4014 16.8501 94.4216 15.1121 94.9898 13.4094C95.6858 11.3228 97.0671 9.8412 99.0609 8.9444C100.025 8.51024 101.04 8.29859 102.094 8.26332C103.093 8.23076 104.083 8.29995 105.041 8.60522C106.075 8.9349 107.004 9.44775 107.734 10.2767C108.475 11.1206 108.854 12.107 108.84 13.2412C108.834 13.7703 108.77 14.2872 108.622 14.796C108.443 15.4133 108.105 15.9248 107.635 16.3535C107.102 16.8393 106.472 17.1513 105.798 17.3806C105.04 17.637 104.262 17.8039 103.47 17.9097C102.257 18.0725 101.037 18.097 99.8148 18.082C99.5698 18.0793 99.3248 18.0644 99.0798 18.0563C99.0313 18.0563 98.9842 18.0617 98.9142 18.0644ZM98.6637 15.0294C98.7472 15.0389 98.8011 15.0484 98.8549 15.0497C100.063 15.0646 101.27 15.1026 102.474 14.9466C103.073 14.8693 103.654 14.7309 104.202 14.4636C104.625 14.2574 104.886 13.9304 104.932 13.4488C104.986 12.902 104.858 12.4244 104.379 12.1042C104.113 11.9265 103.825 11.7868 103.513 11.7054C102.903 11.548 102.287 11.5222 101.671 11.6348C100.625 11.8275 99.7892 12.3471 99.2292 13.2792C98.968 13.7133 98.8065 14.1828 98.7149 14.678C98.6961 14.7879 98.6839 14.8964 98.6637 15.0294Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M91.0789 13.035C90.8864 12.8817 90.7113 12.7257 90.5202 12.5954C90.0059 12.2441 89.427 12.0433 88.8293 11.8913C87.9515 11.6674 87.0616 11.5358 86.1555 11.5358C85.5847 11.5358 85.0153 11.567 84.4579 11.7122C84.1711 11.7868 83.8938 11.8872 83.6649 12.0853C83.3782 12.3336 83.2799 12.6484 83.358 13.0188C83.4159 13.2942 83.5922 13.476 83.8332 13.5981C84.1873 13.7772 84.5656 13.8844 84.9547 13.9413C85.6884 14.0485 86.4248 14.1435 87.1599 14.2425C88.0524 14.3633 88.9491 14.461 89.8174 14.7201C90.4798 14.9182 91.1098 15.1814 91.6928 15.5599C92.5598 16.123 93.0943 16.9085 93.2827 17.9301C93.4429 18.7971 93.3972 19.6546 93.105 20.489C92.8304 21.2745 92.3444 21.9081 91.7062 22.4277C90.9604 23.0356 90.1028 23.3978 89.1779 23.6176C88.1979 23.851 87.203 23.9012 86.2 23.8944C84.6666 23.8822 83.1735 23.6203 81.7115 23.1699C80.9845 22.946 80.2723 22.6747 79.6086 22.2921C79.3945 22.1686 79.202 22.018 79.0149 21.8308C79.5399 20.8906 80.065 19.9517 80.5981 18.9979C80.6627 19.044 80.7193 19.0793 80.7704 19.1214C81.0491 19.3507 81.3682 19.5067 81.7007 19.6396C82.5744 19.9883 83.4711 20.2624 84.4 20.4171C85.4218 20.5866 86.4477 20.6436 87.4776 20.508C87.8801 20.455 88.2732 20.356 88.6408 20.1742C88.8858 20.0534 89.0877 19.8866 89.1577 19.6057C89.2237 19.3398 89.2089 19.0779 89.0958 18.827C89.0312 18.6831 88.9275 18.5746 88.7916 18.4905C88.4281 18.2653 88.0255 18.1581 87.6122 18.0957C87.0306 18.0061 86.445 17.9437 85.8621 17.8691C85.0543 17.766 84.2371 17.7185 83.4401 17.5353C82.6593 17.3562 81.9094 17.0917 81.2201 16.6711C80.4716 16.2139 79.9506 15.5613 79.6544 14.7418C79.2168 13.5316 79.2626 12.3214 79.7917 11.1505C80.209 10.2266 80.9253 9.57942 81.8125 9.12084C82.4681 8.78166 83.1695 8.58764 83.8898 8.45468C84.9708 8.25524 86.06 8.24032 87.1531 8.29187C87.8707 8.32579 88.5842 8.39363 89.2897 8.53066C89.9965 8.66905 90.6831 8.8712 91.3185 9.22395C91.5999 9.37998 91.8597 9.56449 92.0792 9.80192C92.6042 10.3677 92.7173 11.1342 92.3821 11.8302C92.2098 12.1871 91.962 12.4815 91.6632 12.7352C91.4895 12.8844 91.2835 12.9753 91.0789 13.035Z" fill="currentColor"></path>
          </svg></router-link-stub>
        <div data-v-eb91cb4e="" data-v-714e2f76="" class="menu-links">
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item">Início</router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Promoções </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Descubra </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Viagens </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Ajuda </router-link-stub>
          <div data-v-b3598c4b="" data-v-714e2f76="" class="profile-action-wrapper" data-testid="profile-action-wrapper">
            <!--
      Por mais tentador que seja trocar esse @click="login" por um :to="{ name: 'login', query: { next: filteredQuery } }"
      não faça isso, gafanhoto! O SSR renderiza o \`router-link\` com \`to\` transformando num \`a\` com \`href\` e faz cache disso.

      A taskbar é utilizada em todas as páginas e ao cachear o \`next\` um usuário pode acabar recebendo o \`next\` do outro.
      Você foi avisado, se fizer isso vou puxar seu pé de madrugada.
    -->
            <div data-v-b3598c4b="" class="ada-float hide-tablet">
              <div class="f-reference">
                <!-- @slot Elemento de referência para o posicionamento do balão flutuante --><button data-v-b3598c4b="" class="is-button b-color-primary b-transparent b-icon b-rounded ada-button paw-avatar-button" type="button" aria-label="Minha conta">
                  <!--v-if-->
                  <!-- @slot Conteúdo renderizado no button --><span data-v-b3598c4b="" class="user-avatar ua-default-logged-out" style="height: 36px; width: 36px; min-height: 36px; min-width: 36px;"><!--v-if--></span>
                  <!--v-if-->
                </button>
              </div>
              <!--v-if-->
            </div>
          </div>
        </div>
      </div>
      <div data-v-82c6489a="" data-v-24aa6fda="" class="taskbar-mobile t-mobile show-tablet"><button data-v-82c6489a="" class="is-button b-color-primary b-transparent b-icon b-rounded ada-button tm-back-button" type="button" aria-label="Voltar para página anterior">
          <!--v-if-->
          <!-- @slot Conteúdo renderizado no button -->
          <fa-sprite-stub data-v-82c6489a="" sprite="andromeda" icon="far-fa-arrow-left" size="lg"></fa-sprite-stub>
        </button>
        <div data-v-82c6489a="" class="tm-wrapper pr-6">
          <p data-v-82c6489a="" class="tmw-title">Test title</p>
          <!--v-if-->
        </div>
      </div>
    </div>
  </nav>
  <div data-v-8f4a0123="" class="ada-container narrow">
    <!-- @slot Conteúdo do container -->
  </div>
  <nav data-v-8f48af57="" data-v-8f4a0123="" class="ada-nav-bottom nav-bottom">
    <!-- @slot Lista de \`ada-nav-bottom-item\` -->
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link" exact="">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Início</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Promoções</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <!-- O ícone de ônibus do fontawesome light é bem ruim, ta aí uma versão mais próxima ao ícone ativo --><svg data-v-8f48af57="" class="nbil-icon is-inactive" width="16px" height="18px" viewBox="0 0 448 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g data-v-8f48af57="" id="bus-simple-light">
            <path data-v-8f48af57="" fill="currentColor" d="M346,418 L346,477 C346,482.522847 350.477153,487 356,487 L356,487 L381,487 C386.522847,487 391,482.522847 391,477 L391,477 L391,418 L416,418 L416,482 C416,498.568542 402.568542,512 386,512 L349,512 C332.431458,512 319,498.568542 319,482 L319,418 L346,418 Z M59,477 C59,482.522847 63.4771525,487 69,487 L69,487 L94,487 C99.5228475,487 104,482.522847 104,477 L104,477 L104,418 L129,418 L129,482 C129,498.568542 115.568542,512 99,512 L62,512 C45.4314575,512 32,498.568542 32,482 L32,418 L59,418 Z M224,0 C348.8,0 448,35.2 448,80 L448,416 C448,433.7 433.7,448 416,448 L32,448 C14.3,448 0,433.7 0,416 L0,80 C0,35.2 99.2,0 224,0 Z M224,33 C117.585714,33 33,54.2108058 33,92.4108058 L33,92.4108058 L33,114.857143 L32.9990423,389.318904 C32.9717143,415 32.5657143,415 60.2857143,415 L60.2857143,415 L387.714286,415 C415,415 415,415 415,387.714286 L415,387.714286 L415,92.4108058 C415,54.2108058 330.414286,33 224,33 Z M80,336 C97.673112,336 112,350.326888 112,368 C112,385.673112 97.673112,400 80,400 C62.326888,400 48,385.673112 48,368 C48,350.326888 62.326888,336 80,336 Z M368,336 C385.673112,336 400,350.326888 400,368 C400,385.673112 385.673112,400 368,400 C350.326888,400 336,385.673112 336,368 C336,350.326888 350.326888,336 368,336 Z M352,96 C369.7,96 384,110.3 384,128 L384,128 L384,256 C384,273.7 369.7,288 352,288 L352,288 L96,288 C78.3,288 64,273.7 64,256 L64,256 L64,128 C64,110.3 78.3,96 96,96 L96,96 Z M332.722898,118.000666 L115.277102,118.000666 C91.7508382,118.021534 91.0232218,118.696266 91.0007182,140.512601 L91.0007182,243.487399 C91.02394,266 91.798,266 117.6,266 L330.4,266 C357,266 357,266 357,241.333333 L357,142.666667 C357,118.74 357,118.0222 332.722898,118.000666 Z"></path>
          </g>
        </svg>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Viagens</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Ajuda</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" exactactiveclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Perfil</span>
        <!--v-if-->
      </nuxt-link-stub>
    </div>
  </nav><span data-v-8f4a0123=""></span>
</div>"
`;

exports[`Layout narrow > Revendedor > should renders properly 1`] = `
"<div data-v-8f4a0123="">
  <nav data-v-24aa6fda="" data-v-8f4a0123="" class="ada-taskbar is-fixed taskbar">
    <div class="ada-container t-content">
      <!-- @slot Conteúdo do container -->
      <!-- @slot Conteúdo da taskbar -->
      <div data-v-714e2f76="" data-v-24aa6fda="" class="taskbar-desktop t-desktop hide-tablet">
        <router-link-stub data-v-95bda246="" data-v-714e2f76="" to="[object Object]" title="Ir para página inicial"><svg data-v-95bda246="" width="122" height="24" viewBox="0 0 122 24" class="" xmlns="http://www.w3.org/2000/svg" alt="Logo Buser Brasil">
            <path data-v-95bda246="" d="M15.6299 11.9915C14.6646 10.4937 13.7047 9.0067 12.7394 7.51022C12.765 7.48173 12.7892 7.45324 12.8162 7.42746C13.4153 6.84813 14.0817 6.36242 14.8127 5.9649C15.6353 5.51717 16.505 5.19834 17.4204 5.00975C17.9482 4.90121 18.4813 4.83745 19.0198 4.82523C21.3529 4.77368 23.4423 5.46833 25.2571 6.94717C26.8901 8.27678 27.9537 9.98627 28.4814 12.0336C28.6039 12.5071 28.6807 12.9887 28.7265 13.4772C28.7776 14.0253 28.7843 14.5734 28.7386 15.1202C28.5757 17.0549 27.9106 18.7956 26.7084 20.3165C25.4281 21.9351 23.7883 23.0177 21.8133 23.5808C21.3219 23.7205 20.8225 23.8128 20.3149 23.8671C19.7683 23.9268 19.2218 23.9417 18.6738 23.9023C17.1189 23.7897 15.6703 23.3312 14.3415 22.5063C13.1164 21.7465 12.1228 20.7479 11.3393 19.5323C9.38451 16.4959 7.42703 13.4609 5.47089 10.4245C4.11519 8.32698 2.76219 6.22539 1.40649 4.12516C1.1655 3.75205 1.00934 3.3491 0.974333 2.90409C0.921828 2.22572 1.0955 1.60976 1.50746 1.06707C1.91269 0.533871 2.44985 0.20554 3.10683 0.122779C4.19193 -0.0128944 5.0697 0.372419 5.67687 1.29364C6.4806 2.50793 7.25471 3.73849 8.04228 4.95955C9.92706 7.88332 11.8145 10.8071 13.7007 13.7295C14.3509 14.7376 15.0012 15.7443 15.6555 16.7496C16.3259 17.7821 17.2602 18.4469 18.4598 18.6857C19.9393 18.9801 21.2425 18.5853 22.3411 17.5447C23.0209 16.9002 23.4369 16.0984 23.6052 15.1744C24.0293 12.8517 22.5969 10.6646 20.3863 10.1043C19.9016 9.98084 19.4089 9.932 18.9121 9.98627C17.5524 10.1355 16.4753 10.7745 15.6918 11.9088C15.6797 11.9264 15.6689 11.944 15.6568 11.9603C15.6514 11.9671 15.6474 11.9712 15.6299 11.9915Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M29.067 7.27818C29.0859 7.24562 29.0993 7.2212 29.1155 7.19813C30.3864 5.22272 31.6559 3.2446 32.9295 1.27055C33.3603 0.601673 33.9769 0.210933 34.7577 0.101037C35.859 -0.0549881 36.9104 0.522983 37.3789 1.54732C37.7734 2.41021 37.7236 3.26088 37.2147 4.06543C36.5631 5.09655 35.8967 6.11953 35.237 7.14522C33.9473 9.15048 32.6575 11.1557 31.3665 13.161C31.3463 13.1922 31.3247 13.2234 31.287 13.2763C31.0784 11.0621 30.3366 9.07315 29.067 7.27818Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M52.7732 23.9215C52.5981 23.8971 52.4231 23.8713 52.2481 23.8496C51.4148 23.7438 50.6151 23.5213 49.8585 23.1495C49.3657 22.908 48.9013 22.6218 48.461 22.2948C47.5483 21.6164 46.9088 20.725 46.4443 19.6939C46.0714 18.8649 45.8493 17.9953 45.7429 17.093C45.6675 16.4595 45.6406 15.8245 45.6406 15.1868C45.6406 11.5264 45.6392 7.86587 45.6433 4.20539C45.6433 3.95439 45.6702 3.69797 45.7173 3.45104C45.786 3.09422 45.9785 2.80524 46.2693 2.58002C46.7647 2.19606 47.3342 2.09973 47.9373 2.12415C48.4207 2.14315 48.8824 2.2639 49.3321 2.43213C49.3496 2.43892 49.3671 2.44434 49.3846 2.45113C49.6552 2.56374 49.6552 2.56374 49.6565 2.85408C49.6606 6.59461 49.6646 10.3365 49.6687 14.077C49.6687 14.6143 49.6579 15.1529 49.6565 15.6902C49.6552 16.2546 49.7279 16.8108 49.8612 17.3603C50.0348 18.074 50.4172 18.6587 50.9355 19.1621C50.9395 19.1661 50.9449 19.1702 50.949 19.1743C51.7904 20.0073 52.823 20.2705 53.9592 20.1471C55.5963 19.9693 56.878 18.7618 57.193 17.1229C57.3855 16.1243 57.3357 15.1339 56.9412 14.191C56.4391 12.993 55.5169 12.2821 54.2662 12.0107C53.6267 11.8723 52.9845 11.8995 52.3545 12.0799C51.8698 12.2183 51.4323 12.4476 51.0526 12.784C51.0176 12.8153 50.9786 12.8424 50.9207 12.8872C50.9166 12.8044 50.9126 12.7447 50.9126 12.685C50.9126 11.5101 50.9153 10.3351 50.9086 9.1602C50.9072 9.01367 50.9517 8.94312 51.0849 8.88342C51.8792 8.52931 52.7085 8.33259 53.5769 8.29867C54.6243 8.25932 55.6515 8.37465 56.6289 8.76403C58.6537 9.56993 60.0713 10.9999 60.8683 13.0432C61.3287 14.2249 61.4782 15.4555 61.3987 16.7186C61.3368 17.7104 61.116 18.6601 60.7027 19.5623C59.9973 21.1022 58.8704 22.2324 57.3922 23.0206C56.4687 23.5131 55.4832 23.7886 54.4452 23.8849C54.3914 23.8903 54.3389 23.9079 54.285 23.9202C53.7802 23.9215 53.2767 23.9215 52.7732 23.9215Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M69.6716 23.9214C69.5019 23.8984 69.3336 23.8699 69.164 23.8522C68.5205 23.7844 67.8918 23.6433 67.286 23.4221C65.1871 22.6569 63.8583 21.189 63.3589 18.9856C63.2323 18.4293 63.2108 17.8595 63.2108 17.2897C63.2108 14.9832 63.196 12.6768 63.2202 10.3717C63.2269 9.7313 63.5097 9.19403 64.1303 8.89962C64.4817 8.73274 64.8532 8.66626 65.2369 8.66083C65.8926 8.65133 66.5132 8.79922 67.1029 9.08549C67.1244 9.09634 67.1459 9.10855 67.1675 9.12077C67.1769 9.12755 67.185 9.13569 67.2079 9.15604C67.2079 9.22388 67.2079 9.30393 67.2079 9.38397C67.2079 11.8139 67.2079 14.2438 67.2079 16.6737C67.2079 17.2259 67.2536 17.7727 67.4192 18.3032C67.4529 18.409 67.4906 18.5135 67.531 18.6166C67.8312 19.3587 68.3697 19.8417 69.133 20.0534C69.9287 20.2745 70.7338 20.2813 71.5348 20.0846C72.4395 19.8634 73.0157 19.276 73.3159 18.4009C73.4559 17.9952 73.5219 17.5746 73.5421 17.1472C73.5515 16.9437 73.5515 16.7389 73.5515 16.5353C73.5515 14.5884 73.5502 12.6401 73.5542 10.6932C73.5542 10.4897 73.569 10.2848 73.6054 10.0854C73.7306 9.41382 74.1371 8.99187 74.7793 8.78972C75.1482 8.67304 75.5279 8.65676 75.9088 8.67983C76.4272 8.71103 76.9239 8.84128 77.4046 9.03936C77.5351 9.09363 77.5836 9.16689 77.5823 9.31614C77.5769 12.0242 77.5796 14.7322 77.5769 17.4403C77.5755 18.3005 77.4301 19.1362 77.134 19.9448C76.6224 21.3409 75.6827 22.3463 74.3862 23.0314C73.6229 23.4344 72.8084 23.6745 71.9616 23.8075C71.726 23.8441 71.4863 23.8617 71.2494 23.8902C71.2023 23.8956 71.1551 23.9119 71.108 23.9241C70.6288 23.9214 70.1508 23.9214 69.6716 23.9214Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M121.031 10.8997C121.017 10.9458 121.004 10.9919 120.993 11.0394C120.953 11.1995 120.937 11.3718 120.869 11.5197C120.666 11.9688 120.395 12.3704 119.981 12.6743C119.92 12.6499 119.853 12.6227 119.788 12.5929C119.186 12.3202 118.567 12.1126 117.91 12.0258C117.377 11.9566 116.845 11.9471 116.317 12.0529C115.313 12.2523 114.709 12.8669 114.478 13.8655C114.384 14.278 114.361 14.6958 114.361 15.1178C114.363 17.838 114.363 20.6288 114.363 23.3478C114.363 23.4278 114.363 23.5065 114.363 23.5825C114.217 23.6232 110.686 23.6367 110.37 23.6001C110.366 23.5499 110.357 23.497 110.357 23.4441C110.357 20.31 110.355 17.1054 110.36 13.9713C110.362 13.0257 110.535 12.114 110.962 11.2646C111.579 10.0341 112.561 9.22139 113.835 8.75196C114.763 8.4087 115.725 8.29067 116.708 8.29338C117.489 8.29474 118.264 8.35036 119.021 8.56473C119.411 8.67598 119.789 8.81844 120.135 9.03823C120.557 9.30686 120.85 9.67454 120.973 10.167C121 10.2742 121.015 10.3828 121.035 10.4913C121.031 10.627 121.031 10.7626 121.031 10.8997Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M98.9142 18.0644C98.9424 18.1472 98.9613 18.2177 98.9909 18.2828C99.3598 19.1023 99.9818 19.6518 100.798 19.9883C101.285 20.189 101.799 20.28 102.32 20.3179C103.276 20.3885 104.218 20.3166 105.141 20.0303C105.559 19.9001 105.944 19.6979 106.336 19.5053C106.533 19.4076 106.732 19.314 106.947 19.2095C107.464 20.2013 107.973 21.1795 108.486 22.1645C108.454 22.193 108.431 22.2214 108.401 22.2404C107.771 22.6624 107.116 23.0382 106.389 23.2648C105.58 23.5171 104.76 23.722 103.914 23.8129C103.174 23.8929 102.433 23.9269 101.692 23.8753C100.318 23.7803 99.0178 23.433 97.8506 22.6719C96.3455 21.691 95.3438 20.3207 94.8713 18.58C94.4014 16.8501 94.4216 15.1121 94.9898 13.4094C95.6858 11.3228 97.0671 9.8412 99.0609 8.9444C100.025 8.51024 101.04 8.29859 102.094 8.26332C103.093 8.23076 104.083 8.29995 105.041 8.60522C106.075 8.9349 107.004 9.44775 107.734 10.2767C108.475 11.1206 108.854 12.107 108.84 13.2412C108.834 13.7703 108.77 14.2872 108.622 14.796C108.443 15.4133 108.105 15.9248 107.635 16.3535C107.102 16.8393 106.472 17.1513 105.798 17.3806C105.04 17.637 104.262 17.8039 103.47 17.9097C102.257 18.0725 101.037 18.097 99.8148 18.082C99.5698 18.0793 99.3248 18.0644 99.0798 18.0563C99.0313 18.0563 98.9842 18.0617 98.9142 18.0644ZM98.6637 15.0294C98.7472 15.0389 98.8011 15.0484 98.8549 15.0497C100.063 15.0646 101.27 15.1026 102.474 14.9466C103.073 14.8693 103.654 14.7309 104.202 14.4636C104.625 14.2574 104.886 13.9304 104.932 13.4488C104.986 12.902 104.858 12.4244 104.379 12.1042C104.113 11.9265 103.825 11.7868 103.513 11.7054C102.903 11.548 102.287 11.5222 101.671 11.6348C100.625 11.8275 99.7892 12.3471 99.2292 13.2792C98.968 13.7133 98.8065 14.1828 98.7149 14.678C98.6961 14.7879 98.6839 14.8964 98.6637 15.0294Z" fill="currentColor"></path>
            <path data-v-95bda246="" d="M91.0789 13.035C90.8864 12.8817 90.7113 12.7257 90.5202 12.5954C90.0059 12.2441 89.427 12.0433 88.8293 11.8913C87.9515 11.6674 87.0616 11.5358 86.1555 11.5358C85.5847 11.5358 85.0153 11.567 84.4579 11.7122C84.1711 11.7868 83.8938 11.8872 83.6649 12.0853C83.3782 12.3336 83.2799 12.6484 83.358 13.0188C83.4159 13.2942 83.5922 13.476 83.8332 13.5981C84.1873 13.7772 84.5656 13.8844 84.9547 13.9413C85.6884 14.0485 86.4248 14.1435 87.1599 14.2425C88.0524 14.3633 88.9491 14.461 89.8174 14.7201C90.4798 14.9182 91.1098 15.1814 91.6928 15.5599C92.5598 16.123 93.0943 16.9085 93.2827 17.9301C93.4429 18.7971 93.3972 19.6546 93.105 20.489C92.8304 21.2745 92.3444 21.9081 91.7062 22.4277C90.9604 23.0356 90.1028 23.3978 89.1779 23.6176C88.1979 23.851 87.203 23.9012 86.2 23.8944C84.6666 23.8822 83.1735 23.6203 81.7115 23.1699C80.9845 22.946 80.2723 22.6747 79.6086 22.2921C79.3945 22.1686 79.202 22.018 79.0149 21.8308C79.5399 20.8906 80.065 19.9517 80.5981 18.9979C80.6627 19.044 80.7193 19.0793 80.7704 19.1214C81.0491 19.3507 81.3682 19.5067 81.7007 19.6396C82.5744 19.9883 83.4711 20.2624 84.4 20.4171C85.4218 20.5866 86.4477 20.6436 87.4776 20.508C87.8801 20.455 88.2732 20.356 88.6408 20.1742C88.8858 20.0534 89.0877 19.8866 89.1577 19.6057C89.2237 19.3398 89.2089 19.0779 89.0958 18.827C89.0312 18.6831 88.9275 18.5746 88.7916 18.4905C88.4281 18.2653 88.0255 18.1581 87.6122 18.0957C87.0306 18.0061 86.445 17.9437 85.8621 17.8691C85.0543 17.766 84.2371 17.7185 83.4401 17.5353C82.6593 17.3562 81.9094 17.0917 81.2201 16.6711C80.4716 16.2139 79.9506 15.5613 79.6544 14.7418C79.2168 13.5316 79.2626 12.3214 79.7917 11.1505C80.209 10.2266 80.9253 9.57942 81.8125 9.12084C82.4681 8.78166 83.1695 8.58764 83.8898 8.45468C84.9708 8.25524 86.06 8.24032 87.1531 8.29187C87.8707 8.32579 88.5842 8.39363 89.2897 8.53066C89.9965 8.66905 90.6831 8.8712 91.3185 9.22395C91.5999 9.37998 91.8597 9.56449 92.0792 9.80192C92.6042 10.3677 92.7173 11.1342 92.3821 11.8302C92.2098 12.1871 91.962 12.4815 91.6632 12.7352C91.4895 12.8844 91.2835 12.9753 91.0789 13.035Z" fill="currentColor"></path>
          </svg></router-link-stub>
        <div data-v-eb91cb4e="" data-v-714e2f76="" class="menu-links">
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item">Início</router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Promoções </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Descubra </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Viagens </router-link-stub>
          <router-link-stub data-v-eb91cb4e="" to="[object Object]" class="ml-item"> Ajuda </router-link-stub>
          <div data-v-b3598c4b="" data-v-714e2f76="" class="profile-action-wrapper" data-testid="profile-action-wrapper">
            <!--
      Por mais tentador que seja trocar esse @click="login" por um :to="{ name: 'login', query: { next: filteredQuery } }"
      não faça isso, gafanhoto! O SSR renderiza o \`router-link\` com \`to\` transformando num \`a\` com \`href\` e faz cache disso.

      A taskbar é utilizada em todas as páginas e ao cachear o \`next\` um usuário pode acabar recebendo o \`next\` do outro.
      Você foi avisado, se fizer isso vou puxar seu pé de madrugada.
    -->
            <div data-v-b3598c4b="" class="ada-float hide-tablet">
              <div class="f-reference">
                <!-- @slot Elemento de referência para o posicionamento do balão flutuante --><button data-v-b3598c4b="" class="is-button b-color-primary b-transparent b-icon b-rounded ada-button paw-avatar-button" type="button" aria-label="Minha conta">
                  <!--v-if-->
                  <!-- @slot Conteúdo renderizado no button --><span data-v-b3598c4b="" class="user-avatar ua-default-logged-out" style="height: 36px; width: 36px; min-height: 36px; min-width: 36px;"><!--v-if--></span>
                  <!--v-if-->
                </button>
              </div>
              <!--v-if-->
            </div>
          </div>
        </div>
      </div>
      <div data-v-82c6489a="" data-v-24aa6fda="" class="taskbar-mobile t-mobile show-tablet"><button data-v-82c6489a="" class="is-button b-color-primary b-transparent b-icon b-rounded ada-button tm-back-button" type="button" aria-label="Voltar para página anterior">
          <!--v-if-->
          <!-- @slot Conteúdo renderizado no button -->
          <fa-sprite-stub data-v-82c6489a="" sprite="andromeda" icon="far-fa-arrow-left" size="lg"></fa-sprite-stub>
        </button>
        <div data-v-82c6489a="" class="tm-wrapper pr-6">
          <p data-v-82c6489a="" class="tmw-title">Test title</p>
          <!--v-if-->
        </div>
      </div>
    </div>
  </nav>
  <div data-v-8f4a0123="" class="ada-container narrow">
    <!-- @slot Conteúdo do container -->
  </div>
  <nav data-v-8f48af57="" data-v-8f4a0123="" class="ada-nav-bottom nav-bottom">
    <!-- @slot Lista de \`ada-nav-bottom-item\` -->
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link" exact="">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Início</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Promoções</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <!-- O ícone de ônibus do fontawesome light é bem ruim, ta aí uma versão mais próxima ao ícone ativo --><svg data-v-8f48af57="" class="nbil-icon is-inactive" width="16px" height="18px" viewBox="0 0 448 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g data-v-8f48af57="" id="bus-simple-light">
            <path data-v-8f48af57="" fill="currentColor" d="M346,418 L346,477 C346,482.522847 350.477153,487 356,487 L356,487 L381,487 C386.522847,487 391,482.522847 391,477 L391,477 L391,418 L416,418 L416,482 C416,498.568542 402.568542,512 386,512 L349,512 C332.431458,512 319,498.568542 319,482 L319,418 L346,418 Z M59,477 C59,482.522847 63.4771525,487 69,487 L69,487 L94,487 C99.5228475,487 104,482.522847 104,477 L104,477 L104,418 L129,418 L129,482 C129,498.568542 115.568542,512 99,512 L62,512 C45.4314575,512 32,498.568542 32,482 L32,418 L59,418 Z M224,0 C348.8,0 448,35.2 448,80 L448,416 C448,433.7 433.7,448 416,448 L32,448 C14.3,448 0,433.7 0,416 L0,80 C0,35.2 99.2,0 224,0 Z M224,33 C117.585714,33 33,54.2108058 33,92.4108058 L33,92.4108058 L33,114.857143 L32.9990423,389.318904 C32.9717143,415 32.5657143,415 60.2857143,415 L60.2857143,415 L387.714286,415 C415,415 415,415 415,387.714286 L415,387.714286 L415,92.4108058 C415,54.2108058 330.414286,33 224,33 Z M80,336 C97.673112,336 112,350.326888 112,368 C112,385.673112 97.673112,400 80,400 C62.326888,400 48,385.673112 48,368 C48,350.326888 62.326888,336 80,336 Z M368,336 C385.673112,336 400,350.326888 400,368 C400,385.673112 385.673112,400 368,400 C350.326888,400 336,385.673112 336,368 C336,350.326888 350.326888,336 368,336 Z M352,96 C369.7,96 384,110.3 384,128 L384,128 L384,256 C384,273.7 369.7,288 352,288 L352,288 L96,288 C78.3,288 64,273.7 64,256 L64,256 L64,128 C64,110.3 78.3,96 96,96 L96,96 Z M332.722898,118.000666 L115.277102,118.000666 C91.7508382,118.021534 91.0232218,118.696266 91.0007182,140.512601 L91.0007182,243.487399 C91.02394,266 91.798,266 117.6,266 L330.4,266 C357,266 357,266 357,241.333333 L357,142.666667 C357,118.74 357,118.0222 332.722898,118.000666 Z"></path>
          </g>
        </svg>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Viagens</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" activeclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Ajuda</span>
      </nuxt-link-stub>
    </div>
    <div data-v-8f48af57="" class="ada-nav-bottom-item nb-item">
      <!-- @slot Conteúdo do \`ada-nav-bottom-item\` -->
      <nuxt-link-stub data-v-8f48af57="" to="[object Object]" exactactiveclass="is-active" class="nbi-link">
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-inactive"></fa-stub>
        <fa-stub data-v-8f48af57="" icon="[object Object]" class="nbil-icon is-active"></fa-stub><span data-v-8f48af57="" class="nbil-text">Perfil</span>
        <!--v-if-->
      </nuxt-link-stub>
    </div>
  </nav>
  <!---->
</div>"
`;
