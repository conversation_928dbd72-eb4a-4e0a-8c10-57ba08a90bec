<template>
  <div class="noticias">
    <page-header
      title="Notícias sobre a Buser"
      subtitle="Fique atualizado nas nossas postagens e sobre o que a midia diz sobre nós."
      subtitle-class="mt-1 mb-2"
    >
      <template #breadcrumbs>
        <ada-breadcrumbs :items="breadcrumbs" />
      </template>
    </page-header>

    <ada-container class="my-5">
      <h2 class="title-md pb-2">Buser na mídia</h2>
      <h3 class="fw-normal mb-4">
        O que alguns dos principais veículos de comunicação falam sobre nós.
      </h3>
      <list-toggle
        class="noticias-list"
        :items="midias"
        :limit-height="listLimitHeight"
      >
        <template #item="{ item }">
          <midia-item class="noticias-item" :midia="item" />
        </template>
      </list-toggle>
    </ada-container>

    <ada-container class="my-5">
      <h2 class="title-md pb-2">Postagens do blog da Buser</h2>
      <h3 class="text-md fw-normal mb-4">
        Confira nossas principais postagens do
        <a
          href="https://blog.buser.com.br/"
          aria-label="Link para blog da Buser"
          target="_blank"
          rel="noopener external"
        >
          nosso blog </a
        >.
      </h3>
      <list-toggle
        class="noticias-list"
        :items="articles"
        :limit-height="listLimitHeight"
      >
        <template #item="{ item }">
          <article-item class="noticias-item" :article="item" />
        </template>
      </list-toggle>
    </ada-container>
  </div>
</template>

<script setup>
import { useHead, useAsyncData } from '#app'
import metahelper from '~/helpers/metahelper.js'
import { useBlogStore } from '~/stores/blog.js'
import { useMidiasStore } from '~/stores/midias.js'

const listLimitHeight = 500
const breadcrumbs = [
  {
    route: { name: 'home' },
    text: 'Home'
  },
  {
    route: { name: 'noticias' },
    text: 'Notícias'
  }
]

const blogStore = useBlogStore()
const midiasStore = useMidiasStore()

const { data: contentData } = await useAsyncData(
  'noticias-content',
  async () => {
    const [articles, midias] = await Promise.all([
      blogStore.getArticles(),
      midiasStore.getMidias()
    ])
    return { articles, midias }
  }
)

const articles = contentData.value?.articles || []
const midias = contentData.value?.midias || []

useHead({
  ...metahelper.generateMetaTags({
    title: 'Notícias',
    description:
      'O que alguns dos principais veículos de comunicação falam sobre nós.',
    url: 'https://www.buser.com.br/noticias'
  })
})
</script>

<style lang="scss" scoped>
.noticias {
  .noticias-list {
    :deep(.lt-item) {
      width: 100%;

      @media (min-width: $screen-tablet-min) {
        width: auto;
      }
    }

    .noticias-item {
      width: 100%;
      display: block;
      flex-grow: 1;

      @media (min-width: $screen-tablet-min) {
        max-width: 250px;
      }
    }
  }
}
</style>
