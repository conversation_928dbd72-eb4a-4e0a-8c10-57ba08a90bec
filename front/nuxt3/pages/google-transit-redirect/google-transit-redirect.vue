<template>
  <div>Redirecionando...</div>
</template>
<script setup lang="ts">
// Essa página é responsável por receber o acesso do google transit e redirectionar para a search
// https://developers.google.com/travel/transport/guides/partner-integration/stop-only-integration#ticketing_deep_links

import { navigateTo } from '#app'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'

interface GoogleTransitRedirectQuery {
  utmSource?: string
  service_date?: string // ["20250701"]
  ticketing_trip_id?: string
  from_ticketing_stop_time_id?: string
  to_ticketing_stop_time_id?: string
  boarding_time?: string // ISO string com timezone
  arrival_time?: string // ISO string com timezone
  booking_token?: string
}

redireciona()

function redireciona() {
  const route = useRoute()
  const query: GoogleTransitRedirectQuery = route.query
  const foundStatusCode = 302

  if (!query) {
    return navigateTo(`/`, {
      external: true,
      redirectCode: foundStatusCode
    })
  }

  const dataIda = getDataIda(query.service_date)

  const grupoDestaque = serializaCampo(query.ticketing_trip_id)
  const slugOrigem = serializaCampo(query.from_ticketing_stop_time_id)
  const slugDestino = serializaCampo(query.to_ticketing_stop_time_id)

  if (!(slugOrigem && slugDestino && dataIda && grupoDestaque)) {
    return navigateTo(`/`, {
      external: true,
      redirectCode: foundStatusCode
    })
  }

  const params = new URLSearchParams({
    ida: dataIda,
    idGrupoDestaque: grupoDestaque
  })

  if (query.utmSource) {
    params.append('utmSource', query.utmSource)
  }

  const queryString = params.toString()
  return navigateTo(`/onibus/${slugOrigem}/${slugDestino}/?${queryString}`, {
    external: true,
    redirectCode: foundStatusCode
  })
}

function getDataIda(data: string | undefined): string {
  if (!data) return ''

  return dayjs(JSON.parse(data)[0], 'YYYYMMDD').format('YYYY-MM-DD')
}

function serializaCampo(value: string | undefined): string {
  if (!value) return ''

  return JSON.parse(value)[0]
}
</script>
