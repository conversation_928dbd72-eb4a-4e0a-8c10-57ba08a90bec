from dataclasses import dataclass
from datetime import timed<PERSON>ta
from decimal import Decimal

from core.forms.buckets_forms import get_fator_mudanca_de_assento
from core.models_grupo import Grupo, TrechoClasse
from core.service import globalsettings_svc
from core.service.remanejamento.commons.constants import PESOS_CUSTO
from core.service.remanejamento.commons.types import Params

DOWNGRADES_PROIBITIVOS = {
    "leito": ["carro", "van"],
    "leito individual": ["carro", "van"],
    "leito cama": [
        "carro",
        "van",
        "convencional",
        "convencional individual",
        "executivo",
        "executivo individual",
        "semi leito",
        "semi leito individual",
    ],
    "leito cama individual": [
        "carro",
        "van",
        "convencional",
        "convencional individual",
        "executivo",
        "executivo individual",
        "semi leito",
        "semi leito individual",
    ],
    "cama premium": [
        "carro",
        "van",
        "convencional",
        "convencional individual",
        "executivo",
        "executivo individual",
        "semi leito",
        "semi leito individual",
    ],
    "cama premium individual": [
        "carro",
        "van",
        "convencional",
        "convencional individual",
        "executivo",
        "executivo individual",
        "semi leito",
        "semi leito individual",
    ],
}


@dataclass(frozen=True)
class CustosRemanejamento:
    custo_assento: Decimal
    custo_deslocamento: Decimal
    custo_mudanca_horario: Decimal
    custo_repasse_hibrido: Decimal

    @property
    def total(self) -> Decimal:
        return self.custo_assento + self.custo_deslocamento + self.custo_mudanca_horario + self.custo_repasse_hibrido


def custo_assento(params: Params):
    tp_origem = params["classes"]["tc"]
    tp_destino = params["classes"]["tcp"]
    fator_mudanca_de_assento = get_fator_mudanca_de_assento(
        tipo_assento_anterior=tp_origem, tipo_assento_novo=tp_destino
    )
    peso = Decimal(PESOS_CUSTO.upgrade) if fator_mudanca_de_assento > 0 else Decimal(PESOS_CUSTO.downgrade)
    return round(abs(fator_mudanca_de_assento) * peso, 7)


def custo_deslocamento(params: Params):
    na_origem = Decimal(f"{params['dist_origem']}")
    no_destino = Decimal(f"{params['dist_destino']}")
    return round((na_origem + no_destino) * Decimal(PESOS_CUSTO.mudanca_local), 7)


def custo_mudanca_horario(trecho_classe_atual: TrechoClasse, params: Params):
    duracao_minutos_original = _time_to_minutes(trecho_classe_atual.duracao_ida)
    time_diff = _time_to_minutes(params["time_diff"])
    return round((time_diff / duracao_minutos_original) * Decimal(PESOS_CUSTO.mudanca_horario), 7)


def _time_to_minutes(time: timedelta) -> Decimal:
    return Decimal(f"{time.total_seconds() / 60}")


def custo_repasse_hibrido(
    trecho_classe_atual: TrechoClasse, trecho_classe_possivel_remanejar: TrechoClasse, ticket_medio: Decimal
):
    TAXA_REPASSE_HIBRIDO = Decimal(globalsettings_svc.get("repasse_hibrido", Decimal("0")))

    trecho_atual_hibrido_com_repasse = (
        trecho_classe_atual.grupo.modelo_venda == Grupo.ModeloVenda.HIBRIDO
        and trecho_classe_atual.grupo.company
        and trecho_classe_atual.grupo.company.parent_company_hibrido_id is not None
    )
    trecho_possivel_hibrido_com_repasse = (
        trecho_classe_possivel_remanejar.grupo.modelo_venda == Grupo.ModeloVenda.HIBRIDO
        and trecho_classe_possivel_remanejar.grupo.company
        and trecho_classe_possivel_remanejar.grupo.company.parent_company_hibrido_id is not None
    )
    taxa_repasse_atual = TAXA_REPASSE_HIBRIDO if trecho_atual_hibrido_com_repasse else 0
    taxa_repasse_possivel = TAXA_REPASSE_HIBRIDO if trecho_possivel_hibrido_com_repasse else 0
    taxa_ajuste_entre_tickets = trecho_classe_possivel_remanejar.max_split_value_bucket / ticket_medio
    # TODO: Rever caso em que custo é negativo. Ex remanejamento de híbrido para fretamento
    return round(taxa_repasse_possivel * taxa_ajuste_entre_tickets - taxa_repasse_atual, 7)


def _ticket_medio_trecho_classe(tc: TrechoClasse) -> Decimal:
    travels = [(t.max_split_value, t.count_seats, t.status) for t in tc.travel_set.all() if t.status != "canceled"]
    if len(travels) == 0:
        return tc.max_split_value or Decimal("10")
    current_gmv = Decimal(sum(max_split_value * count_seats for max_split_value, count_seats, status in travels))
    current_pax = Decimal(sum(count_seats for max_split_value, count_seats, status in travels))
    return current_gmv / current_pax


def calcula_custos(trecho_classe_atual: TrechoClasse, trecho_classe_possivel_remanejar: TrechoClasse, params: Params):
    """Calcula taxa de custos a ser aplicado ao max_split_value"""
    ticket_medio = _ticket_medio_trecho_classe(trecho_classe_atual)

    return CustosRemanejamento(
        custo_assento=custo_assento(params),
        custo_deslocamento=custo_deslocamento(params),
        custo_mudanca_horario=custo_mudanca_horario(trecho_classe_atual, params),
        custo_repasse_hibrido=custo_repasse_hibrido(
            trecho_classe_atual, trecho_classe_possivel_remanejar, ticket_medio
        ),
    )
