from django.db.models import QuerySet

from commons.dateutils import to_tz
from core.forms.staff_forms import (
    EventoExtraDetailsFilterForm,
    EventoExtraFiltersForm,
    EventoExtraForm,
    EventoExtraNegociacaoForm,
    EventoExtraSolicitacaoForm,
    SolicitacaoPernaFiltersForm,
    SolicitacaoPernaForm,
)
from core.models_grupo import EventoExtra, EventoExtraNegociacao, EventoExtraSolicitacao, EventoExtraSolicitacaoPerna
from core.serializers.serializer_evento_extra import (
    EventoExtraSerializer,
    EventoExtraSolicitacaoPernaSerializer,
    EventoExtraSolicitacaoSerializer,
)
from core.service.timezone_svc import rota_origem_tz


def list_eventos_extra(filters: EventoExtraFiltersForm, order_by: str) -> QuerySet:
    eventos_extra = EventoExtra.objects.to_serialize(EventoExtraSerializer)
    if filters.evento_id:
        eventos_extra = eventos_extra.filter(id=filters.evento_id)
    if filters.status:
        eventos_extra = eventos_extra.filter(status=filters.status)
    if filters.data_inicial:
        eventos_extra = eventos_extra.filter(DATA_LIMITE_INICIAL_MULTA__gte=filters.data_inicial)
    if filters.data_final:
        eventos_extra = eventos_extra.filter(data_final__lte=filters.data_final)

    eventos_extra = _sort_evento(eventos_extra, order_by)
    return eventos_extra


def _sort_evento(eventos_extra: QuerySet, order_by: str) -> QuerySet:
    if order_by == "nome":
        eventos_extra = eventos_extra.order_by("nome")
    elif order_by == "dataInicial":
        eventos_extra = eventos_extra.order_by("data_inicial")
    elif order_by == "dataFinal":
        eventos_extra = eventos_extra.order_by("data_final")
    elif order_by == "status":
        eventos_extra = eventos_extra.order_by("status")
    return eventos_extra


def create_or_update_evento_extra(form: EventoExtraForm) -> EventoExtra:
    evento_extra, _ = EventoExtra.objects.update_or_create(id=form.id, defaults=form.dict())
    return evento_extra


def list_evento_extra_details(evento_extra_id: int, filters: EventoExtraDetailsFilterForm) -> QuerySet:
    extras = ["negociacao", "perna"]
    serializer = EventoExtraSolicitacaoSerializer(extra=extras)
    eventos_extra_details = EventoExtraSolicitacao.objects.to_serialize(serializer).filter(
        evento_extra_id=evento_extra_id
    )
    if filters.regional:
        eventos_extra_details = eventos_extra_details.filter(regional=filters.regional)
    if filters.prioridade:
        eventos_extra_details = eventos_extra_details.filter(prioridade=filters.prioridade)
    if filters.rota_principal:
        eventos_extra_details = eventos_extra_details.filter(rota_principal=filters.rota_principal)

    return eventos_extra_details


def delete_evento_extra(evento_extra_id: int) -> None:
    EventoExtra.objects.get(id=evento_extra_id).delete()


def create_or_update_evento_extra_solicitacao(form: EventoExtraSolicitacaoForm) -> EventoExtraSolicitacao:
    evento_extra_solicitacao, _ = EventoExtraSolicitacao.objects.update_or_create(id=form.id, defaults=form.dict())
    return evento_extra_solicitacao


def delete_evento_extra_solicitacao(evento_extra_solicitacao_id: int) -> None:
    EventoExtraSolicitacao.objects.get(id=evento_extra_solicitacao_id).delete()


def create_or_update_evento_extra_negociacao(negociacao_form: EventoExtraNegociacaoForm) -> EventoExtraNegociacao:
    evento_extra_id = EventoExtraSolicitacao.objects.get(id=negociacao_form.solicitacao_extra_id).evento_extra_id
    evento_extra_negociacao, _ = EventoExtraNegociacao.objects.update_or_create(
        id=negociacao_form.id,
        defaults=negociacao_form.dict(),
        evento_extra_id=evento_extra_id,
    )
    return evento_extra_negociacao


def delete_evento_extra_negociacao(evento_extra_negociacao_id: int) -> None:
    EventoExtraNegociacao.objects.get(id=evento_extra_negociacao_id).delete()


def create_or_update_evento_extra_solicitacao_perna(form: SolicitacaoPernaForm) -> EventoExtraSolicitacaoPerna:
    if form.rota_id is not None:
        tz_origem = rota_origem_tz(form.rota_id)
        datetime_tz_aware = to_tz(form.datetime_ida, tz_origem)
        form.datetime_ida = datetime_tz_aware

    evento_extra_solicitacao_perna, _ = EventoExtraSolicitacaoPerna.objects.update_or_create(
        id=form.id, defaults=form.dict()
    )
    return evento_extra_solicitacao_perna


def delete_evento_extra_solicitacao_perna(solicitacao_perna_id: int) -> None:
    EventoExtraSolicitacaoPerna.objects.get(id=solicitacao_perna_id).delete()


def _sort_pernas(pernas_qs: QuerySet, order_by: str) -> QuerySet:
    if order_by == "rota":
        pernas_qs = pernas_qs.order_by("rota")
    elif order_by == "sentido":
        pernas_qs = pernas_qs.order_by("sentido")
    elif order_by in ["data", "hora"]:
        pernas_qs = pernas_qs.order_by("datetime_ida")
    elif order_by == "turno":
        pernas_qs = pernas_qs.order_by("turno")
    return pernas_qs


def list_pernas_solicitacao(filters: SolicitacaoPernaFiltersForm, order_by: str) -> QuerySet:
    pernas_qs = EventoExtraSolicitacaoPerna.objects.to_serialize(EventoExtraSolicitacaoPernaSerializer)

    if filters.ids is not None:
        pernas_qs = pernas_qs.filter(id__in=filters.ids)
    if filters.evento_extra_id is not None:
        pernas_qs = pernas_qs.filter(solicitacao_extra__evento_extra_id=filters.evento_extra_id)
    if filters.solicitacao_extra_id is not None:
        pernas_qs = pernas_qs.filter(solicitacao_extra_id=filters.solicitacao_extra_id)
    if filters.sentido is not None:
        pernas_qs = pernas_qs.filter(sentido=filters.sentido)
    if filters.turno is not None:
        pernas_qs = pernas_qs.filter(turno=filters.turno)
    if filters.rota_id is not None:
        pernas_qs = pernas_qs.filter(rota_id=filters.rota_id)
    if filters.min_datetime_ida is not None:
        pernas_qs = pernas_qs.filter(datetime_ida__gte=filters.min_datetime_ida)
    if filters.max_datetime_ida is not None:
        pernas_qs = pernas_qs.filter(datetime_ida__lte=filters.max_datetime_ida)

    pernas_qs = _sort_pernas(pernas_qs, order_by)
    return pernas_qs
