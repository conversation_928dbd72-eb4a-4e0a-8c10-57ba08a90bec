from datetime import timed<PERSON><PERSON>
from itertools import chain
from typing import Callable, cast

from django.contrib.auth.models import User
from django.db import models

from commons import redis
from commons.dateutils import now
from commons.enum import ModeloVenda
from core.models_company import PoltronaOnibus
from core.models_grupo import Checkpoint, TrechoClasse
from core.models_travel import ItemAdicional, Passageiro, Travel
from core.service.selecao_assento import (
    AbstractSeatsController,
    NoSeatLayoutAvailable,
    SeatAlreadyTaken,
    selecao_automatica,
)

from .models import Assento, BlockedSeat, Deck, MapaPoltronasOnibus


class FretamentoSeatController(AbstractSeatsController):
    def get_trecho_classe_idx(self, trecho_classe: TrechoClasse):
        start_idx = self.checkpoint_map[trecho_classe.trecho_vendido.origem_id]
        end_idx = self.checkpoint_map[trecho_classe.trecho_vendido.destino_id]
        return start_idx, end_idx

    def _get_taken_seats(self):
        if self._taken_seats is None:
            self._taken_seats = list(
                (
                    Passageiro.objects.filter(
                        travel__trecho_classe__in=self.trechos_classe,
                        travel__status=Travel.Status.PENDING,
                        poltrona__isnull=False,
                        removed=False,
                    ).values_list("travel__trecho_classe_id", "id", "poltrona")  # poltronas vinculadas a travel
                ).union(
                    ItemAdicional.objects.filter(
                        status=ItemAdicional.StatusItemAdicional.PENDENTE,
                        travel__trecho_classe__in=self.trechos_classe,
                        travel__status=Travel.Status.PENDING,
                        tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO,
                    )
                    .exclude(poltrona=None)
                    .values_list("travel__trecho_classe_id", "pax_id", "poltrona")  # poltronas com pagamento pendente
                )
            )
        return self._taken_seats

    def _popula_assentos_ocupados(self):
        self._taken = [set() for _ in range(len(self.checkpoints))]

        taken_seats_query = self._get_taken_seats()

        taken_seats_by_tc = {}
        for tc_id, _, poltrona in taken_seats_query:
            seats = taken_seats_by_tc.setdefault(int(tc_id), set())
            seats.add(poltrona)

        for tc in self.trechos_classe:
            start_idx, end_idx = self.get_trecho_classe_idx(tc)
            taken_seats = taken_seats_by_tc.get(tc.id, set())

            taken_seats.update(self._collect_poltronas_blocked(tc.id))

            for idx in range(start_idx, end_idx):
                self._taken[idx].update(taken_seats)

    def __init__(self, trecho_classe: TrechoClasse | int, user: User | None = None):
        super().__init__(trecho_classe, user)

        # TODO: tentar remover futuramente estas lógicas na hora de instanciar o objeto
        if self.trecho_classe.grupo.modelo_venda != ModeloVenda.BUSER:
            raise ValueError(f"Modelo de venda diferente do esperado ({self.trecho_classe.grupo.modelo_venda})")

        self.checkpoints = list(self.trecho_classe.grupo.rota.itinerario.all().order_by("idx"))

        self.checkpoint_map = {cp.local_id: idx for idx, cp in enumerate(self.checkpoints)}

        self.trechos_classe = TrechoClasse.objects.filter(
            grupo=self.trecho_classe.grupo, closed=False, grupo_classe__closed=False
        ).select_related("trecho_vendido__origem", "trecho_vendido__destino")
        self._poltronas_onibus = list(
            PoltronaOnibus.objects.filter(
                onibus=self.trecho_classe.grupo.onibus, tipo=self.trecho_classe.grupo_classe.tipo_assento
            )
        )
        self._taken_seats = None

    @property
    def trecho_classe(self) -> TrechoClasse:
        if self._trecho_classe is not None:
            return self._trecho_classe

        self._trecho_classe = (
            TrechoClasse.objects.select_related("grupo__onibus", "grupo_classe")
            .prefetch_related(
                models.Prefetch(
                    "grupo__rota__itinerario",
                    queryset=Checkpoint.objects.to_serialize(),
                )
            )
            .get(id=self._trecho_classe_id)
        )
        return self._trecho_classe

    def has_marcacao_assento(self) -> bool:
        return self.trecho_classe.get_has_marcacao_assento()

    @property
    def trechos_classe_intermediarios(self):
        origem_cp_idx = self.checkpoint_map[self.trecho_classe.trecho_vendido.origem_id]
        destino_cp_idx = self.checkpoint_map[self.trecho_classe.trecho_vendido.destino_id]
        return self.trechos_classe[origem_cp_idx + 1 : destino_cp_idx]

    def bloquear_poltrona(self, poltrona: Assento, timeout: int | None = None) -> BlockedSeat:
        if not timeout:
            timeout = 600

        key = self._poltrona_cache_key(self.trecho_classe_id, poltrona)
        if self.get_poltrona_key_cache(self.trecho_classe_id, poltrona):
            raise SeatAlreadyTaken("Poltrona já bloqueada")

        # ex é segundos
        self.redis().set(
            name=key,
            value="blocked",
            ex=timeout,
        )
        return BlockedSeat(
            poltrona=poltrona,
            tempo_limite_bloqueio=now() + timedelta(seconds=timeout),
        )

    def desbloquear_poltrona(self, poltrona_bloqueada: BlockedSeat) -> bool:
        key = self._poltrona_cache_key(self.trecho_classe_id, poltrona_bloqueada.poltrona)
        keys_deleted = cast(int, self.redis().delete(key))
        return keys_deleted > 0 if keys_deleted else False

    @property
    def assentos_ocupados(self) -> set:
        self._popula_assentos_ocupados()
        start_idx, end_idx = self.get_trecho_classe_idx(self.trecho_classe)
        return set(chain.from_iterable(self._taken[start_idx:end_idx]))

    def _parse_poltronas(self, poltronas: list[PoltronaOnibus]) -> list[Deck]:
        decks = {}
        assentos_ocupados = self.assentos_ocupados
        for poltrona in poltronas:
            deck = decks.get(poltrona.andar)
            if not deck:
                deck = Deck(andar=poltrona.andar, assentos=[])
                decks[poltrona.andar] = deck

            deck.assentos.append(
                Assento.from_poltrona_onibus(
                    poltrona,
                    livre=(True if poltrona.poltrona not in assentos_ocupados and poltrona.ativo is True else False),
                )
            )

        return list(decks.values())

    def _set_mapa(self) -> MapaPoltronasOnibus:
        return MapaPoltronasOnibus(layout=self._parse_poltronas(self._poltronas_onibus))

    def get_layout_onibus(self) -> MapaPoltronasOnibus:
        # TODO add cache para o layout do ônibus

        if not self.has_marcacao_assento():
            raise NoSeatLayoutAvailable("Não foi possível obter o layout do ônibus")

        return self._set_mapa()

    def redis(self) -> redis.Redis:
        redis_client = redis.get_master_client()
        return redis_client

    def _poltrona_cache_key(self, trecho_classe_id: int, assento: Assento | None = None) -> str:
        poltrona_suffix = f"{assento.numero}" if assento else "*"
        return f"FRETAMENTO_BUSER_{trecho_classe_id}_{poltrona_suffix}"

    @property
    def _mapa_poltrona_cache_key(self) -> str:
        return f"FRETAMENTO_BUSER_{self.trecho_classe.id}"

    def get_poltrona_key_cache(self, trecho_classe_id: int, assento: Assento) -> str | None:
        key = self._poltrona_cache_key(trecho_classe_id, assento)
        return cast(str, self.redis().get(name=key))

    def _collect_poltronas_blocked(self, trecho_classe_id: int) -> set[int]:
        cached_keys = []
        poltronas_cached = []
        for poltrona in self._poltronas_onibus:
            cached_keys.append(f"FRETAMENTO_BUSER_{trecho_classe_id}_{poltrona.poltrona}")
        poltronas_cached = self.redis().mget(cached_keys)
        poltronas_blocked = set()
        for poltrona, cached in zip(self._poltronas_onibus, cast(list, poltronas_cached)):
            if cached is not None:
                poltronas_blocked.add(poltrona.poltrona)
        return poltronas_blocked

    def unset_mapa_poltronas_cache(self):
        self.redis().delete(self._mapa_poltrona_cache_key)

    def atribui_poltronas(self, passageiros: list[Passageiro], override_poltrona=False):
        layout_onibus = self.get_layout_onibus()
        tipo_assento = self.trecho_classe.grupo_classe.tipo_assento

        if not override_poltrona:
            # limpando passageiros que já tem poltrona
            passageiros = [passageiro for passageiro in passageiros if not passageiro.poltrona]

        assentos = selecao_automatica.escolher_poltronas(layout_onibus, len(passageiros), tipo_assento, None)

        assentos_paxs = []
        for passageiro, assento in zip(passageiros, assentos):
            passageiro.poltrona = assento.numero
            assentos_paxs.append(passageiro)

        Passageiro.objects.bulk_update(assentos_paxs, ["poltrona"])


def get_poltronas_disponiveis_por_tc(
    fretamento_seat_controller: FretamentoSeatController, pax_bought_before_change_class: list[int] = []
) -> dict:
    layout_onibus = fretamento_seat_controller.get_layout_onibus()

    bought_before_change_class = _build_bought_seats_map(fretamento_seat_controller, pax_bought_before_change_class)

    available_seats = [
        (f"{assento.x}_{assento.y}_{assento.andar}", assento)
        for deck in layout_onibus.layout
        for assento in deck.assentos
        if _is_seat_available(assento, bought_before_change_class, pax_bought_before_change_class)
    ]

    return dict(available_seats)


def _build_bought_seats_map(fretamento_seat_controller, pax_bought_before_change_class):
    bought_before_change_class = {}
    if pax_bought_before_change_class:
        taken_seats = fretamento_seat_controller._get_taken_seats()
        for _, pax_id, poltrona in taken_seats:
            bought_before_change_class.setdefault(poltrona, []).append(pax_id)
    return bought_before_change_class


def _is_seat_available(assento, bought_before_change_class, pax_bought_before_change_class):
    if assento.livre:
        return True

    buyers = bought_before_change_class.get(assento.numero, [])
    return len(buyers) == 1 and buyers[0] in pax_bought_before_change_class


class SeatControllerManager:
    """
    Gerenciador de cache para FretamentoSeatController.

    Mantém uma instância de FretamentoSeatController por TrechoClasse para evitar
    recriar controllers desnecessariamente.
    """

    def __init__(self, factory: Callable[[TrechoClasse], FretamentoSeatController]):
        """
        Inicializa o gerenciador.

        Args:
            factory: Função que cria um FretamentoSeatController para um TrechoClasse
        """
        self.factory = factory
        self._cache = {}

    def get(self, trecho_classe: TrechoClasse) -> FretamentoSeatController:
        """
        Obtém um FretamentoSeatController para o trecho_classe especificado.
        Utiliza cache para evitar recriar controllers desnecessariamente.

        Args:
            trecho_classe: TrechoClasse para obter o controller

        Returns:
            FretamentoSeatController para o trecho_classe
        """
        trecho_classe_id = trecho_classe.id
        if trecho_classe_id not in self._cache:
            self._cache[trecho_classe_id] = self.factory(trecho_classe)
        return self._cache[trecho_classe_id]

    def clear_cache(self) -> None:
        """Limpa todo o cache de controllers."""
        self._cache.clear()

    def remove_from_cache(self, trecho_classe_id: int) -> None:
        """
        Remove um controller específico do cache.

        Args:
            trecho_classe_id: ID do trecho_classe a ser removido do cache
        """
        self._cache.pop(trecho_classe_id, None)

    def cache_size(self) -> int:
        """
        Retorna o número de controllers em cache.

        Returns:
            Número de controllers em cache
        """
        return len(self._cache)
