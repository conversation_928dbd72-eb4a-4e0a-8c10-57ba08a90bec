import hashlib
import json
from collections import defaultdict
from dataclasses import asdict
from datetime import datetime, timedelta
from importlib.metadata import version
from itertools import chain, pairwise

import networkx as nx
from beeline import traced
from django.db import IntegrityError
from django.db.models import Prefetch, Q, QuerySet, prefetch_related_objects
from django.utils import timezone

from commons.dateutils import today_at
from commons.django_celery_utils import my_shared_task
from commons.logging import buserlogger
from core.forms.remanejamento_forms import SimulaRemanejamentoAutomatico
from core.models_grupo import (
    ConfirmacaoInteligenteExecutionLogger,
    ConfirmacaoInteligenteLogger,
    Grupo,
    TrechoClasse,
)
from core.service import itinerario_svc
from core.service.remanejamento.solver.solver_svc import (
    RemanejamentoSolverManager,
    map_parametros_trechos_classe_possiveis,
    set_solver_running,
)
from optools.errors import DataInconsistency, SolverFail
from optools.forms import (
    RemanejamentoSolverSolutionForm,
    SolverGrupoLog,
    SolverInputForm,
    SolverParams,
)
from optools.models import (
    Cenario,
    ConfirmacaoInteligenteExecucao,
    ConfirmacaoInteligenteInput,
    ConfirmacaoInteligenteStrategy,
)
from optools.remanejamento import solve_cenario_remanejamento


def confirmacao_inteligente(
    parametros_execucao: ConfirmacaoInteligenteExecucao,
    dry_run: bool,
    start_datetime_str: str,
    end_datetime_str: str,
    cenario: Cenario,
):
    if dry_run is False:
        set_solver_running()

    start_datetime = datetime.fromisoformat(start_datetime_str)
    end_datetime = datetime.fromisoformat(end_datetime_str)

    grupo_ids_ida_volta_list = _build_grupos_ida_volta_list(parametros_execucao, start_datetime, end_datetime)
    for grupos_ida_volta_id_map in grupo_ids_ida_volta_list:
        _confirmacao_inteligente.delay(
            execucao_id=parametros_execucao.id,
            grupos_ida_volta_ids=grupos_ida_volta_id_map,
            start_datetime_str=start_datetime_str,
            end_datetime_str=end_datetime_str,
            nome_cenario=cenario.name,
            dry_run=dry_run,
        )


def confirmacao_inteligente_sincrona(
    parametros_execucao: ConfirmacaoInteligenteExecucao,
    start_datetime_str: str,
    end_datetime_str: str,
    cenario: Cenario,
):
    start_datetime = datetime.fromisoformat(start_datetime_str)
    end_datetime = datetime.fromisoformat(end_datetime_str)

    grupo_ids_ida_volta_list = _build_grupos_ida_volta_list(parametros_execucao, start_datetime, end_datetime)
    for grupos_ida_volta_id_map in grupo_ids_ida_volta_list:
        _confirmacao_inteligente(
            execucao_id=parametros_execucao.id,
            grupos_ida_volta_ids=grupos_ida_volta_id_map,
            start_datetime_str=start_datetime_str,
            end_datetime_str=end_datetime_str,
            nome_cenario=cenario.name,
            dry_run=True,
        )


def _build_grupos_ida_volta_list(
    parametros_execucao: ConfirmacaoInteligenteExecucao, start_datetime: datetime, end_datetime: datetime
) -> list[dict[int, int]]:
    all_grupos_ida_volta_ids = _filtra_grupos(parametros_execucao, start_datetime, end_datetime)

    if parametros_execucao.particionado is False:
        return [all_grupos_ida_volta_ids]

    trechos_classe_dict = _build_trechos_classe_dict(all_grupos_ida_volta_ids)
    grupos_set = _build_grupos_set(trechos_classe_dict, all_grupos_ida_volta_ids)

    return _particiona_grupos_em_subgrafos(grupos_set, all_grupos_ida_volta_ids)


def _build_trechos_classe_dict(all_grupos_ida_volta_ids: dict[int, int]) -> dict[int, TrechoClasse]:
    grupo_ids = list(chain(all_grupos_ida_volta_ids, all_grupos_ida_volta_ids.values()))

    grupos = Grupo.objects.prefetch_related(
        Prefetch(
            "trechoclasse_set",
            queryset=TrechoClasse.objects.select_related(
                "grupo_classe", "trecho_vendido__origem", "trecho_vendido__destino"
            ),
        )
    ).filter(id__in=grupo_ids)

    return {tc.id: tc for g in grupos for tc in g.trechoclasse_set.all()}


def _build_grupos_set(trechos_classe_dict: dict, all_grupos_ida_volta_ids: dict[int, int]) -> set:
    grupos_set = set()

    tc_tcp_map = map_parametros_trechos_classe_possiveis(trechos_classe_dict, 20, 40)

    for tc, tcps_params in tc_tcp_map.items():
        for tcp_params in tcps_params:
            grupos_set.add((tc.grupo_id, tcp_params["tcp"].grupo_id))

    for grupo_ida_id, grupo_volta_id in all_grupos_ida_volta_ids.items():
        grupos_set.add((grupo_ida_id, grupo_volta_id))

    return grupos_set


def _particiona_grupos_em_subgrafos(grupos_set: set, all_grupos_ida_volta_ids: dict[int, int]) -> list[dict[int, int]]:
    G = nx.Graph()
    G.add_edges_from(grupos_set)
    connected_components = list(nx.connected_components(G))

    subgrafos_ida_volta_list = []
    set_grupos_ida = set(all_grupos_ida_volta_ids.keys())

    for subgrafo_grupos_ids in connected_components:
        grupos_ida_volta_map = {
            grupo_id: all_grupos_ida_volta_ids[grupo_id]
            for grupo_id in subgrafo_grupos_ids
            if grupo_id in set_grupos_ida
        }
        subgrafos_ida_volta_list.append(grupos_ida_volta_map)

    return subgrafos_ida_volta_list


@my_shared_task(queue="confirmacao_inteligente")
@traced("confirmacao_inteligente_svc._confirmacao_inteligente")
def _confirmacao_inteligente(
    execucao_id: int,
    grupos_ida_volta_ids: dict,
    start_datetime_str: str,
    end_datetime_str: str,
    nome_cenario: datetime,
    dry_run: bool = False,
):
    """Executa a confirmação inteligente para os grupos que obedecem às condições
    de confirmação inteligente. Executa simulação usando o solver e aplica o resultado
    da simulação. Se a simulação falhar, loga a falha e captura a exceção.
    Se a simulação ocorrer conforme esperado, remaneja pax e fecha grupos que foram sugeridos
    cancelamento. Pax sugeridos cancelamentos são deixados nos grupos fechados e aguardam
    processo manual dos analistas para cancelamento das reservas. Grupos que não foram fechados
    (sugestão de cancelamento pelo solver) passam a ter status confirmado caso sejam pending

    Args:
        eixos (list[str]): Conjunto de rotas principais para criar cenário

    Returns:
        None: None
    """
    start_datetime = datetime.fromisoformat(start_datetime_str)
    end_datetime = datetime.fromisoformat(end_datetime_str)

    parametros_execucao = ConfirmacaoInteligenteExecucao.objects.select_related("estrategia_aplicada").get(
        id=execucao_id
    )
    rotas_principais = list(parametros_execucao.rotas_principais.all().values_list("eixo", flat=True))
    coeficientes = parametros_execucao.estrategia_aplicada.coeficientes
    estrategia_nome = parametros_execucao.estrategia_aplicada.nome

    solver_params = SolverParams(
        **coeficientes, dry_run=dry_run, start_datetime=start_datetime, end_datetime=end_datetime
    )
    solution = None
    solver_logs = None
    solver_input_form = None
    input_form = None
    hash_code = None
    error_msg = None

    grupo_ids = list(chain(grupos_ida_volta_ids, grupos_ida_volta_ids.values()))

    form = SimulaRemanejamentoAutomatico(
        grupo_ids=grupo_ids,
        grupos_ida_volta=grupos_ida_volta_ids,
        marketplace=False,
        hibrido=True,
        downgrade=True,
        grupo_fechado=False,
        range_minutos=240,
        range_km=20,
    )
    try:
        rsm = RemanejamentoSolverManager.from_grupos_check_de_cancelamento(form=form, solver_params=solver_params)
        solver_input_form = rsm.build_solver_input_form(start_datetime, end_datetime)
        rsm.confirma_grupos_com_breakeven(solver_input_form, dry_run)
        rsm.cancelamento_fora_da_base(solver_input_form, dry_run)
        input_form = solver_input_form.model_dump()
        hash_code = hashlib.md5(json.dumps(input_form).encode()).hexdigest()
        try:
            ConfirmacaoInteligenteInput.objects.create(
                params_id=execucao_id,
                input_data=input_form,
                hash_code=hash_code,
                erro=None,
                cenario=nome_cenario,
                intervalo_selecao=(
                    start_datetime,
                    end_datetime,
                ),
                created_at_original=timezone.now(),
            )
        except IntegrityError as ex:
            buserlogger.exception(
                "confirmacao_inteligente.IntegrityError",
                extra={
                    "eixos": rotas_principais,
                    "hash_code": hash_code,
                    "error_type": IntegrityError,
                    "err_msg": str(ex),
                },
            )
        solution, solver_logs = solve_cenario_remanejamento(solver_input_form, solver_params, estrategia_nome)
        rsm.applies_solution(solution, dry_run=dry_run)

    except (DataInconsistency, SolverFail) as ex:
        error_msg = ex.message
        buserlogger.exception(
            "confirmacao_inteligente.exception",
            extra={
                "eixos": rotas_principais,
                "hash_code": hash_code,
                "error_type": ex.status_code,
                "err_msg": error_msg,
            },
        )
    finally:
        salva_logs_execucao(
            input_form if input_form else {},
            eixos=rotas_principais,
            solution=solution,
            error_exception=error_msg,
            versao_solver=version("ortools"),
            nome_estrategia=estrategia_nome,
            solver_params=solver_params,
        )
        salva_logs_detalhados(solver_logs, solver_params, estrategia_nome)


@my_shared_task(queue="confirmacao_inteligente")
def _execute_dry_run_from_solver_input(
    solver_input_id: int, strategy_name: str, start_datetime: datetime, end_datetime: datetime
):
    solver_input = ConfirmacaoInteligenteInput.objects.select_related("params").get(id=solver_input_id)
    rotas_principais = list(solver_input.params.rotas_principais.all().values_list("eixo", flat=True))
    estrategia = ConfirmacaoInteligenteStrategy.objects.get(nome=strategy_name)

    # start_datetime e end_datetime só estão aqui pra logar na tabela antiga.
    solver_params = SolverParams(**estrategia.coeficientes, start_datetime=start_datetime, end_datetime=end_datetime)

    solver_input_form = SolverInputForm.model_validate(solver_input.input_data)

    error_msg = None
    output = None
    solver_logs = None
    try:
        output, solver_logs = solve_cenario_remanejamento(solver_input_form, solver_params, strategy=estrategia.nome)

    except (DataInconsistency, SolverFail) as ex:
        error_msg = ex.message
        buserlogger.exception(
            "confirmacao_inteligente.exception",
            extra={"solver_input_id": solver_input.id, "error_type": ex.status_code, "err_msg": ex.message},
        )
    finally:
        salva_logs_execucao(
            solver_input_dict=solver_input.input_data,
            eixos=rotas_principais,
            solution=output,
            error_exception=error_msg,
            versao_solver=version("ortools"),
            nome_estrategia=estrategia.nome,
            solver_params=solver_params,
        )
        salva_logs_detalhados(solver_logs, solver_params, estrategia.nome)


@traced("confirmacao_inteligente_svc._filtra_grupos")
def _filtra_grupos(
    parametros_execucao: ConfirmacaoInteligenteExecucao, start_datetime: datetime, end_datetime: datetime
) -> dict[int, int]:
    """
    Recupera os grupos que podem ser considerados para a confirmação inteligente.
    Condições dos grupos:

    - tem rota principal no eixo
    - tem data de ida entre start_datetime e end_datetime horas daqui
    - tem status pending ou travel_confirmed
    - tem frete atual associado a rotina maior que 0
    - tem modelo de venda do grupo hibrido ou buser
    - rotina não é extra

    Considerando que cada rotina só possui um carro rodando,
    se um grupo é ida a volta correspondente é o grupo com data de partida
    mais próxima do futuro. Analogamente, se um grupo é volta, a ida correspondente
    é o grupo com data de partida mais próxima do passado.

    Rota apontada pela rotina não necessariamente é a rota do grupo.

    Args:
        grupo_ids (list[int]): Ids de grupos dentro da janela de 48 a 73 horas

    Raises:
        ValidationError: _description_

    Returns:
        dict[int, int]: Dicionário de pares de grupos ida e volta. Chave: grupo_ida_id. Valor: grupo_volta_id.
    """

    datetime_ida_grupos_volta = start_datetime + timedelta(hours=(24 * 5) + 12)
    grupos_solver_base_query = (
        Grupo.objects.select_related("rotina_onibus")
        .filter(
            (Q(rotina_onibus__frete_atual__gt=0) | Q(valor_frete__gt=0)),
            rotina_onibus__solver_threshold__isnull=False,
            datetime_ida__range=(start_datetime, datetime_ida_grupos_volta),
            status__in=(Grupo.Status.PENDING, Grupo.Status.TRAVEL_CONFIRMED),
            rotina_onibus__rota_principal__in=parametros_execucao.rotas_principais.all(),
            modelo_venda__in=("hibrido", "buser"),
        )
        .exclude(rotina_onibus__is_extra=True)
        .order_by("datetime_ida")
    )

    prefetch_related_objects(
        grupos_solver_base_query,
        Prefetch(
            "trechoclasse_set",
            queryset=TrechoClasse.objects.select_related(
                "trecho_vendido__origem", "trecho_vendido__destino", "grupo_classe"
            ),
        ),
        "rota__itinerario",
    )

    grupos_solver_base_query_list = list(grupos_solver_base_query)

    # # Grupos de ida na janela são grupos com ao menos um trecho classe com
    # origem na base operacional da rotina e data de partida do grupo
    # dentro da janela de remanejamento.
    grupos_ida_janela = set()
    grupos_volta_janela = set()
    rotas_id = grupos_solver_base_query.values_list("rota_id", flat=True)
    itinerario_rota_map = itinerario_svc.get_itinerario_data(rotas_id, local=True)
    for grupo in grupos_solver_base_query_list:
        base_operacional_id = grupo.rotina_onibus.base_operacional_id
        origens = {}
        destinos = {}
        position_map = {}
        if start_datetime <= grupo.datetime_ida <= end_datetime + timedelta(hours=24):
            for checkpoint in itinerario_rota_map[grupo.rota_id]:
                position_map[checkpoint["local"]["cidade_id"]] = checkpoint["idx"]

            for trecho_classe in grupo.trechoclasse_set.all():
                origem_id = trecho_classe.trecho_vendido.origem.cidade_id
                destino_id = trecho_classe.trecho_vendido.destino.cidade_id
                origens[origem_id] = position_map.get(origem_id)
                destinos[destino_id] = position_map.get(destino_id)

            sorted_origens_temp = sorted(origens.items(), key=lambda item: item[1])
            sorted_destinos_temp = sorted(destinos.items(), key=lambda item: item[1])
            sorted_origens = {k: i for i, (k, v) in enumerate(sorted_origens_temp)}
            sorted_destinos = {k: i for i, (k, v) in enumerate(sorted_destinos_temp)}
            index_origem = sorted_origens.get(base_operacional_id, float("inf"))
            index_destino = sorted_destinos.get(base_operacional_id, float("inf"))

            if index_origem < index_destino:
                grupos_ida_janela.add(grupo.id)
            elif index_origem > index_destino:
                grupos_volta_janela.add(grupo.id)

    return get_ida_volta_pairs(grupos_solver_base_query, grupos_ida_janela, grupos_volta_janela)


def get_ida_volta_pairs(grupos_query: QuerySet, grupos_ida_janela: set[int], grupos_volta_janela: set[int]) -> dict:
    """
    Para cada grupo de volta dentro da janela de remanejamento coleta-se o grupo de ida correspondente.
    O grupo de ida correspondente é o grupo na mesma rotina com data de ida mais próxima do passado da
    data de ida do grupo de volta.
    """
    grupos_por_rotina = defaultdict(list)

    # Uma query pra pegar tudo que é preciso
    grupos = grupos_query.values("id", "rotina_onibus_id", "datetime_ida").order_by("rotina_onibus_id", "datetime_ida")
    # agrupa por rotina
    for grupo in grupos:
        grupos_por_rotina[grupo["rotina_onibus_id"]].append(grupo["id"])

    all_grupos_ida_volta = {}
    for _, grupo_ids in grupos_por_rotina.items():
        for grupo_id, next_grupo_id in pairwise(grupo_ids):
            if grupo_id in grupos_ida_janela:
                all_grupos_ida_volta[grupo_id] = next_grupo_id
        # Parece redundância, mas não é. Quando a base_operacional não é o primeiro nem o último checkpoint da rota
        # então os grupos de ida e volta são incluídos no grupos_volta_janela.
        for grupo_id, next_grupo_id in pairwise(reversed(grupo_ids)):
            if grupo_id in grupos_volta_janela:
                all_grupos_ida_volta[next_grupo_id] = grupo_id

    return all_grupos_ida_volta


def salva_logs_detalhados(solver_logs: list[SolverGrupoLog] | None, solver_params: SolverParams, nome_estrategia: str):
    if solver_logs is None:
        return
    log_objs = []
    solver_params_dict = asdict(solver_params)
    for log in solver_logs:
        log.additional_data = log.additional_data | solver_params_dict if log.additional_data else solver_params_dict
        log.solver_strategy = nome_estrategia
        log_objs.append(ConfirmacaoInteligenteLogger(**asdict(log)))
    ConfirmacaoInteligenteLogger.objects.bulk_create(log_objs)


def salva_logs_execucao(
    solver_input_dict: dict,
    eixos: str | list[str],
    solution: RemanejamentoSolverSolutionForm | None,
    error_exception: str | None,
    versao_solver: str,
    nome_estrategia: str,
    solver_params: SolverParams,
):
    solution_dict = {}
    if solution is not None:
        solution_dict = asdict(solution)
    params = asdict(solver_params)
    log = ConfirmacaoInteligenteExecutionLogger(
        eixo=eixos,
        params=params,
        input=solver_input_dict,
        output=solution_dict,
        strategy=nome_estrategia,
        error=error_exception,
        versao_solver=versao_solver,
    )
    log.save()


def get_start_end_datetime_based_on_execution_cenario(
    parametros_execucao: ConfirmacaoInteligenteExecucao, cenario: Cenario
):
    """Dado um cenário, retorna o horário de início e término da execução.

    Args:
        parametros_execucao (ConfirmacaoInteligenteExecucao): Parâmetros de execução.
        cenario (Cenario): Cenário.

    Returns:
        tuple[datetime, datetime]: Horário de início e término da execução.
    """
    TODAY_AT_1300 = today_at(hour=13, minute=0, second=0)
    hours_ahead_start = parametros_execucao.horas_a_frente
    hours_ahead_end = hours_ahead_start + parametros_execucao.intervalo_horas
    hours_offset = cenario.value
    start_datetime = TODAY_AT_1300 + hours_ahead_start + timedelta(hours=hours_offset)
    end_datetime = TODAY_AT_1300 + hours_ahead_end + timedelta(hours=hours_offset)

    return start_datetime.isoformat(), end_datetime.isoformat()


def get_default_start_end_datetime():
    """Retorna a janela padrão de start_datetime e end_datetime para operações relacionadas
    a confirmação inteligente, mas que independem de parâmetros e cenários.

    Returns:
        tuple[datetime, datetime]: Horário de início e término da execução.
    """
    TODAY_AT_1300 = today_at(hour=13, minute=0, second=0)
    start_datetime = TODAY_AT_1300 + timedelta(hours=48)
    end_datetime = TODAY_AT_1300 + timedelta(hours=73)

    return start_datetime.isoformat(), end_datetime.isoformat()
