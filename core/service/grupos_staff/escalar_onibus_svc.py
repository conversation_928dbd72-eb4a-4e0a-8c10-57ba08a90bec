import logging
from bisect import bisect_left
from collections import defaultdict
from copy import deepcopy
from datetime import timed<PERSON><PERSON>
from enum import Enum, auto
from typing import Any

from beeline import traced
from celery import group
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Prefetch, Q, prefetch_related_objects

from adapters import new_incidents_adapter
from commons import dateutils
from commons.async_task import TaskProgressTracker
from commons.dateutils import now, to_default_tz
from commons.django_celery_utils import my_shared_task
from commons.redis import lock
from core.constants import TIPOS_ASSENTO_ORDER_MAP, TIPOS_ASSENTO_PESO
from core.forms.buckets_forms import RateioDataclass
from core.forms.company_forms import CalculateFreightReductionForm
from core.forms.staff_forms import EscalarEmpresaOnibusRotinaForm
from core.models_commons import AsyncTask, BusChangeRequestRules
from core.models_company import Onibus, OnibusClasse
from core.models_grupo import (
    Grupo,
    GrupoClasse,
    PedidoAlteracaoOnibus,
    T<PERSON>choClass<PERSON>,
    get_pedido_alteracao_onibus_data_default,
)
from core.models_rotina import AlteracaoClasseRotina
from core.models_travel import AlteracaoTravel, MotivoAlteracao, Passageiro, Travel
from core.serializers import serializer_pedidos_alteracao_onibus
from core.serializers.serializer_travel import MotivoAlteracaoSerializer
from core.service import (
    globalsettings_svc,
    grupo_autorizacao_svc,
    log_svc,
    multitrecho_svc,
    preco_svc,
    processamento_nf_svc,
    rodoviaria_svc,
    travel_comunicada_svc,
)
from core.service.grupos_staff import escalar_onibus_svc
from core.service.notifications import company_notification_svc
from core.service.onibus_svc import get_available_capacidade
from core.service.preco_svc import procura_match_rateio
from core.service.remanejamento import remanejamento_svc
from pagamento_parceiro.service import reducao_frete_svc, set_frete_svc

buserlogger = logging.getLogger("buserlogger")


def _agrupa_por_onibus_e_rotina(grupos: list[Grupo]) -> dict[tuple, dict]:
    resultado = defaultdict(lambda: {"max_datetime_ida": None, "grupos_ids": set()})
    for grupo in grupos:
        if grupo.onibus_id is not None and grupo.rotina_onibus_id is not None:
            r: dict = resultado[(grupo.onibus_id, grupo.rotina_onibus_id)]
            if r["max_datetime_ida"]:
                if r["max_datetime_ida"] < grupo.datetime_ida:
                    r["max_datetime_ida"] = grupo.datetime_ida
            else:
                r["max_datetime_ida"] = grupo.datetime_ida
            r["grupos_ids"].add(grupo.id)
    return resultado


def _get_alteracoesclasserotina(agrupados, onibus) -> dict[tuple[int, int], AlteracaoClasseRotina]:
    q_existentes = Q()
    for (old_onibus_id, rotina_onibus_id), grupos_ids in agrupados.items():
        if onibus and old_onibus_id != onibus.id:
            q_existentes |= Q(
                rotina_id=rotina_onibus_id,
                old_onibus_id=old_onibus_id,
                new_onibus_id=onibus.id,
                ajuste_preco=None,
            )

    return {(acr.old_onibus_id, acr.new_onibus_id): acr for acr in AlteracaoClasseRotina.objects.filter(q_existentes)}


def _create_alteracaoclasserotina(grupos, onibus):
    agrupados = _agrupa_por_onibus_e_rotina(grupos)
    to_create = []
    to_update = []

    existentes = _get_alteracoesclasserotina(agrupados, onibus)
    for (old_onibus_id, rotina_onibus_id), data in agrupados.items():
        max_datetime_ida = data["max_datetime_ida"]
        grupos_ids = list(data["grupos_ids"])
        key = (old_onibus_id, onibus.id)
        if key in existentes:
            instance = existentes[key]
            instance.max_datetime_ida = max_datetime_ida
            if instance.grupos_ids:
                instance.grupos_ids += grupos_ids
            else:
                instance.grupos_ids = grupos_ids
            to_update.append(instance)
        else:
            to_create.append(
                AlteracaoClasseRotina(
                    rotina_id=rotina_onibus_id,
                    grupos_ids=grupos_ids,
                    old_onibus_id=old_onibus_id,
                    new_onibus_id=onibus.id,
                    max_datetime_ida=max_datetime_ida,
                )
            )

    AlteracaoClasseRotina.objects.bulk_create(to_create)
    AlteracaoClasseRotina.objects.bulk_update(to_update, ["grupos_ids", "max_datetime_ida"])


@traced("escalar_onibus_svc.bulk_escalar_onibus")
def bulk_escalar_onibus(
    form: EscalarEmpresaOnibusRotinaForm,
    origem_alteracao_placa: str,
    operacoes_rodoviaria: dict[int, dict[str, bool]] | None = None,
):
    alterar_layout = form.update_group_layout
    onibus_id = form.onibus_id
    grupo_ids = form.grupo_ids
    motivo_remanejamento = form.motivo_remanejamento
    motivo_description = form.motivo_description
    grupos = remanejamento_svc.get_grupos_para_remanejamento(grupo_ids)
    if onibus_id:
        onibus = Onibus.objects.prefetch_related("classes", "infoonibus_set").get(pk=onibus_id)
    else:
        onibus = None

    # alteração de classe ou capacidade
    if alterar_layout:
        _create_alteracaoclasserotina(grupos, onibus)

    tasks_rodoviaria = []
    for grupo in grupos:
        atualizar_onibus_api = (
            operacoes_rodoviaria
            and operacoes_rodoviaria.get(grupo.id)
            and operacoes_rodoviaria[grupo.id]["escalar_onibus"]
        )
        if atualizar_onibus_api:
            grupos_classe_ids_antigos = list(grupo.grupoclasse_set.values_list("id", flat=True))
            tasks_rodoviaria.append(
                rodoviaria_svc.escalar_onibus_grupo_hibrido(grupo, onibus, grupos_classe_ids_antigos)
            )

    bulk_alterar_onibus_grupo(
        onibus,
        grupos,
        motivo_remanejamento,
        motivo_description,
        alterar_layout=alterar_layout,
        staff=True,
        origem_alteracao_placa=origem_alteracao_placa,
        causada_por=AlteracaoTravel.CausadaPor.ANALISTA,
    )
    log_svc.log_bulk_change_bus(
        {
            "grupo_ids": grupo_ids,
            "onibus_id": onibus_id,
            "update_group_layout": alterar_layout,
        }
    )

    if tasks_rodoviaria:
        rodoviaria_svc.verifica_criacao_grupos_hibrido_n_empresas(grupos)
        return group(tasks_rodoviaria)


@transaction.atomic
def bulk_alterar_onibus_grupo(
    onibus: Onibus | None,
    grupos: list[Grupo],
    motivo_remanejamento: str | None,
    motivo_description: str | None,
    alterar_layout: bool | None = False,
    staff: bool = False,
    origem_alteracao_placa: str = "nao-identificado",
    causada_por: AlteracaoTravel.CausadaPor = AlteracaoTravel.CausadaPor.AUTOMATICO,
    motivo_id: int | None = None,
) -> None:
    _bulk_alterar_onibus(grupos, onibus, staff=staff, alterar_layout=alterar_layout)
    needs_to_cancel_accops = False
    travels = []

    if onibus and not alterar_layout:
        travels = (
            Travel.objects.filter(grupo__in=grupos)
            .prefetch_related(Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False)))
            .exclude(status="canceled")
        )
        needs_to_cancel_accops = True
        for travel in travels:
            travel_comunicada_svc.salvar_travel_comunicada(travel, onibus=travel.grupo.onibus)

    # check if freight reduction based on the original group layout
    should_apply_freight_reduction_map = _bulk_should_apply_freight_reduction(grupos, onibus, causada_por)
    for grupo in grupos:
        onibus_antigo = deepcopy(grupo.onibus)
        grupo.onibus = onibus
        if onibus and alterar_layout:
            atualizar_grupoclasse_por_onibus(
                grupo,
                onibus,
                motivo_remanejamento,
                motivo_description,
                onibus_antigo,
                origem_alteracao_placa=origem_alteracao_placa,
                causada_por=causada_por,
                motivo_id=motivo_id,
            )
        if should_apply_freight_reduction_map[grupo.id]:
            # TODO: precisa otimizar para fazer em bulk
            # calculate freight reduction based on the downgrades that happens on atualizar_grupoclasse_por_onibus
            update_freight_value_with_reduction(grupo, onibus_antigo, onibus, "alterar_onibus_grupo_reducao_frete")

    if not onibus or needs_to_cancel_accops:
        travels = travels or (
            Travel.objects.filter(grupo__in=grupos)
            .prefetch_related(Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False)))
            .exclude(status="canceled")
        )
        remanejamento_svc.cancelar_accops_e_remarcar_assentos(travels)
    Grupo.objects.bulk_update(grupos, ["onibus", "updated_on"])


@transaction.atomic
def alterar_onibus_grupo(
    onibus: Onibus | None,
    grupo: Grupo,
    motivo_remanejamento: str | None,
    motivo_description: str | None,
    alterar_layout: bool | None = False,
    staff: bool = False,
    origem_alteracao_placa: str = "nao-identificado",
    causada_por: AlteracaoTravel.CausadaPor = AlteracaoTravel.CausadaPor.AUTOMATICO,
    motivo_id: int | None = None,
) -> None:
    onibus_antigo = grupo.onibus
    prefetch_related_objects([onibus], "infoonibus_set")
    if not staff or alterar_layout:
        prefetch_related_objects([onibus], "classes")
    _alterar_onibus(grupo, onibus, staff=staff, alterar_layout=alterar_layout)

    needs_to_cancel_accops = False
    travels = []

    if onibus and not alterar_layout:
        travels = grupo.travel_set.prefetch_related(
            Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False))
        ).exclude(status="canceled")
        needs_to_cancel_accops = True
        for travel in travels:
            travel_comunicada_svc.salvar_travel_comunicada(travel, onibus=onibus_antigo)

    # check if freight reduction based on the original group layout
    should_apply_freight_reduction = _should_apply_freight_reduction(grupo, onibus_antigo, onibus, causada_por)
    if onibus and alterar_layout:
        atualizar_grupoclasse_por_onibus(
            grupo,
            onibus,
            motivo_remanejamento,
            motivo_description,
            onibus_antigo,
            origem_alteracao_placa=origem_alteracao_placa,
            causada_por=causada_por,
            motivo_id=motivo_id,
        )

    if not onibus or needs_to_cancel_accops:
        travels = travels or grupo.travel_set.prefetch_related(
            Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False))
        ).exclude(status="canceled")
        remanejamento_svc.cancelar_accops_e_remarcar_assentos(travels)

    # calculate freight reduction based on the downgrades that happens on atualizar_grupoclasse_por_onibus
    if should_apply_freight_reduction:
        update_freight_value_with_reduction(grupo, onibus_antigo, onibus, "alterar_onibus_grupo_reducao_frete")

    grupo.save(update_fields=["onibus"])


@my_shared_task(queue="async_task")
def simular_escalar_onibus_task(async_task_id: int):
    async_task = AsyncTask.objects.get(id=async_task_id)
    with TaskProgressTracker(async_task) as tracker:
        grupo_ids = async_task.input_data["group_ids"]
        onibus_id = async_task.input_data["onibus_id"]

        onibus = Onibus.objects.prefetch_related(
            Prefetch("classes", queryset=OnibusClasse.objects.filter(capacidade__gt=0))
        ).get(pk=onibus_id)
        grupos = remanejamento_svc.get_grupos_para_remanejamento(grupo_ids)
        _valida_capacidade_onibus(onibus, grupos)

        classes_form = list(onibus.classes.all())
        simulacoes = []
        for grupo in grupos:
            _alterar_onibus(grupo, onibus, alterar_layout=True, staff=True)
            if globalsettings_svc.get("simula_sem_precificar", False):
                precificacao = {}  # NÃO VAI LEVAR EM CONTA BUCKETS E PRECIFICAÇÃO!
            else:
                precificacao = procura_match_rateio(grupo, classes_form, onibus)
            sim = remanejamento_svc.simular_remanejamento_massivo(
                _parametros_troca_de_classe(grupo, classes_form, precificacao, onibus)
            )
            if precificacao is not None:
                sim.update({"precificacao": [preco.dict() for preco in precificacao.values()]})
            simulacoes.append(sim)

        tracker.save_result({"simulacoes": simulacoes})
    return simulacoes


def verificar_conflito_onibus(grupos_ids, onibus_id, intervalo_de_tempo: timedelta | None = None):
    if intervalo_de_tempo is None:
        intervalo_de_tempo = timedelta(hours=2)

    grupos_com_onibus = (
        Grupo.objects.select_related("rota")
        .filter(onibus_id=onibus_id, datetime_ida__gte=now() - timedelta(days=7))
        .exclude(status="canceled")
        .order_by("datetime_ida")
    )

    grupos_com_onibus_ida = [grupo.datetime_ida for grupo in grupos_com_onibus]
    grupos_com_onibus = list(grupos_com_onibus)
    grupos_a_serem_escalados = Grupo.objects.select_related("rota").filter(id__in=grupos_ids).order_by("datetime_ida")

    for grupo in grupos_a_serem_escalados:
        index = bisect_left(grupos_com_onibus_ida, grupo.datetime_ida)
        grupos_com_onibus_ida.insert(index, grupo.datetime_ida)
        grupos_com_onibus.insert(index, grupo)
        _validar_grupo_anterior(index, grupo, grupos_com_onibus, intervalo_de_tempo)
        _validar_proximo_grupo(index, grupo, grupos_com_onibus, intervalo_de_tempo)


def _validar_grupo_anterior(index, grupo, grupos_com_onibus, intervalo_de_tempo: timedelta):
    if index > 0:
        grupo_anterior = grupos_com_onibus[index - 1]
        if grupo.datetime_ida <= grupo_anterior.datetime_ida + grupo_anterior.duracao_total + intervalo_de_tempo:
            raise ValidationError(
                f"Pode ser que o ônibus selecionado já esteja escalado dentro do horário do grupo {grupo_anterior.id}"
            )


def _validar_proximo_grupo(index, grupo, grupos_com_onibus, intervalo_de_tempo: timedelta):
    if index < len(grupos_com_onibus) - 1:
        proximo_grupo = grupos_com_onibus[index + 1]
        if proximo_grupo.datetime_ida <= grupo.datetime_ida + grupo.duracao_total + intervalo_de_tempo:
            raise ValidationError(
                f"Pode ser que o ônibus selecionado já esteja escalado dentro do horário do grupo {proximo_grupo.id}"
            )


def _valida_capacidade_onibus(onibus: Onibus, grupos: list[Grupo]):
    for grupo in grupos:
        capacidade = sum(get_available_capacidade(grupo, onibus, c) for c in onibus.classes.all())
        if capacidade == 0:
            raise ValidationError("Ônibus não possui assentos vendidos")


@traced("escalar_onibus_svc.atualizar_grupoclasse_por_onibus")
def atualizar_grupoclasse_por_onibus(
    grupo,
    onibus,
    motivo_remanejamento,
    motivo_description,
    onibus_antigo,
    origem_alteracao_placa: str,
    causada_por: AlteracaoTravel.CausadaPor = AlteracaoTravel.CausadaPor.AUTOMATICO,
    motivo_id: int | None = None,
):
    """
    A definição/alteração do ônibus de um grupo é uma tarefa complexa.

    O bulk_change_class reconstrói grupos classe e trechos classe, deletando
    os objetos anteriores, o que é bem ruim para o sistema como um todo. É
    uma operação lenta no banco, perdemos logs e mais alguns outros detalhes.

    Para cada trecho_classe reconstruído, precisa recalcular duração da rota.

    Cada travel desse grupo é associado com o novo grupo_classe e trecho_classe.

    Se tiver downgrade, faz ressarcimento dos pax afetados.

    Tem também que atualizar a contagem de pessoas/vagas (aqui tá atualisando
    vagas dos trechos?).

    Alguns casos foram otimizados para não fazer esse fluxo todo. Se a mudança
    de ônibus não teve alterações de classe, podemos apenas ajustar as
    capacidades das classes.
    """
    novas_classes = list(onibus.classes.all())
    precificacao = procura_match_rateio(grupo, novas_classes, onibus)

    if not _precisa_remanejar(grupo, onibus):
        _resize_grupo(grupo, onibus, precificacao)
        return
    kwargs = {"motivo_remanejamento": motivo_remanejamento, "motivo_description": motivo_description}

    parametros_remanejamento = _parametros_troca_de_classe(grupo, novas_classes, precificacao, onibus, **kwargs)
    log_params = parametros_remanejamento.copy()
    parametros_remanejamento["onibus_antigo"] = onibus_antigo

    remanejamento_svc.bulk_change_class(
        parametros_remanejamento,
        origem_alteracao_placa=origem_alteracao_placa,
        causada_por=causada_por,
        motivo_id=motivo_id,
    )

    _log_precificacao(grupo, precificacao, log_params)
    log_svc.log_troca_onibus(
        grupo,
        old_bus_id=onibus_antigo.id if onibus_antigo else None,
        new_bus_id=onibus.id,
    )


def _precisa_remanejar(grupo, onibus):
    # Tem pax.
    # TODO: Se os pax cabem no ônibus, não precisa remanejar.
    tem_pessoas = any(tc.pessoas for tc in grupo.trechoclasse_set.all())
    if tem_pessoas:
        return True

    # Classes diferentes.
    onibus_tipo_classes = {o.tipo for o in onibus.classes.all()}
    grupo_tipo_classes = {gc.tipo_assento for gc in grupo.grupoclasse_set.all()}
    if onibus_tipo_classes != grupo_tipo_classes:
        return True

    return False


@traced("escalar_onibus_svc._resize_grupo")
def _resize_grupo(grupo, onibus, precificacao):
    """
    Altera a capacidade das classes e o tipo de assento quando necessário,
    Define os novos valores por trecho.
    """
    onibus_classe_map = {c.tipo: c for c in onibus.classes.all()}
    grupos_classe = grupo.grupoclasse_set.all()
    grupos_classe_map = {gc.tipo_assento: gc for gc in grupos_classe}

    novas_classes = [
        onibus_classe for tipo, onibus_classe in onibus_classe_map.items() if tipo not in grupos_classe_map
    ]

    for grupo_classe in grupos_classe:
        tipo = grupo_classe.tipo_assento
        try:
            # o fluxo com a troca de tipo_assento, sem remanejamento, não deve mais ocorrer.
            # acompanhar por algumas semanas e remover esse tratamento.
            onibus_classe = onibus_classe_map[tipo]
        except KeyError:
            # Muda um grupo classe para o tipo que o ônibus tem.
            onibus_classe = novas_classes.pop()
            grupo_classe.tipo_assento = onibus_classe.tipo
            # espero que esse caso não ocorra mais. Logando para acompanhar antes de apagar.
            buserlogger.info(
                "escalar_onibus_svc._resize_grupo",
                extra={"tipo_assento_antigo": tipo, "tipo_assento_novo": onibus_classe.tipo},
            )
        grupo_classe.capacidade = get_available_capacidade(grupo, onibus, onibus_classe)

    # O cálculo de vagas é bem mais complexo do que isso, mas esse resize por enquanto
    # só acontece para grupos vazios.
    # TODO: Implementar o resize quando houver pax, mas o tipo assento é mantido e não ocorre overbooking.
    grupos_classe_map = {gc.id: gc for gc in grupos_classe}
    trechos_classe = TrechoClasse.objects.prefetch_related("price_manager__buckets").filter(grupo_id=grupo.id)
    price_managers, price_logs, price_buckets = [], [], []
    for trecho_classe in trechos_classe:
        grupo_classe = grupos_classe_map[trecho_classe.grupo_classe_id]
        trecho_classe.vagas = grupo_classe.capacidade

        match = precificacao[trecho_classe.trecho_vendido_id, grupo_classe.tipo_assento]
        trecho_classe.max_split_value = match.valor
        trecho_classe.ref_split_value = match.valor_ref
        price_manager, price_bucket, price_log = preco_svc.determine_bucket_update(
            trecho_classe, match.buckets_to_dict()
        )
        price_buckets += price_bucket
        price_logs += price_log
        price_managers.append(price_manager)
        trecho_classe.price_manager = price_manager

    preco_svc.bulk_update_price_objects(price_managers, price_buckets, price_logs)
    for tc in trechos_classe:
        tc.price_manager_id = tc.price_manager.id

    GrupoClasse.objects.bulk_update(grupos_classe, ["tipo_assento", "capacidade"])
    TrechoClasse.objects.bulk_update(
        trechos_classe, ["vagas", "max_split_value", "ref_split_value", "price_manager_id"]
    )


def _parametros_troca_de_classe(
    grupo, novas_classes: list[OnibusClasse], precificacao: dict[tuple[int, str], RateioDataclass], onibus, **kwargs
) -> dict[str, Any]:
    """
    Constrói os parâmetros para o bulk_change_class.
    """
    confirmed_reason = "porque o ônibus que estava reservado precisou passar por uma manutenção não planejada"
    pending_reason = "porque não encontramos ônibus disponível da classe que você desejava"
    status_that_notify = ("pending", "travel_confirmed")

    class_list = remanejamento_svc.match_rateio_classes_bucketizadas(grupo, novas_classes, precificacao, onibus)

    return {
        "class_change_reason": confirmed_reason if grupo.status == Grupo.Status.TRAVEL_CONFIRMED else pending_reason,
        "groups": [grupo],
        "notify_users_class_changed": grupo.status in status_that_notify,
        "classes": class_list,
        "motivo_remanejamento": kwargs.get("motivo_remanejamento"),
        "motivo_description": kwargs.get("motivo_description"),
    }


def _log_precificacao(grupo: Grupo, precificacao, parametros_remanejamento):
    log_params = parametros_remanejamento.copy()
    try:
        log_params["groups"] = [g.id for g in log_params["groups"]]
    except AttributeError:
        pass

    log_svc.log_precificacao_automatica(
        grupo,
        {
            "precificacao": [preco.dict() for preco in precificacao.values()],
            "parametros_remanejamento": log_params,
        },
    )


@transaction.atomic
def create_pedido_alteracao_onibus(**data):
    PedidoAlteracaoOnibus.objects.filter(status=PedidoAlteracaoOnibus.Status.PENDENTE, grupo=data["grupo"]).update(
        status=PedidoAlteracaoOnibus.Status.SOBRESCRITO
    )

    return PedidoAlteracaoOnibus.objects.create(**data)


def motivos_alteracao():
    return MotivoAlteracao.objects.to_serialize(MotivoAlteracaoSerializer).all()


@lock("julgar_pedidos_alteracoes_onibus_parceiro")
@transaction.atomic
def julgar_pedidos_alteracoes_onibus_parceiro(
    lista_pedidos: list[dict[str, Any]], aprovado: bool, motivo_id: str | None, origem_alteracao_placa: str
):
    pedidos_ids = [pedido["id"] for pedido in lista_pedidos]
    pedidos_qs = PedidoAlteracaoOnibus.objects.to_serialize(
        serializer_pedidos_alteracao_onibus.PedidoAlteracaoOnibusSerializer
    ).filter(id__in=pedidos_ids)

    for pedido in pedidos_qs:
        if pedido.grupo.onibus and pedido.grupo.onibus.id == pedido.next_bus.id:
            pedido.status = PedidoAlteracaoOnibus.Status.REJEITADO
            pedido.save(update_fields=["updated_at", "status"])
            continue

        pedido_serialized = pedido.serialize()
        pedido_serialized_data = pedido_serialized["data"]
        pedido.data = get_pedido_alteracao_onibus_data_default(
            is_emergency=pedido_serialized_data["is_emergency"],
            is_downgrade=pedido_serialized_data["is_downgrade"],
            is_overbooking=pedido_serialized_data["is_overbooking"],
            is_older_bus=pedido_serialized_data["is_older_bus"],
            is_out_of_time=pedido_serialized_data["is_out_of_time"],
            frete_atual=pedido_serialized_data["frete_atual"],
            frete_novo=pedido_serialized_data["frete_novo"],
            frete_mudou=pedido_serialized_data["frete_mudou"],
        )

        if aprovado:
            motivo_remanejamento = "PARCEIRO_SOLICITOU"
            motivo_description = pedido.reason_type

            _create_alteracaoclasserotina([pedido.grupo], pedido.next_bus)
            log_svc.log_troca_onibus(pedido.grupo, old_bus_id=pedido.grupo.onibus_id, new_bus_id=pedido.next_bus.id)
            alterar_onibus_grupo(
                onibus=pedido.next_bus,
                grupo=pedido.grupo,
                motivo_remanejamento=motivo_remanejamento,
                motivo_description=motivo_description,
                alterar_layout=True,
                staff=True,
                origem_alteracao_placa=origem_alteracao_placa,
                causada_por=AlteracaoTravel.CausadaPor.PARCEIRO,
                motivo_id=motivo_id,
            )
            pedido.status = PedidoAlteracaoOnibus.Status.APROVADO
            pedido.save(update_fields=["updated_at", "data", "status"])

            log_svc.log_aprovar_alteracao_onibus_parceiro(pedido.grupo, pedido_serialized)
            grupo_autorizacao_svc.reset_autorizacao_if_exist(pedido.grupo)
        else:
            pedido.status = PedidoAlteracaoOnibus.Status.REJEITADO
            pedido.save(update_fields=["updated_at", "data", "status"])
            log_svc.log_reprovar_alteracao_onibus_parceiro(pedido.grupo, pedido_serialized)

        pedido_serialized["grupo_id"] = pedido.grupo.id
        company_notification_svc.pedido_mudanca_onibus(pedido_serialized, aprovado=aprovado)


def update_freight_value_with_reduction(grupo, old_bus, new_bus, fluxo: str, user=None):
    try:
        freight = reducao_frete_svc.calculate_freight_reduction(
            CalculateFreightReductionForm(new_bus_id=new_bus.id, group_id=grupo.id), old_bus=old_bus
        )

        set_frete_svc.set_valor_frete(grupo, freight.get("new_value"), user, fluxo)

        if grupo.notafiscal:
            processamento_nf_svc.cancelar_async(grupo.notafiscal.id)

        log_svc.log_reduzir_frete(
            grupo,
            freight.get("current_value"),
            freight.get("new_value"),
            freight.get("reduction"),
            old_bus.id,
            old_bus.placa,
            new_bus.id,
            new_bus.placa,
        )

    except ValidationError as e:
        buserlogger.warning(
            f"Freight reduction not avaiable: {e}",
            extra=dict(group=grupo, new_bus=new_bus, old_bus=old_bus),
        )


def _bulk_should_apply_freight_reduction(
    groups: list[Grupo], new_bus: Onibus | None, causada_por: AlteracaoTravel.CausadaPor
) -> dict[int, bool]:
    group_should_reduce_freight_payment_map = {}
    if causada_por != AlteracaoTravel.CausadaPor.PARCEIRO:
        return {group.id: False for group in groups}
    group_ids = [group.id for group in groups]
    group_has_apprehension_map = new_incidents_adapter.groups_have_incidents(
        group_ids=group_ids, subcategories=["viagem-descoberta-volta-apreensao"]
    )
    bus_type_has_changed_map = _bulk_get_bus_type_changed(groups, new_bus)
    group_has_overbooking_map = _bulk_has_overbooking(groups, new_bus)
    group_has_class_downgrade_map = _bulk_has_class_downgrade(groups, new_bus)

    for grupo in groups:
        has_apprehension = group_has_apprehension_map[grupo.id]
        has_overbooking = group_has_overbooking_map[grupo.id]
        has_class_downgrade = group_has_class_downgrade_map[grupo.id]
        group_should_reduce_freight_payment_map[grupo.id] = not has_apprehension and (
            bus_type_has_changed_map[grupo.id] or has_overbooking or has_class_downgrade
        )
    return group_should_reduce_freight_payment_map


def _bulk_get_bus_type_changed(grupos: list[Grupo], new_bus: Onibus | None) -> dict[int, bool]:
    changed_type_map = dict()
    for grupo in grupos:
        changed_type_map[grupo.id] = (
            grupo.onibus is not None and new_bus is not None and grupo.onibus.tipo == new_bus.tipo
        )
    return changed_type_map


def _should_apply_freight_reduction(
    group: Grupo, old_bus: Onibus, new_bus: Onibus | None, causada_por: AlteracaoTravel.CausadaPor
) -> bool:
    if causada_por != AlteracaoTravel.CausadaPor.PARCEIRO:
        return False

    has_apprehension = new_incidents_adapter.groups_have_incidents(
        group_ids=[group.id], subcategories=["viagem-descoberta-volta-apreensao"]
    ).get(group.id, False)
    bus_type_has_changed = old_bus and new_bus and old_bus.tipo != new_bus.tipo
    has_overbooking = _has_overbooking(group, old_bus, new_bus)
    has_class_downgrade = _has_class_downgrade(group, old_bus, new_bus)
    return not has_apprehension and (bus_type_has_changed or has_overbooking or has_class_downgrade)


def _bulk_has_class_downgrade(groups: list[Grupo], new_bus: Onibus | None) -> dict[int, bool]:
    if new_bus is None:
        return {group.id: True for group in groups}

    simulacao_map = {}
    capacity_manager_map = multitrecho_svc.bulk_prepare_capacity_manager(groups)
    for grupo in groups:
        novas_classes = list(new_bus.classes.all())
        precificacao = procura_match_rateio(grupo, novas_classes, new_bus)
        params = _parametros_troca_de_classe(grupo, novas_classes, precificacao, new_bus)
        trechos_vendidos_map = remanejamento_svc._build_trechos_vendidos_map(params["classes"])

        simulacao = remanejamento_svc.simular_remanejamento(
            grupo=grupo,
            params=params,
            trechos_vendidos_map=trechos_vendidos_map,
            grupo_cm=capacity_manager_map[grupo.id],
        )
        simulacao_map[grupo.id] = bool(simulacao["downgraded"])

    return simulacao_map


def _has_class_downgrade(group: Grupo, old_bus: Onibus | None, new_bus: Onibus | None) -> bool:
    if not new_bus:
        return True
    if not old_bus:
        return False

    grupo = remanejamento_svc.get_grupos_para_remanejamento([group])[0]

    count_seats_map = multitrecho_svc._prepare_count_seats_map(group)
    capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(group, count_seats_map)

    classes_form = list(new_bus.classes.all())
    precificacao = procura_match_rateio(grupo, classes_form, new_bus)
    params = _parametros_troca_de_classe(grupo, classes_form, precificacao, new_bus)
    trechos_vendidos_map = remanejamento_svc._build_trechos_vendidos_map(params["classes"])

    simulacao = remanejamento_svc.simular_remanejamento(
        grupo=grupo, params=params, trechos_vendidos_map=trechos_vendidos_map, grupo_cm=capacity_manager
    )

    #  simulação altera os objetos sem salvar no banco.
    group.refresh_from_db()
    group.onibus = new_bus

    return bool(simulacao["downgraded"])


def _has_overbooking(group: Grupo, old_bus: Onibus | None, new_bus: Onibus | None) -> bool:
    if new_bus:
        classes = new_bus.classes.all()
        qtd_pessoas_onibus_suporta = sum(get_available_capacidade(group, new_bus, c) for c in classes)
    else:
        # ato de remover o ônibus
        qtd_pessoas_onibus_suporta = 0

    capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(group)
    quantidade_pessoas_no_grupo = max(capacity_manager.ocupacao_por_trecho_vendido().values(), default=0)

    return qtd_pessoas_onibus_suporta < quantidade_pessoas_no_grupo


def _bulk_has_overbooking(groups: list[Grupo], new_bus: Onibus | None) -> dict[int, bool]:
    capacity_manager = multitrecho_svc.bulk_prepare_capacity_manager(groups)
    group_has_overbooking_map = {}
    for grupo in groups:
        quantidade_pessoas_no_grupo = max(capacity_manager[grupo.id].ocupacao_por_trecho_vendido().values(), default=0)
        if new_bus:
            classes = new_bus.classes.all()
            qtd_pessoas_onibus_suporta = sum(get_available_capacidade(grupo, new_bus, c) for c in classes)
        else:
            # ato de remover o ônibus
            qtd_pessoas_onibus_suporta = 0
        group_has_overbooking_map[grupo.id] = qtd_pessoas_onibus_suporta < quantidade_pessoas_no_grupo
    return group_has_overbooking_map


def get_pedidos_alteracoes_parceiros():
    pedidos_alteracao_onibus = (
        PedidoAlteracaoOnibus.objects.select_related("next_bus", "previous_bus", "grupo", "grupo__rota", "created_by")
        .prefetch_related("next_bus__classes", "previous_bus__classes")
        .filter(status=PedidoAlteracaoOnibus.Status.PENDENTE)
    ).to_serialize(serializer_pedidos_alteracao_onibus.PedidoAlteracaoOnibusBasicSerializer)

    pedidos_dict = [pedido.serialize() for pedido in pedidos_alteracao_onibus]
    return pedidos_dict


def get_pedido_alteracao_onibus(group_id):
    try:
        pedido_alteracao_onibus = (
            PedidoAlteracaoOnibus.objects.to_serialize(
                serializer_pedidos_alteracao_onibus.PedidoAlteracaoOnibusSerializer
            )
            .select_related("next_bus", "previous_bus", "grupo", "grupo__rota")
            .filter(grupo=group_id)
            .latest()
        )
    except PedidoAlteracaoOnibus.DoesNotExist:
        return

    pedido_alteracao_onibus_serialized = pedido_alteracao_onibus.serialize()
    pedido_alteracao_onibus_serialized["horario_ida_grupo"] = pedido_alteracao_onibus.grupo.datetime_ida
    pedido_alteracao_onibus_serialized["placa_atual"] = (
        pedido_alteracao_onibus.previous_bus.placa if pedido_alteracao_onibus.previous_bus else None
    )
    pedido_alteracao_onibus_serialized["placa_solicitada"] = (
        pedido_alteracao_onibus.next_bus.placa if pedido_alteracao_onibus.next_bus else None
    )
    return pedido_alteracao_onibus_serialized


def _teste_trocar_onibus(grupos: list[Grupo], onibus, *, staff=False, alterar_layout=False):
    grupos_to_process = []
    for grupo in grupos:
        if not onibus:
            grupo.onibus = None
            continue
        grupos_to_process.append(grupo)
    resultados_permitidos = [MudancaOnibus.APROVADA]
    testes = _REGRAS_TROCA_PARCEIRO
    if staff and alterar_layout:
        testes = _REGRAS_TROCA_STAFF
        resultados_permitidos.append(MudancaOnibus.PRECISA_APROVACAO)
    elif staff:
        testes = _REGRAS_TROCA_SEM_MUDANCA_LAYOUT
        resultados_permitidos.append(MudancaOnibus.PRECISA_APROVACAO)
    capacity_manager_map = multitrecho_svc.bulk_prepare_capacity_manager(grupos_to_process)
    for grupo in grupos_to_process:
        for teste in testes:
            resultado, erro = testa_regra(teste, onibus, grupo, capacity_manager_map.get(grupo.id))
            if resultado not in resultados_permitidos:
                raise ModificationError(erro, resultado)

    return testes


def testa_regra(func, onibus, grupo, capacity_manager):
    return func(onibus, grupo, capacity_manager)


def _bulk_alterar_onibus(grupos: list[Grupo], onibus, alterar_layout, *, staff=False):
    _teste_trocar_onibus(grupos, onibus, staff=staff, alterar_layout=alterar_layout)


def _alterar_onibus(grupo, onibus, alterar_layout, *, staff=False):
    _teste_trocar_onibus([grupo], onibus, staff=staff, alterar_layout=alterar_layout)
    grupo.onibus = onibus


_REGRAS_TROCA_PARCEIRO = []
_REGRAS_TROCA_STAFF = []
_REGRAS_TROCA_SEM_MUDANCA_LAYOUT = []


class MudancaOnibus(Enum):
    # DOCUMENTACAO_NAO_APROVADA está setado para abrir um toast no frontend indicando o erro. NAO_APROVADA abre um poup pra gerar um pedido de alteração de placa.
    APROVADA = auto()
    NAO_APROVADA = auto()
    PRECISA_APROVACAO = auto()
    DOCUMENTACAO_NAO_APROVADA = auto()
    MICRO_ONIBUS_OR_VAN = auto()


def regra_alteracao_onibus(staff=True, parceiro=True, mudanca_layout=True):
    def decorator(funcao_regra):
        if parceiro:
            _REGRAS_TROCA_PARCEIRO.append(funcao_regra)
        if staff:
            _REGRAS_TROCA_STAFF.append(funcao_regra)
        if staff and not mudanca_layout:
            _REGRAS_TROCA_SEM_MUDANCA_LAYOUT.append(funcao_regra)

    return decorator


@regra_alteracao_onibus(staff=False, parceiro=False)
def _preparacao_contra_covid19(onibus, grupo, capacity_manager):
    msg_erro = "Durante a retomada, todos os carros devem estar preparados contra o COVID-19."
    resultado = MudancaOnibus.PRECISA_APROVACAO
    if "adaptado_contra_covid" in onibus.jsondata.get("features", []):
        resultado = MudancaOnibus.APROVADA
    return resultado, msg_erro


@regra_alteracao_onibus(staff=False)
def _horario_permitido(onibus, grupo, capacity_manager):
    msg_erro = ""
    resultado = MudancaOnibus.PRECISA_APROVACAO
    horario_limite = to_default_tz(grupo.datetime_ida - timedelta(hours=1))
    in_time = now() < horario_limite

    if in_time:
        resultado = MudancaOnibus.APROVADA
    else:
        padrao_hora = "%H:%M de %d/%m/%Y "
        agora = to_default_tz(now()).strftime(padrao_hora)
        limite = horario_limite.strftime(padrao_hora)
        msg_erro = f"Alterações devem ocorrer até {limite} (agora são {agora})"

    return resultado, msg_erro


@regra_alteracao_onibus()
def _nao_permite_overbooking(onibus, grupo, capacity_manager):
    msg_erro = "A mudança de ônibus causará overbooking"
    if capacity_manager is None:
        capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(grupo)

    classes = onibus.classes.all()
    qtd_pessoas_onibus_suporta = sum(get_available_capacidade(grupo, onibus, c) for c in classes)

    if qtd_pessoas_onibus_suporta >= max(capacity_manager.ocupacao_por_trecho_vendido().values()):
        resultado = MudancaOnibus.APROVADA
    else:
        resultado = MudancaOnibus.NAO_APROVADA

    return resultado, msg_erro


@regra_alteracao_onibus(staff=False)
def _nao_permite_overlap(onibus, grupo, capacity_manager):
    resultado = MudancaOnibus.APROVADA
    msg_erro = ""
    try:
        escalar_onibus_svc.verificar_conflito_onibus([grupo.id], onibus, intervalo_de_tempo=timedelta(minutes=0))
        return resultado, msg_erro
    except ValidationError as err:
        resultado = MudancaOnibus.NAO_APROVADA
        msg_erro = err

    return resultado, msg_erro


@regra_alteracao_onibus(staff=False)
def _downgrade(onibus, grupo, capacity_manager):
    has_downgrade = _has_class_downgrade(grupo, old_bus=grupo.onibus, new_bus=onibus)

    msg_erro = " Mudança precisa de autorização:\n causará rebaixamento de classe do ônibus"

    resultado = MudancaOnibus.APROVADA
    if has_downgrade:
        resultado = MudancaOnibus.PRECISA_APROVACAO
    return resultado, msg_erro


def update_bus_change_request_rules(form):
    _now = now()
    for f in form.rules:
        f.rule.reduction = f.reduction
        f.rule.updated_at = _now

    BusChangeRequestRules.objects.bulk_update([f.rule for f in form.rules], ["reduction", "updated_at"])


class ModificationError(ValidationError):
    def __init__(self, erros: str, status: MudancaOnibus, type=None) -> None:
        super().__init__(erros)
        self.status = status
        self.type = type


def get_classe_inferior(classes: list[str]) -> str:
    return min(classes, key=lambda k: TIPOS_ASSENTO_ORDER_MAP[k])


def is_downgrade(previous_bus, next_bus):
    if not previous_bus or not next_bus:
        return False

    new_bus_classes_tipos = [classe.tipo for classe in next_bus.classes.all()]
    for previous_bus_classe_tipo in [classe.tipo for classe in previous_bus.classes.all()]:
        current_classe_value = TIPOS_ASSENTO_PESO[previous_bus_classe_tipo]
        for new_bus_classe_tipo in new_bus_classes_tipos:
            if TIPOS_ASSENTO_PESO[new_bus_classe_tipo] >= current_classe_value:
                break
        else:
            return True
    return False


def get_hour_conflict(bus, grupo_id):
    ids_grupos_deste_onibus = [grupo_id]
    try:
        verificar_conflito_onibus(ids_grupos_deste_onibus, bus)
        return None
    except ValidationError as erro:
        return erro.message


def is_emergency(created_at, datetime_ida):
    return (created_at - datetime_ida) < timedelta(days=1)


def is_older_bus(previous_bus, next_bus):
    return next_bus and next_bus.ano and previous_bus and previous_bus.ano and next_bus.ano < previous_bus.ano


def is_out_of_time(created_at, datetime_ida):
    return (created_at - datetime_ida) >= timedelta(hours=1)


@regra_alteracao_onibus(mudanca_layout=False, staff=False)
def verifica_crlv_ou_seguro_vencido(onibus, grupo, capacity_manager):
    msg_erro = ""
    resultado = MudancaOnibus.APROVADA

    if not onibus.available:
        msg_erro = "Não é possivel escalar um onibus inativo."
        resultado = MudancaOnibus.DOCUMENTACAO_NAO_APROVADA
        return resultado, msg_erro

    dut_seguro = []

    for infoonibus in onibus.infoonibus_set.all():
        if infoonibus.tipo == "dut" or infoonibus.tipo == "seguro":
            dut_seguro.append(infoonibus)

    if not dut_seguro:
        msg_erro = "Atualize o documento na página de ônibus."
        resultado = MudancaOnibus.DOCUMENTACAO_NAO_APROVADA

    for doc in dut_seguro:
        if not doc.data_vencimento:
            msg_erro = "Atualize o documento na página de ônibus."
            resultado = MudancaOnibus.DOCUMENTACAO_NAO_APROVADA
            break
        if doc.data_vencimento.date() <= dateutils.now().date():
            msg_erro = "O carro não pode ser escalado porque está com o CRLV ou seguro vencido. Atualize o documento na página de ônibus."
            resultado = MudancaOnibus.DOCUMENTACAO_NAO_APROVADA
            break

        elif doc.data_vencimento.date() <= grupo.datetime_ida.date():
            msg_erro = "O CRLV ou seguro deste carro estará vencido no dia dessa viagem, é necessário atualiza-lo antes de escalar este ônibus para essa viagem. Atualize o documento na página de ônibus."
            resultado = MudancaOnibus.DOCUMENTACAO_NAO_APROVADA
            break

    return resultado, msg_erro


@regra_alteracao_onibus(mudanca_layout=False, staff=False)
def bus_is_micro_onibus_or_van(onibus: Onibus, group: Grupo, capacity_manager):
    msg_erro = ""
    resultado = MudancaOnibus.APROVADA

    if onibus.tipo in ("van", "micro-onibus"):
        msg_erro = f"o veiculo de tipo: {onibus.tipo} não pôde ser escalado, entrar em contato com seu gestor."
        resultado = MudancaOnibus.MICRO_ONIBUS_OR_VAN

    return resultado, msg_erro
