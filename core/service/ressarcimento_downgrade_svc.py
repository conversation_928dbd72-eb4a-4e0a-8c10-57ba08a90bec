from collections import defaultdict, namedtuple
from decimal import Decimal as D
from typing import Iterable

from django.db import transaction
from django.db.models import prefetch_related_objects
from sentry_sdk import capture_exception

from accounting.service import accounting_svc
from commons import utils
from commons.memoize import memoize_local
from commons.redis import lock
from core import tasks
from core.constants import DOWNGRADE_FATOR_TIPO_ASSENTO, TIPOS_ASSENTO_PESO
from core.models_travel import AlteracaoTravel, HistoricoRemanejamento, Passageiro, RessarcimentoDowngrade, Travel
from core.service.reserva import reserva_extrato_svc

TravelInfo = namedtuple("TravelInfo", ["tipo_assento_original", "tipo_assento_atual", "extrato", "travel_original"])


@lock("create_ressarcimento_downgrade_{pax.id}", except_timeout=True)
def create_ressarcimento_downgrade(value, pax, travel, reason_key=None):
    if value < D("0.01"):
        return
    with transaction.atomic():
        _, created = RessarcimentoDowngrade.objects.get_or_create(
            passenger=pax,
            defaults={
                "value": value,
                "reason_key": reason_key,
            },
        )
        if created:
            accounting_svc.reserva_downgrade(value, pax, travel, reason_key=reason_key)


def bulk_ressarcimento_unico_downgrade(passageiro_ids: list[int]):
    passageiros_a_ressarcir = _prepare_passageiros_ressarcimento_unico_downgrade(passageiro_ids)
    passageiros = [pax[0] for pax in passageiros_a_ressarcir]
    valor_ja_pago_map = _get_valor_ja_pago_map(passageiros)  # pax_id -> valor ja pago
    travel_info_map = _get_travel_info_map(passageiros)  # travel_id -> travel info

    for pax, user in passageiros_a_ressarcir:
        if user.email.endswith("@buser.com.br"):
            continue
        travel_atual = pax.travel
        travel_info = travel_info_map[travel_atual.id]

        downgrade_total_travel = _traz_valor_downgrade_ressarc_unico(
            travel_info.tipo_assento_original, travel_info.tipo_assento_atual, travel_atual, travel_info.extrato
        )

        downgrade_por_pax = downgrade_total_travel / travel_atual.count_seats

        valor_ja_ressarcido = valor_ja_pago_map[pax.id]
        valor_a_ressarcir = max(downgrade_por_pax - valor_ja_ressarcido, D(0))
        if D(valor_a_ressarcir) >= D("0.01"):
            tasks.create_ressarcimento_downgrade.delay(
                valor_a_ressarcir,
                pax,
                user,
                pax.travel,
                reason_key=RessarcimentoDowngrade.ReasonsDowngrade.RESSARCIMENTO_UNICO,
            )


def _get_valor_ja_pago_map(passageiros):
    ressarcimento_qs = RessarcimentoDowngrade.objects.filter(passenger__in=passageiros).values("passenger_id", "value")
    valor_ja_pago_map = defaultdict(D)
    for ressarcimento in ressarcimento_qs:
        valor_ja_pago_map[ressarcimento["passenger_id"]] += ressarcimento["value"]
    return valor_ja_pago_map


def _prepare_passageiros_ressarcimento_unico_downgrade(passageiro_ids: Iterable):
    passageiros_ja_ressarcidos = set(
        RessarcimentoDowngrade.objects.filter(
            passenger_id__in=passageiro_ids,
            reason_key=RessarcimentoDowngrade.ReasonsDowngrade.RESSARCIMENTO_UNICO,
        ).values_list("passenger_id", flat=True)
    )
    passageiros_a_ressarcir_ids = set(passageiro_ids) - passageiros_ja_ressarcidos
    passageiros_qs = (
        Passageiro.objects.filter(id__in=passageiros_a_ressarcir_ids, removed=False)
        .exclude(travel__status="canceled")
        .select_related("travel", "travel__grupo_classe", "buseiro__user")
    )
    return [(pax, pax.buseiro.user) for pax in passageiros_qs]


def _get_travel_info_map(passageiros_a_ressarcir):
    # vai ter um N+1 aqui
    travel_atual_original_map = {}
    historico_remanejamento_map = {}
    for pax in passageiros_a_ressarcir:
        historico, travel_original = _get_first_historico_remanejamento_and_travel_original(pax.travel)
        travel_atual_original_map[pax.travel] = travel_original
        historico_remanejamento_map[pax.travel] = historico

    travels_originais = list(travel_atual_original_map.values())
    # hidrata as travels originais com grupo_classe
    prefetch_related_objects(travels_originais, "grupo_classe")

    # constroi histórico de alterações das travels originais
    alteracoes = AlteracaoTravel.objects.filter(
        travel__in=travels_originais, tipo=AlteracaoTravel.TipoAlteracao.MUDANCA_DE_CLASSE
    ).order_by("created_at")
    travel_alteracoes_map = utils.group_by(alteracoes, key=lambda alt: alt.travel_id)
    travel_extrato_map = accounting_svc.bulk_extrato_travel([t.id for t in travel_atual_original_map.keys()])
    travel_info_map = {}
    for travel_atual, travel_original in travel_atual_original_map.items():
        alteracoes = travel_alteracoes_map.get(travel_original.id, [])
        historico_remanejamento = historico_remanejamento_map.get(travel_atual, None)
        if historico_remanejamento:
            alteracoes.append(historico_remanejamento)
        alteracoes.sort(key=lambda alt: alt.created_at)
        # preferimos sempre pegar o antigo_tipo_assento das tabelas, pois o grupo classe original pode já não ter a info correta do que a pessoa comprou :(
        tipo_assento_original = (
            alteracoes[0].antigo_tipo_assento if alteracoes else travel_original.grupo_classe.tipo_assento
        )
        tipo_assento_atual = travel_atual.grupo_classe.tipo_assento
        travel_info_map[travel_atual.id] = TravelInfo(
            tipo_assento_original=tipo_assento_original,
            tipo_assento_atual=tipo_assento_atual,
            extrato=travel_extrato_map[travel_atual.id],
            travel_original=travel_original,
        )
    return travel_info_map


@memoize_local(timeout=60 * 60)
def _get_first_historico_remanejamento_and_travel_original(
    travel_atual: Travel,
) -> tuple[HistoricoRemanejamento | None, Travel]:
    primeiro_historico = travel_atual.get_primeiro_historico_remanejamento() if travel_atual.movido_de else None
    travel_original = primeiro_historico.travel_antiga if primeiro_historico else travel_atual
    return primeiro_historico, travel_original


def get_downgrade_value(travel, old_tipo_assento, new_tipo_assento):
    diff_percentual = TIPOS_ASSENTO_PESO.get(old_tipo_assento, 0) - TIPOS_ASSENTO_PESO.get(new_tipo_assento, 0)
    if diff_percentual <= 0:
        return D("0")
    value = travel.max_split_value * diff_percentual * travel.count_seats
    return max(D(value), D("5.0"))


def _traz_valor_downgrade_ressarc_unico(
    antigo_tipo_assento, atual_tipo_assento, travel_atual, extrato_travel_pre_ressarc
):
    """
    Verifica o quanto o passageiro realmente teve de custo para se calcular o downgrade.
    A forma antiga usava max_split_value, que não correspondia ao custo real.
    Aqui se calcula o valor da travel menos os descontos que o pax aplicou
    """
    eh_downgrade = False
    try:
        eh_downgrade = (
            DOWNGRADE_FATOR_TIPO_ASSENTO[atual_tipo_assento] < DOWNGRADE_FATOR_TIPO_ASSENTO[antigo_tipo_assento]
        )
    except KeyError as error:
        capture_exception(error=error)
    if not eh_downgrade:
        return D("0")
    #: Se usa o ratio para manter consistência com a forma que a bucketização calcula os valores das novas reservas
    # Mais informações no MR https://gitlab.buser.com.br/buser/buser_django/-/merge_requests/11721
    diff_percentual = (
        D(DOWNGRADE_FATOR_TIPO_ASSENTO[atual_tipo_assento]) / D(DOWNGRADE_FATOR_TIPO_ASSENTO[antigo_tipo_assento])
    ) - 1
    custo_da_travel = reserva_extrato_svc.calcula_downgrade_maximo_reais(
        travel=travel_atual, realizado=extrato_travel_pre_ressarc
    )
    valor = custo_da_travel * abs(diff_percentual)
    return max(D(valor), D("5.0"))
