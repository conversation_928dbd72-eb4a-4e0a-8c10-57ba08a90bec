import itertools
import json
import random
from collections import defaultdict
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from decimal import Decimal
from enum import StrEnum
from functools import partial, wraps
from typing import Any, Callable, cast

import sentry_sdk
from beeline import traced
from django.conf import settings
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import F, Q, Subquery
from django.dispatch.dispatcher import receiver
from pydantic.v1 import ValidationError as PydanticValidationError
from requests.exceptions import ConnectionError
from sentry_sdk.api import capture_exception
from tenacity import retry, retry_if_not_exception_type, stop_after_attempt, wait_random

from accounting.models import AccountingOperation
from accounting.service import accounting_svc, credit_accounting_svc
from adapters.new_incidents_adapter import groups_with_incidents
from adapters.torre_de_controle_adapter.exceptions import (
    TorreNotOkError,
    TorreResponseNotFound,
    TorreTimeoutError,
)
from adapters.torre_de_controle_adapter.forms import (
    BusTelemetryForm,
    TelemetryInfoForPax,
)
from commons import dateutils, guard, signer, utils
from commons.batch_task import batch_task
from commons.bunch import Bunch
from commons.dateutils import now, to_default_tz_required, to_tz_required
from commons.django_celery_utils import my_shared_task
from commons.django_model_utils import first
from commons.django_utils import error_str
from commons.grouputils import hashid_grupos, unhashid_grupos
from commons.logging import buserlogger
from commons.redis import LockError, lock
from commons.remember import notagainfor
from commons.utils import hashint, only_numbers, pluralize, unhashint
from core import signals, tasks
from core.constants import DOWNGRADE_FATOR_TIPO_ASSENTO, REPRICE_VALID_CLIENTS
from core.dto.reserva.reserva_dto import ReservaInputDTO, SimulaReservaInputDTO
from core.enums import CategoriaEspecial
from core.forms.core_forms import SolicitacaoRessarcimentoForm
from core.forms.notifications_forms import SlackDataForm
from core.forms.remanejamento_forms import RemanejamentoForm
from core.forms.reserva_forms import RemarcarGratis, TaxaCancelamentoDic
from core.models_credito import CreditAccountingOperation
from core.models_grupo import ClosedReasons, Grupo, TrechoClasse
from core.models_rota import HistoricoAlteracaoEmbarque
from core.models_travel import (
    AlteracaoTravel,
    Cupom,
    Estorno,
    HistoricoRemanejamento,
    HistoricoRemarcacao,
    ItemAdicional,
    LinkPagamento,
    Pagamento,
    Passageiro,
    PromocaoParceria,
    Reserva,
    Ressarcimento,
    SolicitacaoRessarcimento,
    SolicitacaoRessarcimentoComprovante,
    Travel,
    TravelConexao,
    Voucher,
)
from core.serializers import serializer_travel
from core.serializers.serializer_link_pagamento import LinkPagamentoSanitizedSerializer
from core.service import (
    amplitude_svc,
    auth_svc,
    buseiro_svc,
    globalsettings_svc,
    log_svc,
    multitrecho_svc,
    preco_svc,
    price_ab_svc,
    profile_svc,
    rodoviaria_svc,
    taxa_cancelamento_svc,
    timezone_svc,
    torre_de_controle_svc,
    user_svc,
)
from core.service.itens_adicionais_svc import (
    cancela_itens_adicionais_por_pax,
    cancela_itens_adicionais_travel,
    concluir_itens_adicionais,
    salva_itens_adicionais,
)
from core.service.notifications import staff_notification_svc, user_notification_svc
from core.service.notifications.notification_svc import NotificationBuilder, slack_post
from core.service.pagamento import (
    divida_svc,
    estorno_svc,
    pagamento_status_svc,
    pagamento_svc,
)
from core.service.pagamento.exceptions import (
    MercadoPagoApiError,
    PaymentError,
    PaymentException,
    ProviderError,
    TransactionError,
)
from core.service.pagamento.order_info_factory_svc import order_info_factory
from core.service.remanejamento import remanejamento_svc
from core.service.reserva import (
    reserva_eventos_svc,
    reserva_extrato_visualizacao_svc,
    reserva_validations_svc,
    rodoviaria_reserva_svc,
)
from core.service.reserva.exceptions import (
    ReservaAsyncTimeoutError,
    ReservaTimeoutError,
)
from core.service.reserva.promocao import promocao_svc
from core.service.reserva.promocao.promo import TipoCancelamento
from core.service.reserva.promocao.promo_trecho_baixa_ocupacao import apply_trecho_promo_coupon
from core.service.reserva.reserva_eventos_svc import (
    CancelByAdmin,
    CancelByEditRota,
    CancelByMarketplaceError,
    CancelByMovePax,
    CancelByNoPayment,
    CancelByPromotor,
    CancelByRemarcacao,
    CancelByRemarcacaoAtraso,
    CancelByRemarcacaoGratuita,
    CancelByRisco,
    CancelByRodoviariaError,
    CancelByUpgrade,
    CancelByUserRequest,
    CancelStrategy,
    EventoCancelaReserva,
    EventoRemarcaReserva,
    EventoRemoverPassageiro,
    EventoReserva,
)
from core.service.reserva.travel import travel_svc
from core.service.selecao_assento import NoSeatLayoutAvailable
from core.service.selecao_assento import marketplace as marketplace_selecao_assento
from core.service.selecao_assento.models import BlockedSeat
from core.service.tags_svc import get_tag_cpfs_subquery
from core.service.timezone_svc import to_tz_trecho
from core.signals import (
    cancel_pagamento_travel,
    cancel_reserva_signal,
    passageiro_removido_signal,
    promocao_cancelada_signal,
    travel_canceled_signal,
)
from integrations.rodoviaria_client.exceptions import (
    RodoviariaException,
    RodoviariaViagemBloqueada,
)
from promo.service import fidelidade_svc
from search_result.service.ofertas_esgotadas_mc_svc import set_oferta_esgotada

DELAY_TOLERANCE_TIME = timedelta(minutes=5)

MAX_ALLOWED_RESSARCIMENTO_VALUE = 50

SOURCE_RESSARCIMENTOS = [
    AccountingOperation.Source.PASSAGEIRO_RESSARCIDO,
    AccountingOperation.Source.RESERVA_RESSARCIDA,
    AccountingOperation.Source.PASSAGEIRO_COMPENSADO,
    AccountingOperation.Source.ADMIN_REAIS,
    AccountingOperation.Source.PASSAGEIRO_COMPENSADO_CANCELADO,
]


def simula_reserva(d, user, validate_non_grato=True) -> dict:
    is_authenticated = user.is_authenticated
    if not is_authenticated:
        email = d["email"]
        try:
            user = User.objects.select_related("profile").get(email=email)
        except User.DoesNotExist:
            pass
    _validar_e_processar_trecho_classe(d)
    reserva_dto = SimulaReservaInputDTO(**d)
    d.get("categoria_especial", CategoriaEspecial.NORMAL)
    evento = reserva_eventos_svc.new_event_cria_reserva(
        d,
        user,
        reserva_dto,
        is_authenticated=is_authenticated,
        validate_non_grato=validate_non_grato,
    )
    extrato = evento.extrato
    extrato_visualizacao = reserva_extrato_visualizacao_svc.get_extrato_nova_reserva_visualizacao(extrato)
    res = {
        "extrato_reserva": extrato_visualizacao,
        # TODO: REMOVER PARCELAMENTO_OPTIONS.
        "parcelamento_options": extrato["credit_card"]["parcelamento_options"],
        "pagamento_total": extrato["pagamento_total"],
        "credit_card": extrato["credit_card"],
        "boleto": extrato["boleto"],
        "nupay": extrato["nupay"],
        "pix": extrato["pix"],
        "dinheiro": extrato["dinheiro"],
    }
    if "no_promo_alert" in extrato:
        res["no_promo_alert"] = extrato["no_promo_alert"]
    if "needs_travel_cpf" in extrato:
        res["needs_travel_cpf"] = extrato["needs_travel_cpf"]
    if "price_cookie" in extrato:
        res["price_cookie"] = extrato["price_cookie"]
    if "alerta_buckets" in extrato:
        res["alerta_buckets"] = extrato["alerta_buckets"]
    return res


def simula_remarcacao(d, user, travel: Travel) -> dict:
    atraso_torre = _has_atraso_maior_que_meia_hora(travel.trecho_classe)
    evento = reserva_eventos_svc.new_event_remarcacao_reserva(d, user, atraso_torre, dryrun=True)
    extrato = evento.data["extrato"]
    if not extrato:
        raise ValidationError("Erro ao simular remarcação: extrato ausente.")
    ext_visualizacao = reserva_extrato_visualizacao_svc.get_extrato_nova_reserva_visualizacao(extrato)
    ext_cancelamento_visualizacao = reserva_extrato_visualizacao_svc.get_extrato_cancelamento_travel(
        evento.travel_remarcada_devolucao,
        evento.travel_remarcada_devolucao_creditos,
    )
    res = {
        "extrato_cancelamento": ext_cancelamento_visualizacao,
        "extrato": ext_visualizacao,
        # TODO: REMOVER PARCELAMENTO_OPTIONS.
        "parcelamento_options": extrato["credit_card"]["parcelamento_options"],
        "pagamento_total": extrato["pagamento_total"],
        "credit_card": extrato["credit_card"],
        "boleto": extrato["boleto"],
        "pix": extrato["pix"],
        "nupay": extrato["nupay"],
        "dinheiro": extrato["dinheiro"],
    }
    if "no_promo_alert" in extrato:
        res["no_promo_alert"] = extrato["no_promo_alert"]
    if "price_cookie" in extrato:
        res["price_cookie"] = extrato["price_cookie"]
    return res


@my_shared_task(queue="reserva")
def _efetuar_reserva_assincrona(reserva_id: int, google_tracking_id=None, protocolo_atendimento=None):
    reserva = Reserva.objects.get(id=reserva_id)
    try:
        _efetuar_reserva_wrapper(reserva, google_tracking_id, protocolo_atendimento=protocolo_atendimento)
    except PydanticValidationError:
        # Mantendo um comportamento de antes da criação do `_efetuar_reserva_wrapper`.
        # O `PydanticValidationError` era capturado só no async, acho que porque
        # no sync já tem o middleware para tratar esse erro.
        pass


def _create_reserva(data, user, *, is_async: bool) -> Reserva:
    is_authenticated = user.is_authenticated
    bypass_promo = data.pop("bypass_low_occupation", False)
    if not (bypass_promo or data.get("client") in REPRICE_VALID_CLIENTS):
        if not apply_trecho_promo_coupon(data.get("trechos_ida", [])):
            apply_trecho_promo_coupon(data.get("trechos_volta", []))
    if not is_authenticated:
        user = auth_svc.get_or_create_anonymous_user(
            data["email"],
            name=data.get("name"),
            phone=data.get("phone"),
        )

        if profile_svc.check_user_is_blocked(user):
            auth_svc.login_error(user)
            raise ValidationError("Não foi possível realizar a reserva, sua conta está bloqueada")

    return Reserva.objects.create(
        status=Reserva.Status.CRIADA,
        is_async=is_async,
        is_authenticated=is_authenticated,
        input_payload=data,
        user=user,
    )


def efetuar_reserva_assincrona(
    data: dict,
    user: User,
    google_tracking_id: str | None = None,
) -> Reserva:
    protocolo_atendimento = data.get("protocolo")
    reserva = _create_reserva(data, user, is_async=True)
    _efetuar_reserva_assincrona.delay(reserva.id, google_tracking_id, protocolo_atendimento=protocolo_atendimento)
    return reserva


def clean_error_data(data: dict) -> dict:
    data.pop("travel_ida", None)
    data.pop("travel_volta", None)
    data.pop("dpayment", None)
    data.pop("dpassengers", None)
    return data


def efetuar_reserva_sincrona(
    data: dict,
    user: User,
    google_tracking_id: str = "",
    validate_non_grato: bool = True,
    pax_can_edit: bool = True,
    seller: str | None = Travel.Seller.BUSER,
) -> tuple[list[Travel], Reserva]:
    protocolo_atendimento = data.get("protocolo")
    reserva = _create_reserva(data, user, is_async=False)
    travels = _efetuar_reserva_wrapper(
        reserva,
        google_tracking_id,
        validate_non_grato,
        pax_can_edit,
        seller=seller,
        protocolo_atendimento=protocolo_atendimento,
    )
    return travels, reserva


def _lista_ultimos_status_reserva(count) -> list[Reserva]:
    qs = Reserva.objects.filter(is_async=True).order_by("-created_at")
    return list(qs.values_list("status", flat=True)[:count])


@my_shared_task(queue="notification")
def _notifica_reserva_async_desligada():
    title = "Reserva async desligada"
    content = (
        "A reserva async foi desligada, pois as ultimas reservas nao estao sendo processadas. "
        'Conferir se esta tudo certo com os workers do celery que consomem a fila "reserva".'
    )
    staff_notification_svc.notifica_dev_se_deu_pau(title, content)


_5min = 60 * 5


@notagainfor(_5min, "desliga_reserva_async")
def _desliga_reserva_async():
    globalsettings_svc.set("porcentagem_reserva_async", 0)
    _notifica_reserva_async_desligada.delay()


def decide_is_reserva_async() -> bool:
    flag_value = cast(float, globalsettings_svc.get("porcentagem_reserva_async", 0))
    # Comparação  com 1 é redundante, mas evita o random lento desnecessário.
    is_async = flag_value == 1 or random.random() < flag_value

    # já é sync, não precisa analisar as últimas reservas
    if not is_async:
        return False

    max_fila_reserva = cast(int, globalsettings_svc.get("max_fila_reserva_async"))
    status_list = _lista_ultimos_status_reserva(max_fila_reserva)

    # sem quantidade de reservas suficiente para analisar
    if len(status_list) < max_fila_reserva:
        return True

    # checa se alguma das reservas já começou a ser processada
    if any(status != Reserva.Status.CRIADA for status in status_list):
        return True

    # se nenhuma começou a ser processada e já acumulou até o máximo esperado,
    # então provavelmente os workers que consomem a fila da reserva estão off
    _desliga_reserva_async()
    return False


def check_reservation_status(reserva: Reserva) -> Reserva:
    if reserva.timed_out():
        try:
            _efetuar_timeout(reserva)
        except ReservaAsyncTimeoutError as e:
            sentry_sdk.capture_exception(e)
        except LockError:
            # reserva ainda em processamento
            buserlogger.error("reserva.error.lock", extra={"reserva_id": reserva.id})
    return reserva


def lock_reserva(f):
    @wraps(f)
    def wrapper(reserva, *args, **kwargs):
        lock_key = generate_key_for_reserva(reserva)
        lock_decorator = lock(key=f"reserva/{lock_key}", max_wait_time=0)
        decorated_function = lock_decorator(f)

        return decorated_function(reserva, *args, **kwargs)

    return wrapper


@lock_reserva
def _efetuar_timeout(reserva):
    reserva.refresh_from_db()  # pode ter mudado, antes de obter o lock, revalidar para evitar condição de corrida
    _efetuar_timeout_sem_lock(reserva)


def _efetuar_timeout_sem_lock(reserva):
    if reserva.timed_out():
        reserva.timeout()
        buserlogger.error("reserva.error.timeout", extra={"reserva_id": reserva.id})
        if reserva.is_async:
            raise ReservaAsyncTimeoutError(reserva)
        else:
            raise ReservaTimeoutError(reserva)


@dataclass
class BlockedSeatReserva:
    class Tipo(StrEnum):
        MANUAL = "manual"
        AUTOMATICO = "automatico"

    blocked_seat: BlockedSeat
    trecho_classe_id: int
    pax_key: str | int | None  # Legado do frontend, ver função pax_possible_keys
    tipo: Tipo

    @staticmethod
    def pax_possible_keys(pax: dict) -> set[str | int]:
        # TODO - Tem que ter uma idempotency_key no passenger
        return {
            k for k in {pax.get("id"), str(pax.get("id", "")), pax.get("cpf"), pax.get("rg_number")} if k is not None
        }

    @property
    def numero_poltrona(self):
        return self.blocked_seat.poltrona.numero

    @property
    def extra(self) -> dict:
        return {"bloqueio_poltrona": self.blocked_seat.model_dump()}


def _get_manual_blocked_seats(evento: EventoReserva) -> list[BlockedSeatReserva]:
    # Retorna as poltronas bloqueadas manualmente pelo pax no front
    blocked_seats = []

    for travel in evento.travels:
        trecho_classe = travel.get("trechoclasse")
        modelo_venda = trecho_classe.grupo.modelo_venda if trecho_classe and trecho_classe.grupo else None
        for pax_key, assento_selecionado in travel.get("assentos_selecionados", {}).items():
            try:
                blocked_seat_model = _get_blocked_seat_model(
                    modelo_venda, travel["trechoclasse_id"], assento_selecionado
                )
                if blocked_seat_model:
                    blocked_seats.append(
                        BlockedSeatReserva(
                            blocked_seat=blocked_seat_model,
                            tipo=BlockedSeatReserva.Tipo.MANUAL,
                            pax_key=pax_key,
                            trecho_classe_id=travel["trechoclasse_id"],
                        )
                    )
            except NoSeatLayoutAvailable:
                continue

    return blocked_seats


def _get_blocked_seat_model(
    modelo_venda: Grupo.ModeloVenda, travel_classe_id: int, assento_selecionado: int
) -> BlockedSeat | None:
    bloqueio_func = {
        Grupo.ModeloVenda.BUSER: partial(BlockedSeat.from_numero_poltrona, assento_selecionado),
        Grupo.ModeloVenda.MARKETPLACE: partial(
            marketplace_selecao_assento.get_bloqueio_poltrona, travel_classe_id, assento_selecionado
        ),
    }
    if modelo_venda not in bloqueio_func:
        return None
    return bloqueio_func[modelo_venda]()


def bloqueia_poltronas(evento: EventoReserva | EventoRemarcaReserva) -> list[BlockedSeatReserva]:
    # Caso tenha alguma poltrona faltante para algum trecho classe, faz o bloqueio na rodoviária

    current_seats = _get_manual_blocked_seats(evento)

    count_current_seats = defaultdict(int)
    for seat in current_seats:
        count_current_seats[seat.trecho_classe_id] += 1

    for trecho in evento.trechos:
        quantidade = len(evento.passengers) - count_current_seats.get(trecho.id, 0)
        if quantidade <= 0:
            continue

        if trecho.grupo.modelo_venda == Grupo.ModeloVenda.BUSER:
            continue

        try:
            controller = marketplace_selecao_assento.MarketplaceSeatsController(trecho)
        except NoSeatLayoutAvailable:
            continue

        new_seats = controller.escolhe_e_bloqueia_poltronas(
            quantidade_poltronas=quantidade,
            categoria_especial=evento.categoria_especial,
            timeout=20,
        )

        current_seats += [
            BlockedSeatReserva(
                blocked_seat=seat,
                pax_key=None,
                trecho_classe_id=trecho.id,
                tipo=BlockedSeatReserva.Tipo.AUTOMATICO,
            )
            for seat in new_seats
        ]

    return current_seats


def _get_poltrona(bloqueios: list[BlockedSeatReserva], pax: dict, trecho_classe_id: int) -> BlockedSeatReserva | None:
    selected_index = None

    for index, bloqueio in enumerate(bloqueios):
        if bloqueio.trecho_classe_id != trecho_classe_id:
            continue

        if bloqueio.pax_key in BlockedSeatReserva.pax_possible_keys(pax):
            selected_index = index
            break

        if not bloqueio.pax_key:
            # Se no final não achar ninguém que dá o match com a chave, pega qualquer um sem chave
            selected_index = index

    if selected_index is not None:
        return bloqueios.pop(selected_index)


@lock_reserva
def _efetuar_reserva(  # noqa
    reserva,
    google_tracking_id=None,
    validate_non_grato=True,
    pax_can_edit=True,
    seller: str | None = Travel.Seller.BUSER,
    protocolo_atendimento=None,
) -> list[Travel] | None:
    if reserva.status in {Reserva.Status.TIMEOUT, Reserva.Status.ERRO_NO_PROCESSAMENTO}:
        return
    data = reserva.input_payload
    user = reserva.user
    _validar_e_processar_trecho_classe(data)
    reserva_dto = ReservaInputDTO(**data)
    contact_phone = data.get("phone")
    user_svc.generate_user_acceptance(user, reserva_dto.acceptance)
    _efetuar_timeout_sem_lock(reserva)  # já esta dentro do lock, não precisa usar novamente
    reserva.init_processamento()
    evento = reserva_eventos_svc.new_event_cria_reserva(
        data,
        user,
        reserva_dto,
        is_authenticated=reserva.is_authenticated,
        validate_non_grato=validate_non_grato,
    )
    buseiro_svc.upsert_buseiros(user, evento.passengers)

    evento.poltronas_bloqueadas = bloqueia_poltronas(evento)

    # Não tem pagamento quando o valor é baixo ou feito em créditos/cupom/voucher.
    pagamento = None

    if evento.user_should_pay:
        reserva.enviado_para_pagamento()
        try:
            pagamento = _executa_pagamento(evento, reserva)
        except Exception:
            for poltrona in evento.poltronas_bloqueadas:
                marketplace_selecao_assento.desbloquear_poltrona.delay(
                    trecho_classe=poltrona.trecho_classe_id, poltrona=poltrona.numero_poltrona
                )
            raise

    reserva.pagamento_processado()

    try:
        travels, evt_contabil, evt_credito = _save_data_after_payment(
            user,
            reserva,
            pagamento,
            evento,
            google_tracking_id,
            pax_can_edit,
            seller=seller,
            protocolo_atendimento=protocolo_atendimento,
        )
    except Exception as e:
        rodoviaria_svc.handle_rodoviaria_after_error(e)
        _handle_payment_after_error(pagamento)
        if isinstance(e, RodoviariaException):
            capture_exception(e)
        raise e
    else:
        reserva.concluida(evt_contabil, evt_credito)
        signals.purchase_signal.send(None, travels=travels, reserva_id=reserva.id, data=evento.data)
        user_notification_svc.reserva_realizada.delay(
            reserva,
            pagamento,
            evento="reserva_svc._efetuar_reserva - Reserva concluída",
        )
        if not evento.has_volta():
            user_notification_svc.agendar_compre_sua_volta_10min.delay(
                reserva,
                user,
                evento_comunicacao="reserva_svc._efetuar_reserva - Reserva concluída",
            )

        pagamento_confirmado = (
            pagamento and pagamento.status == Pagamento.Status.PAID and pagamento.method == Pagamento.Method.CREDIT_CARD
        )
        if not pagamento or pagamento_confirmado:
            pagamento_confirmado_callback(
                pagamento=pagamento,
                value=pagamento.net_value if pagamento else Decimal(0),
                travels=travels,
                is_pagamento_reserva=True,
            )

        for travel in travels:
            travel_svc.atribuir_poltronas_automaticamente(travel)
            # Quando um novo user tenta cadastrar um telefone já existente cadastramos cell_phone vazio
            if user.profile.cell_phone == "":
                travel.contact_phone = contact_phone
                travel.save(
                    update_fields=[
                        "contact_phone",
                        "updated_on",
                    ]
                )
        return travels


def _executa_pagamento(evento: EventoReserva | EventoRemarcaReserva, reserva: Reserva) -> Pagamento:
    # TODO: extrair essa regra do 3ds daqui
    enable_3ds = True
    if evento.client == "vendas":
        enable_3ds = False

    return pagamento_svc.gerar_pagamento(
        evento.user,
        order_info_factory(
            evento.user,
            evento.payment or {},
            evento.passengers,
            evento.trecho_classe_ids,
            enable_3ds,
        ),
        reserva=reserva,
    )


def _efetuar_reserva_wrapper(
    reserva: Reserva,
    google_tracking_id=None,
    validate_non_grato=True,
    pax_can_edit=True,
    seller: str | None = Travel.Seller.BUSER,
    protocolo_atendimento=None,
) -> list[Travel]:
    travels = None
    try:
        travels = _efetuar_reserva(
            reserva,
            google_tracking_id,
            validate_non_grato,
            pax_can_edit,
            seller=seller,
            protocolo_atendimento=protocolo_atendimento,
        )
    except ValidationError as e:
        message, help, extra = _purchase_validation_error(e, reserva.input_payload)
        reserva.error(message, help, extra, error_code=getattr(e, "code") or "validation_error")
    except PydanticValidationError as e:
        error_message = str(e)
        error_data = e.errors()
        log_svc.log_erro_na_reserva(error_message, reserva.input_payload, error_data)
        reserva.error(
            "Houve um problema",
            ["Revise os dados de pagamento ou entre em contato com o suporte."],
            error_code="pydantic_validation",
        )
        raise
    except TransactionError as e:
        reserva.error(e.code, error_code=e.code)
        buserlogger.error("reserva.error.payment", extra=dict(err_msg=e.code))
    except ProviderError as e:
        sentry_sdk.capture_exception(e)
        reserva.error(e.code, error_code=e.code)
        buserlogger.error(
            f"reserva.error.provider.{e.provider}",
            extra=dict(err_msg=e.code, provider=e.provider, payment_method=e.payment_method),
        )
    except PaymentError as e:
        message, help, extra = _purchase_payment_error(e, reserva.input_payload)
        reserva.error(message, help, extra)
        buserlogger.error("reserva.error.payment", extra=dict(err_msg=message, **extra))
    except RodoviariaException as e:
        message, help, extra = _purchase_rodoviaria_error(e, reserva.input_payload)
        reserva.error(message, help, extra)
        _log_reserva_error_com_informacoes_do_trecho("reserva.error.rodoviaria", e, reserva.input_payload)
    except MercadoPagoApiError as e:
        sentry_sdk.capture_exception()
        message, help, extra = _purchase_mercadopago_api_error(e, reserva.input_payload)
        reserva.error(message, help, extra)
        buserlogger.error("reserva.error.mercadopago", extra={"err_msg": message})
    except LockError as e:
        message, help, extra = _purchase_lock_error(e, reserva.input_payload)
        reserva.error(message, help, extra)
        buserlogger.error("reserva.error.lock", extra={"err_msg": message})
    except Exception as e:
        message, help, extra = _purchase_desconhecido_error(e, reserva.input_payload)
        reserva.error(message, help, extra)
        buserlogger.error("reserva.error.generic", extra={"err_msg": message})
        raise e
    return travels or []


def _save_data_after_payment(
    user,
    reserva,
    pagamento,
    evento: EventoReserva,
    google_tracking_id=None,
    pax_can_edit=True,
    seller: str | None = Travel.Seller.BUSER,
    protocolo_atendimento=None,
) -> tuple[list[Travel], list[dict[Any, Any]], list[dict[Any, Any]]]:
    with transaction.atomic():
        # Lock no grupo para evitar duas reservas simultâneas.
        # Precisa converter em lista para executar o queryset.
        grupos = list(Grupo.objects.select_for_update().filter(trechoclasse__pk__in=evento.trecho_classe_ids))

        # Desculpa, isso é absurdamente redundante.
        # Precisa forçar essa atualização para a validação de overbooking
        # funcionar direito.
        for obj in evento.trechos_ida + evento.trechos_volta:
            obj.refresh_from_db()

        # Valida de novo, dentro da transacao as coisas podem ter mudado
        # Nao valida pagamento pq o pagamento ja foi gerado
        evento.validate(validate_payment=False)
        travels = _cria_travels(
            evento,
            pagamento,
            google_tracking_id,
            reserva,
            pax_can_edit,
            seller=seller,
            categoria_especial=evento.categoria_especial,
            protocolo_atendimento=protocolo_atendimento,
        )

        any_marketplace = any(travel.grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE for travel in travels)
        pagamento_confirmado = bool(pagamento and pagamento.status == Pagamento.Status.PAID)
        if any_marketplace and pagamento_confirmado:
            emitir_passagens_async.delay([t.id for t in travels])

        _atualiza_contagem_cupom(travels)
        _save_promo_parceria(travels, evento.data.get("cpf_da_promo"))
        evt_contabil = _cria_evento_contabil(evento, travels)
        evt_credito = credit_accounting_svc.cria_eventos_creditos_reserva(evento.extrato, travels)
        evt_contrapartida_contabil = credit_accounting_svc.cria_evento_contrapartida(evt_credito)
        evt_contabil += evt_contrapartida_contabil

        reserva.set_evento_contabil(
            evt_contabil
        )  # gambs porque daqui pra baixo às vezes dá erro, mas as travels já estão criadas

        evt_credito_dict = [evt.dict() for evt in evt_credito]
        reserva.set_evento_credito(evt_credito_dict)

        status_item = (
            ItemAdicional.StatusItemAdicional.CONCLUIDO
            if pagamento_confirmado
            else ItemAdicional.StatusItemAdicional.PENDENTE
        )
        salva_itens_adicionais(evt_contabil, status=status_item, pagamento=pagamento)

    try:
        _cria_logs_reserva(travels, evento)
        _cria_logs_desconto_categoria_especial_marketplace(travels, evento.categoria_especial)
        _atualiza_contagem_paxes(grupos)

        price_token = evento.extrato["price_cookie"]["value"]

        _registra_conversao_mab(travels, evento.extrato, user, price_token)

        if globalsettings_svc.get("check_pax_cpf"):
            buseiro_svc.batch_check_cpfs(travels)
    except Exception:
        # temporário para entender o que tá quebrando aqui
        capture_exception()
        raise

    return travels, evt_contabil, evt_credito_dict


def _cria_evento_contabil(evento: EventoReserva | EventoRemarcaReserva, travels: list[Travel]) -> list[dict[Any, Any]]:
    eventos_contabil = accounting_svc.cria_eventos_reserva(evento.extrato, travels)
    return [evt.dict() for evt in eventos_contabil]


@lock("reserva/{user.id}", max_wait_time=0)
def remarcar(d, user, travel: Travel):
    # código quase copiado do reservar
    # TODO: refatoração
    reserva = Reserva.objects.create(status=Reserva.Status.CRIADA, is_async=False, user=user, input_payload=d)
    atraso_torre = _has_atraso_maior_que_meia_hora(travel.trecho_classe)
    evento = reserva_eventos_svc.new_event_remarcacao_reserva(d, user, atraso_torre)
    trechos_classe_ids = [g.id for g in evento.trechos_ida]

    is_upgrade = d.get("is_upgrade", False)

    evento.poltronas_bloqueadas = bloqueia_poltronas(evento)
    with transaction.atomic():
        # Lock no grupo para evitar duas reservas simultâneas.
        # Precisa converter em lista para executar o queryset.
        grupos = list(Grupo.objects.select_for_update().filter(trechoclasse__pk__in=trechos_classe_ids))

        reserva.init_processamento()
        reserva_validations_svc.validate_payment(evento)  # De novo. Dentro da transacao as coisas podem ter mudado

        # O cancel já recalcula vagas do grupo da travel cancelada.
        travel_cancelada = cancel_travel_remarcacao(evento.travel.id, atraso_torre=atraso_torre, is_upgrade=is_upgrade)
        reserva_validations_svc.validate_before_join_groups(evento)
        _copia_source_da_viagem_cancelada_se_nao_existir(evento.data, travel_cancelada)
        _salva_promo_parceria_da_viagem_cancelada(evento.data, travel_cancelada)
        travels = _cria_travels(evento, reserva=reserva)

        # Não tem pagamento quando o valor é baixo ou feito em créditos/cupom/voucher.
        pagamento = None
        try:
            if evento.user_should_pay:
                reserva.enviado_para_pagamento()
                pagamento = _executa_pagamento(evento, reserva)

                for travel in travels:
                    travel.pagamento = pagamento
                Travel.objects.bulk_update(travels, ["pagamento"])
                reserva.pagamento_processado()

            d = evento.data

            emitir_passagens_rodoviaria_retry_wrapper(evento.extrato["user"], reserva, travels)

            evt_contabil = _cria_evento_contabil(evento, travels)
            evt_credito = credit_accounting_svc.cria_eventos_creditos_reserva(evento.extrato, travels)
            evt_credito_dict = [evt.dict() for evt in evt_credito]
            evt_contrapartida_contabil = credit_accounting_svc.cria_evento_contrapartida(evt_credito)
            evt_contabil += evt_contrapartida_contabil

        except Exception as ex:
            _handle_payment_after_error(pagamento)
            if isinstance(ex, RodoviariaException):
                capture_exception(ex)
            buserlogger.info(f"Erro {error_str(ex)} em remarcar com payload {d}. Deletando travels.")
            for travel in travels:
                travel.delete()
            raise ex

        reserva.concluida(evt_contabil, evt_credito_dict)

        tem_itens_adicionais = bool(evento.itens_adicionais_reserva_remarcada)
        liberar_bagagem = bool(d.get("quantidade_bagagem_adicional"))
        liberar_seguro = bool(d.get("seguro_extra"))
        if tem_itens_adicionais and (liberar_bagagem or liberar_seguro):
            evt_contabil_ajustado = _atualiza_pax_info_com_itens_adicionais(
                evt_contabil, evento.itens_adicionais_reserva_remarcada, liberar_bagagem, liberar_seguro
            )
            salva_itens_adicionais(
                evt_contabil_ajustado,
                status=ItemAdicional.StatusItemAdicional.CONCLUIDO,
                pagamento=pagamento,
            )
        _atualiza_contagem_cupom(travels)
        _save_promo_parceria(travels, evento.data.get("cpf_da_promo"))
        _cria_logs_remarcacao(travels, evento)

        for travel in travels:
            travel_svc.atribuir_poltronas_automaticamente(travel)
            HistoricoRemarcacao.objects.create(old_travel=evento.travel, new_travel=travel)

    _cria_logs_reserva(travels, evento)
    _atualiza_contagem_paxes(grupos)

    signals.remarcacao_signal.send(None, travels=travels, reserva_id=reserva.id)

    travels_novas_ids = [t.id for t in travels]
    pagamento_id = pagamento.id if pagamento else None
    user_notification_svc.viagem_remarcada_pelo_usuario_task.delay(
        travel_cancelada.id,
        travels_novas_ids,
        pagamento_id,
        evento_comunicacao="reserva_svc.remarcar",
    )
    return travels


def _atualiza_pax_info_com_itens_adicionais(
    evento_contabil, itens_adicionais: list[ItemAdicional], liberar_bagagem: bool, liberar_seguro: bool
):
    if all([not liberar_bagagem, not liberar_seguro]):
        return evento_contabil

    quantidade_bagagem_upsell = 0
    is_seguro_upsell = False
    for evento in evento_contabil:
        new_pax_info = evento.get("pax_info")

        if liberar_bagagem:
            quantidade_bagagem_upsell = len(
                [item for item in itens_adicionais if item.tipo == ItemAdicional.TipoItemAdicional.BAGAGEM_ADICIONAL]
            )

        if liberar_seguro:
            is_seguro_upsell = bool(
                len([item for item in itens_adicionais if item.tipo == ItemAdicional.TipoItemAdicional.SEGURO_EXTRA])
            )

        for pax_info in new_pax_info:
            if pax_info.get("valor_seguro_extra") and is_seguro_upsell:
                pax_info["seguro_upsell"] = True
            if pax_info.get("quantidade_bagagem_adicional") and quantidade_bagagem_upsell:
                pax_info["bagagem_upsell"] = quantidade_bagagem_upsell

        evento["pax_info"] = new_pax_info

    return evento_contabil


def _has_atraso_maior_que_meia_hora(trecho_classe: TrechoClasse) -> bool:
    # Quando soubermos pela torre/analista que vai rolar um atraso de mais de 30 minutos,
    # vamos permitir a remarcação numa janela de 40 minutos antes da viagem até a previsão de chegada do ônibus.
    # Não vamos permitir para marketplace e nem para hibrido
    if trecho_classe.grupo.is_marketplace or trecho_classe.grupo.is_hibrido:
        return False
    city_of_origin_tz = trecho_classe.trecho_vendido.origem.cidade.timezone
    datetime_atual = dateutils.to_tz(dateutils.now(), city_of_origin_tz)
    try:
        estimated_arrival_time = get_telemetry_info_from_trecho_classe(trecho_classe)
    except ValidationError:
        return False
    arrival_datetime = estimated_arrival_time["arrival_datetime"]
    if arrival_datetime is None:
        return False
    travel_trechoclasse_datetime = dateutils.to_tz(trecho_classe.datetime_ida, city_of_origin_tz)
    minutos_atrasados = (arrival_datetime - travel_trechoclasse_datetime).total_seconds() / 60
    if minutos_atrasados < 30:
        return False
    if not travel_trechoclasse_datetime or not datetime_atual:
        return False
    minutos_para_datetime_ida = (travel_trechoclasse_datetime - datetime_atual).total_seconds() / 60
    if minutos_para_datetime_ida > 40 or datetime_atual > arrival_datetime:
        return False
    return True


def _copia_source_da_viagem_cancelada_se_nao_existir(evento_data, travel_cancelada):
    evento_data["source"] = evento_data.get("source") or travel_cancelada.source
    evento_data["source_id"] = evento_data.get("source_id") or travel_cancelada.source_id


def _salva_promo_parceria_da_viagem_cancelada(evento_data, travel_cancelada):
    if not travel_cancelada.is_parceria:
        return
    try:
        promo_parceria = PromocaoParceria.objects.get(travel=travel_cancelada)
    except PromocaoParceria.DoesNotExist:
        return
    evento_data["cpf_da_promo"] = promo_parceria.cpf


def _handle_payment_after_error(pagamento):
    if not pagamento:
        return
    # se estiver processando e for cartão/nupay, talvez já tenha dado certo, então tentamos tanto cancelar quanto estornar
    should_try_cancel = pagamento.status in Pagamento.STATUSES_WAIT
    should_try_estornar = pagamento.status in Pagamento.STATUSES_WAIT | {
        Pagamento.Status.PAID
    } and pagamento.method in (Pagamento.Method.CREDIT_CARD, Pagamento.Method.NUPAY)
    try:
        if should_try_cancel:
            _cancelar_transacao(pagamento)
        if should_try_estornar:
            estorno_svc.estornar_transacao(
                pagamento.transaction_id,
                pagamento.provider,
                pagamento.method,
                pagamento.net_value,
                None,
            )
    except Exception as err:
        buserlogger.error(
            "reserva_svc.handle_payment_after_error",
            extra={"pagamento_id": pagamento.id, "error": str(err)},
        )


def link_pagamento_create(reserva_data, user):
    jsondata = json.dumps(reserva_data)
    link_pagamento_validate_reserva_data(reserva_data, user)
    link_pagamento_validate_tempo_espera(jsondata)
    return hashint(LinkPagamento.create(jsondata=jsondata).id)


def link_pagamento_validate_reserva_data(data, user):
    dados_reserva = data.get("reservationDataCleaned")
    # é necessario client="vendas" para calcular corretamente o extrato.
    dados_reserva["client"] = data["client"]
    dados_reserva["host"] = data.get("host")
    infos_pagamento = data.get("paymentInfo")

    user_revenda_id = dados_reserva.get("user_revenda")
    user_revenda = User.objects.get(pk=user_revenda_id)

    if dados_reserva.get("user_revendedor") != user.id:
        raise ValidationError("Usuário adulterado!")
    if dados_reserva["client"] is None:
        raise ValidationError("Client não informado. Verifique!")
    unhashid_grupos(dados_reserva.get("groups"))

    extrato_esperado = simula_reserva(dados_reserva, user_revenda)

    if _check_link_pagamento_adulterado(extrato_esperado, infos_pagamento, dados_reserva.get("groups")):
        raise ValidationError("Pagamento adulterado!")


def link_pagamento_validate_tempo_espera(jsondata):
    now_str = now().isoformat()
    if LinkPagamento.objects.filter(jsondata=jsondata, expiration_date__gt=now_str).exists():
        raise ValidationError("Criando novo link idêntico antes de expirar o antigo!")


def link_pagamento_get(code, reservation_data_cleaned=False, payment_info=False) -> dict:
    try:
        link_pag = LinkPagamento.objects.get(pk=unhashint(code))
        pagamento = link_pag.pagamento
    except (LinkPagamento.DoesNotExist, ValueError, TypeError):
        raise ValidationError("Não existe link de pagamento com esse código")
    data_dict = {}
    if pagamento and pagamento.status == "paid":
        data_dict["status"] = "paid"
        travels = Travel.objects.filter(pagamento_id=pagamento.id)
        data_dict["travels"] = [serializer_travel.serialize_purchase_travels(travel) for travel in travels]
    elif to_default_tz_required(now()) > link_pag.expiration_date:
        raise ValidationError("Esse link expirou! Peça para seu revendedor gerar um novo para continuar a reserva.")
    if not link_pag.jsondata:
        raise ValidationError("Sem dados de pagamento no jsondata")
    jsondata = json.loads(link_pag.jsondata, parse_float=Decimal)
    data_dict["client"] = jsondata.get("client")
    if payment_info:
        data_dict["paymentInfo"] = jsondata["paymentInfo"]
        data_dict["passageiros"] = jsondata["reservationDataCleaned"]["passengers"]
        data_dict["trechos"] = jsondata["reservationDataCleaned"]["groups"]
    if reservation_data_cleaned:
        data_dict.update(jsondata["reservationDataCleaned"])
    return data_dict


def get_dados_link_pagamento_sanitizados(code) -> dict:
    try:
        link_pagamento = LinkPagamento.objects.to_serialize(LinkPagamentoSanitizedSerializer).get(pk=unhashint(code))
        pagamento = link_pagamento.pagamento
    except (LinkPagamento.DoesNotExist, ValueError, TypeError):
        raise ValidationError("Não existe link de pagamento com esse código")

    if pagamento and pagamento.status == "paid":
        raise ValidationError("Esta compra já foi efetuada. Contate o revendedor.")
    elif to_default_tz_required(now()) > link_pagamento.expiration_date:
        raise ValidationError("Esse link expirou! Peça para seu revendedor gerar um novo para continuar a reserva.")

    return link_pagamento.serialize()


def set_pagamento_link_pagamento(code, travel: Travel):
    link_pag = LinkPagamento.objects.get(pk=unhashint(code))
    link_pag.pagamento = travel.pagamento
    link_pag.save(update_fields=["pagamento"])


def get_travel(
    travel: Travel | int,
    withuser: bool = True,
    with_extrato: bool = True,
    with_payment: bool = True,
    with_dados_receita: bool = True,
    with_historico_viagem: bool = True,
    with_buseiro_info: bool = False,
    with_connection_info: bool = False,
    with_revendedor: bool = False,
    with_marcacao_assento: bool = False,
    hash_group_info: bool = False,
    travel_serializer: Callable = serializer_travel.serialize_get_travel,
) -> dict | None:
    if isinstance(travel, int):
        try:
            otravel = (
                Travel.objects.select_related(
                    "grupo",
                    "pagamento",
                    "trecho_classe",
                    "trecho_vendido",
                    "travel_ida",
                    "grupo_classe",
                    "cupom",
                    "voucher",
                    "travel_ida__cupom",
                    "travel_ida__voucher",
                )
                .prefetch_related("passageiro_set")
                .get(id=travel)
            )
        except Travel.DoesNotExist:
            otravel = None
    else:
        otravel = travel
    travel_serialized: dict = {}
    if otravel:
        travel_serialized: dict = travel_serializer(
            otravel,
            withpayment=with_payment,
            withdadosreceita=with_dados_receita,
            with_buseiro_info=with_buseiro_info,
            withuser=withuser,
            with_connection_info=with_connection_info,
            with_revendedor=with_revendedor,
            with_marcacao_assento=with_marcacao_assento,
        )
        if with_historico_viagem:
            travel_serialized["historico_viagem"] = travel_svc.get_historico_viagem(otravel)
        if with_extrato:
            travel_serialized["extrato"] = reserva_extrato_visualizacao_svc.get_extrato_visualizacao_travel_existente(
                otravel
            )
        travels_pairs = get_travels_relacionadas(otravel)
        if travels_pairs:
            travel_serialized["travels_pairs"] = [tp.to_dict_json() for tp in travels_pairs]
            # TODO: remove this as it's legacy, now we have a list of travel pairs (above)
            travel_serialized["travel_pair"] = travel_serialized["travels_pairs"][0]
        if hash_group_info:
            grupos = [travel_serialized["grupo"]]
            if grupos:
                hashid_grupos(grupos)
                travel_serialized["grupo"] = grupos[0]

    return travel_serialized or None


def get_travels_relacionadas(travel: Travel) -> list[Travel]:
    if travel.travel_ida_id:
        travels_conexoes_ids = Travel.objects.filter(id=travel.travel_ida_id)
    else:
        _filter = Q(travel_ida_id=travel.id)
        if travel.travel_conexao_id:
            _filter |= Q(
                travel_ida_id__in=Subquery(
                    Travel.objects.filter(travel_conexao_id=travel.travel_conexao_id).values_list("id", flat=True)
                ),
                travel_conexao_id__isnull=False,
            )
        travels_conexoes_ids = Travel.objects.filter(_filter, travel_ida_id__isnull=False)
    # cancela a ordem default para evitar join desnessário

    travels_conexoes_ids = travels_conexoes_ids.order_by()
    travels_conexoes_ids = [tci for tci in travels_conexoes_ids.values("id", "travel_conexao_id") if tci is not None]
    ids_travels = {t["id"] for t in travels_conexoes_ids}
    travel_conexoes_ids = {t["travel_conexao_id"] for t in travels_conexoes_ids}

    # query final
    travels = Travel.objects.select_related("grupo", "grupo_classe", "trecho_classe", "trecho_vendido")
    _filter = Q(id__in=ids_travels)
    if travel_conexoes_ids:
        _filter |= Q(travel_conexao_id__in=travel_conexoes_ids)

    return list(travels.filter(_filter))


def get_travel_opened(reservation_code: str, departure_date):
    """
    Consulta uma viagem pelo código de reserva e pela data da viagem.
    :param reservation_code: ≥ (str) Código de reserva.
    :param departure_date: ≥ (date) Data da viagem.
    :return: Objeto sem as informações pessoais da viagem e do passageiro.
    """
    if not isinstance(reservation_code, str) or not isinstance(departure_date, date):
        raise ValidationError("Código de reserva ou Data de viagem não estão definidos.")
    travel_qs = first(
        Travel.objects.select_related("grupo", "grupo_classe", "trecho_classe", "trecho_vendido", "user").filter(
            reservation_code=reservation_code.upper()
        )
    )
    if travel_qs:
        travel_date = timezone_svc.to_tz_trecho(travel_qs.trecho_classe.datetime_ida, travel_qs.trecho_classe)
        if travel_date and (travel_date.date() == departure_date):
            _travel = get_travel(
                travel_qs,
                with_extrato=False,
                with_payment=False,
                with_dados_receita=False,
            )
            return _cleaning_sensitive_data_travel(_travel)


def get_sanitized_travel(travel):
    # Se for um objeto, precisa serializar
    if isinstance(travel, Travel):
        travel = get_travel(travel)
    return _cleaning_sensitive_data_travel(travel)


def verifica_se_travel_do_usuario_tem_atraso(travels_id: list[int]):
    status_autorizados = ["viagem_confirmada", "embarque_proximo", "onibus_em_transito"]
    travels_atraso_dict = {}
    travels_qs = Travel.objects.select_related(
        "trecho_classe__grupo", "trecho_classe__trecho_vendido", "grupo", "pagamento"
    ).filter(pk__in=travels_id)
    travel_by_id = {t.id: t for t in travels_qs}
    for travel_id in travels_id:
        travel_obj = travel_by_id[int(travel_id)]
        historico_viagem = travel_svc.get_historico_viagem(travel_obj)
        current_status = travel_svc.get_current_travel_status(historico_viagem)
        if current_status["status"] in status_autorizados:
            try:
                trecho_classe = travel_obj.trecho_classe
                _validate_checkin_status_group(trecho_classe)
                _validate_arrival_time_request(trecho_classe)
                info_atraso = get_telemetry_info_from_trecho_classe(trecho_classe)
                if info_atraso.get("arrival_datetime") is not None:
                    trecho_classe_datetime_ida = to_tz_trecho(trecho_classe.datetime_ida, trecho_classe)
                    is_delayed = (info_atraso["arrival_datetime"] - trecho_classe_datetime_ida).total_seconds() > 0
                    if is_delayed:
                        travels_atraso_dict[travel_id] = True
            except ValidationError as exc:
                if isinstance(exc.__cause__, TorreNotOkError | TorreResponseNotFound):
                    buserlogger.warning(f"Não foi possível verificar atraso {exc}")
            except ConnectionError as exc:
                buserlogger.warning(f"Não foi possível conectar na torre {exc}")
                sentry_sdk.capture_exception(exc)

    return travels_atraso_dict


def get_estimated_arrival_time_from_trecho_classe(trecho_classe: TrechoClasse) -> dict:
    """
    :param trecho_classe: Objeto da viagem que irá consultar o tempo estimado do embarque conforme consulta na API da torre
    :return: Objeto serializado com os dados
    """

    _validate_checkin_status_group(trecho_classe)
    _validate_arrival_time_request(trecho_classe)

    return get_telemetry_info_from_trecho_classe(trecho_classe)


def retrieve_telemetry_info(trecho_classe: TrechoClasse) -> BusTelemetryForm:
    try:
        telemetry_info = torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding(
            trecho_classe.grupo_id, trecho_classe.trecho_vendido.origem_id
        )
        return telemetry_info
    except TorreTimeoutError:
        buserlogger.info("Timeout na requisição da torre")
        raise ValidationError(
            "Timeout durante comunicação com a torre",
            params={"can_repeat_request": True},
        )
    except TorreNotOkError as exc:
        capture_exception(exc)
        raise ValidationError("Erro de comunicação com a Torre", params={"can_repeat_request": False}) from exc
    except TorreResponseNotFound:
        raise ValidationError("Trecho sem telemetria", params={"can_repeat_request": False})
    except ConnectionError as exc:
        capture_exception(exc)
        raise ValidationError("Erro de conexão com a torre", params={"can_repeat_request": False})


def get_telemetry_info_from_trecho_classe(trecho_classe: TrechoClasse) -> dict:  # noqa
    telemetry_info = retrieve_telemetry_info(trecho_classe)

    # Devolve para o front com o timezone da rota
    if telemetry_info.bus_eta.arrival_datetime:
        telemetry_info.bus_eta.arrival_datetime = to_tz_trecho(telemetry_info.bus_eta.arrival_datetime, trecho_classe)
    if telemetry_info.bus_eta.updated_at:
        telemetry_info.bus_eta.updated_at = to_tz_trecho(telemetry_info.bus_eta.updated_at, trecho_classe)
    if telemetry_info.bus_location.updated_at:
        telemetry_info.bus_location.updated_at = to_tz_trecho(telemetry_info.bus_location.updated_at, trecho_classe)

    grupo_tem_check_in = _get_checkin_status_grupo(trecho_classe)
    has_delay_in_updated_at = _has_delay_in_bus_updated_at(telemetry_info, trecho_classe)
    has_telemetry = telemetry_info.bus_eta.updated_at is not None
    has_delayed_time = telemetry_info.bus_delayed_info.delayed_time is not None

    # Posso ter e_delayed_time null, mas ter a reason delayed preenchida
    if telemetry_info.bus_delayed_info.reason_delayed:
        excluded_reasons = {
            "accident",
            "mechanical_failure",
            "arrest",
            "no_show",
            "checkin_problems",
        }
        is_an_excluded_reason = telemetry_info.bus_delayed_info.reason_delayed in excluded_reasons
        # Nesse caso, se for uma razão excluída, não retorna dados de atualização do ETA
        # Mas retorna o delayed_time se o analista tratou e já tem uma previsão
        if is_an_excluded_reason:
            if has_delayed_time:
                datetime_ida = cast(datetime, trecho_classe.datetime_ida)
                new_arrival_datetime = to_tz_trecho(
                    datetime_ida + timedelta(seconds=telemetry_info.bus_delayed_info.delayed_time or 0),
                    trecho_classe,
                )
                return TelemetryInfoForPax(arrival_datetime=new_arrival_datetime).dict()
            return TelemetryInfoForPax().dict()

    if not has_telemetry and not has_delayed_time:
        raise ValidationError(
            "Não existe informação de telemetria deste ônibus",
            params={"can_repeat_request": True},
        )

    is_bus_near = _is_bus_near_boarding_place(telemetry_info)
    telemetry_not_reliable = has_delay_in_updated_at and not is_bus_near
    is_bus_far_away = _is_bus_far_from_boarding_place(telemetry_info)

    if has_telemetry and not has_delayed_time:
        if is_bus_far_away or telemetry_not_reliable:
            raise ValidationError(
                "Não é possível retornar a telemetria do ônibus neste momento",
                params={"can_repeat_request": True},
            )
        return TelemetryInfoForPax(
            updated_at=telemetry_info.bus_location.updated_at,
            position=telemetry_info.bus_location.position,
            arrival_datetime=telemetry_info.bus_eta.arrival_datetime,
        ).dict()

    datetime_ida = cast(datetime, trecho_classe.datetime_ida)
    new_arrival_datetime = to_tz_trecho(
        datetime_ida + timedelta(seconds=telemetry_info.bus_delayed_info.delayed_time or 0),
        trecho_classe,
    )

    # se o eta da telemetria for maior que o delayed_time, mantém o eta
    if has_telemetry and has_delayed_time:
        _now = to_tz_trecho(dateutils.now(), trecho_classe)
        if not _now or not new_arrival_datetime:
            return TelemetryInfoForPax().dict()
        arrival_datetime = telemetry_info.bus_eta.arrival_datetime
        if not arrival_datetime:
            return TelemetryInfoForPax().dict()
        if _now > new_arrival_datetime:
            if not has_delay_in_updated_at or arrival_datetime > _now:
                return TelemetryInfoForPax(
                    updated_at=telemetry_info.bus_location.updated_at,
                    position=telemetry_info.bus_location.position,
                    arrival_datetime=arrival_datetime,
                ).dict()
            if arrival_datetime < _now and not grupo_tem_check_in:
                return TelemetryInfoForPax().dict()

    if is_bus_far_away or telemetry_not_reliable:
        return TelemetryInfoForPax(arrival_datetime=new_arrival_datetime).dict()
    return TelemetryInfoForPax(
        updated_at=telemetry_info.bus_location.updated_at,
        position=telemetry_info.bus_location.position,
        arrival_datetime=new_arrival_datetime,
    ).dict()


def _get_checkin_status_grupo(trecho_classe: TrechoClasse):
    checkin_opened = trecho_classe.get_checkin_status()
    current_datetime = to_tz_trecho(now(), trecho_classe)
    datetime_ida = to_tz_trecho(trecho_classe.datetime_ida, trecho_classe)
    if not current_datetime or not datetime_ida:
        return False

    # Se checkin não iniciado ou iniciado antes do datetime_ida
    if checkin_opened is None or current_datetime < datetime_ida:
        return False

    checkin_datetime = to_tz_trecho(datetime.fromisoformat(checkin_opened), trecho_classe)
    if not checkin_datetime:
        return False
    foi_checkin_adiantado = checkin_datetime < datetime_ida
    base_datetime = datetime_ida if foi_checkin_adiantado else checkin_datetime
    # Valida se o checkin foi iniciado ha mais de 10 minutos
    return ((current_datetime - base_datetime).total_seconds() / 60) > 10


def _validate_checkin_status_group(trecho_classe: TrechoClasse):
    if _get_checkin_status_grupo(trecho_classe):
        raise ValidationError("Checkin já foi iniciado.", params={"can_repeat_request": False})


def _validate_arrival_time_request(trecho_classe: TrechoClasse):
    # Calcula o tempo (em minutos) entre a data da viagem e o momento em que a requisição é feita.
    # Como regra de negócio, a informação de telemetria só pode ser consultada em uma janela de 40 minutos da viagem.
    travel_trechoclasse_datetime = to_tz_trecho(trecho_classe.datetime_ida, trecho_classe)
    current_datetime = to_tz_trecho(now(), trecho_classe)
    if not travel_trechoclasse_datetime or not current_datetime:
        return
    delta_time_trechoclasse = (travel_trechoclasse_datetime - current_datetime).total_seconds() / 60

    # Não prossegue na consulta se não estiver na janela de tempo
    min_allowed_time = 40  # Tempo mínimo para consulta é de 40 minutos antes do horário de embarque
    max_allowed_time = -(
        60 * 6
    )  # Tempo máximo para consulta é de 6 horas depois do horário de embarque (considerando incidentes)
    if delta_time_trechoclasse > min_allowed_time or delta_time_trechoclasse < max_allowed_time:
        raise ValidationError(
            "Não é possível consultar essa informação fora do período permitido",
            params={"can_repeat_request": True},
        )


def _is_bus_far_from_boarding_place(telemetry_info: BusTelemetryForm) -> bool:
    is_bus_one_hour_away = telemetry_info.bus_eta.eta and telemetry_info.bus_eta.eta > 3600
    is_bus_50km_away = telemetry_info.bus_location.distance and telemetry_info.bus_location.distance > 50000
    return bool(is_bus_one_hour_away or is_bus_50km_away)


def _is_bus_near_boarding_place(telemetry_info: BusTelemetryForm) -> bool:
    if not telemetry_info.bus_location.distance:
        return False
    return telemetry_info.bus_location.distance <= 600


def _has_delay_in_bus_updated_at(telemetry_info: BusTelemetryForm, trecho_classe: TrechoClasse) -> bool:
    # aqui o bus_location já vem com a data convertida para o timezone do trecho_classe
    if not telemetry_info.bus_location.updated_at:
        return False
    current_datetime = to_tz_trecho(now(), trecho_classe)
    if not current_datetime:
        return False
    is_delay_more_than_10_minutes = (
        (current_datetime - telemetry_info.bus_location.updated_at).total_seconds() / 60
    ) > 10
    return is_delay_more_than_10_minutes


def _cleaning_sensitive_data_travel(travel: dict | None):
    try:
        sensitive_data_travel = ["max_split_value", "payment", "extrato"]
        if travel:
            travel.update((k, None) for k, v in travel.items() if k in sensitive_data_travel)

            if travel.get("passengers"):
                utils.mask_passengers_sensitive_data(travel.get("passengers"))

        return travel
    except Exception as err:
        raise Exception(f"Erro ao remover dados sensíveis da viagem ou do pax: {str(err)}")


def travel_contact_phone(travel_id: int, phone, user):
    travel = user.travel_set.filter(pk=travel_id).first()
    phone = only_numbers(phone)
    if not phone or not 10 <= len(phone) <= 11:
        raise ValidationError("Telefone inválido")
    travel.contact_phone = phone
    travel.save(
        update_fields=[
            "contact_phone",
            "updated_on",
        ]
    )


@lock("remove_passenger_travel_{passenger.travel_id}")
def remove_passenger(passenger, reason=None, by_admin=False, por_revendedor=False, notify=False):
    passenger.refresh_from_db()

    removing_last_pax = [passenger.id] == list(
        passenger.travel.passageiro_set.filter(removed=False).values_list("id", flat=True)
    )
    if removing_last_pax:
        cancel_strategy = _get_cancel_strategy(reason, by_admin, notify)
        _cancel_travel(passenger.travel.id, cancel_strategy, should_notify=notify)
        _handle_pagamento_waiting_payment(passenger.travel, cancel_strategy)
        if not by_admin:
            _handle_taxa_cancelamento(passenger.travel, passageiros=[passenger], por_revendedor=por_revendedor)
            _handle_pagamento_reprocessado(passenger.travel)
        passenger.set_removed()
        return

    evento = EventoRemoverPassageiro(passenger, by_admin)

    with transaction.atomic():
        # chama de novo dentro da transaction
        evento.validate()
        passenger.set_removed()
        travel = passenger.travel

        impactos = ImpactosCancelamentoPax(passenger)
        impactos.aplicar()

        if is_last_passenger_com_promocao(passenger):
            _remover_promo_travel(travel)
        _remover_promo_passenger(passenger)
        cancela_itens_adicionais_por_pax(pax=passenger)
        travel.update_count_seats()
        multitrecho_svc.atualiza_vagas_trechos_classe_do_grupo(travel.grupo)
        rodoviaria_svc.remove_passageiro(travel, passenger)

    passageiro_removido_signal.send(None, travel=travel, pax_id=passenger.id)

    evento.log()
    if not by_admin:
        _handle_taxa_cancelamento(travel, passageiros=[passenger], por_revendedor=por_revendedor)
        _handle_pagamento_reprocessado(passenger.travel)


def _get_cancel_strategy(reason, by_admin, notify):
    if notify and reason == "ACAO_RISCO":
        return CancelByRisco()
    if by_admin:
        return CancelByAdmin(reason)
    return CancelByUserRequest(reason)


def is_last_passenger_com_promocao(p) -> bool:
    if p.promocao is None:
        return False
    return (
        not Passageiro.objects.filter(travel=p.travel, removed=False, promocao=p.promocao, cupom=p.cupom)
        .exclude(pk=p.id)
        .exists()
    )


def isclose(a: Decimal, b: Decimal) -> bool:
    return abs(Decimal(a) - Decimal(b)) < Decimal("0.01")


def _check_link_pagamento_adulterado(ext_esperado, ext_recebido, groups):
    ext_reserva_esperado = ext_esperado.get("extrato_reserva")
    ext_reserva_recebido = ext_recebido.get("extrato_reserva")
    pag_total_esperado = ext_esperado.get("pagamento_total")
    pag_total_recebido = Decimal(str(ext_recebido.get("pagamento_total")))
    valor_ida_esperado = ext_reserva_esperado[0]["footer"]["value"]
    valor_ida_recebido = Decimal(str(ext_reserva_recebido[0]["footer"]["value"]))
    valor_volta_esperado = Decimal("0")
    valor_volta_recebido = Decimal("0")
    valor_total_a_pagar_esperado = Decimal("0")
    valor_total_a_pagar_recebido = Decimal("0")
    temVolta = "volta" in ext_reserva_esperado[1]["title"]
    if temVolta:  # Tem ida e volta
        valor_volta_esperado = ext_reserva_esperado[1]["footer"]["value"]
        valor_volta_recebido = Decimal(str(ext_reserva_recebido[1]["footer"]["value"]))
        valor_total_a_pagar_esperado = ext_reserva_esperado[2]["value"]
        valor_total_a_pagar_recebido = Decimal(str(ext_reserva_recebido[2]["value"]))
    else:  # Só tem ida
        valor_total_a_pagar_esperado = ext_reserva_esperado[1]["value"]
        valor_total_a_pagar_recebido = Decimal(str(ext_reserva_recebido[1]["value"]))
    adulterado = (
        (not isclose(pag_total_esperado, pag_total_recebido))
        or (len(ext_reserva_esperado) != len(ext_reserva_recebido))
        or (not isclose(valor_ida_esperado, valor_ida_recebido))
        or (not isclose(valor_volta_esperado, valor_volta_recebido))
        or (not isclose(valor_total_a_pagar_esperado, valor_total_a_pagar_recebido))
    )
    if adulterado:
        buserlogger.error(
            "Pagamento Adulterado",
            extra={
                "pag_total_esperado": pag_total_esperado,
                "pag_total_recebido": pag_total_recebido,
                "len_ext_reserva_esperado": len(ext_reserva_esperado),
                "len_ext_reserva_recebido": len(ext_reserva_recebido),
                "valor_ida_esperado": valor_ida_esperado,
                "valor_ida_recebido": valor_ida_recebido,
                "valor_volta_esperado": valor_volta_esperado,
                "valor_volta_recebido": valor_volta_recebido,
                "valor_total_a_pagar_esperado": valor_total_a_pagar_esperado,
                "valor_total_a_pagar_recebido": valor_total_a_pagar_recebido,
                "groups": groups,
            },
        )
    return adulterado


def _remover_promo_travel(travel: Travel):
    travel.promocao = None
    travel.cupom = None
    travel.save(
        update_fields=[
            "promocao",
            "cupom",
            "updated_on",
        ]
    )


def _remover_promo_passenger(pax: Passageiro):
    pax.promocao = None
    pax.cupom = None
    pax.save(update_fields=["promocao", "cupom"])


@my_shared_task(queue="staff")
def admin_cancel_travel(travel_id, reason=None) -> Travel:
    strategy = CancelByAdmin(reason)
    return _cancel_travel(travel_id, strategy)


@my_shared_task(queue="staff")
def marketplace_cancel_travel(travel_id, reason=None) -> Travel:
    strategy = CancelByMarketplaceError(reason)
    return _cancel_travel(travel_id, strategy)


@my_shared_task(queue="staff")
def edit_rota_cancel_travel(travel_id, reason=None, custom_description=None) -> Travel:
    strategy = CancelByEditRota(reason, custom_description)
    return _cancel_travel(travel_id, strategy)


def cancel_travel_move_pax(
    travel: Travel,
    should_update_vacancies=True,
    is_async=False,
    execute_on_rodoviaria=True,
) -> Travel:
    return _cancel_travel(
        travel=travel,
        travel_id=travel.id,
        is_async=is_async,
        cancel_strategy=CancelByMovePax(),
        should_update_vacancies=should_update_vacancies,
        should_cancel_rodoviaria=execute_on_rodoviaria,
    )


def cancel_travel_remarcacao_gratuita(
    travel_id,
    should_update_vacancies=True,
    is_async=False,
    execute_on_rodoviaria=True,
    reason=None,
) -> Travel:
    return _cancel_travel(
        travel_id,
        CancelByRemarcacaoGratuita(reason=reason),
        should_update_vacancies=should_update_vacancies,
        is_async=is_async,
        should_cancel_rodoviaria=execute_on_rodoviaria,
    )


@transaction.atomic
def _cancel_travels_com_conexao(travel_pivo, reason=None, por_revendedor=False):
    """Cancel all travels connected to the main travel if possible."""
    related_travels_qs = travel_pivo.travel_conexao.travel_set.all()
    related_travel_ids = [travel.id for travel in related_travels_qs]

    buserlogger.info(
        "reserva_svc._cancel_travels_com_conexao",
        extra={"related_travel_ids": related_travel_ids},
    )

    for travel in related_travels_qs:
        try:
            user_cancel_travel(travel.id, reason=reason, por_revendedor=por_revendedor)
        except ValidationError as err:
            if err.code == "already_canceled":
                continue
            elif err.code in ("group_is_done", "deadline_exceded"):
                # se as viagens anteriores já finalizaram a gente pode cancelar mesmo assim
                if travel.trecho_conexao.idx < travel_pivo.trecho_conexao.idx:
                    continue
            raise


@dataclass
class BulkNotificaNaoPodeCancelarPrimeiraPerna:
    grupo_1a_perna_id: int
    grupo_2a_perna_id: int
    travel_1a_perna_id: int
    travel_2a_perna_id: int


NOTIFICA_RISCO_SEGUNDA_PERNA_CONEXAO_CANCELADA = """
Segunda perna de conexão foi cancelada e a primeira perna foi iniciada

Grupo :one: perna: {staff_base_url}/staff/grupos/{grupo_1a_perna_id}
Grupo :two: perna: {staff_base_url}/staff/grupos/{grupo_2a_perna_id}

Travel :one: perna: {staff_base_url}/staff/travels/{travel_1a_perna_id}
Travel :two: perna: {staff_base_url}/staff/travels/{travel_2a_perna_id}
"""


@batch_task
def _bulk_notifica_risco_nao_pode_cancelar_primeira_perna(
    args: list[BulkNotificaNaoPodeCancelarPrimeiraPerna],
):
    for a in args:
        slack_data = SlackDataForm(
            username="[ALERTA DE CANCELAMENTO DE CONEXÕES]",
            channel="#temp-ops-conexoes",
            text=NOTIFICA_RISCO_SEGUNDA_PERNA_CONEXAO_CANCELADA.format(
                grupo_1a_perna_id=a.grupo_1a_perna_id,
                grupo_2a_perna_id=a.grupo_2a_perna_id,
                travel_1a_perna_id=a.travel_1a_perna_id,
                travel_2a_perna_id=a.travel_2a_perna_id,
                staff_base_url=settings.STAFF_BASE_URL,
            ),
            icon_emoji=":no_entry_sign:",
            icon_url=None,
            attachments=None,
            blocks=None,
        )
        slack_post(slack_data.json())


def cancel_travel_erro_emissao_async(travels: list[Travel], exception: Exception):
    success = True
    for travel in travels:
        try:
            _cancel_travel(
                travel.id,
                CancelByRodoviariaError(exception),
                travel=travel,
                should_cancel_payment=False,
                should_cancel_rodoviaria=True,
            )
        except Exception as err:
            success = False
            sentry_sdk.capture_exception(err)

    return success


@transaction.atomic
def _grupo_cancel_travels_com_conexao(travel_pivo, reason=None, por_revendedor=False):
    """Group cancel connected travels."""
    related_travels = list(
        travel_pivo.travel_conexao.travel_set.select_related(
            "grupo",
            "trecho_classe",
            "trecho_conexao",
        )
        .exclude(status=Travel.Status.CANCELED)
        .all()
    )
    related_travel_ids = [travel.id for travel in related_travels]

    buserlogger.info(
        "reserva_svc._grupo_cancel_travels_com_conexao",
        extra={"related_travel_ids": related_travel_ids},
    )
    for travel in related_travels:
        # primeira perna da conexão
        if travel.trecho_conexao.idx == 0 and not _should_cancel_travel(travel):
            # se não podemos cancelar a primeira perna
            # comunicamos risco que o pax ficará sem a segunda viagem
            travel_relacionada = next(t for t in related_travels if t.trecho_conexao.idx == 1)
            _bulk_notifica_risco_nao_pode_cancelar_primeira_perna.push(
                travel.grupo.id,
                travel_relacionada.grupo.id,
                travel.id,
                travel_relacionada.id,
            )
            continue

        try:
            grupo_cancel_travel(travel, reason=reason, por_revendedor=por_revendedor)
        except ValidationError as err:
            if err.code == "already_canceled":
                travel_pair_ids = [tp.id for tp in travel.get_travel_pairs()]
                if err.params.get("travel_id", -1) in travel_pair_ids:
                    continue  # Already handled by _cancel_travel_pair
            raise


def grupo_cancel_travels_conexao(travel_ids: list[int], reason="GROUP_CANCELED"):
    """Cancel a group of travels by their IDs."""
    pivot_travels = (
        Travel.objects.select_related(
            "trecho_conexao",
            "travel_conexao",
            "grupo",
        )
        .prefetch_related("travel_conexao__travel_set")
        .filter(pk__in=travel_ids)
    )
    for travel in pivot_travels:
        if not travel.travel_conexao:
            grupo_cancel_travel(travel, reason=reason)
        else:
            _grupo_cancel_travels_com_conexao(travel, reason=reason)


def grupo_cancel_travel(travel: Travel, reason=None, *, por_revendedor=False) -> Travel:
    """Group cancel a single travel."""
    strategy = CancelByAdmin(reason)
    travel = _cancel_travel(travel.id, strategy, travel=travel)
    _handle_pagamento_waiting_payment(travel, strategy)
    _handle_taxa_cancelamento(travel, por_revendedor=por_revendedor)
    _handle_pagamento_reprocessado(travel)
    return travel


def cancel_travels(travel: Travel, reason=None, *, por_revendedor=False):
    if getattr(travel, "travel_conexao", None) is None:
        user_cancel_travel(travel.id, reason=reason, por_revendedor=por_revendedor)
        return
    _cancel_travels_com_conexao(travel, reason=reason, por_revendedor=por_revendedor)


def _handle_estorno_travel(travel: Travel, strategy: CancelStrategy):
    if isinstance(strategy, CancelByPromotor):
        return

    _estornar_travel(travel=travel)


def user_cancel_travel(travel_id: int, reason=None, *, por_revendedor=False) -> Travel:
    CancelStrategy = CancelByUserRequest if not por_revendedor else CancelByPromotor

    strategy = CancelStrategy(reason)
    travel = _cancel_travel(travel_id, strategy)
    _handle_pagamento_waiting_payment(travel, strategy)
    _handle_taxa_cancelamento(travel, por_revendedor=por_revendedor)
    _handle_estorno_travel(travel, strategy)

    return travel


def _handle_taxa_cancelamento(travel: Travel, passageiros=None, por_revendedor=False):
    if por_revendedor:
        return
    # se teve crédito no pagamento, não cobra taxa de cancelamento, por enquanto
    if CreditAccountingOperation.objects.filter(source="PAGAMENTO_USADO", travel_id=travel.id).exists():
        return
    pagamento = travel.pagamento
    if pagamento and (pagamento.status not in Pagamento.STATUSES_OK or pagamento.method == Pagamento.Method.DINHEIRO):
        return
    if passageiros is None:
        passageiros = travel.passageiro_set.filter(removed=False)
    taxa_cancelamento_dict = taxa_cancelamento_svc.get_taxa_cancelamento_by_travel(travel.id)
    infos_taxa = TaxaCancelamentoDic(**taxa_cancelamento_dict)
    if infos_taxa and (infos_taxa.prazo_expirado or infos_taxa.remarcacao_expirada):
        for pax in passageiros:
            accounting_svc.taxa_cancelamento(pax, infos_taxa.taxa)


def _handle_pagamento_waiting_payment(travel: Travel, strategy: CancelStrategy):
    pagamento = travel.pagamento
    if pagamento and pagamento.status in Pagamento.STATUSES_WAIT:
        _cancel_travel_pair(travel, strategy)


def _cancel_travel_pair(travel: Travel, strategy: CancelStrategy):
    travel_pair = travel.get_travel_pairs() or []
    for pair in travel_pair:
        if pair and pair.status == "pending":
            _cancel_travel(pair.id, strategy)


def _handle_pagamento_reprocessado(travel: Travel):
    pagamento = travel.pagamento
    if (
        pagamento
        and pagamento.status == "paid"
        and pagamento.method == Pagamento.Method.CREDIT_CARD
        and pagamento.transaction_reprocessada_id
    ):
        _estornar_travel(travel)


def _estornar_travel(travel: Travel):
    has_estorno_last_minute = (
        travel.pagamento_id
        and Estorno.objects.filter(
            pagamento_id=travel.pagamento_id, created_at__gt=now() - timedelta(minutes=1)
        ).exists()
    )
    countdown = 60 if has_estorno_last_minute else 0
    tasks.estornar_travel.apply_async(args=[travel.id], countdown=countdown)


def cancel_travel_remarcacao(travel_id: int, atraso_torre: bool, is_upgrade: bool = False) -> Travel:
    if atraso_torre:
        return _cancel_travel(travel_id, CancelByRemarcacaoAtraso())

    if is_upgrade:
        return _cancel_travel(travel_id, CancelByUpgrade())

    return _cancel_travel(travel_id, CancelByRemarcacao())


def cancel_travel_sem_pagamento(travel_id: int, pagamento: Pagamento) -> Travel:
    # esse fluxo é chamado já pelo cancelamento do pagamento, então não precisa cancelar o pagamento
    try:
        return _cancel_travel(travel_id, CancelByNoPayment(pagamento), should_cancel_payment=False)
    except Exception as err:
        buserlogger.error(
            "reserva_svc.cancel_travel_sem_pagamento",
            extra=dict(travel_id=travel_id, err_msg=str(err)),
        )
        raise


def _is_travel_from_api(travel: Travel) -> bool:
    return bool(
        travel.pagamento
        and travel.pagamento.method == "dinheiro"
        and travel.revendedor_user.revendedor.kind == "parceiro"
    )


def _get_travel_api_with_accops(travel: Travel | None) -> Travel:
    if not travel:
        raise ValidationError("Erro ao recuperar Travel com accops")
    historico_remanejamento = travel.get_primeiro_historico_remanejamento()
    if not historico_remanejamento:
        return travel
    return historico_remanejamento.travel_antiga


@lock("cancel_travel_{travel_id}")
def _cancel_travel(
    travel_id: int,
    cancel_strategy: CancelStrategy,
    should_notify: bool | None = True,
    should_cancel_payment: bool | None = True,
    should_update_vacancies: bool | None = True,
    should_cancel_rodoviaria: bool | None = True,
    is_async: bool | None = False,
    travel: Travel | None = None,
) -> Travel:
    buserlogger.info("reserva_svc._cancel_travel", extra={"travel_id": travel_id})
    if not travel:
        travel = Travel.objects.get(pk=travel_id)
    with transaction.atomic():
        evento = EventoCancelaReserva(travel, cancel_strategy)
        reason = cancel_strategy.reason if cancel_strategy.reason else ""
        _type = cancel_strategy.type if cancel_strategy.type else ""
        travel.cancel(cancel_strategy.type, reason)
        if should_cancel_rodoviaria:
            rodoviaria_svc.efetua_cancelamento(travel, _type)
        if should_update_vacancies:
            tasks.atualiza_vagas_trechos_classe_do_grupo_task.delay(travel.grupo_id)

        impactos = ImpactosCancelamento(evento.travel)
        impactos.aplicar()
        cancela_itens_adicionais_travel(travel=travel)
    if not is_async:
        cancel_reserva_signal.send(None, travel=travel)
    travel_canceled_signal.send(None, travel=travel)

    if _keep_payment_dinheiro(travel, cancel_strategy):
        should_cancel_payment = False

    if _is_travel_from_api(travel):
        travel_with_accops = _get_travel_api_with_accops(travel)
        _cancelar_pagamento_se_invalido(travel.pagamento)
        _send_signal_cancel_pagamento_travel(travel_with_accops.id)
    elif should_cancel_payment:
        travel_pagamento = _get_travel_pagamento(travel)
        pagamento = travel_pagamento.pagamento
        _cancelar_pagamento_se_invalido(pagamento)
        _send_signal_cancel_pagamento_travel(travel_pagamento.id)

    if should_notify:
        evento.notify()
    evento.log()

    return travel


def _get_travel_pagamento(travel: Travel) -> Travel:
    """Caso esteja cancelando uma travel remanejada que foi feita em dinheiro,
    precisamos cancelar o pagamento da travel original"""
    travel_remanejada = None
    # nao faz query sem necessidade
    if travel.movido_em:
        historico = travel.get_primeiro_historico_remanejamento()
        if historico:
            travel_remanejada = historico.travel_antiga
        if travel_remanejada and is_travel_dinheiro(travel_remanejada):
            return travel_remanejada
    return travel


def _keep_payment_dinheiro(travel, cancel_strategy):
    # Caso esteja remanejando uma travel feita em dinheiro, mantem o pagamento
    return is_travel_dinheiro(travel) and cancel_strategy.type in ("MOVE_PAX")


def is_travel_dinheiro(travel: Travel):
    return travel.pagamento and travel.pagamento.method == Pagamento.Method.DINHEIRO


def _send_signal_cancel_pagamento_travel(travel_id: int):
    if transaction.get_connection().in_atomic_block:
        transaction.on_commit(lambda: cancel_pagamento_travel.send(None, travel_id=travel_id))
    else:
        cancel_pagamento_travel.send(None, travel_id=travel_id)


@lock("cancelar_travel_{travel.id}")
def impactos_cancelamento_travel(travel: Travel):
    # log de debug do REC-1840, vou remover depois que entender o BO
    buserlogger.info(
        "reserva_svc.impactos_cancelamento_travel",
        extra={"travel_id": travel.id, "grupo_id": travel.grupo_id},
    )
    todos_pax_removidos = not travel.passageiro_set.filter(removed=False).exists()
    if todos_pax_removidos:
        buserlogger.info(f"Cancelando reserva {travel.id} sem impacto contábil")
        return

    impactos = ImpactosCancelamento(travel)
    impactos.aplicar()
    _cancelar_pagamento_se_invalido(travel.pagamento)

    signals.cancel_reserva_signal.send(None, travel=travel, impacto_contabil=True)


def _cria_travels(
    evento: EventoReserva | EventoRemarcaReserva,
    pagamento: Pagamento | None = None,
    google_tracking_id=None,
    reserva=None,
    pax_can_edit=True,
    seller: str | None = Travel.Seller.BUSER,
    categoria_especial: str | None = CategoriaEspecial.NORMAL,
    protocolo_atendimento=None,
) -> list[Travel]:
    d, user, trechos_classes_ida, trechos_classes_volta = (
        evento.data,
        evento.user,
        evento.trechos_ida,
        evento.trechos_volta,
    )
    try:
        source_id = d["source_id"][:256]
    except (KeyError, TypeError):
        source_id = None

    try:
        source = d["source"][:128]
    except (KeyError, TypeError):
        source = None
    extratos_ida, extratos_volta = (
        evento.extrato["travels_ida"],
        evento.extrato["travels_volta"],
    )
    trechos_ida = d["trechos_ida"]
    trechos_volta = d["trechos_volta"]

    travels = []
    buseiros = buseiro_svc.upsert_buseiros(user, evento.passengers)

    travel_conexao_volta = None
    if evento.conexao_volta:
        travel_conexao_volta = TravelConexao.objects.create(conexao=evento.conexao_volta)

    travel_conexao_ida = None
    if evento.conexao_ida:
        travel_conexao_ida = TravelConexao.objects.create(
            conexao=evento.conexao_ida, travel_conexao_volta=travel_conexao_volta
        )

    # ida
    travel_ida = None
    for dgroup, trecho_classe, extrato in zip(trechos_ida, trechos_classes_ida, extratos_ida):
        trecho_conexao = None
        if evento.conexao_ida:
            trecho_conexao = evento.conexao_ida.trechos_conexao.get(
                origem_id=trecho_classe.trecho_vendido.origem.cidade_id,
                destino_id=trecho_classe.trecho_vendido.destino.cidade_id,
            )

        travel = _adiciona_no_grupo(
            d,
            user,
            trecho_classe,
            extrato,
            buseiros,
            travel_ida,
            dgroup,
            pagamento,
            source,
            source_id,
            google_tracking_id,
            reserva,
            pax_can_edit,
            seller=seller,
            categoria_especial=categoria_especial,
            travel_conexao=travel_conexao_ida,
            trecho_conexao=trecho_conexao,
            protocolo_atendimento=protocolo_atendimento,
            poltronas_bloqueadas=evento.poltronas_bloqueadas,
        )
        travels.append(travel)

    # TODO Tech-Debt: check if this is the best way to deal if we have a connection
    travel_ida = travels[0]
    for dgroup, trecho_classe, extrato in zip(trechos_volta, trechos_classes_volta, extratos_volta):
        trecho_conexao = None
        if evento.conexao_volta:
            trecho_conexao = evento.conexao_volta.trechos_conexao.get(
                origem_id=trecho_classe.trecho_vendido.origem.cidade_id,
                destino_id=trecho_classe.trecho_vendido.destino.cidade_id,
            )

        travel = _adiciona_no_grupo(
            d,
            user,
            trecho_classe,
            extrato,
            buseiros,
            travel_ida,
            dgroup,
            pagamento,
            source,
            source_id,
            google_tracking_id,
            reserva,
            pax_can_edit,
            seller=seller,
            travel_conexao=travel_conexao_volta,
            trecho_conexao=trecho_conexao,
            poltronas_bloqueadas=evento.poltronas_bloqueadas,
        )
        travels.append(travel)
    return travels


def _atualiza_contagem_cupom(travels):
    cupom = travels[0].cupom
    if not cupom:
        voucher = travels[0].voucher
        if voucher:
            cupom = voucher.cupom
    if cupom:
        Cupom.objects.filter(pk=cupom.id).update(count_usages=F("count_usages") + 1)


def _save_promo_parceria(travels, cpf_da_promo):
    if not cpf_da_promo:
        return
    promos_parceria = [
        PromocaoParceria(travel=travel, cpf=only_numbers(cpf_da_promo)) for travel in travels if travel.is_parceria
    ]
    PromocaoParceria.objects.bulk_create(promos_parceria)


def _atualiza_contagem_paxes(grupos):
    for grupo in grupos:
        multitrecho_svc.atualiza_vagas_trechos_classe_do_grupo(grupo)


def _registra_conversao_mab(travels, extrato, user, price_token):
    extrato_by_trecho_classe = {
        travel["trechoclasse_id"]: travel
        for travel in itertools.chain(extrato["travels_ida"], extrato["travels_volta"])
    }
    for travel in travels:
        travel_extrato = extrato_by_trecho_classe[travel.trecho_classe_id]
        taxas = {
            "seguro_extra": travel_extrato["valor_seguro_extra"],
            "taxa_servico": travel_extrato["valor_taxa_servico"],
        }
        price_ab_svc.conversion(user, travel, taxas, price_token)


def _cria_logs_reserva(travels, evento):
    for travel in travels:
        log_svc.log_joined_grupo_classe(evento.data, travel)


def _cria_logs_desconto_categoria_especial_marketplace(travels, categoria_especial):
    if not categoria_especial or categoria_especial == CategoriaEspecial.NORMAL:
        return
    for travel in travels:
        is_marketplace = travel.grupo.is_marketplace
        if not is_marketplace:
            continue
        log_svc.log_compra_gratuidade_marketplace(travel, categoria_especial)


def _cria_logs_remarcacao(travels, evento):
    for travel in travels:
        log_svc.log_remarcacao(evento.data, travel)


def _get_cupom_voucher(code, cupom_id=None) -> tuple:
    if not (code or cupom_id):
        return None, None

    code = str(code).upper().strip()
    if code:
        filtro = Q(code=code)
    else:
        filtro = Q(id=cupom_id)
    cupom = Cupom.objects.filter(filtro).first()

    if cupom:
        return cupom, None

    voucher = Voucher.objects.filter(code=code).select_related("cupom").first()
    if voucher:
        return voucher.cupom, voucher
    return None, None


def _adiciona_no_grupo(
    dreserva,
    user,
    trecho_classe,
    extrato,
    buseiros,
    travel_ida,
    dgroup,
    pagamento: Pagamento | None = None,
    source=None,
    source_id=None,
    google_tracking_id=None,
    reserva=None,
    pax_can_edit=True,
    seller: str | None = Travel.Seller.BUSER,
    categoria_especial: str | None = CategoriaEspecial.NORMAL,
    travel_conexao=None,
    trecho_conexao=None,
    protocolo_atendimento=None,
    poltronas_bloqueadas: list[BlockedSeatReserva] = [],
):
    promocao = extrato.get("promocao", {}) or {}
    promocao_name = promocao.get("name")
    cupom_code = promocao.get("cupom_code")
    cupom_id = promocao.get("cupom_id")
    cupom, voucher = _get_cupom_voucher(cupom_code, cupom_id)
    revendedor_user_id = dreserva.get("user_revendedor")
    is_retencao = dreserva.get("is_retencao")
    revendedor_user = None
    if revendedor_user_id:
        revendedor_user = User.objects.get(pk=revendedor_user_id)
    dpassengers = [{**dp, **ep} for ep, dp in zip(extrato["passengers"], dreserva["passengers"])]
    activepassengers = [pax for pax in dpassengers if not pax["removed"]]
    grupo = trecho_classe.grupo
    grupo_classe = trecho_classe.grupo_classe

    max_split_value_sr, ref_split_value_sr = preco_svc.get_precos_search_result(dgroup)
    max_split_value, ref_split_value = preco_svc.get_precos_para_travel(
        user,
        trecho_classe,
        max_split_value_sr,
        ref_split_value_sr,
        client=dreserva.get("client"),
        user_revendedor_id=revendedor_user_id,
        price_token=dreserva.get("price_token"),
    )
    _cupom = cupom if not voucher else None
    travel = Travel.objects.create(
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        user=user,
        pagamento=pagamento,
        reservation_code=Travel.generate_reservation_code(),
        max_split_value=max_split_value,
        split_value=max_split_value,
        ref_split_value=ref_split_value,
        probability_on_reservation=grupo.confirming_probability,
        promocao=promocao_name,
        cupom=_cupom,
        voucher=voucher,
        count_seats=len(activepassengers),
        travel_ida=travel_ida,
        source=source,
        source_id=source_id,
        revendedor_user=revendedor_user,
        is_retencao=is_retencao if revendedor_user else False,
        reserva=reserva,
        google_tracking_id=google_tracking_id,
        pax_can_edit=pax_can_edit,
        seller=seller,
        travel_conexao=travel_conexao,
        trecho_conexao=trecho_conexao,
        protocolo_atendimento=protocolo_atendimento,
    )
    taxa_cancelamento = taxa_cancelamento_svc.get_taxa_cancelamento_by_modelo_venda_active(
        modelo_venda=grupo.modelo_venda
    )
    taxa_cancelamento_svc.save_taxa_cancelamento_travel(
        taxa_cancelamento=taxa_cancelamento,
        travel=travel,
        travel_remarcada_id=dreserva.get("travel_id"),
    )
    passageiros = []

    for dpassenger, buseiro in zip(dpassengers, buseiros):
        poltrona = _get_poltrona(poltronas_bloqueadas, dpassenger, trecho_classe.id)
        passageiro = Passageiro.create(
            travel,
            dpassenger,
            buseiro.id,
            voucher=voucher,
            categoria_especial=categoria_especial,
            cupom=_cupom,
            commit=False,
            poltrona=poltrona.numero_poltrona if poltrona else None,
            extra=poltrona.extra if poltrona else {},
        )
        passageiros.append(passageiro)

    Passageiro.objects.bulk_create(passageiros)

    if globalsettings_svc.get("salva_ofertas_esgotadas") and max_split_value < ref_split_value:
        vagas_bucket = trecho_classe.vagas_bucket
        if len(dpassengers) >= vagas_bucket:
            set_oferta_esgotada(
                trecho_classe=trecho_classe,
                value=max_split_value,
                ref_value=ref_split_value,
            )

    return travel


def _cancelar_pagamento_se_invalido(pagamento: Pagamento | None) -> None:
    if not pagamento:
        return

    pagamento_em_dinheiro = pagamento.method == Pagamento.Method.DINHEIRO
    pagamento_processando = pagamento.status in Pagamento.STATUSES_WAIT

    if not pagamento_em_dinheiro and not pagamento_processando:
        return

    for ptravel in pagamento.travels.all():
        travel_em_aberto = ptravel.status != "canceled" and ptravel.grupo.status != Grupo.Status.CANCELED
        if travel_em_aberto:
            return

    if pagamento.net_value is not None and pagamento.refunded_net_value is not None:
        value = pagamento.net_value - pagamento.refunded_net_value
        signals.evento_pagamento.send(
            None,
            pagamento=pagamento,
            value=value,
            evento=pagamento_status_svc.PAGAMENTO_CANCELADO,
        )


class ImpactosCancelamento:
    def __init__(self, travel):
        self.travels_remover_promocao = []
        self.cancelamentos_promocao = []
        self.travel = travel
        self._append_impactos_pricing()
        self._append_impactos_promocao()

    def aplicar(self):
        for travel in self.travels_remover_promocao:
            travel.remove_promocao()

    def _add_cancelar_promocao(
        self,
        travel: Travel,
        promocao,
        cupom,
        value=Decimal(0),
        remover_promo_travel=True,
    ):
        if travel and remover_promo_travel:
            self.travels_remover_promocao.append(travel)
        if value:
            self.cancelamentos_promocao.append(
                Bunch(
                    travel=travel,
                    promocao=promocao,
                    cupom=cupom,
                    value=value,
                )
            )

    def get_valor_promocao_cancelada(self, travel: Travel):
        cancelamentos = [c for c in self.cancelamentos_promocao if c.travel.id == travel.id]
        return cancelamentos[0].value if cancelamentos else 0

    def _append_impactos_pricing(self):
        # futuramente pode ter alguma promoção de pricing pra compra de ida e volta
        # mas, por enquanto, somente a viagem que tá sendo cancelada (self.travel)
        # que é afetada
        promo_pricing = self._promo_pricing()
        if not promo_pricing:
            return
        self._add_cancelar_promocao(
            travel=self.travel,
            value=-promo_pricing,
            promocao="PROMO_PRICING",
            cupom=None,
            remover_promo_travel=False,
        )
        promocao_cancelada_signal.send(None, travel=self.travel)

    def _append_impactos_promocao(self):
        if not self.travel.promocao:
            return
        promocao_statuses = promocao_svc.get_promocao_status(self.travel) or []
        for promo_status in promocao_statuses:
            self._impacto_promocao(promo_status)

    def _promo_realizado(self, travel: Travel):
        return accounting_svc.extrato_travel(travel.id)["promo_reais"]

    def _promo_pricing(self):
        return accounting_svc.extrato_travel(self.travel.id)["promo_pricing"]

    def _impacto_promocao(self, promo_status):
        travel, value = promo_status["travel"], promo_status["value"]
        promo_realizado = self._promo_realizado(travel)
        remover_promo_travel = not promo_status["promocao"]

        if value < promo_realizado:
            self._add_cancelar_promocao(
                travel=travel,
                value=value - promo_realizado,
                remover_promo_travel=remover_promo_travel,
                promocao=travel.promocao,
                cupom=travel.cupom,
            )

        elif travel.promocao and remover_promo_travel:
            self._add_cancelar_promocao(
                travel=travel,
                remover_promo_travel=True,
                promocao=travel.promocao,
                cupom=travel.cupom,
            )

        if not promo_realizado:
            buserlogger.warning(
                "impactos_promocao.travel_remanejada.bug_promocao_sem_accop_de_promocao",
                extra=dict(travel=travel.id),
            )

        promocao_cancelada_signal.send(None, travel=travel)


class ImpactosCancelamentoPax(ImpactosCancelamento):
    def __init__(self, pax):
        self.pax = pax
        promocao = promocao_svc.PROMOCAO_MAP.get(pax.travel.promocao)
        self.promo_por_pax = promocao and promocao.tipo_cancelamento == TipoCancelamento.POR_PAX
        super().__init__(pax.travel)

    def _promo_realizado(self, travel):
        if self.promo_por_pax:
            return round(
                accounting_svc.extrato_travel(travel.id)["promo_reais"] / travel.count_seats,
                2,
            )
        return super()._promo_realizado(travel)

    def _promo_pricing(self):
        return round(
            accounting_svc.extrato_travel(self.travel.id)["promo_pricing"] / self.travel.count_seats,
            2,
        )


def pagamento_confirmado_divida_callback(pagamento: Pagamento, value, **kwargs):
    user_cpf = pagamento.user.profile.cpf
    pagamento_cpf = pagamento.cpf
    accounting_svc.pagamento(pagamento)
    divida_svc.remove_persona_non_grata_se_quitou_divida(pagamento.cpf)
    divida_svc.remove_bloqueio_da_conta_se_pagou_divida(pagamento.user)
    if user_cpf and user_cpf == pagamento_cpf:
        user_notification_svc.pagamento_divida_confirmado(
            pagamento, evento="reserva_svc.pagamento_confirmado_divida_callback"
        )


def pagamento_parcial_divida_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.pagamento_parcial(pagamento, value)


def pagamento_extra_divida_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.pagamento_extra(pagamento, value)


def estorno_divida_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.estorno(pagamento, value)
    divida_svc.adiciona_non_grata_em_caso_de_estorno(pagamento.cpf)


def pagamento_confirmado_callback(
    pagamento: Pagamento | None,
    value: Decimal | None,
    travels: list[Travel] = [],
    is_pagamento_reserva: bool = True,
    **kwargs,
):
    travels = travels or (list(pagamento.travels.all()) if pagamento else [])
    emitir_passagens_async.delay([t.id for t in travels])

    if pagamento and value:
        user_notification_svc.payment_confirmed(pagamento=pagamento, evento="Reserva - pagamento confirmado")
        accounting_svc.pagamento_confirmado(pagamento, value)
        log_svc.pagamento_confirmado(pagamento)
        is_pagamento_reserva = pagamento.is_pagamento_reserva
        method = "cartao_credito" if pagamento.method == Pagamento.Method.CREDIT_CARD else pagamento.method
        buserlogger.info(f"pagamento.{method}.confirmed")
        amplitude_svc.create_event(pagamento.user, pagamento_status_svc.PAGAMENTO_CONFIRMADO)

    signals.evento_transacao_completa.send(
        None,
        pagamento=pagamento,
        evento=pagamento_status_svc.PAGAMENTO_CONFIRMADO,
        is_pagamento_reserva=is_pagamento_reserva,
    )

    concluir_itens_adicionais(travels, pagamento=pagamento)


def pagamento_cancelado_callback(pagamento, value=None, **kwargs):
    accounting_svc.pagamento_cancelado(pagamento, value)
    _cancelar_transacao(pagamento)


def _cancelar_transacao(pagamento: Pagamento):
    # só porque ainda não foi implementado cancelamento pro Stark
    if (
        pagamento.provider in Pagamento.MERCADOPAGO_PROVIDERS | {Pagamento.Provider.NUBANK}
        and pagamento.status in Pagamento.STATUSES_WAIT
    ):
        try:
            pagamento_svc.cancelar_transacao(pagamento)
        except Exception:
            buserlogger.error("_cancelar_transacao.error", extra={"pagamento_id": pagamento.id})


def cancelar_reservas_callback(pagamento: Pagamento, **kwargs):
    _cancela_reservas_futuras(pagamento)


def pagamento_parcial_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.pagamento_cancelado(pagamento)
    accounting_svc.pagamento_parcial(pagamento, value)


def pagamento_extra_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.pagamento_extra(pagamento, value)


def estorno_falhou_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.estorno_falhou(pagamento, value)


def estorno_callback(pagamento: Pagamento, value, **kwargs):
    accounting_svc.estorno(pagamento, value)


def chargeback_refund_callback(pagamento: Pagamento, **kwargs):
    value = accounting_svc.chargeback_refund_value(pagamento)
    accounting_svc.chargeback_refund(pagamento, value)


def _get_evento_pagamento_reserva_callback_map():
    return {
        pagamento_status_svc.PAGAMENTO_CONFIRMADO: pagamento_confirmado_callback,
        pagamento_status_svc.PAGAMENTO_CANCELADO: pagamento_cancelado_callback,
        pagamento_status_svc.PAGAMENTO_PARCIAL: pagamento_parcial_callback,
        pagamento_status_svc.PAGAMENTO_EXTRA: pagamento_extra_callback,
        pagamento_status_svc.ESTORNO_FALHOU: estorno_falhou_callback,
        pagamento_status_svc.ESTORNO: estorno_callback,
        pagamento_status_svc.CHARGEBACK_REFUND: chargeback_refund_callback,
        pagamento_status_svc.CANCELAR_RESERVAS: cancelar_reservas_callback,
    }


def _get_evento_pagamento_divida_callback_map():
    return {
        pagamento_status_svc.PAGAMENTO_CONFIRMADO: pagamento_confirmado_divida_callback,
        pagamento_status_svc.PAGAMENTO_PARCIAL: pagamento_parcial_divida_callback,
        pagamento_status_svc.PAGAMENTO_EXTRA: pagamento_extra_divida_callback,
        pagamento_status_svc.ESTORNO: estorno_divida_callback,
    }


@receiver(signals.evento_pagamento)
def handle_evento_pagamento_reserva(pagamento: Pagamento, evento, **kwargs):
    # Atualmente, infelizmente, não marcamos pagamento como reserva, então temos que excluir
    # os outros casos
    buserlogger.info(
        "reserva_svc.handle_evento_pagamento_reserva",
        extra={"pagamento_id": pagamento.id, "evento": evento},
    )

    if pagamento.is_pagamento_reserva:
        evento_callback_map = _get_evento_pagamento_reserva_callback_map()
    elif pagamento.is_divida:
        evento_callback_map = _get_evento_pagamento_divida_callback_map()
    else:
        return
    callback = evento_callback_map.get(evento)
    if not callback:
        return
    callback(pagamento, **kwargs)


def _purchase_validation_error(
    e,
    data,
    message: str = "Realize os ajustes necessários e tente realizar sua reserva novamente",
) -> tuple[str, list[str], dict]:
    error_data = getattr(e, "params") or {}
    error_message = error_str(e)
    log_svc.log_erro_na_reserva(error_message, data, error_data)
    return (
        error_message,
        [message],
        {
            "error_code": getattr(e, "code") or "validation_error",
            "payment_method": _get_payment_method(data),
        },
    )


def _cancela_reservas_futuras(pagamento: Pagamento):
    for travel in pagamento.travels.all():
        if _should_cancel_travel(travel):
            cancel_travel_sem_pagamento(travel.id, pagamento)

        # cancela reservas originadas da primeira
        travel_remanejada = remanejamento_svc.get_travel_remanejada(travel.id)
        while travel_remanejada:
            if _should_cancel_travel(travel_remanejada):
                cancel_travel_sem_pagamento(travel_remanejada.id, pagamento)
            travel_remanejada = remanejamento_svc.get_travel_remanejada(travel_remanejada.id)


def _should_cancel_travel(travel: Travel) -> bool:
    viagem_ainda_vai_acontecer = bool(
        travel.trecho_classe.datetime_ida and travel.trecho_classe.datetime_ida > dateutils.now()
    )
    pendente = travel.status == "pending"
    return viagem_ainda_vai_acontecer and pendente


def _purchase_mercadopago_api_error(e, data) -> tuple[str, list[str], dict]:
    log_svc.log_excecao_no_pagamento(e, data)
    extra = {
        "error_code": f"mp_api_error_{e.status_code}",
        "payment_method": _get_payment_method(data),
    }
    return e.reason, e.help, extra


def _purchase_lock_error(e, data) -> tuple[str, list[str], dict]:
    sentry_sdk.capture_exception(e)
    log_svc.log_excecao_no_pagamento(e, data)
    message = "Existe uma outra reserva do usuário sendo processada"
    return (
        message,
        ["Tente novamente em alguns minutos"],
        {"payment_method": _get_payment_method(data), "error_code": "lock_error"},
    )


def _purchase_rodoviaria_error(e, data) -> tuple[str, list[str], dict]:
    error_type = e.type if hasattr(e, "type") else None
    log_svc.log_erro_na_rodoviaria(e, error_str(e), error_type, data)
    return (
        e.message,
        [e.help],
        {"error_code": "rodoviaria_error", "payment_method": _get_payment_method(data)},
    )


def _purchase_payment_exception(e: PaymentException, data: dict | None) -> tuple[str, list, dict]:
    info = {"error_code": e.code}
    if data:
        info["payment_method"] = _get_payment_method(data)
    return e.code, [], info


def _log_reserva_error_com_informacoes_do_trecho(log_msg, e, data):
    trechos_ids = [t["id"] for t in data.get("groups", []) if t.get("id")]
    trechos_infos = TrechoClasse.objects.filter(id__in=trechos_ids).values(
        "id",
        "grupo_id",
        "trecho_vendido__origem__cidade__slug",
        "trecho_vendido__destino__cidade__slug",
        "grupo_classe__tipo_assento",
        "grupo__company_id",
        "grupo__company__name",
        "grupo__modelo_venda",
    )
    trechos_map = {t["id"]: t for t in trechos_infos}
    trechos_infos = {}
    if trechos_ids:
        trecho_ida = trechos_map[trechos_ids[0]]
        trechos_infos["origem"] = trecho_ida["trecho_vendido__origem__cidade__slug"]
        trechos_infos["destino"] = trecho_ida["trecho_vendido__destino__cidade__slug"]
        trechos_infos["grupo_ida"] = trecho_ida["grupo_id"]
        trechos_infos["tipo_assento_ida"] = trecho_ida["grupo_classe__tipo_assento"]
        trechos_infos["company_ida_id"] = trecho_ida["grupo__company_id"]
        trechos_infos["company_ida_name"] = trecho_ida["grupo__company__name"]
        trechos_infos["modelo_venda_ida"] = trecho_ida["grupo__modelo_venda"]
        trechos_infos["trecho_classe_ida"] = trecho_ida["id"]
    if len(trechos_ids) > 1:
        trecho_volta = trechos_map[trechos_ids[1]]
        trechos_infos["grupo_volta"] = trecho_volta["grupo_id"]
        trechos_infos["tipo_assento_volta"] = trecho_volta["grupo_classe__tipo_assento"]
        trechos_infos["company_volta_id"] = trecho_volta["grupo__company_id"]
        trechos_infos["company_volta_name"] = trecho_volta["grupo__company__name"]
        trechos_infos["modelo_venda_volta"] = trecho_volta["grupo__modelo_venda"]
        trechos_infos["trecho_classe_volta"] = trecho_volta["id"]
    extra_infos = {"err_msg": e.message}
    if trechos_infos:
        extra_infos["jsondata"] = json.dumps(trechos_infos)
    buserlogger.error(log_msg, extra=extra_infos)


def _get_payment_method(data: dict) -> str:
    """Evita qualquer erro ao tentar recuperar o payment_method
    do payload do pagamento para o tratamento de erros da purchase
    """
    try:
        return data["payment"]["payment_method"]
    except Exception:
        return ""


def _purchase_payment_error(e: PaymentError, data) -> tuple[str, list[str], dict]:
    reason = e.get_reason()
    log_svc.log_erro_no_pagamento(reason, data, e.transaction or {}, e.params)
    return (
        reason,
        e.get_help(),
        {
            "fallback_provider": e.fallback_provider,
            "title": e.get_title(),
            "error_code": e.get_error_code(),
            "payment_method": e.payment_method,
        },
    )


def _purchase_desconhecido_error(e: Exception, data) -> tuple[str, list[str], dict]:
    log_svc.log_excecao_no_pagamento(e, data, error_str="unknown")
    return (
        "Houve um problema desconhecido no pagamento",
        ["Tente novamente em alguns minutos"],
        {"error_code": "unknown_error", "payment_method": _get_payment_method(data)},
    )


_30min = 60 * 30


@notagainfor(_30min, key="reserva_pax_vip_send_message")
def reserva_pax_vip_send_message():
    _now = now()
    thirty_minutes_ago = _now - timedelta(minutes=30)
    cpf_vip_query = get_tag_cpfs_subquery("vip")

    qs = (
        Passageiro.objects.filter(
            travel__created_on__gte=_now - timedelta(minutes=32),
            buseiro__cpf__in=Subquery(cpf_vip_query),  # Usando a subquery para verificar CPFs VIP
        )
        .exclude(travel__status="canceled", travel__grupo__status__in=["canceled", "done"])
        .values(
            "travel__grupo_id",
            "travel__user__profile__fullname",
            "travel__user__first_name",
            "travel__user__last_name",
        )
        .distinct()
    )

    if qs:
        context = {
            "objects": [
                {
                    "grupo_id": obj["travel__grupo_id"],
                    "fullname": obj["travel__user__profile__fullname"]
                    or f"{obj['travel__user__first_name']} {obj['travel__user__last_name']}",
                }
                for obj in qs
            ],
            "thirty_minutes_ago": thirty_minutes_ago,
            "now": _now,
        }
        NotificationBuilder(
            toemail=settings.RESERVA_VIP_EMAIL,
            template="notificacao/staff/reserva_pax_vip_message",
            ctx=context,
        ).send(channels={"email"})


def validate_is_travel_compartilhada(travel: dict, user) -> bool:
    for pax in travel["passengers"]:
        if pax["linked_user"] == user:
            return True
    return False


def validate_remarcacao_gratuita(remanejamento: RemanejamentoForm, user_id: int) -> tuple[str | None, Any]:  # noqa
    if globalsettings_svc.get("remarcacao_gratuita_enabled") is False:
        raise ValidationError("Remarcação gratuita desabilitada")

    travel = Travel.objects.select_related(
        "grupo__rota__origem",
        "grupo__rota__destino",
        "trecho_vendido",
        "trecho_classe",
        "grupo_classe",
        "grupo",
        "pagamento",
    ).get(pk=remanejamento.travel_id)
    if travel.user_id != user_id:
        raise ValidationError("Usuário diferente do usuário da viagem")

    trecho_classe = TrechoClasse.objects.select_related("grupo__rota__origem", "grupo__rota__destino").get(
        pk=remanejamento.trecho_classe_destino
    )
    if travel.trecho_vendido.origem.cidade_id != trecho_classe.trecho_vendido.origem.cidade_id:
        raise ValidationError("A cidade de origem diferente da original")
    if travel.trecho_vendido.destino.cidade_id != trecho_classe.trecho_vendido.destino.cidade_id:
        raise ValidationError("A cidade de destino diferente da original")
    if travel.trecho_classe == trecho_classe:
        raise ValidationError("Não é possível remarcar para o mesmo trecho classe")
    if trecho_classe.grupo.status == Grupo.Status.CANCELED:
        raise ValidationError("Esta viagem não está mais disponível para remarcação")
    if trecho_classe.grupo.modelo_venda != Grupo.ModeloVenda.BUSER:
        raise ValidationError("O modelo de venda do grupo escolhido não é válido para remarcação gratuita")

    range_aceitavel_de_datas = (
        cast(datetime, travel.trecho_classe.datetime_ida) - timedelta(days=10),
        cast(datetime, travel.trecho_classe.datetime_ida) + timedelta(days=10),
    )
    if (
        trecho_classe.datetime_ida < range_aceitavel_de_datas[0]
        or trecho_classe.datetime_ida > range_aceitavel_de_datas[1]
    ):
        raise ValidationError("A data da viagem não está dentro do prazo para remarcação gratuita")

    capacity_manager = multitrecho_svc.prepare_capacity_manager_from_grupo(trecho_classe.grupo)
    vagas = capacity_manager.vagas(trecho_classe)
    if vagas < travel.count_seats:
        raise ValidationError(f"Esta viagem possui apenas {pluralize(vagas, 'vaga')}")

    remarcar_gratis_dict = pode_remarcar_gratuitamente(travel)
    if remarcar_gratis_dict["pode_remarcar_gratis"] is False:
        raise ValidationError("A viagem não atende aos requisitos para remarcação gratuita")

    return travel.reservation_code, remarcar_gratis_dict["motivos"]


def solicitar_ressarcimento(travel_id: int, data: SolicitacaoRessarcimentoForm, comprovantes: list) -> None:
    travel = Travel.objects.get(id=travel_id)
    if not pode_solicitar_ressarcimento_automatico(travel):
        raise ValidationError("Não é possível solicitar ressarcimento para esta viagem")

    with transaction.atomic():
        solicitacao = SolicitacaoRessarcimento(
            travel=travel,
            valor=data.value,
            categoria=data.category,
        )
        solicitacao.save()
        for comprovante in comprovantes:
            comprovante = SolicitacaoRessarcimentoComprovante(solicitacao=solicitacao, file=comprovante)
            comprovante.save()

    julgar_solicitacao_ressarcimento(solicitacao)


def julgar_solicitacao_ressarcimento(solicitacao: SolicitacaoRessarcimento):
    if solicitacao.valor > MAX_ALLOWED_RESSARCIMENTO_VALUE:
        solicitacao.negar()
        raise ValidationError("Não é possível solicitar ressarcimento para esta viagem")

    solicitacao.pronto_para_pagar()
    solicitacao.save()


def get_travel_ressarcivel(user) -> int | None:
    travels_recentes = Travel.objects.filter(user=user, grupo__datetime_ida__gte=now() - timedelta(weeks=4)).order_by(
        "-id"
    )
    for travel in travels_recentes:
        if pode_solicitar_ressarcimento_automatico(travel):
            return travel.id


def pode_solicitar_ressarcimento_automatico(travel: Travel) -> bool:
    """
    verifica se o grupo teve incidente, ele não pediu ressarcimento pelos ultimos X dias e ele fez checking no grupo
    """
    passengers = list(travel.passageiro_set.all())
    pax_list_id = [p.id for p in passengers]

    last_month = now() - timedelta(weeks=4)
    incidents = groups_with_incidents(last_month, pax_list_auto_reimbursement=pax_list_id, grupo_ids=[travel.grupo_id])
    ressarciu_automaticamente = pax_ressarciu_automaticamente_100_ultimos_dias(passengers)
    travel_esta_ressarcida = verifica_se_travel_foi_ressarcida(travel)

    if incidents.get("count", 0) > 0 and not ressarciu_automaticamente and not travel_esta_ressarcida:
        return True

    return False


def verifica_se_travel_foi_ressarcida(travel: Travel) -> bool:
    return Ressarcimento.objects.filter(
        accounting_operation__travel=travel,
        accounting_operation__source__in=SOURCE_RESSARCIMENTOS,
    ).exists()


def pax_ressarciu_automaticamente_100_ultimos_dias(pax_list: list[Passageiro]) -> bool:
    pax_document_list = [pax.buseiro.cpf for pax in pax_list]
    solicitou_anteriormente = SolicitacaoRessarcimento.objects.filter(
        travel__passageiro__buseiro__cpf__in=pax_document_list,
        created_at__gte=now() - timedelta(days=100),
    ).exists()

    return solicitou_anteriormente


def pode_remarcar_gratuitamente(travel: Travel) -> dict:  # noqa
    status_autorizados = ["viagem_confirmada", "embarque_proximo", "onibus_em_transito"]
    historico_viagem = travel_svc.get_historico_viagem(travel)
    current_status = travel_svc.get_current_travel_status(historico_viagem)
    pode_remarcar_gratis = False
    motivos = []
    tipo_assento_novo = ""
    tipo_assento_antigo = ""
    downgrade_dict = {}
    modelo_venda_diferente_de_buser = travel.grupo.modelo_venda != Grupo.ModeloVenda.BUSER

    if current_status["status"] not in status_autorizados:
        return RemarcarGratis(
            pode_remarcar_gratis=pode_remarcar_gratis,
            motivos=motivos,
        ).dict()

    if modelo_venda_diferente_de_buser:
        tz_cidade_origem = travel.trecho_vendido.origem.cidade.timezone
        cancellation_deadline = to_tz_required(travel.get_cancellation_deadline(), tz_cidade_origem)
        datetime_atual = to_tz_required(now(), tz_cidade_origem)
        if datetime_atual > cancellation_deadline:
            return RemarcarGratis(
                pode_remarcar_gratis=pode_remarcar_gratis,
                motivos=motivos,
            ).dict()

    if not modelo_venda_diferente_de_buser and _has_atraso_maior_que_meia_hora(travel.trecho_classe):
        motivos.append("atraso")

    if not _is_viagem_futura(travel) and "atraso" not in motivos:
        return RemarcarGratis(
            pode_remarcar_gratis=False,
            motivos=motivos,
        ).dict()

    historico_remanejamento = (
        HistoricoRemanejamento.objects.filter(
            travel_nova=travel,
            created_at__gt=cast(datetime, travel.trecho_classe.datetime_ida) - timedelta(hours=72),
        )
        .exclude(motivo="REMARCACAO_GRATUITA")
        .first()
    )
    travel_antiga = travel_svc.get_travel_original(travel)

    if travel_antiga.grupo.modelo_venda != Grupo.ModeloVenda.BUSER:
        return RemarcarGratis(
            pode_remarcar_gratis=pode_remarcar_gratis,
            motivos=motivos,
        ).dict()

    downgrade_dict = _infos_downgrade(travel_nova=travel, travel_antiga=travel_antiga)
    if (
        downgrade_dict
        and DOWNGRADE_FATOR_TIPO_ASSENTO[downgrade_dict["tipo_assento_novo"]]
        < DOWNGRADE_FATOR_TIPO_ASSENTO[downgrade_dict["tipo_assento_antigo"]]
    ):
        motivos.append("downgrade")
        tipo_assento_novo = downgrade_dict["tipo_assento_novo"]
        tipo_assento_antigo = downgrade_dict["tipo_assento_antigo"]

    has_boarding_changes = _has_local_changes(
        travel_nova=travel,
        travel_antiga=travel_antiga,
        is_origem=True,
        historico_remanejamento=historico_remanejamento,
    )
    if has_boarding_changes:
        motivos.append("alteracao_local_embarque")

    has_destination_changes = _has_local_changes(
        travel_nova=travel,
        travel_antiga=travel_antiga,
        is_origem=False,
        historico_remanejamento=historico_remanejamento,
    )
    if has_destination_changes:
        motivos.append("alteracao_local_desembarque")

    has_alteracao_horario_embarque = _has_alteracao_horario_embarque(
        travel_nova=travel,
        travel_antiga=travel_antiga,
        historico_remanejamento=historico_remanejamento,
    )
    if has_alteracao_horario_embarque:
        motivos.append("alteracao_horario_embarque")

    has_alteracao_duracao_ida = _has_alteracao_duracao_ida(
        travel_nova=travel,
        travel_antiga=travel_antiga,
        historico_remanejamento=historico_remanejamento,
    )
    if has_alteracao_duracao_ida:
        motivos.append("alteracao_duracao_ida")

    if motivos:
        pode_remarcar_gratis = True

    return RemarcarGratis(
        pode_remarcar_gratis=pode_remarcar_gratis,
        motivos=motivos,
        tipo_assento_novo=tipo_assento_novo,
        tipo_assento_antigo=tipo_assento_antigo,
    ).dict()


def _is_viagem_futura(travel: Travel) -> bool:
    return bool(travel.trecho_classe.datetime_ida and travel.trecho_classe.datetime_ida > dateutils.now())


def _infos_downgrade(travel_nova: Travel, travel_antiga: Travel):
    if travel_antiga != travel_nova:
        tipo_assento_antigo = travel_antiga.grupo_classe.tipo_assento
        tipo_assento_novo = travel_nova.grupo_classe.tipo_assento
        data_alteracao = travel_nova.created_on
    else:
        alteracao = (
            AlteracaoTravel.objects.filter(travel=travel_nova, tipo=AlteracaoTravel.TipoAlteracao.MUDANCA_DE_CLASSE)
            .order_by("created_at")
            .first()
        )
        if not alteracao:
            return {}
        tipo_assento_antigo = alteracao.antigo_tipo_assento
        tipo_assento_novo = travel_nova.grupo_classe.tipo_assento
        data_alteracao = alteracao.created_at
    return {
        "tipo_assento_antigo": tipo_assento_antigo,
        "tipo_assento_novo": tipo_assento_novo,
        "data_alteracao": data_alteracao,
    }


def verifica_downgrade(travel: Travel) -> dict:
    travel_antiga = travel_svc.get_travel_original(travel)
    downgrade_dict = _infos_downgrade(travel_nova=travel, travel_antiga=travel_antiga)
    if not downgrade_dict:
        return {}
    if (
        DOWNGRADE_FATOR_TIPO_ASSENTO[downgrade_dict["tipo_assento_novo"]]
        < DOWNGRADE_FATOR_TIPO_ASSENTO[downgrade_dict["tipo_assento_antigo"]]
    ):
        downgrade_dict["downgrade"] = True
        return downgrade_dict
    return {}


def _has_local_changes(
    travel_nova: Travel,
    travel_antiga: Travel,
    is_origem: bool,
    historico_remanejamento: HistoricoRemanejamento | None = None,
) -> bool | None:
    local_id = travel_nova.trecho_vendido.origem_id if is_origem else travel_nova.trecho_vendido.destino_id

    if historico_remanejamento:
        if is_origem:
            return travel_antiga.trecho_vendido.origem != travel_nova.trecho_vendido.origem
        return travel_antiga.trecho_vendido.destino != travel_nova.trecho_vendido.destino
    else:
        return HistoricoAlteracaoEmbarque.objects.filter(
            grupo_novo__id=travel_nova.grupo_id,
            local_novo__id=local_id,
            created_at__gt=cast(datetime, travel_nova.trecho_classe.datetime_ida) - timedelta(hours=72),
        ).exists()


def _has_alteracao_horario_embarque(
    travel_nova: Travel,
    travel_antiga: Travel,
    historico_remanejamento: HistoricoRemanejamento | None = None,
) -> bool:
    TRINTA_MINUTOS_EM_SEGUNDOS = 1800
    novo_datatime_ida = cast(datetime, travel_nova.trecho_classe.datetime_ida)
    velho_datatime_ida = cast(datetime, travel_antiga.trecho_classe.datetime_ida)

    trinta_min_diferenca_remanejamento = (
        abs((novo_datatime_ida - velho_datatime_ida).total_seconds()) >= TRINTA_MINUTOS_EM_SEGUNDOS
    )

    if historico_remanejamento and trinta_min_diferenca_remanejamento:
        return True

    alteracao = (
        AlteracaoTravel.objects.filter(
            travel=travel_nova,
            tipo=AlteracaoTravel.TipoAlteracao.MUDANCA_DE_HORARIO,
            created_at__gt=cast(datetime, travel_nova.trecho_classe.datetime_ida) - timedelta(hours=72),
        )
        .order_by("created_at")
        .first()
    )

    trinta_min_diferenca_alteracao = (
        alteracao is not None
        and abs((cast(datetime, alteracao.antigo_horario) - cast(datetime, alteracao.novo_horario)).total_seconds())
        >= TRINTA_MINUTOS_EM_SEGUNDOS
    )

    return trinta_min_diferenca_alteracao


def _has_alteracao_duracao_ida(
    travel_nova: Travel,
    travel_antiga: Travel,
    historico_remanejamento: HistoricoRemanejamento | None = None,
) -> bool | None:
    if historico_remanejamento:
        TRINTA_MINUTOS_EM_SEGUNDOS = 1800
        duracao_ida_antiga = travel_antiga.trecho_classe.duracao_ida or timedelta(0)
        duracao_ida_nova = travel_nova.trecho_classe.duracao_ida or timedelta(0)
        alteracao_maior_30min = abs(duracao_ida_antiga - duracao_ida_nova).total_seconds() >= TRINTA_MINUTOS_EM_SEGUNDOS
        alteracao_dentro_das_72_horas = cast(datetime, historico_remanejamento.created_at) > cast(
            datetime, travel_nova.trecho_classe.datetime_ida
        ) - timedelta(hours=72)
        return alteracao_maior_30min and alteracao_dentro_das_72_horas


def get_incentivo_remarcacao(travel_id) -> str:
    travel = Travel.objects.select_related("trecho_classe__trecho_vendido__origem__cidade", "user", "grupo").get(
        pk=travel_id
    )

    remarcacao_gratuita = pode_remarcar_gratuitamente(travel)

    if remarcacao_gratuita.get("pode_remarcar_gratis"):
        return "remarcacao_gratuita"

    if _viagem_nas_proximas_24_horas(travel.trecho_classe):
        return "viagem_proxima"

    if travel.voucher_id or travel.cupom_id:
        return "cupom"

    if fidelidade_svc.travel_contabilizaria_no_programa_fidelidade(travel):
        return "fidelidade"

    return ""


def _viagem_nas_proximas_24_horas(trecho_classe) -> bool:
    trecho_classe_tz = trecho_classe.trecho_vendido.origem.cidade.timezone
    datetime_ida = to_tz_required(trecho_classe.datetime_ida, trecho_classe_tz)
    _now = to_tz_required(dateutils.now(), trecho_classe_tz)
    if datetime_ida < _now:
        return False
    _DAQUI_24_HORAS = _now + timedelta(hours=24)
    if datetime_ida <= _DAQUI_24_HORAS:
        return True
    return False


def transform_groups_into_trechos_ida_trechos_volta(data: dict, is_many_trechos_ida_volta: bool = False):
    # after that point we will separate the groups into trechos_ida and trechos_volta
    groups = data.pop("groups")

    if is_many_trechos_ida_volta:
        data["trechos_ida"] = groups.get("trechos_ida")
        data["trechos_volta"] = groups.get("trechos_volta", [])
        unhashid_grupos(data["trechos_ida"])
        unhashid_grupos(data["trechos_volta"])
        data["has_trecho_conexao"] = len(data["trechos_ida"]) > 1 or len(data["trechos_volta"]) > 1

        return

    if len(groups) > 2:
        raise ValidationError("BUG! We don't expect more than 2 groups on the endpoints")

    data["has_trecho_conexao"] = any(":" in grupo["id"] for grupo in groups)

    trechos_ida = groups[0]
    trechos_volta = groups[1] if len(groups) > 1 else None
    trechos_ida = _split_unhash_trechos(trechos_ida)
    trechos_volta = _split_unhash_trechos(trechos_volta) if trechos_volta else []

    data["trechos_ida"] = trechos_ida
    data["trechos_volta"] = trechos_volta


def _split_unhash_trechos(trecho: dict) -> list[dict]:
    id_trecho = trecho.pop("id")
    hashed_groups = id_trecho.split(":")
    splitted_trechos = [{"id": gid} for gid in hashed_groups]
    unhashid_grupos(splitted_trechos)

    if trecho:
        signed = trecho.pop("signed", None)
        if signed:
            if isinstance(signed, dict):
                splitted_trechos[0]["signed"] = signed["ida"]
                splitted_trechos[1]["signed"] = signed["volta"]
            else:
                if len(splitted_trechos) > 1:
                    sentry_sdk.set_context(
                        "Trecho",
                        {"id": id_trecho, "signed": signed, "extra_data": trecho},
                    )
                    sentry_sdk.capture_message("We received a trecho with a wrong signed data", "error")
                splitted_trechos[0]["signed"] = signed

        # if trecho has more information we should copy to splitted groups
        for splitted_trecho in splitted_trechos:
            splitted_trecho.update(trecho)

    return splitted_trechos


def get_travel_user(user, dados_reserva):
    if not user.is_authenticated:
        return user
    if not guard.is_revendedor(user):
        return user
    if not dados_reserva.get("user_revenda"):
        return user
    user_revenda = User.objects.get(pk=dados_reserva["user_revenda"])
    if not user_revenda:
        return user
    return user_revenda


def generate_key_for_reserva(reserva):
    if not reserva.input_payload:
        return str(reserva.user.id)

    keys = (
        [g.get("id", "") for g in reserva.input_payload.get("groups", [])]
        + [t.get("id", "") for t in reserva.input_payload.get("trechos_ida", [])]
        + [t.get("id", "") for t in reserva.input_payload.get("trechos_volta", [])]
        + [(p.get("cpf", "") or "")[-6:] for p in reserva.input_payload.get("passengers", [])]
    )

    key = "-".join(sorted(map(str, filter(None, keys))))

    return f"{reserva.user.id}-{key}"


@my_shared_task(queue="rodoviaria_emissao_async")
@traced("reserva_svc.emitir_passagens_async")
@lock("emitir_passagens_async_{travel_ids}", max_wait_time=0, except_timeout=True, expire=15 * 60)
def emitir_passagens_async(travel_ids: list[int]):
    buserlogger.info("Emitindo passagens async", extra={"travel_ids": travel_ids})

    travels = Travel.objects.filter(id__in=travel_ids)
    if not (travels and travels[0].reserva):
        return

    if rodoviaria_reserva_svc.passagens_emitidas(list(travels)):
        return

    user = travels[0].user
    reserva = travels[0].reserva
    try:
        emitir_passagens_rodoviaria_retry_wrapper(user, reserva, list(travels))
    except Exception as e:
        buserlogger.info(
            f"[rodoviaria_reserva_apos_pgto] emitir_passagens_rodoviaria_retry_wrapper error={error_str(e)}",
            extra={
                "user": user,
                "reserva_id": reserva.id,
                "travels": travels,
                "exception": e,
            },
        )
        handle_erro_emissao(travels, e)


def handle_erro_emissao(travels, e):
    rodoviaria_svc.handle_rodoviaria_after_error(e)

    if travels[0].revendedor_user_id:
        travels[0].reserva.set_emissao_revendedor()
        return

    success = cancel_travel_erro_emissao_async(travels, e)
    if success and travels[0].pagamento_id:
        try:
            estorno_svc.estornar(travels[0].user, [travels[0].pagamento_id])
        except Exception as err:
            sentry_sdk.capture_exception(err)


@retry(
    retry=retry_if_not_exception_type(RodoviariaViagemBloqueada),
    wait=wait_random(5, 50),
    stop=stop_after_attempt(4),
    reraise=True,
)
def emitir_passagens_rodoviaria_retry_wrapper(user: User | None, reserva: Reserva, travels: list[Travel]):
    rodoviaria_reserva_svc.emitir_passagens_rodoviaria_passagem_unica(user, reserva, travels)


@traced("reserva_svc._validar_e_processar_trecho_classe")
def _validar_e_processar_trecho_classe(input_data):
    for i, trecho in enumerate(input_data.get("trechos_ida", [])):
        input_data["trechos_ida"][i] = _get_and_assign_trecho_classe(trecho)

    for i, trecho in enumerate(input_data.get("trechos_volta", [])):
        input_data["trechos_volta"][i] = _get_and_assign_trecho_classe(trecho)


def _get_and_assign_trecho_classe(trecho_classe: dict):
    tc_id_antigo = trecho_classe.get("id")
    tc = TrechoClasse.objects_all.prefetch_related("price_manager__buckets").get(pk=tc_id_antigo)

    if not (tc and tc.closed and tc.closed_reason == ClosedReasons.CLEANUP_OLD_CLASSES):
        return trecho_classe

    trecho_classe_novo = (
        TrechoClasse.objects.prefetch_related("price_manager__buckets")
        .select_related("grupo", "grupo_classe", "trecho_vendido")
        .exclude(pk=tc_id_antigo)
        .filter(
            datetime_ida=tc.datetime_ida,
            grupo_classe__tipo_assento=tc.grupo_classe.tipo_assento,
            trecho_vendido_id=tc.trecho_vendido_id,
            grupo_classe__closed=False,
        )
        .first()
    )
    if not trecho_classe_novo:
        buserlogger.info(
            "Trecho fechado: %s, trecho alternativo não encontrado.",
            tc_id_antigo,
        )
        raise ValidationError("Este grupo não possui mais vagas")

    preco_visto = tc.max_split_value_bucket

    preco_max_bucket = trecho_classe_novo.max_split_value_bucket
    preco_ref_value_bucket = trecho_classe_novo.ref_split_value_bucket

    diferenca = abs(preco_max_bucket - preco_visto)
    if diferenca > 0:
        buserlogger.info(
            "Trecho fechado: %s, trecho alternativo %s diverge preço.",
            tc_id_antigo,
            trecho_classe_novo.id,
        )
        raise ValidationError("Este grupo não possui mais vagas com este preço")

    sign = signer.sign_object(
        {
            "id": trecho_classe_novo.id,
            "max_split_value": preco_max_bucket,
            "ref_split_value": preco_ref_value_bucket,
        }
    )

    trecho_classe_novo_dict = {
        "id": trecho_classe_novo.id,
        "signed": sign,
    }

    buserlogger.info(
        "Trecho fechado: %s, usando trecho alternativo: %s.",
        tc_id_antigo,
        trecho_classe_novo.id,
    )

    return trecho_classe_novo_dict
