from django.core.management.base import BaseCommand
from django.db.models import Exists, OuterRef

from commons.dateutils import now, timedelta
from core import tasks
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse


class Command(BaseCommand):
    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        _now = now()
        yesterday = _now - timedelta(days=1)
        _35_days_ahead = _now + timedelta(days=35)

        trecho_exists = TrechoClasse.objects.filter(grupo_id=OuterRef("id"), pessoas__gt=0).exclude(closed=True)
        classe_exists = GrupoClasse.objects.filter(grupo_id=OuterRef("id")).exclude(closed=True)

        grupos_ids = (
            Grupo.objects.annotate(has_valid_trecho=Exists(trecho_exists))
            .annotate(has_valid_classe=Exists(classe_exists))
            .filter(
                has_valid_trecho=True,
                has_valid_classe=True,
                datetime_ida__range=(yesterday, _35_days_ahead),
                modelo_venda="buser",
                onibus__isnull=False,
            )
            .exclude(status="canceled")
            .values_list("id", flat=True)
        )
        for gid in grupos_ids.iterator():
            tasks.cria_atualiza_grupo_cenario_task.delay(gid)
