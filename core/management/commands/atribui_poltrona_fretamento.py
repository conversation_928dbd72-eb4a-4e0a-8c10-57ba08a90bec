import logging
from collections import defaultdict
from datetime import timedelta

from django.core.management import BaseCommand
from django.db.models import Prefetch

from commons.dateutils import now
from core.models_grupo import Grupo
from core.models_travel import Passageiro, Travel
from core.service.selecao_assento.fretamento import FretamentoSeatController

buserlogger = logging.getLogger("buserlogger")


class Command(BaseCommand):
    help = "Atribui poltrona de fretamento aos pax que ainda não possuem antes da viagem"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options) -> None:
        # Este comando fará a atribuição de poltronas aos passageiros
        # que não possuem poltronas atribuidas em viagens de fretamento
        # que estão com marcação liberada

        _now = now()

        travels = (
            Travel.objects.select_related("trecho_classe")
            .prefetch_related(
                Prefetch("passageiro_set", queryset=Passageiro.objects.filter(removed=False, poltrona__isnull=True)),
            )
            .filter(
                trecho_classe__grupo__onibus__isnull=False,
                trecho_classe__grupo__onibus__poltronas__isnull=False,
                trecho_classe__grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
                trecho_classe__datetime_ida__range=(_now, _now + timedelta(hours=24)),
                passageiro__poltrona__isnull=True,
            )
            .exclude(status=Travel.Status.CANCELED)
            .order_by("-count_seats", "trecho_classe__datetime_ida")
            .distinct()
        )
        travels_by_trecho_classe = defaultdict(list)
        for travel in travels:
            travels_by_trecho_classe[travel.trecho_classe].append(travel)

        for trecho_classe, travels_list in travels_by_trecho_classe.items():
            seat_controller = FretamentoSeatController(trecho_classe)
            buserlogger.info(
                "Encontradas %s travels para atribuir poltronas no trecho_classe %s",
                len(travels_list),
                trecho_classe.id,
            )
            for travel in travels_list:
                if seat_controller.has_marcacao_assento():
                    passageiros = travel.passageiro_set.all()
                    if not passageiros:
                        continue
                    seat_controller.atribui_poltronas(list(passageiros))
