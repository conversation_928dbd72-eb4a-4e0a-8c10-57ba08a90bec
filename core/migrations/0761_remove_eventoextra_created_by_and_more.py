# Generated by Django 4.1.13 on 2025-07-12 14:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0760_eventoextra_eventoextrasolicitacao_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="eventoextra",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="eventoextranegociacao",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="eventoextrasolicitacao",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="eventoextrasolicitacaoperna",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextra",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextranegociacao",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextrasolicitacao",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="historicaleventoextrasolicitacaoperna",
            name="created_by",
        ),
        migrations.AlterField(
            model_name="eventoextranegociacao",
            name="breakeven",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
        migrations.AlterField(
            model_name="eventoextranegociacao",
            name="cash_in_gmv",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
        migrations.AlterField(
            model_name="eventoextrasolicitacao",
            name="breakeven_esperado",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
        migrations.AlterField(
            model_name="historicaleventoextranegociacao",
            name="breakeven",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
        migrations.AlterField(
            model_name="historicaleventoextranegociacao",
            name="cash_in_gmv",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
        migrations.AlterField(
            model_name="historicaleventoextrasolicitacao",
            name="breakeven_esperado",
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
        ),
    ]
