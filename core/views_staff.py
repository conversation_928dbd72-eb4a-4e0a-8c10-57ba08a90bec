import csv
import hashlib
import json
import logging
import re
from dataclasses import asdict
from datetime import datetime, timedelta
from decimal import Decimal
from http import HTTPStatus
from io import TextIOWrapper

from beeline import traced
from django.conf import settings
from django.contrib.auth.models import User
from django.core.exceptions import SuspiciousOperation, ValidationError
from django.core.paginator import EmptyPage, Paginator
from django.db import IntegrityError, models, transaction
from django.db.models import Count, prefetch_related_objects
from django.http import HttpRequest, HttpResponse
from django.http.response import HttpResponseNotFound, JsonResponse
from django.shortcuts import get_object_or_404, redirect, resolve_url
from django.utils import timezone
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_GET, require_http_methods, require_POST
from pydantic.v1 import ValidationError as ValidationErrorPydantic
from pydantic.v1.dataclasses import dataclass
from requests import HTTPError, JSONDecodeError
from sentry_sdk import capture_exception

import core.service.localadm_svc
import core.service.profile_svc
from accounting.service import accounting_svc
from buser import roles
from buser.types import AuthenticatedHttpRequest
from commons import feature_flags, sns_helper, storage
from commons.async_task import idempotent_make_async_task, make_async_task
from commons.django_model_utils import get_or_none
from commons.django_utils import error_str, get_client_ip
from commons.django_views_utils import (
    ajax_can_create_coupon,
    ajax_can_edit_communication_template,
    ajax_can_give_reais,
    ajax_legal_required,
    ajax_permission_required,
    ajax_roles_required,
    ajax_seguranca_required,
    ajax_staff_required,
    api_key_required,
    cache_page,
    no_toast,
    owner,
    public_endpoint,
)
from commons.guard import is_rotas
from commons.keyset_paginator import KeysetPaginator
from commons.phone_helper import validate_phone
from commons.redis import get_master_client
from commons.squads import SQUAD_AQUISICAO
from commons.storage import BuserDataStorage
from commons.utils import chunks, is_valid_cpf, pluralize, unhashint
from core.forms.buckets_forms import (
    AlterarPrecosClasseAutomaticaForm,
    AlterarPrecosForm,
)
from core.forms.company_forms import CreateOrUpdateCompanyProfileForm, EmprestarDinheiroForm
from core.forms.feriado_forms import PaginaFeriadoForm
from core.forms.promo_forms import PromoForm
from core.forms.staff_forms import (
    AbrirTrechosForm,
    AlterarAutorizacaoGrupoHibrido,
    AlterarClassesForm,
    AlterarDuracoesForm,
    AlterarHiddenForPaxStatus,
    AlteraRotaDeGruposForm,
    AnnotationForm,
    AnnotationMultipleForm,
    AtualizacaoPassagemRodoviariaForm,
    AtualizaPermissaoTaxaServicoCheckoutForm,
    BaseParadaListForm,
    BasePontoDeVendaForm,
    BaseSendWhatsappTemplateForm,
    BaseSendWhatsappTemplateFormV2,
    BulkRemoveBusForm,
    BulkRemoveDriversForm,
    BypassRestrictionForm,
    CadastrarGruposHibridosForm,
    CancelarGrupoForm,
    CategoriaTuristicaEditForm,
    CategoriaTuristicaForm,
    CheckBusConflictForm,
    CheckPaxForm,
    CheckUserInfoForm,
    CidadeCategoriaTuristicaForm,
    CidadeForm,
    CidadeInfoForm,
    CompanyIdForm,
    ConexaoCreateForm,
    ConexaoUpdateForm,
    ConfiguracaoPagamentoForm,
    CreateCheckpointParadaListForm,
    CreateGroupForm,
    CreateRodoviariaCompanyForm,
    CreateRotaForm,
    CriarGruposMarketplaceForm,
    CriarRotaHibridoForm,
    EditarRepasseTaxaServicoForm,
    EditRotaForm,
    EscalarEmpresaOnibusRotinaForm,
    EventoExtraDetailsFilterForm,
    EventoExtraForm,
    EventoExtraListParamsForm,
    EventoExtraNegociacaoForm,
    EventoExtraSolicitacaoForm,
    FecharTrechosForm,
    FinishGroupForm,
    GerarPagamentoDividaForm,
    GetAvisosParceirosForm,
    GetCidadeForm,
    GetHiddenPaxForStatusForm,
    HelpQuestionIndexUpdateForm,
    HistoricoAlteracaoEmbarqueForm,
    IndicarMultaDjangoForm,
    IndicarMultaTorreForm,
    KeysetPaginatorForm,
    LandingPageForm,
    Liberar1MotoraForm,
    ListaEmpresasAPIParams,
    ListaFormasPagamentoRodoviariaForm,
    ListBuseiroTravelsForm,
    ListBusUnsupportedLocalForm,
    ListCidadesForm,
    ListCompaniesForm,
    ListDriversForm,
    ListLocaisForm,
    ListLocaisSelectOptionsForm,
    ListLociasSimpleForm,
    ListLogsForm,
    ListOnibusPlacaForm,
    ListPaxViagemRodoviariaForm,
    ListPontosDeVendaForm,
    ListRestrictionsAlternativeForm,
    ListRestrictionsForm,
    ListRodoviariaCompaniesForm,
    ListRotasForm,
    ListRotasPrincipaisSelectOptionsForm,
    ListRotasSelectOptionsForm,
    ListSimpleCompaniesForm,
    ListUserAccountingOperationsForm,
    ListUserCommunicationLogsForm,
    LocalEmbarqueForm,
    LocalRetiradaMarketplaceForm,
    LocalRetiradaMarketplaceIdForm,
    NewIncidentsGeneralGroupInfosForm,
    NotificationForm,
    PagamentosDividaPendentesForm,
    PaginatorForm,
    ParametrosPrecificacaoForm,
    PdePositionForm,
    PlataformaListGroup,
    PlataformaParadaForm,
    PosSalvarRotaForm,
    PraxioCreateLoginForm,
    RemovePassengersForm,
    RemovePoltronaFretamentoForm,
    RemoveUsersInfoForm,
    ReplaceLocalBatchForm,
    ReplaceLocalForm,
    RodoviariaLinkarTiposAssentosParams,
    RodoviariaListarTiposAssentosParams,
    RodoviariaRemovePaxForm,
    RotaHibridoForm,
    SetRevendedorAttributesForm,
    SimulaAlteracaoRotaForm,
    SolicitacaoPernaFiltersForm,
    SolicitacaoPernaForm,
    TotalbusLoginForm,
    UpdateDuracoesForm,
    UpdateGestorPontoDeVendaForm,
    UpdateLinkLocalForm,
    UpdateLocalEmbarqueForm,
    UpdatePontoDeVendaForm,
    WikipediaPageForm,
)
from core.models_commons import ActivityLog, AsyncTask, Lead, Revendedor, TransactionEmail, WhatsappTemplate
from core.models_company import MotivoMulta
from core.models_driving import EventoDesatencaoMotorista, Geofence
from core.models_grupo import (
    AjustePreco,
    ClosedReasons,
    EventoExtra,
    EventoExtraNegociacao,
    EventoExtraSolicitacao,
    EventoExtraSolicitacaoPerna,
    Grupo,
    TrechoClasse,
)
from core.models_rota import Checkpoint, LocalEmbarque
from core.models_travel import AlteracaoTravel, Cupom, Pagamento, Travel
from core.serializers import (
    serializer_anomalias,
    serializer_aviso_parceiro,
    serializer_cidade,
    serializer_evento_extra,
    serializer_locais,
    serializer_onibus,
    serializer_plataforma,
    serializer_rota,
    serializer_user,
)
from core.serializers.serializer_cidade import CidadeWithMetricasSerializer
from core.serializers.serializer_evento_extra import (
    EventoExtraNegociacaoSerializer,
    EventoExtraSerializer,
    EventoExtraSolicitacaoPernaSerializer,
    EventoExtraSolicitacaoSerializer,
)
from core.serializers.serializer_landing_page import LandingPageSerializer
from core.serializers.serializer_multas import MotivoMultaSerializer
from core.serializers.serializer_ponto_de_venda import PontoDeVendaSerializer
from core.service import (
    auth_svc,
    autorizacao_hibrido_svc,
    avisos_svc,
    canal_atendimento_svc,
    categoria_turistica_svc,
    checkpoint_parada_svc,
    cidade_info_svc,
    company_lending_svc_v2,
    company_svc,
    cupomadm_svc,
    doc_check_svc,
    driver_svc,
    duracao_dinamica_svc,
    ead_svc,
    editar_porcentagem_repasse_taxa_servico_svc,
    evento_extra_svc,
    fadiga_svc,
    freshchat_svc,
    globalsettings_svc,
    grupos_svc,
    gruposadm_svc,
    help_svc,
    itinerario_svc,
    landing_page_svc,
    lead_svc,
    locais_retirada_svc,
    localadm_svc,
    log_svc,
    multa_svc,
    onibus_svc,
    plataforma_svc,
    pontos_de_venda_svc,
    preco_svc,
    privacy_svc,
    rateio_svc,
    repasse_empresa_marketplace_svc,
    revenda_svc,
    rodoviaria_svc,
    rotasadm_svc,
    tags_svc,
    telemetria_svc,
    textos_svc,
    user_documents_svc,
    user_search_svc,
    user_svc,
    useradm_svc,
    valores_ressarcimento_svc,
    wikipedia_info_svc,
    zapadm_svc,
)
from core.service.feriado import feriado_adm_svc
from core.service.grupos_staff import (
    escalar_onibus_svc,
    grupo_crud_svc,
    grupo_filtros_svc,
    grupo_status_svc,
    passenger_manager_svc,
    ressarcimento_svc,
)
from core.service.invite import invite_svc
from core.service.itinerario_dinamico_svc import ItinerarioDinamico
from core.service.notifications import staff_notification_svc, user_notification_svc
from core.service.notifications.notification_svc import zap
from core.service.onibus_svc import serializa_onibus
from core.service.pagamento import estorno_svc, pagamento_svc
from core.service.remanejamento import remanejamento_svc
from core.service.remanejamento.solver import solver_svc
from core.service.reserva import cupom_svc, rodoviaria_reserva_svc, taxa_servico_checkout_svc
from core.service.reserva.cupom_svc import staff_remove_cupom, staff_remove_voucher
from core.service.rodoviaria.marketplace_auto_group_manager import atualizacao_operacao_completa_svc
from integrations import get_client
from integrations.google_maps_client import GoogleMapsClient
from integrations.risk_score_api_client import RiskScoreClient
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    CancelamentoNaoDisponivelException,
    PassageiroJaCanceladoException,
    PassengerTicketAlreadyPrintedException,
    PoltronaTrocadaException,
    RodoviariaClientError,
    RodoviariaEmpresaExistenteException,
    RodoviariaException,
    RodoviariaLoginNotFoundException,
    RodoviariaUnauthorizedError,
)
from marketplace.services import trecho_estoque_manager_svc
from pagamento_parceiro.service import configuracao_pagamento_svc, dia_parado_svc
from pagamento_parceiro.service.atribui_valor_repasse_svc import InfoGrupoMarketplace
from promo.service import fidelidade_svc
from search_result.service import conexao_adm_svc
from telemetria.local_tempo_parada_svc import create_grupo_local_tempo_parada
from telemetria.models import GrupoLocalTempoParada

buserlogger = logging.getLogger("buserlogger")


csv.register_dialect("metabase", delimiter=",", lineterminator="\n", quoting=csv.QUOTE_NONE)


@ajax_staff_required
def list_rotas(request):
    form_data = ListRotasForm.parse_raw(request.GET.get("filters", "{}"))
    try:
        rotas = rotasadm_svc.list_rotas(form_data)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)}, status=HTTPStatus.BAD_REQUEST)
    if form_data.paginator and (sort_key := form_data.paginator.sort_by):
        if sort_key == "itinerario__local__nickname":
            sort_key = "nickname"
            nickname_first_checkpoint_qs = Checkpoint.objects.filter(rota=models.OuterRef("pk"), idx=0).values_list(
                "local__nickname", flat=True
            )[:1]
            rotas = rotas.annotate(nickname=models.Subquery(nickname_first_checkpoint_qs))
        if sort_key == "count_groups":
            rotas = rotas.annotate(count_groups=Count("grupo"))
        if form_data.paginator.descending:
            sort_key = "-" + sort_key
        rotas = rotas.order_by(*[sort_key])

    if form_data.paginator:
        paginator = Paginator(rotas, per_page=form_data.paginator.rows_per_page)
        page = paginator.get_page(form_data.paginator.page)
        drotas = serializer_rota.serialize(
            page.object_list, form_data.with_trechos, with_paradas=form_data.with_paradas
        )
        return JsonResponse({"items": drotas, "count": paginator.count, "num_pages": paginator.num_pages}, safe=False)
    limit = 100
    rotas = rotas[:limit]
    drotas = serializer_rota.serialize(rotas, form_data.with_trechos, with_paradas=form_data.with_paradas)
    return JsonResponse(
        {
            "items": drotas,
            "count": len(drotas),
            "num_pages": 1,
        },
        safe=False,
    )


@ajax_staff_required
def list_rotas_principais(request):
    params = ListRotasPrincipaisSelectOptionsForm.parse_raw(request.GET.get("params", "{}"))
    rotas_principais_qs = rotasadm_svc.list_rotas_principais(params)
    if params.paginator:
        rotas_principais_qs = rotas_principais_qs.order_by(*params.paginator.order_by)
        paginator = Paginator(rotas_principais_qs, per_page=params.paginator.rows_per_page)
        page = paginator.get_page(params.paginator.page)
        rotas_principais = [rp.serialize() for rp in page.object_list]
        return JsonResponse(
            {"items": rotas_principais, "count": paginator.count, "num_pages": paginator.num_pages}, safe=False
        )
    return JsonResponse([rp.serialize() for rp in rotas_principais_qs], safe=False)


@ajax_staff_required
@require_POST
def adiciona_cidade_rota_principal(request, rota_principal_id):
    form = CidadeForm.parse_raw(request.body)
    rotasadm_svc.adiciona_cidade_rota_principal(rota_principal_id, form.cidade_id)
    return JsonResponse({})


@ajax_staff_required
@require_http_methods(["DELETE"])
def remove_cidade_rota_principal(request, rota_principal_id, cidade_id):
    rotasadm_svc.remove_cidade_rota_principal(rota_principal_id, cidade_id)
    return JsonResponse({})


@ajax_staff_required
def list_rotas_by_local_id(request):
    params = json.loads(request.GET.get("params"))
    local_id_list = params["localIdList"]
    from_date = params.get("fromDate")
    if from_date:
        from_date = datetime.strptime(f"{from_date} -0300", "%Y-%m-%d %z").date()
    return JsonResponse(rotasadm_svc.list_rotas_by_local_id(local_id_list, from_date), safe=False)


@ajax_staff_required
@require_GET
def get_rota_by_id(request, rota_id):
    route = rotasadm_svc.get_rota_by_id(rota_id)
    return JsonResponse({"rota": serializer_rota.serialize(route, with_trechos=True, with_paradas=True)})


@ajax_roles_required(["Infra", "Rotas", "Risco"])
def simula_replace_local_for_selected_routes(request):
    form = request.POST.get("form") if request.method == "POST" else request.GET.get("form")
    request_user = request.user
    replace_local_form = ReplaceLocalForm.parse_raw(form)
    impactos = rotasadm_svc.simula_replace_local_from_routes(replace_local_form, request_user)
    return JsonResponse([impacto.dict() for impacto in impactos], safe=False)


@ajax_roles_required(["Infra", "Rotas", "Risco"])
def replace_restrictions(request):
    replace_local_form = ReplaceLocalForm.parse_raw(request.POST.get("form"))
    restricoes = rotasadm_svc.replace_restrictions(replace_local_form)
    return JsonResponse(restricoes, safe=False)


@ajax_roles_required(["Infra", "Rotas", "Risco"])
def replace_local_for_selected_routes_async(request: HttpRequest) -> HttpResponse:
    payload = ReplaceLocalForm.parse_raw(request.body)

    operation = "replace_local_for_selected_routes"
    payload_hash = hashlib.md5(payload.json(exclude_defaults=True, sort_keys=True, exclude={"reason_change"}).encode())
    idempotency_key = f"async_task:{operation}#{payload_hash.hexdigest()}"

    input_data = payload.dict(exclude_defaults=True)
    async_task = idempotent_make_async_task(
        request, input_data, rotasadm_svc.replace_local_from_routes_task, idempotency_key
    )

    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@ajax_roles_required(["Infra", "Rotas", "RiscoLider"])
def replace_local_for_selected_routes_batch(request):
    request_data_str = request.POST.get("form")
    buserlogger.info("Payload replace_local_for_selected_routes_batch: %s", request_data_str)
    replace_local_form = ReplaceLocalBatchForm.parse_raw(request_data_str)
    request_data = json.loads(request_data_str)
    request_user = request.user
    log_svc.log_admin_local_alterado_request(request_data)
    new_rota_id_list = rotasadm_svc.replace_local_from_routes_batch(replace_local_form, request_user)
    if replace_local_form.inactivate_existing_local:
        update_local_form = UpdateLocalEmbarqueForm.parse_obj(
            {"id": replace_local_form.replacing_local, "ativo": False}
        )
        localadm_svc.update_local(update_local_form)
    return JsonResponse({"new_rota_id_list": new_rota_id_list}, safe=False)


@ajax_staff_required
def historico_alteracao_embarque(request):
    grupo_id = int(request.GET["grupo_id"])
    local_id = int(request.GET["local_id"])
    form = HistoricoAlteracaoEmbarqueForm.parse_obj({"grupo_id": grupo_id, "local_id": local_id})
    return JsonResponse(rotasadm_svc.get_historico_alteracao_embarque(form.grupo_id, form.local_id))


@ajax_roles_required("Rotas")
def creategroup(request):
    dgroup = request.POST["group"]
    form = CreateGroupForm.parse_raw(dgroup)
    groups = grupo_crud_svc.creategroup(
        form.rota,
        form.status,
        form.confirming_probability,
        form.modelo_venda,
        form.company,
        form.onibus,
        form.rotina_onibus,
        form.valor_frete,
        form.is_extra,
        form.departure_dates,
        form.departure_times,
        form.percentual_repasse,
        form.percentual_taxa_servico,
        form.trechos_classes,
        form.classes,
        form.autorizacao_hibrido,
        request.user,
        form.dict(),
        modelo_operacao=form.modelo_operacao,
    )
    return JsonResponse([g.id for g in groups], safe=False)


@ajax_staff_required
def list_logs(request):
    form = ListLogsForm.parse_obj(request.GET.dict())
    logs = log_svc.list_logs(form.entity, form.id, form.date_start, form.date_end)
    return JsonResponse(logs)


@ajax_staff_required
def list_user_communication_logs(request):
    form_data = ListUserCommunicationLogsForm.parse_raw(request.GET.get("params"))
    communication_logs = useradm_svc.list_user_communication_logs(form_data.user, form_data.search)
    paginator = Paginator(communication_logs, per_page=form_data.paginator.rows_per_page)
    page = paginator.get_page(form_data.paginator.page)
    return JsonResponse({"items": page.object_list, "count": paginator.count}, safe=False)


@ajax_roles_required(["Suporte", "Marketing"])
@ajax_staff_required
def update_tag_cpf(request):
    request_user = request.user
    passenger_cpf = request.POST.get("passenger_cpf")
    tag_nome = request.POST.get("tag_nome")
    referencia = request.POST.get("referencia")

    if not is_valid_cpf(passenger_cpf):
        return JsonResponse({"error": "O usuário não possui um CPF válido."}, status=400)

    tags_svc.update_tags_by_cpf(passenger_cpf, tag_nome, requested_by=request_user, referencia=referencia)

    return JsonResponse({})


@ajax_staff_required
def delete_tag_cpf(request):
    request_user = request.user
    passenger_cpf = request.POST.get("passenger_cpf")
    atribuicao_id = request.POST.get("atribuicao_id")

    tags_svc.delete_tag_by_cpf(passenger_cpf, atribuicao_id, requested_by=request_user)
    return JsonResponse({})


@ajax_staff_required
@cache_page(8 * 60 * 60, key_prefix="tags")
def get_tags(request):
    tags_list = tags_svc.get_all_tags()
    return JsonResponse({"tags": list(tags_list)})


@ajax_staff_required
def list_payment_logs(request):
    payment_id = int(request.GET["payment"])
    payment = Pagamento.objects.get(pk=payment_id)
    payment_logs = pagamento_svc.list_payment_logs(payment)
    return JsonResponse(payment_logs, safe=False)


@ajax_roles_required("Suporte")
def gerar_pagamento_divida(request):
    form = GerarPagamentoDividaForm.parse_raw(request.body)
    try:
        pagamento = pagamento_svc.gerar_pagamento_divida(
            form.payment_method, form.cpf, form.fullname, form.email, form.phone, form.value, form.user
        )
        log_svc.pagamento_divida_gerado(request.user, pagamento)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse(pagamento.to_dict_json())


@ajax_staff_required
def get_pagamentos_divida_pendentes(request):
    form = PagamentosDividaPendentesForm.parse_raw(request.body)
    pagamentos_pendentes = pagamento_svc.get_pagamentos_divida_pendentes(form.user)
    res = []
    for pagamento in pagamentos_pendentes:
        pagamento_dict = {
            "id": pagamento.id,
            "method": pagamento.method,
            "value": pagamento.value,
            "expiration_date": pagamento.expiration_date,
            "cpf": pagamento.cpf,
            "link": pagamento.url,
            "brcode": pagamento.pix_brcode,
        }
        res.append(pagamento_dict)
    return JsonResponse(res, safe=False)


@ajax_staff_required
def list_groups(request):
    params = json.loads(request.GET.get("params", "{}"))
    filters = {
        "all_onibus": False,
        "availability": params.get("availability"),
        "checkin_status_in": params.get("checkin_status", []),
        "cid_destino_filter_and": params.get("cidadesDestinoAndOr", False),
        "cid_origem_filter_and": params.get("cidadesOrigemAndOr", False),
        "cidades_destino": [int(s) for s in params.get("cidadesDestino", []) if s],
        "cidades_origem": [int(s) for s in params.get("cidadesOrigem", []) if s],
        "cidades": [int(s) for s in params.get("cidades", []) if s],
        "classe_in": params.get("classes"),
        "company_in": [int(id) if id else None for id in params.get("empresas", [])],
        "confirmation_with_forecast": params.get("confirmation_with_forecast"),
        "confirming_probability_in": params.get("confirming_probability", []),
        "contar_dia_parado": params.get("contar_dia_parado", False),
        "departure_hour": params.get("departureHour", []),
        "dia_semana_in": params.get("dia_semana", []),
        "gestor": params.get("gestor"),
        "grupo_fechado": params.get("grupo_fechado", False),
        "grupo_id": params["grupo_id"].strip().split(" ") if params.get("grupo_id") else None,
        "ida_after": params.get("departureAfter"),
        "ida_before": params.get("departureBefore"),
        "incident_grade_change": params.get("incident_grade_change"),
        "items_per_page": int(params.get("items_per_page", 30)),
        "locadora": params.get("locadora", False),
        "local_filter": [
            int(local_id_as_str) for local_id_as_str in params.get("localEmbarque", []) if local_id_as_str
        ],
        "local_filter_is_and": params.get("localDeEmbarqueIsAnd"),
        "modelo_venda_in": params.get("modelo_venda", []),
        "motoristas": params.get("motoristas", []),
        "nao_teve_frete": params.get("nao_teve_frete", False),
        "sugestao_solver": params.get("sugestao_solver", False),
        "ocasiao_status": params.get("ocasiao_status"),
        "onibus_in": params.get("onibus"),
        "page": params.get("page", 1),
        "regional": params.get("regional"),
        "rota_in": [int(rota_id_as_str) for rota_id_as_str in params.get("rotas", []) if rota_id_as_str],
        "rotas_principais_in": [
            int(rota_id_as_str) for rota_id_as_str in params.get("rotas_principais", []) if rota_id_as_str
        ],
        "rotina_onibus_in": [(s and int(s)) for s in params.get("rotinas_onibus", [])],
        "status_in": params.get("status", []),
        "tem_rotina": params.get("temRotina", False),
    }

    if filters.get("onibus_in"):
        # TODO Arrumar essa baixaria
        if "tem_onibus" in filters["onibus_in"]:
            filters["all_onibus"] = True
            filters["onibus_in"].remove("tem_onibus")
            onibus_id_list = [int(id) if id else None for id in filters["onibus_in"]]
            filters["onibus_in"] = onibus_id_list

    include_solver_data = filters.get("sugestao_solver") and is_rotas(request.user)
    grupos = grupo_crud_svc.search(filters, include_solver_data)

    paginator = Paginator(grupos, int(params.get("items_per_page", 30)))
    try:
        page_grupos = paginator.page(params.get("page", 1))
    except EmptyPage:
        page_grupos = paginator.page(1)

    dgrupos = [g.serialize() for g in page_grupos]
    return JsonResponse({"groups": dgrupos, "total_length": paginator.count}, safe=False)


@ajax_staff_required
def forecast_e_gmv_previsto_grupos(request):
    grupo_ids = request.POST.getlist("grupo_ids")
    grupo_ids = [int(grupo_id) for grupo_id in grupo_ids]
    (
        forecast_grupos,
        gmv_real_grupos,
        gmv_previsto_grupos,
        grupos,
    ) = solver_svc.get_forecast_e_gmv_previsto_grupos(grupo_ids)
    if not forecast_grupos:
        return JsonResponse({})

    return JsonResponse(
        {
            grupo_id: {
                "forecast_grupo": forecast_grupos[grupo_id],
                "gmv_previsto": gmv_previsto_grupos[grupo_id],
                "gmv_atual": gmv_real_grupos[grupo_id],
            }
            for grupo_id in forecast_grupos
        }
    )


@ajax_staff_required
def list_group_dates_related_to_route(request):
    rota_id = int(request.GET.get("rotaId"))
    departure = str(request.GET.get("departure"))
    grupos = grupo_crud_svc.list_group_dates_related_to_route(rota_id, departure)
    return JsonResponse(grupos, safe=False)


@ajax_staff_required
def list_drivers_keyset(request):
    drivers = useradm_svc.list_unordered_drivers(ListDriversForm.parse_raw(request.GET.get("params", "{}")))
    paginator = KeysetPaginator(
        query_set=drivers, paginator_params=KeysetPaginatorForm.parse_raw(request.GET.get("paginator", "{}"))
    )
    try:
        ead_score = ead_svc.get_ead_score([u.id for u in paginator.page])
        ead_driver_score = {item.user_id: item for item in ead_score}
        for driver in paginator.page:
            driver.ead_score = ead_driver_score[driver.id]
    except (JSONDecodeError, HTTPError) as e:
        capture_exception(e)
    return JsonResponse(
        {
            "prev": paginator.prev,
            "next": paginator.next,
            "items": serializer_user.serialize_adm_drivers(paginator.page),
        }
    )


@ajax_staff_required
def list_drivers(request):
    form = ListDriversForm.parse_raw(request.GET.get("params", "{}"))
    drivers = useradm_svc.list_drivers(form)

    if form.has_pagination:
        paginator = Paginator(drivers, per_page=form.paginator.rows_per_page)
        page = paginator.get_page(form.paginator.page)
        response = {
            "items": serializer_user.serialize_adm_drivers(page),
            "count": paginator.count,
            "num_pages": paginator.num_pages,
        }
    else:
        response = {"items": serializer_user.serialize_adm_drivers(drivers), "count": drivers.count(), "num_pages": 1}

    return JsonResponse(response, safe=False)


@ajax_staff_required
def list_onibus_keyset(request):
    filters = json.loads(request.GET.get("params", "{}"))
    onibus = onibus_svc.list_unordered_onibus(filters)
    paginator = KeysetPaginator(
        query_set=onibus, paginator_params=KeysetPaginatorForm.parse_raw(request.GET.get("paginator", "{}"))
    )
    return JsonResponse(
        {"prev": paginator.prev, "next": paginator.next, "items": serializa_onibus(filters, paginator.page)}
    )


@ajax_staff_required
def list_onibus(request):
    params = json.loads(request.GET.get("params", "{}"))
    paginator_params = json.loads(request.GET.get("paginator", "{}"))
    onibus_list = onibus_svc.list_onibus(params, paginator_params)
    donibus_list = onibus_svc.serializa_onibus(params, onibus_list)
    onibus_count = len(onibus_list)
    return JsonResponse({"items": donibus_list, "count": onibus_count})


@ajax_staff_required
def list_onibus_placas(request):
    filters = ListOnibusPlacaForm.parse_obj(request.GET.dict())
    onibus_list = onibus_svc.list_onibus_placa(filters)
    onibus_count = len(onibus_list)
    return JsonResponse({"items": onibus_list, "count": onibus_count})


@ajax_staff_required
def list_companies_keyset(request):
    companies = company_svc.list_unordered_companies(ListCompaniesForm.parse_raw(request.GET.get("params", "{}")))
    paginator = KeysetPaginator(
        query_set=companies, paginator_params=KeysetPaginatorForm.parse_raw(request.GET.get("paginator", "{}"))
    )
    return JsonResponse(
        {"prev": paginator.prev, "next": paginator.next, "items": [c.serialize() for c in paginator.page]}
    )


@ajax_staff_required
def list_companies(request):
    form = ListCompaniesForm.parse_raw(request.GET.get("params", "{}"))
    companies_qs = company_svc.list_companies(form)
    if not form.has_pagination:
        return JsonResponse([c.serialize() for c in companies_qs], safe=False)

    paginator = Paginator(companies_qs, form.paginator.rows_per_page)

    try:
        page = paginator.page(form.paginator.page)
    except EmptyPage:
        page = paginator.page(1)

    return JsonResponse(
        {"items": [c.serialize() for c in page], "count": paginator.count, "num_pages": paginator.num_pages}
    )


@ajax_staff_required
def list_simple_companies(request):
    form = ListSimpleCompaniesForm.parse_obj(request.GET.dict())
    companies = company_svc.list_simple_companies(form)
    return JsonResponse({"items": [company.serialize() for company in companies]})


@ajax_staff_required
def list_companies_rodoviaria(request):
    form = ListRodoviariaCompaniesForm.parse_raw(request.GET.get("params"))
    companies = rodoviaria_svc.list_empresas_paginator(
        name=form.name,
        status=form.status,
        modelo_venda=form.modelo_venda,
        rows_per_page=form.paginator.rows_per_page,
        page=form.paginator.page,
        order_by=form.paginator.order_by[0],
    )
    return JsonResponse(companies, safe=False)


@ajax_staff_required
def list_all_possible_company_features(request):
    is_staff = request.GET.get("is_staff")
    features = rodoviaria_svc.list_all_possible_empresa_features(is_staff)
    return JsonResponse(features, safe=False)


@ajax_staff_required
def get_rodoviaria_company_login(request):
    params = json.loads(request.GET.get("params"))
    try:
        result = rodoviaria_svc.get_empresa_login(
            integracao=params["integracao"], company_id=params["company_id"], modelo_venda=params["modelo_venda"]
        )
    except RodoviariaLoginNotFoundException as exc:
        return JsonResponse({"error": error_str(exc)}, status=400)
    return JsonResponse(result)


@ajax_staff_required
def create_rodoviaria_company(request):
    form = CreateRodoviariaCompanyForm.parse_raw(request.body)
    try:
        response = rodoviaria_svc.create_empresa(
            name=form.name,
            company_internal_id=form.company_internal_id,
            modelo_venda=form.modelo_venda,
            features=form.features,
            login=dict(form.login) if form.login else None,
            max_percentual_divergencia=form.max_percentual_divergencia,
            integracao=form.integracao,
        )
        return JsonResponse(response)
    except RodoviariaEmpresaExistenteException as exc:
        return JsonResponse({"error": exc.message}, status=400)


@ajax_staff_required
def update_rodoviaria_company(request):
    form = CreateRodoviariaCompanyForm.parse_raw(request.body)
    response = rodoviaria_svc.update_empresa(
        name=form.name,
        company_internal_id=form.company_internal_id,
        modelo_venda=form.modelo_venda,
        features=form.features,
        login=dict(form.login),
        max_percentual_divergencia=form.max_percentual_divergencia,
        integracao=form.integracao,
    )
    return JsonResponse(response)


@ajax_staff_required
def fetch_rodoviaria_locais(request):
    company_rodoviaria_id = int(request.GET.get("company_rodoviaria_id"))
    response = rodoviaria_svc.fetch_locais_empresa(company_rodoviaria_id=company_rodoviaria_id)
    return JsonResponse(response)


@ajax_staff_required
def get_rodoviaria_locais_retirada(request: HttpRequest):
    company_internal_id = int(request.GET.get("company_internal_id"))
    company_rodoviaria_id = int(request.GET.get("company_rodoviaria_id"))
    associado_rota = request.GET.get("associado_rota", None)
    linkado_buser = request.GET.get("linkado_buser", None)
    result = rodoviaria_svc.get_locais_retirada_empresa(
        company_internal_id, company_rodoviaria_id, associado_rota, linkado_buser
    )
    return JsonResponse({"locais_retirada": result})


@ajax_staff_required
def fetch_external_totalbus_companies(request):
    login_params = TotalbusLoginForm.parse_raw(request.body)
    try:
        result = rodoviaria_svc.fetch_external_totalbus_companies(
            user=login_params.user, password=login_params.password, tenant_id=login_params.tenant_id
        )
        return JsonResponse(result)
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": exc.message}, status=400)


@ajax_staff_required
def fetch_rodoviaria_formas_pagamento(request):
    login_params = ListaFormasPagamentoRodoviariaForm.parse_raw(request.body)
    try:
        result = rodoviaria_svc.fetch_formas_pagamento(login_params)
        return JsonResponse(result)
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": exc.message}, status=400)


@ajax_staff_required
def fetch_rodoviaria_trechos_vendidos_por_rota(request):
    rodov_rota_id = request.GET.get("rodoviaria_rota_id")
    try:
        result = rodoviaria_svc.fetch_trechos_vendidos_por_rota(rodov_rota_id=rodov_rota_id)
    except RodoviariaClientError as exc:
        return JsonResponse({"error": exc.message}, status=400)
    return JsonResponse({"task_info": result})


@ajax_staff_required
@cache_page(1 * 60)
def get_empresas_integradas_rodoviaria(request):
    try:
        empresas = rodoviaria_svc.empresas_by_features(features="add_pax_staff,buscar_servico")
    except Exception as ex:
        capture_exception(ex)
        empresas = []
    empresas_integradas = [
        {"company_id": e["company_internal_id"], "modelo_venda": e["modelo_venda"]} for e in empresas
    ]
    return JsonResponse({"empresas": empresas_integradas})


@ajax_staff_required
@cache_page(1 * 60)
def get_rotinas_integradas_rodoviaria(request):
    try:
        rotinas_integradas = rodoviaria_svc.rotinas_integradas_hibrido_list()
    except Exception as ex:
        capture_exception(ex)
        rotinas_integradas = []
    return JsonResponse({"rotinas": rotinas_integradas})


@ajax_staff_required
def get_integracao_empresa_rodoviaria(request, company_id):
    empresa = rodoviaria_svc.get_empresa_info(company_id)

    if not empresa:
        return JsonResponse({"message": "Essa empresa não contém integração marketplace mapeada"}, status=404)

    return JsonResponse(
        {
            "company_id": empresa["company_internal_id"],
            "nome_integracao": empresa["integracao"]["name"],
            "id_integracao": empresa["integracao"]["id"],
            "active": empresa["active"],
        }
    )


@ajax_staff_required
def rodoviaria_listar_tipos_assentos(request):
    params = RodoviariaListarTiposAssentosParams.parse_raw(request.GET.get("params"))
    result = rodoviaria_svc.listar_tipos_assentos(params)
    return JsonResponse(result)


@ajax_staff_required
def rodoviaria_linkar_tipo_assento(request):
    data = RodoviariaLinkarTiposAssentosParams.parse_raw(request.POST.get("params"))
    rodoviaria_svc.linkar_tipo_assento(
        data.id_assento, data.tipo_assento_buser_preferencial, data.tipos_assentos_buser_match
    )
    return JsonResponse({})


@ajax_staff_required
def verify_praxio_login(request):
    login_params = PraxioCreateLoginForm.parse_raw(request.body)
    try:
        result = rodoviaria_svc.verify_praxio_login(
            nome=login_params.name, senha=login_params.password, cliente=login_params.cliente
        )
        return JsonResponse(result)
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": exc.message}, status=400)


@ajax_staff_required
def get_auth_key_ti_sistemas(request):
    client: RodoviariaClient = get_client("rodoviaria")
    result = client.get_auth_key_ti_sistemas()
    return JsonResponse(result)


@ajax_staff_required
def lista_empresas_api(request):
    try:
        params = ListaEmpresasAPIParams.parse_raw(request.POST.get("params"))
        empresas = rodoviaria_svc.lista_empresas_api(params)
    except RodoviariaUnauthorizedError as exc:
        return JsonResponse({"error": exc.message}, status=400)
    return JsonResponse(empresas, safe=False)


@ajax_staff_required
def list_gestores(request):
    params = json.loads(request.GET.get("params", "{}"))
    ativos = params.get("ativos", False)
    gestores = company_svc.list_gestores(ativos=ativos)
    return JsonResponse(gestores, safe=False)


@ajax_roles_required(["Qualidade", "Seguranca", "Comercial"])
def save_company(request):
    certificado_digital = request.FILES.get("certificado_digital")
    logo = request.FILES.get("logo")
    ta = request.FILES.get("ta")
    tas = request.FILES.get("tas")

    company_dic = json.loads(request.POST.get("company", "{}"))
    company_dic["invalidated_reason"] = request.POST.get("reason")
    company_dic["doc_estadual_status"] = request.POST.get("status_doc_estadual")
    company_dic["doc_type"] = request.POST.get("doc_type")

    company_form = CreateOrUpdateCompanyProfileForm.validate(company_dic)
    res = company_svc.create_or_update_company(
        company_form=company_form,
        certificado_digital=certificado_digital,
        logo=logo,
        ta=ta,
        tas=tas,
        doc_estadual_status=company_dic["doc_estadual_status"],
        reason=company_dic["invalidated_reason"],
        doc_type=company_dic["doc_type"],
    )
    log_svc.log_company_updated(company_dic)
    return JsonResponse(res, safe=False)


@ajax_staff_required
def list_feedbacks(request):
    params = json.loads(request.GET.get("params", "{}"))
    feedbacks = grupo_crud_svc.list_feedbacks(params)
    return JsonResponse(feedbacks)


@ajax_staff_required
def list_drivers_feedbacks(request):
    grupo_id = json.loads(request.GET.get("grupo_id", ""))

    if not grupo_id:
        raise ValidationError("Buscando feedbacks sem id de grupo")

    feedbacks = grupo_crud_svc.get_driver_feedback(grupo_id=grupo_id)
    return JsonResponse([asdict(feedback) for feedback in feedbacks], safe=False)


@ajax_roles_required(["Operacoes", "Suporte"])
@require_POST
def update_departure_hour(request):
    groups = json.loads(request.POST.get("groups"))
    horario = request.POST.get("horario")
    notify = json.loads(request.POST.get("notify", "{}"))

    grupo_ids = [int(g) for g in groups]
    grupo_crud_svc.update_groups_departure_hour(grupo_ids, horario, notify)
    return JsonResponse({})


@ajax_staff_required
def get_group_details(request):
    is_hashed = False
    params = json.loads(request.GET.get("params", "{}"))
    group_id = params["group_id"]
    simple = params.get("simple", False)

    if not str(group_id).isdigit():
        try:
            group_id = unhashint(group_id)
            is_hashed = True
        except ValueError:
            raise ValidationError("ID do grupo inválido")

    grupo = grupo_crud_svc.get_group_details(group_id, is_hashed, simple=simple)

    return JsonResponse(grupo, safe=False)


@ajax_staff_required
def get_passengers(request):
    grupo_id = int(request.GET["grupo_id"])

    all_passengers, possiveis_embarques, possiveis_desembarques = grupo_crud_svc.get_passengers_and_boarding_locations(
        grupo_id
    )

    return JsonResponse(
        {
            "passengers": list(all_passengers),
            "possiveis_embarques": possiveis_embarques,
            "possiveis_desembarques": possiveis_desembarques,
        },
        safe=False,
    )


@ajax_staff_required
def get_previous_and_next_groups(request):
    params = json.loads(request.GET.get("params", "{}"))
    datetime_ida = params.get("datetime_ida")
    group_id = params.get("group_id")
    rota_id = params.get("rota_id")
    rotina_id = params.get("rotina_id")
    placa = params.get("placa")
    grupos = grupos_svc.get_previous_and_next_groups(datetime_ida, group_id, rota_id, rotina_id, placa)
    return JsonResponse(grupos)


@ajax_staff_required
def search_user(request):
    search_text = request.GET.get("searchText")
    dusers = user_search_svc.search_user(search_text)
    return JsonResponse(dusers, safe=False)


@ajax_staff_required
def search(request):
    search_text = request.GET.get("searchText")
    dusers, dtravels = user_search_svc.search_user_and_travels(search_text)
    return JsonResponse(
        {
            "users": dusers,
            "travels": dtravels,
        },
        safe=False,
    )


@ajax_staff_required
def get_travel(request, travel_id, version="v1"):
    travel_id = int(travel_id)
    travel = grupo_crud_svc.get_travel(travel_id)
    if not travel:
        return JsonResponse({}, status=404)
    if version == "v1":
        # TODO: this is only for compatibility, if it's not being used we can remove it later
        idas = travel["ida_volta"].pop("idas")
        travel["ida_volta"]["ida"] = idas[0]
        voltas = travel["ida_volta"].pop("voltas", [])
        travel["ida_volta"]["volta"] = voltas[0] if len(voltas) > 0 else None
    return JsonResponse(travel)


@ajax_staff_required
def get_local(request, local_id):
    local_id = int(local_id)
    local = get_object_or_404(LocalEmbarque, id=local_id)
    return JsonResponse(serializer_locais.serialize_object(local))


@ajax_staff_required
def get_local_by_city(_, city_id):
    city_id = int(city_id)
    local_list = localadm_svc.list_locais_by_city_id(city_id).to_serialize(
        serializer_locais.LocalEmbarqueSerializer.with_restrictions()
    )
    return JsonResponse([local.serialize() for local in local_list], safe=False)


@ajax_staff_required
def get_driver_details(request, driver_id):
    driver_id = int(driver_id)
    driver = useradm_svc.get_driver_details(driver_id)
    if not driver:
        return JsonResponse({}, status=404)
    return JsonResponse(driver)


@ajax_staff_required
def get_buseiro_details(request, buseiro_id, tags=False):
    buseiro_id = int(buseiro_id)
    if tags:
        buseiro = useradm_svc.get_buseiro_details_with_tags(buseiro_id)
    else:
        buseiro = useradm_svc.get_buseiro_details(buseiro_id)

    if not buseiro:
        return JsonResponse({}, status=404)
    return JsonResponse(buseiro)


@ajax_staff_required
def get_buseiro_invitations(request, buseiro_id=None):
    if buseiro_id is None:
        buseiro_id = request.GET["buseiro_id"]
    buseiro_id = int(buseiro_id)
    paginator = json.loads(request.GET.get("paginator", "{}"))
    page = int(paginator.get("page", 1))
    size = int(paginator.get("rowsPerPage", 25))
    invitations = invite_svc.list_invitations(User.objects.get(pk=buseiro_id), page, size)
    return JsonResponse(invitations)


@ajax_staff_required
def get_buseiro_travels(request):
    data = request.GET.dict()
    data["paginator"] = json.loads(data["paginator"])
    form_data = ListBuseiroTravelsForm.parse_obj(data)
    travels = useradm_svc.get_buseiro_travels(form_data.buseiro, form_data.paginator, form_data.search)
    return JsonResponse(travels, safe=False)


@ajax_staff_required
def get_buseiro_saldo(request):
    buseiro_id = request.GET.get("buseiro_id", None)
    try:
        buseiro = User.objects.get(pk=buseiro_id)
        saldo = accounting_svc.get_saldo(buseiro)
    except User.DoesNotExist:
        raise Exception("Usuário não existente")
    return JsonResponse(saldo)


@ajax_staff_required
def get_new_buseiro_extrato(request):
    data = request.GET.dict()
    data["paginator"] = json.loads(data["paginator"])
    form_data = ListUserAccountingOperationsForm.parse_obj(data)

    new_extrato = accounting_svc.get_new_extrato(form_data.user_id, form_data.filter)
    paginator = Paginator(new_extrato, per_page=form_data.paginator.rows_per_page)
    page = paginator.get_page(form_data.paginator.page)

    return JsonResponse({"extrato": page.object_list, "count": paginator.count}, safe=False)


# temp: transicao do front para endpoint paginado


@ajax_staff_required
def get_new_buseiro_extrato_temp(request, user_id):
    user_id = int(user_id)
    new_extrato = accounting_svc.get_new_extrato(user_id, "all")
    return JsonResponse(new_extrato, safe=False)


@ajax_staff_required
def get_extrato_usuario_v2(request):
    user_id = int(request.GET.get("user_id"))
    rows_per_page = int(request.GET.get("rows_per_page"))
    current_page = int(request.GET.get("current_page"))
    search_str = request.GET.get("search", None)
    show_all = request.GET.get("show_all", "false") == "true"

    extrato_v2 = accounting_svc.get_extrato_v2(user_id, search_str, show_all)
    paginator = Paginator(extrato_v2, per_page=rows_per_page)
    page = paginator.get_page(current_page)

    return JsonResponse({"extrato": page.object_list, "count": paginator.count}, safe=False)


@ajax_staff_required
def details_estorno(request):
    user_id = int(request.GET.get("user_id"))
    user = User.objects.get(id=user_id)
    pagamentos_estornaveis = estorno_svc.estornar(user, dry_run=True, all_options=True)
    valores_estornaveis = [pe.to_values_dict() for pe in pagamentos_estornaveis]
    return JsonResponse({"valoresEstornaveis": valores_estornaveis})


@ajax_roles_required("Rotas")
def confirm_groups(request):
    groups_data = json.loads(request.POST.get("groups"), parse_float=Decimal)
    grupos = grupo_status_svc.manual_confirmation(groups_data, user=request.user, fluxo=request.path)
    return JsonResponse(
        {
            "confirmed": [g.id for g in grupos],
        }
    )


@ajax_roles_required(["Rotas", "RiscoLider"])
def simular_cancelamento_grupo(request):
    params = json.loads(request.POST.get("params"))
    groups = json.loads(request.POST.get("groups"))
    acao_contabil = params["acao_contabil"]
    dias_parados = params.get("dias_parados")
    notificacao = params.get("notificacao")
    motivo = params.get("canceled_reason", "COMPANY_PROBLEM")
    simulacoes = []
    groups = Grupo.objects.prefetch_related("trechoclasse_set", "travel_set", "rota__itinerario__local__cidade").filter(
        id__in=groups
    )

    for group in groups:
        simulacao = grupo_status_svc.simula_cancelamento_grupo(
            group, notificacao=notificacao, acao_contabil=acao_contabil, dias_parados=dias_parados
        )
        simulacoes.append(simulacao)
    return JsonResponse({"simulacoes": simulacoes, "canceled_reason": grupo_status_svc.group_canceled_reasons(motivo)})


@ajax_staff_required
def preview_notifications(request):
    notifications_settings = json.loads(request.POST.get("notifications_settings"))
    dest = notifications_settings["dest"]
    notifications = notifications_settings["notifications"]
    previews = []
    for notification in notifications:
        previews_for_notifications = staff_notification_svc.preview_notification(
            title=notification.get("title") or "",
            content=notification.get("content") or "",
            channels=notification.get("channels"),
            dest_type=dest["type"],
            dest_list=dest["list"],
            request_user=request.user,
        )
        previews.extend(previews_for_notifications)
    return JsonResponse(previews, safe=False)


@ajax_roles_required(["Suporte", "Operacoes", "Marketing"])
def send_notifications(request):
    form = NotificationForm.parse_raw(request.POST.get("notifications_settings"))

    if form.is_scheduled:
        staff_notification_svc.sched_notifications(form, created_by=request.user)
    else:
        staff_notification_svc.send_notifications_to_dest(
            dest_type=form.recipient_type,
            dest_list=form.recipients,
            dest_travel_list=form.recipients_travel,
            notifications=[n.dict() for n in form.notifications],
            request_user=request.user,
            grupo_id=form.group_id,
            from_user=request.user,
            marketing=form.marketing,
        )

    return JsonResponse({})


@ajax_staff_required
def get_group_canceled_reasons(request):
    reasons = grupo_status_svc.group_canceled_reasons()
    return JsonResponse({"canceled_reasons": reasons, "default_code": "NO_PEOPLE"})


@ajax_staff_required
@require_GET
def get_closed_reasons(request: HttpRequest) -> JsonResponse:
    reason_type = request.GET.get("reason_type", "grupo")
    return JsonResponse(ClosedReasons.to_staff(reason_type=reason_type), safe=False)


@ajax_roles_required(["staff", "Rotas"])
def get_motivos_remanejamento(request):
    reasons = remanejamento_svc.grupo_motivos_remanejamento(request.user)
    return JsonResponse(reasons)


@ajax_roles_required(["Rotas", "RiscoLider"])
def cancel_groups(request):
    form = CancelarGrupoForm.parse_raw(request.body)
    result = {"grupos_com_erro": 0, "grupos_cancelados": 0}

    for grupo in form.groups:
        if grupo.status in {Grupo.Status.CANCELED, Grupo.Status.DONE}:
            return JsonResponse(
                {
                    "grupos_com_erro": len(form.groups),
                    "resultado": {
                        "error": "Não foi possível cancelar nenhum grupo, "
                        f"pois o grupo de id {grupo.id} estava com status {grupo.status}"
                    },
                },
                status=400,
            )

    dict_response = {}
    erros = []
    original_statuses = []
    _NOW = timezone.now()
    for grupo in form.groups:
        original_statuses.append(grupo.status)
        grupo.status = Grupo.Status.CANCELED
        grupo.canceled_reason = "Em processo de cancelamento"
        grupo.cancel_requested_at = _NOW
    # altera logo status pra evitar novas reservas
    buserlogger.info(
        "views_staff.cancel_groups - Iniciando cancelamento de grupos",
        extra={"groups": [grupo.id for grupo in form.groups]},
    )
    Grupo.objects.bulk_update(form.groups, ["status", "canceled_reason", "cancel_requested_at"])
    try:
        with transaction.atomic():
            for grupo in form.groups:
                grupo_status_svc.cancel_group(
                    grupo,
                    notificacao=form.notificacao,
                    acao_contabil=form.acao_contabil,
                    canceled_reason=form.canceled_reason,
                    reason_description=form.reason_description,
                    auto=form.auto,
                )
                result["grupos_cancelados"] += 1
            _, erros = dia_parado_svc.altera_dias_parados(form.dias_parados, request.user, request.path)
            transaction.on_commit(lambda: staff_notification_svc.notify_commercial_group_canceled(form.groups))
    except Exception as exc:
        grupos_id = [grupo.id for grupo in form.groups]
        buserlogger.exception(
            "views_staff.cancel_groups",
            exc_info=exc,
            extra={"groups": grupos_id},
        )
        for grupo, original_status in zip(form.groups, original_statuses):
            grupo.status = original_status
        # se não conseguiu executar os cancelamentos, restaura o status dos grupos
        Grupo.objects.bulk_update(form.groups, ["status"])
        result["grupos_com_erro"] = len(grupos_id)
        dict_response["error"] = f"Falha no cancelamento dos {grupos_id=}"
        dict_response["resultado"] = result
        return JsonResponse(dict_response, status=400)

    if erros:
        result["grupos_com_erro"] = len(erros)
        dict_response["error"] = [error_str(ex) for ex in erros]
        buserlogger.info(
            "views_staff.cancel_groups",
            extra={"grupos_validation_error": dict_response["error"]},
        )
    dict_response["resultado"] = result
    return JsonResponse(dict_response)


@ajax_staff_required
def list_group_notifications_pigeon(request: HttpRequest, grupo_id: int):
    """
    Lista as notificações enviadas em massa para os usuários pax de um grupo específico pelo pigeon.
    """
    query_params = request.GET.dict()

    date_interval = {}
    from_datetime = query_params.pop("fromDatetime", None)
    to_datetime = query_params.pop("toDatetime", None)

    if from_datetime:
        date_interval.update(from_datetime=from_datetime)
    if to_datetime:
        date_interval.update(to_datetime=to_datetime)

    response = {}  # quando a feature flag está desligada o retorno é {}
    return JsonResponse(response)


@ajax_staff_required
def list_group_notifications(request: HttpRequest, grupo_id: int):
    """
    Lista as notificações enviadas em massa para os usuários pax de um grupo específico pelo analista.
    """
    communication_logs = staff_notification_svc.list_group_notifications(grupo_id)
    pagination = json.loads(request.GET.get("params"))["paginator"]
    paginator = Paginator(communication_logs, per_page=pagination["rowsPerPage"])
    page = paginator.get_page(pagination["page"])

    return JsonResponse({"items": page.object_list, "count": paginator.count}, safe=False)


@ajax_can_give_reais
def ressarcir_buseiros(request):
    fromuser = request.user
    passenger_ids = json.loads(request.POST["passengers"])
    document = request.FILES.get("document")
    data = json.loads(request.POST["data"], parse_float=Decimal)
    msg = ressarcimento_svc.ressarcir_buseiros(
        fromuser, passenger_ids, data, document, evento_comunicacao="views_staff.ressarcir_buseiros"
    )
    if msg is not None:
        return JsonResponse({"msg": msg})
    return JsonResponse({})


@ajax_can_give_reais
def cancelar_ressarcimento(request):
    pax_id_list = json.loads(request.POST["to"])
    amount = Decimal(request.POST["amount"])
    reason = json.loads(request.POST["reason"])
    reason_key = json.loads(request.POST["reason"])
    ressarcimento_id = request.POST["ressarcimento_id"]
    try:
        useradm_svc.cancelar_ressarcimento(request.user, pax_id_list, amount, reason_key, reason, ressarcimento_id)
    except ValidationError as e:
        res = {"error": error_str(e)}
        return JsonResponse(res)
    return JsonResponse({})


@ajax_roles_required(["Suporte", "Rotas"])
def ressarcimento_lanche(request):
    fromuser = request.user
    passenger_ids = json.loads(request.POST["passengers"])
    data = json.loads(request.POST["data"], parse_float=Decimal)
    ressarcimento_svc.ressarcimento_lanche(
        passenger_ids, data, fromuser, evento_comunicacao="views_staff.ressarcimento_lanche"
    )
    return JsonResponse({})


@ajax_permission_required(roles.CAN_CONCLUIR_GRUPOS)
def finish_group(request, group_id=None):
    form = FinishGroupForm.parse_raw(request.POST["params"])
    grupo = (
        Grupo.objects.select_related("onibus", "rota", "pedagio", "company")
        .prefetch_related("trechoclasse_set")
        .get(pk=form.group_id)
    )
    rateio_svc.finish_group(grupo=grupo, user=request.user)
    return JsonResponse({})


@ajax_roles_required(["Suporte", "Operacoes", "Risco", "PosBO"])
def update_group(request, grupo_id=None):
    if grupo_id is None:
        grupo_id = request.POST["grupo_id"]
    grupo_id = int(grupo_id)
    params = json.loads(request.POST["params"], parse_float=Decimal)
    grupo_crud_svc.update_group(grupo_id, params, updated_by=request.user, origem_alteracao_placa=request.path)
    grupo = grupo_crud_svc.get_group_details(grupo_id)
    return JsonResponse(grupo)


@ajax_roles_required(["Operacoes", "Suporte", "Risco", "PosBO"])
def simular_remanejamento_massivo(request):
    params = json.loads(request.POST["params"], parse_float=Decimal)
    res = remanejamento_svc.simular_remanejamento_massivo(params)
    return JsonResponse(res, safe=False)


@ajax_roles_required(["Operacoes", "Suporte", "Risco", "PosBO"])
def bulk_change_class(request: HttpRequest) -> JsonResponse:
    form = AlterarClassesForm.parse_raw(request.POST["params"])
    data = form.dict()
    motivo_id = data.get("motivo_id")
    remanejamento_svc.bulk_change_class(
        data,
        origem_alteracao_placa=request.path,
        causada_por=AlteracaoTravel.CausadaPor.ANALISTA,
        motivo_id=motivo_id,
    )
    return JsonResponse({})


@ajax_staff_required
def recalcula_precos_classe(request):
    form = AlterarPrecosClasseAutomaticaForm.parse_raw(request.GET.get("params", "{}"))
    novos_precos = preco_svc.recalcula_precos_classe(**form.dict())
    return JsonResponse(
        {
            trecho_vendido_id: {
                "max_split_value": novo_preco.valor,
                "ref_split_value": novo_preco.valor_ref,
                "buckets": novo_preco.buckets_to_dict(),
            }
            for (trecho_vendido_id, _), novo_preco in novos_precos.items()
        }
    )


@ajax_roles_required(["Operacoes", "Suporte"])
@require_POST
def alterar_precos(request):
    form = AlterarPrecosForm.parse_raw(request.body)
    preco_svc.alterar_precos_por_grupos(form.grupos, form.precos, form.alterar_buckets)
    return JsonResponse({})


@ajax_roles_required("Rotas")
def simular_alteracao_rota(request):
    params = SimulaAlteracaoRotaForm.parse_raw(request.body)
    new_rota_setup = rotasadm_svc.simular_alteracao_rota_de_grupo(params.rota, params.groups)
    return JsonResponse(new_rota_setup.get("alteracoes", []), safe=False)


@traced("views_staff.alterar_rota_de_grupos_async")
@ajax_roles_required("Rotas")
def alterar_rota_de_grupos_async(request):
    payload = AlteraRotaDeGruposForm.parse_raw(request.body)
    old_rota_id = Grupo.objects.filter(id=payload.groups[0]).values_list("rota_id", flat=True).first()
    operation = "alterar_rota_de_grupos"
    payload_hash = hashlib.md5(str(old_rota_id).encode())
    idempotency_key = f"async_task:{operation}#{payload_hash.hexdigest()}"

    input_data = payload.dict(by_alias=True, exclude_unset=True)
    async_task = idempotent_make_async_task(
        request, input_data, rotasadm_svc.alterar_rota_de_grupos_task, idempotency_key, timedelta(minutes=3)
    )

    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@traced("views_staff.alterar_rota_de_grupos")
@ajax_roles_required("Rotas")
def alterar_rota_de_grupos(request):
    form = AlteraRotaDeGruposForm.parse_raw(request.body)
    rota = rotasadm_svc.alterar_rota_de_grupos(form)
    return JsonResponse(rota, safe=False)


@ajax_roles_required(["Operacoes", "Suporte"])
@require_POST
def bulk_escalar_onibus(request):
    form = EscalarEmpresaOnibusRotinaForm.parse_raw(request.POST["params"])
    escalar_onibus_svc.bulk_escalar_onibus(form, origem_alteracao_placa=request.path)
    return JsonResponse({})


@require_POST
@ajax_staff_required
def simular_bulk_escalar_onibus_async(request: HttpRequest) -> HttpResponse:
    params = json.loads(request.body)
    async_task = make_async_task(request, params, escalar_onibus_svc.simular_escalar_onibus_task)
    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@require_GET
@never_cache
@ajax_staff_required
def async_task_detail(request: HttpRequest, async_task_id: int) -> JsonResponse:
    async_task = get_object_or_404(AsyncTask, id=async_task_id)
    http_status = HTTPStatus.BAD_REQUEST if async_task.failed() else HTTPStatus.OK
    return JsonResponse(async_task.as_dict(), status=http_status)


@require_POST
@ajax_staff_required
def verificar_conflito_onibus(request):
    params = CheckBusConflictForm.parse_raw(request.POST.get("params"))
    escalar_onibus_svc.verificar_conflito_onibus(params.group_ids, params.onibus_id)
    return JsonResponse({})


@ajax_roles_required(["Rotas", "Comercial"])
def alterar_ocasiao(request):
    groups = json.loads(request.POST.get("groups"))
    params = json.loads(request.POST.get("params"), parse_float=Decimal)
    if "dia_parado" in params and not is_rotas(request.user):
        raise ValidationError("Edição de dias parados só pode ser feita pelo time de Rotas")
    for group_id in groups:
        grupo_crud_svc.alterar_ocasiao(int(group_id), params, request.user, request.path)
    return JsonResponse({})


@ajax_roles_required(["Rotas", "Comercial"])
def alterar_probabilidade(request):
    groups = json.loads(request.POST.get("groups"))
    params = json.loads(request.POST.get("params"))
    grupo_crud_svc.alterar_probabilidade(groups, params)
    return JsonResponse({})


@ajax_roles_required(["Rotas", "Comercial"])
def alterar_valor_encomenda(request):
    groups = json.loads(request.POST.get("groups"))
    params = json.loads(request.POST.get("params"))
    val_encomenda = params.get("valor_encomenda", -1)
    if val_encomenda < 0:
        raise ValidationError("Valor de encomenda inválido")
    grupo_crud_svc.alterar_valor_encomenda(groups, val_encomenda)
    return JsonResponse({})


@ajax_can_give_reais
def remove_credito(request):
    pax_id_list = json.loads(request.POST["to"])
    amount = Decimal(request.POST["amount"])
    reason = json.loads(request.POST["reason"])["text"]

    try:
        useradm_svc.remove_credito(request.user, pax_id_list, amount, reason)
    except ValidationError as e:
        res = {"error": error_str(e)}
        return JsonResponse(res)

    return JsonResponse({})


@ajax_roles_required("Suporte")
def give_voucher(request):
    targets = json.loads(request.POST.get("to"))
    promo_dic = json.loads(request.POST.get("cupom"))
    due_date = request.POST.get("due_date")
    reason = request.POST.get("reason")
    send_mail = json.loads(request.POST.get("send_mail"))
    reason_key = request.POST.get("reason_key")
    reason_description = request.POST.get("reason_description")
    canal_atendimento = request.POST.get("canal_atendimento_id")
    canal_atendimento_id = canal_atendimento if canal_atendimento != "null" else None
    request_link_jira = request.POST.get("link_jira")
    link_jira = request_link_jira if request_link_jira != "null" else None
    request_link_slack = request.POST.get("link_slack")
    link_slack = request_link_slack if request_link_slack != "null" else None
    request_protocolo_atendimento = request.POST.get("protocolo_atendimento")
    protocolo_atendimento = request_protocolo_atendimento if request_protocolo_atendimento != "null" else None

    try:
        cupom = Cupom.objects.get(code=promo_dic.get("code", "0"))
    except Exception:
        return JsonResponse({"error": "Promoção não existente."}, safe=False)
    user_vouchers = useradm_svc.give_voucher(
        request.user,
        targets,
        cupom,
        due_date,
        reason,
        send_mail,
        reason_key,
        reason_description,
        canal_atendimento_id,
        link_jira,
        link_slack,
        protocolo_atendimento,
    )
    if len(user_vouchers) > 0:
        msg = f"{pluralize(len(user_vouchers), 'usuário ressarcido', 'usuários ressarcidos')} com voucher."
    else:
        msg = "Nenhum usuário ressarcido com voucher, pois já foi disponibilizado anteriormente."
    return JsonResponse({"msg": msg})


@ajax_staff_required
def get_user_cupom_lead(request):
    user_id = request.GET.get("user_id")
    travel_id = request.GET.get("travel_id")
    lead = Lead.objects.get(user_id=user_id)
    res = cupom_svc.get_cupom_lead(lead, staff=True, travel_id=travel_id)
    return JsonResponse({"cupons": res})


@dataclass
class RemocaoCupomVoucherData:
    id: int
    userId: int
    reason: str = ""


@ajax_roles_required("Suporte")
def remove_cupom(request):
    data = RemocaoCupomVoucherData(**request.POST.dict())
    cupom_id = data.id
    user_id = data.userId
    reason = data.reason
    try:
        staff_remove_cupom(cupom_id, user_id, request.user.id, reason=reason)
        return JsonResponse({"status": "Tudo deletado nos conformes"})
    except ValidationError as ve:
        return JsonResponse({"error": ve.message}, status=400)


@ajax_roles_required("Suporte")
def remove_voucher(request):
    data = RemocaoCupomVoucherData(**request.POST.dict())
    voucher_id = data.id
    user_id = data.userId
    reason = data.reason
    try:
        staff_remove_voucher(voucher_id, user_id, request.user.id, reason=reason)
        return JsonResponse({"status": "Tudo deletado nos conformes"})
    except ValidationError as ve:
        return JsonResponse({"error": ve.message}, status=400)


@ajax_roles_required(["Suporte", "Rotas", "Risco"])
def removepassengers(request):
    try:
        content = RemovePassengersForm.parse_raw(request.body)
    except Exception:
        content = None

    # Mantém a retrocompatibilidade com a versão antiga do payload
    if content is None:
        try:
            content = request.POST.dict()
            content["passengers"] = json.loads(content["passengers"])
            content = RemovePassengersForm(**content)
        except Exception:
            return JsonResponse({"error": "Formato de payload inválido"}, status=400)

    try:
        passenger_manager_svc.removepassengers(content.passengers, content.reason, content.notify)
    except (
        PassengerTicketAlreadyPrintedException,
        PoltronaTrocadaException,
        CancelamentoNaoDisponivelException,
        PassageiroJaCanceladoException,
    ) as ex:
        return JsonResponse({"error": ex.mensagem_staff})
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message})
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse({})


@ajax_roles_required("Suporte")
def updatebuseiro(request):
    travel_id = int(request.POST["travel_id"])
    buseiro_id = int(request.POST["buseiro_id"])
    params = json.loads(request.POST["params"])
    passenger_manager_svc.updatebuseiro(travel_id, buseiro_id, params)
    return JsonResponse({})


@ajax_permission_required(roles.CAN_RESERVAR_POLTRONA)
@require_POST
def edit_poltrona(request):
    buseiro_id = int(request.POST.get("buseiro_id"))
    travel_id = int(request.POST.get("travel_id"))
    poltrona = json.loads(request.POST.get("poltrona"))
    passenger_manager_svc.edit_poltrona(request.user, buseiro_id, travel_id, poltrona)
    return JsonResponse({})


@ajax_roles_required(["Suporte", "Operacoes", "ComercialAssistente"])
def update_user(request, user_id=None):
    if user_id is None:
        user_id = request.POST["user_id"]
    user_id = int(user_id)
    params = json.loads(request.POST.get("params", "{}"))
    user = useradm_svc.update_user(user_id, params)
    return JsonResponse(user)


@ajax_roles_required("VendasLider")
def set_revendedor_attributes(request):
    formdata = json.loads(request.POST.get("form", "{}"))
    form = SetRevendedorAttributesForm(**formdata)
    try:
        revendedor = revenda_svc.atualiza_revendedor(**form.dict())
        user_dict = serializer_user.serialize_revendedores(revendedor.user)
        return JsonResponse(user_dict)
    except ValidationError as ex:
        return JsonResponse({"error": error_str(ex)}, safe=False, status=HTTPStatus.BAD_REQUEST)


@ajax_roles_required("Suporte")
def unverify_user(request, user_id=None):
    if user_id is None:
        user_id = request.POST["user_id"]
    user_id = int(user_id)
    core.service.profile_svc.unverify_user(user_id, by_staff=True)
    return JsonResponse({})


@ajax_staff_required
def list_types_n_classes(request):
    tipos, classes = onibus_svc.list_types_n_classes()
    return JsonResponse({"tipos": tipos, "classes": classes})


@ajax_staff_required
@cache_page(1 * 60)
def onibus_possible_filter_values(request):
    tipos, classes = onibus_svc.list_types_n_classes()
    gestores_comercial, gestores_qualidade = onibus_svc.list_gestores()
    return JsonResponse(
        {
            "tipos": tipos,
            "classes": classes,
            "gestores_comercial": gestores_comercial,
            "gestores_qualidade": gestores_qualidade,
        }
    )


@ajax_roles_required(["Qualidade", "Seguranca", "Operacoes", "SuporteLider"])
def save_onibus(request):
    params = json.loads(request.POST.get("params", "{}"))
    onibus_id = params.get("id", None)
    onibus_id = int(onibus_id) if onibus_id else None
    homologa = params.get("homologa", False)
    onibus = onibus_svc.staff_update_or_create_onibus(params, onibus_id, filesToUpload=request.FILES, homologa=homologa)
    return JsonResponse(serializer_onibus.serialize_complete_object(onibus))


@require_POST
@ajax_roles_required(["Qualidade", "Seguranca"])
def bulk_remove_bus(request):
    form = BulkRemoveBusForm.parse_raw(request.body)
    removed_by = request.user
    onibus_svc.remove_onibus(single_id=None, bulk_ids=form.bus_ids, reason=form.reason, removed_by=removed_by)
    return JsonResponse({})


@ajax_roles_required(["Qualidade", "Seguranca"])
def remove_onibus(request):
    onibus_id = int(json.loads(request.POST.get("onibus_id")))
    reason = request.POST.get("reason")
    removed_by = request.user
    onibus_svc.remove_onibus(single_id=onibus_id, bulk_ids=[], reason=reason, removed_by=removed_by)
    return JsonResponse({})


@ajax_roles_required(["Qualidade", "Seguranca"])
def inactivate_onibus(request):
    onibus_id = request.POST.get("onibus_id")
    reason = request.POST.get("reason")
    removed_by = request.user
    onibus_svc.inactivate_onibus(single_id=onibus_id, reason=reason, removed_by=removed_by)
    return JsonResponse(None, status=204, safe=False)


@ajax_staff_required
def togglecheckin(request):
    passenger_id = int(request.POST.get("passenger_id"))
    checkin = passenger_manager_svc.togglecheckin(passenger_id)
    return JsonResponse(checkin, safe=False)


@ajax_roles_required("Suporte")
def get_user_verification_data(request, user_id):
    user_id = int(user_id)
    dados_receita = useradm_svc.get_user_verification_data(user_id)
    return JsonResponse(dados_receita, safe=False)


@ajax_roles_required("Suporte")
def verifica_dados_receita(request):
    cpf = request.GET.get("cpf")
    dados_receita = useradm_svc.verifica_dados_receita(cpf)
    return JsonResponse(dados_receita, safe=False)


@ajax_roles_required(["Suporte", "Operacoes"])
def send_whatsapp_template(request):
    form = BaseSendWhatsappTemplateForm.from_params(request.POST)
    for user_id in form.user_ids:
        user = User.objects.get(pk=user_id)
        travel = Travel.objects.filter(user=user, grupo_id=form.group_id).first()
        if travel:
            ctx = user_notification_svc.travel_zap_ctx(travel)
            zap(
                user,
                form.msgid,
                ctx=ctx,
                contact_phone=travel.contact_phone,
                travel_id=travel.id,
                contact_reason=form.contact_reason,
            )
        else:
            zap(user, form.msgid, ctx={"NAME": user.first_name or "viajante"}, contact_reason=form.contact_reason)
    return JsonResponse(True, safe=False)


@ajax_roles_required(["Suporte", "Operacoes"])
def send_whatsapp_template_v2(request):
    form = BaseSendWhatsappTemplateFormV2.from_params(request.POST)
    if form.travel_ids:
        for travel_id in form.travel_ids:
            travel = Travel.objects.select_related("user").get(pk=travel_id)
            user = travel.user
            ctx = user_notification_svc.travel_zap_ctx(travel)
            zap(
                user,
                form.msgid,
                ctx=ctx,
                contact_phone=travel.contact_phone,
                travel_id=travel.id,
                contact_reason=form.contact_reason,
            )
    else:
        for user_id in form.user_ids:
            user = User.objects.get(pk=user_id)
            zap(user, form.msgid, ctx={"NAME": user.first_name or "viajante"}, contact_reason=form.contact_reason)
    return JsonResponse(True, safe=False)


@ajax_roles_required(["Suporte", "Operacoes"])
def send_whatsapp(request):
    user_ids = json.loads(request.POST.get("user_ids"))
    group_id = int(request.POST.get("group_id"))
    text = request.POST.get("text")
    if text:
        text = re.sub(r"[\n\s]+", " ", text).rstrip()
        for user_id in user_ids:
            user = User.objects.get(pk=user_id)
            travel = Travel.objects.filter(user=user, grupo_id=group_id).first()
            if travel:
                ctx = user_notification_svc.travel_zap_ctx(travel)
                ctx["HELP_LINK"] = f"{settings.SITE_BASE_URL}/ajuda"
                ctx["BOOKING"] = "%s. %s" % (ctx["BOOKING"], text)
                zap(user, "important_information_v2", ctx=ctx, contact_phone=travel.contact_phone, travel_id=travel.id)
    return JsonResponse(True, safe=False)


@ajax_roles_required(["Suporte", "Operacoes"])
def send_whatsapp_v2(request):
    travel_ids = json.loads(request.POST.get("travel_ids"))
    text = request.POST.get("text")
    if text:
        text = re.sub(r"[\n\s]+", " ", text).rstrip()
        for travel_id in travel_ids:
            travel = Travel.objects.select_related("user").get(pk=travel_id)
            user = travel.user
            ctx = user_notification_svc.travel_zap_ctx(travel)
            ctx["HELP_LINK"] = f"{settings.SITE_BASE_URL}/ajuda"
            ctx["BOOKING"] = "%s. %s" % (ctx["BOOKING"], text)
            zap(user, "important_information_v2", ctx=ctx, contact_phone=travel.contact_phone, travel_id=travel.id)
    return JsonResponse(True, safe=False)


@ajax_staff_required
def list_zap_templates(request):
    types = json.loads(request.POST.get("types", "[]"))
    if len(types) == 0:
        templates = [
            template.to_dict_json()
            for template in WhatsappTemplate.objects.exclude(msgid="important_information_v2").order_by("msgid")
        ]
    else:
        templates = [
            template.to_dict_json()
            for template in WhatsappTemplate.objects.filter(type__in=types, is_active=True)
            .exclude(msgid="important_information_v2")
            .order_by("msgid")
        ]
    return JsonResponse(templates, safe=False)


@ajax_staff_required
def list_all_whatsapp_templates(request):
    templates = [template.to_dict_json() for template in WhatsappTemplate.objects.all().order_by("msgid")]
    return JsonResponse(templates, safe=False)


@ajax_roles_required("Suporte")
def create_whatsapp_template(request):
    msgid = request.POST.get("msgid")
    type = request.POST.get("type")
    content = request.POST.get("content")
    from_sender = request.POST.get("from_sender")
    template = zapadm_svc.create_whatsapp_template(msgid, type, content, from_sender)
    return JsonResponse(template, safe=False)


@ajax_roles_required("Suporte")
def inactivate_whatsapp_template(request):
    msgid = request.POST.get("msgid")
    reason = request.POST.get("reason")
    zapadm_svc.inactivate_whatsapp_template(msgid, reason)
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Suporte", "Operacoes"])
def create_user(request):
    user_dic = json.loads(request.POST["user"])
    user = user_svc.create_user_from_dict(user_dic)
    log_svc.log_created_new_user(user, user_dic)
    return JsonResponse(serializer_user.serialize_user(user))


@ajax_roles_required(["Suporte", "Operacoes", "Qualidade"])
def save_driver(request):
    user_dic = json.loads(request.POST["user"])
    user_id = user_dic["id"] if "id" in user_dic else None
    files = request.FILES
    update_type = request.POST.get("update_type", "update")
    if not user_id:
        company = user_dic.pop("empresa")
        user = useradm_svc.create_new_driver(user_dic, company["id"], files=files)
    else:
        user = useradm_svc.update_driver(user_dic, user_id, files=files, update_type=update_type, confirm_phone=True)
    return JsonResponse(user)


@require_POST
@ajax_roles_required(["Suporte", "Operacoes", "Qualidade"])
def bulk_remove_drivers(request):
    form = BulkRemoveDriversForm.parse_raw(request.body)
    removed_by = request.user
    user_svc.bulk_remove_drivers(users_ids=form.users_ids, removed_by=removed_by)
    return JsonResponse({})


@require_POST
@ajax_staff_required
def check_user_info(request):
    form = CheckUserInfoForm.parse_raw(request.body)
    users = user_svc.search_user_info_exists(
        user_id=form.id, cpf=form.cpf, email=form.email, cell_phone=form.cell_phone
    )
    return JsonResponse({"exists": bool(users), "users": users})


@require_POST
@ajax_roles_required("Qualidade")
def remove_users_info(request):
    form = RemoveUsersInfoForm.parse_raw(request.body)
    removed_by = request.user
    user_svc.remove_users_info(users_ids=form.users_ids, removed_by=removed_by)
    return JsonResponse({})


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca"])
def create_new_admin(request):
    user_dic = json.loads(request.POST["user"])
    company_id = user_dic.pop("company")
    permissions = user_dic.pop("permissions", {})
    admin = useradm_svc.create_new_admin(user_dic, company_id, permissions=permissions)
    return JsonResponse(admin)


@ajax_roles_required(["Infra", "RiscoLider"])
def create_local(request):
    form = LocalEmbarqueForm.parse_raw(request.POST.get("local"))
    local = localadm_svc.create_local(form)

    return JsonResponse(serializer_locais.serialize_object(local), safe=False)


@ajax_staff_required
def get_cidade(request):
    form = GetCidadeForm.parse_obj(request.GET.dict())
    cidade = rotasadm_svc.get_cidade(form)
    return JsonResponse(cidade)


@ajax_roles_required("Rotas")
def create_rota_por_excel(request):
    rota_csv = request.FILES["rota"]
    trechos_csv = request.FILES["trechos"]
    try:
        excel_csv = rotasadm_svc.create_rota_por_excel(rota_csv=rota_csv, trechos_csv=trechos_csv)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)}, safe=False)

    return JsonResponse(excel_csv, safe=False)


@ajax_roles_required("Rotas")
def create_rota(request):
    form = CreateRotaForm.parse_raw(request.POST.get("rota"))
    rotas = rotasadm_svc.create_rota(form)
    rotas_json = serializer_rota.serialize(rotas, with_trechos=True)
    return JsonResponse(rotas_json, safe=False)


@ajax_roles_required("Rotas")
def simular_edit_rota(request, rota_id=None):
    # TODO: tech-debt: o front da edição de rotas envia as paradas de lanche dentro do itinérario, enquanto que a rota
    # já possui distinção entra rota.get_itinerario() "Checkpoints" e rota.get_paradas() "CheckpointParadas".
    # fazer a distinção entre um e outro pelo atributo apenas_parada no _handle_itinerario_creation é confuso e torna
    # o código mais complicado.
    if rota_id is None:
        rota_id = request.POST["rota_id"]
    rota_id = int(rota_id)
    params = EditRotaForm.parse_raw(request.POST.get("rota"))
    new_rota_setup = rotasadm_svc.simular_edit_rota(rota_id, params.dict())
    return JsonResponse(new_rota_setup.get("alteracoes", []), safe=False)


@ajax_roles_required("Rotas")
def edit_rota_async(request: HttpRequest) -> HttpResponse:
    payload = EditRotaForm.parse_raw(request.body)

    operation = "edit_rota"
    payload_hash = hashlib.md5(
        payload.json(
            exclude_defaults=True, sort_keys=True, include={"rota_id", "itinerario", "trechos_vendidos"}
        ).encode()
    )
    idempotency_key = f"async_task:{operation}#{payload_hash.hexdigest()}"

    input_data = payload.dict(by_alias=True, exclude_unset=True)
    async_task = idempotent_make_async_task(request, input_data, rotasadm_svc.edit_rota_task, idempotency_key)

    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@ajax_roles_required("Rotas")
@require_POST
def edit_rota_status(request, rota_id, ativo):
    rotasadm_svc.edit_rota_status(rota_id, ativo)
    return JsonResponse(
        {
            "rota": {
                "id": rota_id,
                "ativo": ativo,
            }
        }
    )


@ajax_roles_required("Rotas")
@require_POST
def update_duracoes(request, rota_id):
    form = UpdateDuracoesForm.parse_raw(request.body)
    new_route, new_groups = rotasadm_svc.update_duracoes(
        rota_id,
        form.duracoes,
        update_groups=form.update_groups,
        update_groups_after=form.update_groups_after,
        send_mail=form.send_mail,
        send_zap=form.send_zap,
    )
    return JsonResponse(
        {
            "rota": new_route.id,
            "grupos": new_groups,
        }
    )


@require_POST
@ajax_roles_required(["Rotas", "Risco"])
def alterar_duracoes(request: HttpRequest) -> JsonResponse:
    form = AlterarDuracoesForm.parse_raw(request.body)
    duracao_dinamica_svc.create_duracoes_dinamicas(form.duracoes, from_staff=True)
    return JsonResponse({})


@require_GET
@ajax_roles_required(["Rotas", "Risco"])
def get_itinerario_dinamico(request: HttpRequest) -> JsonResponse:
    grupo_id = int(request.GET.get("grupo_id"))
    grupo = Grupo.objects.get(id=grupo_id)
    itinerario = itinerario_svc.get_itinerario(grupo.rota, local=True, with_paradas=True)
    itinerario_dinamico = ItinerarioDinamico(grupo, itinerario).to_dict_json()
    itinerario_svc.append_arrival_and_departure_times(itinerario_dinamico, grupo.datetime_ida)

    return JsonResponse(itinerario_dinamico, safe=False)


@ajax_roles_required(["Rotas", "Infra"])
def list_route_restrictions(request):
    form = ListRestrictionsForm.parse_raw(request.POST["params"])
    restrictions = list(core.service.localadm_svc.local_restrictions(form))
    return JsonResponse(restrictions, safe=False)


@ajax_staff_required
def check_supported_bus(request):
    form = ListBusUnsupportedLocalForm.parse_raw(request.body)
    restrictions = localadm_svc.list_unsupported_local_for_bus(form)
    return JsonResponse(restrictions, safe=False)


@ajax_roles_required(["Rotas", "Infra"])
def list_alternative_route_without_restrictions(request):
    form = ListRestrictionsAlternativeForm.parse_raw(request.body)
    alternative_route = rotasadm_svc.list_alternative_routes(form)
    return JsonResponse(alternative_route, safe=False)


@ajax_staff_required
def list_cidades(request):
    form = ListCidadesForm.parse_raw(request.GET.get("params", "{}"))
    cidades = rotasadm_svc.list_cidades(form)
    if form.with_status:
        cidades_serialized = serializer_cidade.serialize_with_status(cidades)
    else:
        cidades_serialized = serializer_cidade.serialize_withlocals(cidades)
    return JsonResponse(cidades_serialized, safe=False)


@ajax_staff_required
def list_cidades_bases_operacionais(request):
    cidades = rotasadm_svc.list_cidades_bases_operacionais()
    cidades_serialized = serializer_cidade.serialize(cidades)
    return JsonResponse(cidades_serialized, safe=False)


@ajax_roles_required("Escritor")
def list_cidades_with_info(request):
    form = ListCidadesForm.parse_raw(request.GET.get("params", "{}"))
    cidades = rotasadm_svc.list_cidades(form, CidadeWithMetricasSerializer())
    if form.has_pagination:
        paginator = Paginator(cidades, per_page=form.paginator.rows_per_page)
        page = paginator.get_page(form.paginator.page)
        response = {
            "items": [cidade.serialize() for cidade in page.object_list],
            "count_items": paginator.count,
            "count_pages": paginator.num_pages,
        }
    else:
        response = {"items": [cidade.serialize() for cidade in cidades], "count_items": len(cidades), "count_pages": 1}
    return JsonResponse(response)


@ajax_roles_required("Escritor")
def save_cidade_info(request):
    cidade_info = json.loads(request.POST.get("cidade_info"))
    cidade_info["image_files"] = request.FILES.getlist("imagens")
    try:
        form = CidadeInfoForm.parse_obj(cidade_info)
    except ValidationErrorPydantic as e:
        errors = []
        for error in e.args[0]:
            if isinstance(error, list) and len(error):
                error_message = error[0].exc.args[0]
            else:
                error_message = error.exc.args[0]
            errors.append(error_message)
        return JsonResponse({"errors": errors}, status=400)
    cidade_info_svc.save_cidade_info(form)
    return JsonResponse({})


@ajax_roles_required("Escritor")
@cache_page(24 * 60 * 60)
def get_wikipedia_page(request):
    form = WikipediaPageForm.parse_obj(request.GET.dict())
    wikipedia_page = wikipedia_info_svc.get_wikipedia_page(form)
    return JsonResponse(wikipedia_page)


@ajax_staff_required
def list_pontos_de_venda(request):
    params = ListPontosDeVendaForm.parse_raw(request.GET.get("params", "{}"))
    pdvs_queryset = pontos_de_venda_svc.list_pontos_de_venda(params).to_serialize(PontoDeVendaSerializer)

    if params.order_by:
        pdvs_queryset = pdvs_queryset.order_by(*params.order_by)
    else:
        pdvs_queryset = pdvs_queryset.order_by("id")

    if params.has_pagination:
        paginator = Paginator(pdvs_queryset, per_page=params.paginator.rows_per_page)
        page = paginator.get_page(params.paginator.page)
        response = {
            "items": PontoDeVendaSerializer().serialize(page.object_list),
            "count": paginator.count,
            "num_pages": paginator.num_pages,
        }
    else:
        pdvs_list = PontoDeVendaSerializer().serialize(pdvs_queryset)
        response = {
            "items": pdvs_list,
            "count": len(pdvs_list),
            "num_pages": 1,
        }
    return JsonResponse(response)


@ajax_staff_required
def list_configuracao_ponto_de_venda(request):
    pdv_id = int(request.GET["pdv_id"])
    configuracao_pdv = pontos_de_venda_svc.list_configuracao_pdv_validas(pdv_id)
    return JsonResponse(configuracao_pdv, safe=False)


@require_POST
@ajax_staff_required
def editar_configuracao_pdv(request):
    user_id = request.user.id
    data = json.loads(request.body)
    try:
        configuracao_pdv = pontos_de_venda_svc.upsert_configuracao_pdv(user_id, data["pdv_id"], data["configuracao"])
    except ValidationError as err:
        return JsonResponse({"error": str(err)}, status=400, safe=False)
    except Exception as err:
        return JsonResponse({"error": str(err)}, status=500, safe=False)
    return JsonResponse({}, safe=False)


@require_POST
@ajax_staff_required
def create_ponto_de_venda(request):
    form = BasePontoDeVendaForm.parse_raw(request.body)
    pdv = pontos_de_venda_svc.create_ponto_de_venda(form)
    return JsonResponse(PontoDeVendaSerializer().serialize_object(pdv))


@require_POST
@ajax_staff_required
def update_ponto_de_venda(request):
    update_data = UpdatePontoDeVendaForm.parse_raw(request.body)
    pdv = pontos_de_venda_svc.update_ponto_de_venda(update_data)
    return JsonResponse(PontoDeVendaSerializer().serialize_object(pdv))


@require_POST
@ajax_staff_required
def atribuir_gestor_ponto_de_venda(request):
    update_data = UpdateGestorPontoDeVendaForm.parse_raw(request.body)
    pontos_de_venda_svc.tratar_role_do_gestor_anterior(update_data.ponto_de_venda)
    pdv = pontos_de_venda_svc.atribuir_gestor_pdv(update_data)
    return JsonResponse(PontoDeVendaSerializer().serialize_object(pdv))


@require_POST
@ajax_staff_required
def desvincular_gestor_ponto_de_venda(request):
    pdv_data = UpdateGestorPontoDeVendaForm.parse_raw(request.body)
    pontos_de_venda_svc.tratar_role_do_gestor_anterior(pdv_data.ponto_de_venda)
    pdv = pontos_de_venda_svc.desvincular_gestor_pdv(pdv_data.ponto_de_venda)
    return JsonResponse(PontoDeVendaSerializer().serialize_object(pdv))


@ajax_staff_required
def list_locais(request):
    params = ListLocaisForm.parse_raw(request.GET.get("params", "{}"))
    include_group_count = params.include_group_count

    if include_group_count:
        locais_qs = localadm_svc.list_locais(params).to_serialize(
            serializer_locais.LocalEmbarqueSerializer.with_groups()
        )
    else:
        locais_qs = localadm_svc.list_locais(params).to_serialize(
            serializer_locais.LocalEmbarqueSerializer.with_restrictions()
        )

    if params.has_pagination:
        paginator = Paginator(locais_qs, per_page=params.paginator.rows_per_page)
        page = paginator.get_page(params.paginator.page)
        response = {
            "items": [local.serialize() for local in page.object_list],
            "count": paginator.count,
            "num_pages": paginator.num_pages,
        }
    else:
        locais_list = [local.serialize() for local in locais_qs]
        response = {
            "items": locais_list,
            "count": len(locais_list),
            "num_pages": 1,
        }
    return JsonResponse(response, safe=False)


@ajax_staff_required
def list_locais_simple(request):
    params = ListLociasSimpleForm.parse_obj(request.GET.dict())
    locais_list = localadm_svc.list_locais_simple(params)

    return JsonResponse(list(locais_list), safe=False)


@ajax_staff_required
def list_locais_select_options(request):
    params = ListLocaisSelectOptionsForm.parse_raw(request.GET.get("params", "{}"))
    locais_qs = localadm_svc.list_locais_select_options(params).to_serialize(serializer_locais.LocalEmbarqueSerializer)

    locais_qs = locais_qs.order_by("nickname", "-ativo")
    locais_list = [local.serialize() for local in locais_qs]

    return JsonResponse(
        {
            "items": locais_list,
            "count": len(locais_list),
            "num_pages": 1,
        },
        safe=False,
    )


@ajax_staff_required
def list_rotas_select_options(request):
    params = ListRotasSelectOptionsForm.parse_raw(request.GET.get("params", "{}"))
    rotas = rotasadm_svc.list_rotas_select_options(params)
    return JsonResponse({"items": rotas, "count": len(rotas), "num_pages": 1})


@ajax_roles_required(["Infra", "Rotas", "RiscoLider"])
def update_local(request):
    update_data = UpdateLocalEmbarqueForm.parse_raw(request.body)
    local_before_update = update_data.local.serialize()
    local = localadm_svc.update_local(update_data)
    local_after_update = local.serialize()
    log_svc.log_update_local(local_before_update, local_after_update)
    return JsonResponse({}, safe=False)


@ajax_staff_required
def list_promos(request):
    params = request.GET.get("params")
    if params:
        params = json.loads(params)
        is_voucher = params.get("is_voucher")
        categorias = params.get("categorias")
        is_filtro_destaque = params.get("filtro_destaque")
        items_per_page = params.get("items_per_page")
        which_page = params.get("page")
        search_str = params.get("search")
        promos = cupomadm_svc.list_promos(is_filtro_destaque, search_str, is_voucher, categorias)
        if items_per_page:
            paginator = Paginator(promos, per_page=items_per_page)
            page = paginator.get_page(which_page)
            response = {
                "promos": [promos.serialize() for promos in page.object_list],
                "count_items": paginator.count,
                "count_pages": paginator.num_pages,
            }
        else:
            response = [promo.serialize() for promo in promos]
    else:
        promos = cupomadm_svc.list_promos()
        response = [promo.serialize() for promo in promos]
    return JsonResponse(response, safe=False)


@require_POST
@ajax_can_create_coupon
def create_or_update_promo(request):
    cupom = json.loads(request.POST.get("cupom", "{}"))
    background_image = request.FILES.get("background_image")
    trechos_csv = request.FILES.get("trechos_csv")
    if trechos_csv:
        reader = cupomadm_svc.reader_trechos_promo_csv(trechos_csv)
        cupom["trechos"] = list(reader)
    form = PromoForm.parse_obj(cupom)
    return JsonResponse(cupomadm_svc.create_or_update_promo(form, background_image, request.user), safe=False)


@ajax_staff_required
def get_list_categorias_cupom(request):
    categorias = cupomadm_svc.get_list_categorias_cupom()
    categorias_list = list(categorias)
    return JsonResponse({"categorias": categorias_list}, safe=False)


@ajax_can_create_coupon
def add_more_vouchers(request):
    promo_dic = json.loads(request.POST.get("cupom", "{}"))
    qtde_vouchers = int(promo_dic.get("qtde_vouchers", "0"))
    try:
        cupom = Cupom.objects.get(code=promo_dic.get("code", "0"))
    except Exception:
        return JsonResponse({"error": "Promoção/Cupom inexistente"}, safe=False)
    cupomadm_svc.create_vouchers(cupom, qtde_vouchers, fromuser=request.user)
    return JsonResponse({}, safe=False)


@ajax_can_create_coupon
def download_vouchers_xls(request, promo_code):
    file = cupomadm_svc.create_vouchers_xlsx(promo_code)
    response = HttpResponse(file, content_type="application/ms-excel")
    response["Content-Disposition"] = 'attachment; filename="vouchers_da_promo_%s.xlsx"' % promo_code
    return response


@ajax_staff_required
def list_antt_drivers(request):  # todo: deprecated
    return JsonResponse([], safe=False)


@ajax_staff_required
def gerar_antt(request, group_id):  # todo: deprecated
    return JsonResponse([], safe=False)


@ajax_staff_required
def imprimir_antt(request, group_id):  # todo: deprecated
    return JsonResponse({}, safe=False)


@ajax_staff_required
def resend_invoice(request, payment_id=None):
    if payment_id is None:
        payment_id = request.GET["payment_id"]
    payment_id = int(payment_id)
    pagamento_svc.resend_invoice(payment_id, evento="Staff - reenviar comprovante")
    return JsonResponse({})


@ajax_staff_required
def resend_comprovante_estorno(request, payment_id, evento="views_staff.resend_comprovante_estorno"):
    payment_id = int(payment_id)
    pagamento_svc.resend_comprovante_estorno(payment_id, request.user, evento=evento)
    return JsonResponse({})


@ajax_staff_required
@require_GET
def resend_comprovante_saque(request, transaction_id=None):
    if transaction_id is None:
        transaction_id = int(request.GET["transaction_id"])
    try:
        pagamento_svc.resend_comprovante_saque(
            transaction_id, request.user, "Staff - views_staff.resend_comprovante_saque"
        )
    except (ValidationError, SuspiciousOperation) as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse({})


@ajax_staff_required
def get_user_documents(request, user_id):
    user_id = int(user_id)
    user_documents = user_documents_svc.get_user_documents(user_id)
    return JsonResponse(user_documents, safe=False)


@ajax_staff_required
def get_status_integracao(request):
    grupo_id = request.GET.get("grupo_id")
    trecho_classe_id = request.GET.get("trecho_classe_id")
    return JsonResponse(rodoviaria_svc.get_status_integracao_grupo(grupo_id, trecho_classe_id), safe=False)


@require_POST
@ajax_staff_required
def update_status_integracao(request):
    trecho_classe_id = json.loads(request.POST.get("trecho_classe_id"))
    try:
        trecho_classe = TrechoClasse.objects.select_related("grupo").get(pk=trecho_classe_id)
    except TrechoClasse.DoesNotExist:
        return JsonResponse(f"Não existe trecho classe com id: {trecho_classe_id}", status=404, safe=False)

    return JsonResponse(rodoviaria_svc.update_status_integracao_trecho_classe(trecho_classe), safe=False)


@require_POST
@ajax_staff_required
def update_status_integracao_grupo(request):
    grupo_id = json.loads(request.POST.get("grupo_id"))

    return JsonResponse(rodoviaria_svc.update_status_integracao_grupo(grupo_id=grupo_id), safe=False)


@require_POST
@ajax_staff_required
def cadastrar_trecho_rodoviaria(request):
    trecho_classe_id = json.loads(request.POST.get("trecho_classe_id"))
    return JsonResponse(rodoviaria_svc.cadastrar_trecho_rodoviaria(trecho_classe_id=trecho_classe_id), safe=False)


@require_POST
@ajax_staff_required
def cadastrar_trechos_rodoviaria_por_grupo(request):
    grupo_id = json.loads(request.POST.get("grupo_id"))
    return JsonResponse(rodoviaria_svc.cadastrar_trechos_rodoviaria_por_grupo(grupo_id=grupo_id), safe=False)


@ajax_roles_required("Suporte")
def verify_user(request):
    user_data = json.loads(request.POST.get("user_data"))
    user_id = int(user_data.get("user_id"))
    verification_status = user_data.get("verification_status")
    evento_comunicacao = "views_staff.verify_user"
    if verification_status == "verified":
        user_documents_svc.accept_verification(user_id, evento_comunicacao=evento_comunicacao)
    if verification_status == "denied":
        verification_rejection_reason = user_data.get("verification_rejection_reason")
        user_documents_svc.reject_verification(
            user_id, verification_rejection_reason, evento_comunicacao=evento_comunicacao
        )
    return JsonResponse({})


@ajax_roles_required("Suporte")
def get_all_unverified_users(request):
    unverified_users = user_documents_svc.get_all_unverified_users()
    return JsonResponse(unverified_users, safe=False)


@require_POST
@ajax_roles_required("Suporte")
def edit_user(request):
    user_id = int(request.POST.get("user_id"))
    phone = request.POST.get("phone")
    email = request.POST.get("email")
    name = request.POST.get("name")
    res = {}
    if phone != "null":
        validate_phone(phone)
        res = useradm_svc.edit_user_phone(user_id, phone)
    if email != "null":
        res = useradm_svc.edit_user_email(user_id, email)
    if name != "null":
        res = useradm_svc.edit_user_name(user_id, name)
    return JsonResponse(res)


@require_POST
@ajax_roles_required("Suporte")
def resend_phone_confirmation(request):
    user_id = int(request.POST.get("user_id"))
    user = User.objects.get(id=user_id)
    user_notification_svc.confirmar_telefone(
        user, resend=True, bx_resent=True, evento="views_staff.resend_phone_confirmation"
    )
    return JsonResponse({})


@ajax_staff_required
def get_helpquestions(request):
    version = json.loads(request.GET.get("params", "{}")).get("version")
    questions = help_svc.get_helpquestions_for_staff(version=version)
    return JsonResponse(questions, safe=False)


@ajax_roles_required(["Suporte", "Legal", "Redator"])
def save_helpquestion(request):
    question_data = json.loads(request.POST.get("question"))
    question = help_svc.save_helpquestion(question_data)
    return JsonResponse(question, safe=False)


@ajax_roles_required(["Suporte", "Legal", "Redator"])
def publish_faq_version(request):
    version = json.loads(request.POST.get("params")).get("version")
    questions = help_svc.publish_faq_version(version)
    return JsonResponse(questions, safe=False)


@ajax_roles_required(["Suporte", "Legal", "Redator"])
def update_helpquestion_indexes(request):
    body = json.loads(request.body)
    form = HelpQuestionIndexUpdateForm.parse_obj(body)
    help_svc.update_helpquestion_indexes(update_form=form)
    return JsonResponse({"updated": True})


@ajax_legal_required
def salvar_texto(request):
    texto_data = json.loads(request.POST.get("texto"))
    user = request.user
    texto = textos_svc.salvar_texto(texto_data, user)
    return JsonResponse(texto, safe=False)


@ajax_seguranca_required
def list_indicators_anomalias_v2(request, grupo_id):
    indicators_anomalias_qs = fadiga_svc.anomalias(grupo_id)
    anomalias = serializer_anomalias.serialize_anomalias(indicators_anomalias_qs)
    return JsonResponse({"indicators_anomalias": anomalias})


@ajax_seguranca_required
def list_indicators_anomalias_agrupado(request):
    indicators_anomalias_qs = fadiga_svc.anomalias()
    anomalias = serializer_anomalias.serialize_anomalias_agrupado(indicators_anomalias_qs)
    return JsonResponse({"indicators_anomalias": anomalias})


@ajax_seguranca_required
def get_alert_events(request, anomalia_id):
    log_svc.log_abriu_videos_fadiga(request.user)
    anomalia_id = int(anomalia_id)
    events = list(EventoDesatencaoMotorista.objects.filter(event__pk=anomalia_id).values_list("url_video", flat=True))
    return JsonResponse(events, safe=False)


@ajax_seguranca_required
def resolve_anomalia(request, anomalia_type, anomalia_id):
    anomalia_id = int(anomalia_id)
    if anomalia_type == "telemetria":
        telemetria_svc.resolve_anomalia(anomalia_id, user=request.user)
    elif anomalia_type == "fadiga":
        fadiga_svc.resolve_anomalia(anomalia_id, user=request.user)
    return JsonResponse({"result": "Alerta resolvido com sucesso"}, safe=False)


@ajax_seguranca_required
def unresolve_anomalia(request, anomalia_type, anomalia_id):
    anomalia_id = int(anomalia_id)
    if anomalia_type == "telemetria":
        telemetria_svc.unresolve_anomalia(anomalia_id)
    elif anomalia_type == "fadiga":
        fadiga_svc.unresolve_anomalia(anomalia_id)
    return JsonResponse({}, safe=False)


@ajax_staff_required
def list_telemetria(request, grupo_id):
    grupo_id = int(grupo_id)
    grupo = (
        Grupo.objects.select_related(
            "onibus",
            "rota",
        )
        .prefetch_related(
            models.Prefetch(
                "rota__itinerario",
                queryset=Checkpoint.objects.to_serialize(),
            ),
        )
        .get(pk=grupo_id)
    )

    if grupo.onibus is None:
        raise ValidationError("Grupo não possui ônibus definido.")

    indicadores_fadiga = list(fadiga_svc.indicators(grupo))
    traces = telemetria_svc.traces(grupo)
    dtraces = [t.trace() for t in traces]

    checkpoints = telemetria_svc.get_rota_checkpoints(grupo)
    return JsonResponse(
        {
            "grupo": str(grupo),
            "eventos": [],
            "traces": dtraces,
            "indicadores_fadiga": indicadores_fadiga,
            "checkpoints": checkpoints,
        },
        safe=False,
    )


@ajax_staff_required
def tabela_auxiliar_telemetria_debug(request, grupo_id):
    # essa view morre em breve, estou debugando o resultado do algoritmo em prod
    qs = GrupoLocalTempoParada.objects.filter(grupo_id=grupo_id)
    if qs:
        qs.delete()
        redis = get_master_client()
        redis.set(f"last_telemetry_processeced_{grupo_id}", -1, ex=100)

    grupo = Grupo.objects.get(id=grupo_id)
    create_grupo_local_tempo_parada(grupo)
    return JsonResponse({})


@ajax_staff_required
def alerta_annotate_description(request):
    form = AnnotationForm.parse_raw(request.body)
    fadiga_svc.annotate_alerta(form)
    return JsonResponse({})


@ajax_seguranca_required
@require_POST
def alertas_annotate_description(request):
    form = AnnotationMultipleForm.parse_raw(request.body)
    fadiga_svc.annotate_alertas(request.user, form)
    return JsonResponse({})


@ajax_staff_required
def detalhar_onibus(request, onibus_id):
    donibus = onibus_svc.detalhar_onibus(onibus_id)
    return JsonResponse(donibus, safe=False)


@ajax_staff_required
def thumbnail_onibus(request, onibus_id):
    url = onibus_svc.thumbnail_onibus(onibus_id)
    if not url:
        return HttpResponseNotFound()
    return redirect(url)


@ajax_roles_required("Operacoes")
def upload_doc_onibus(request, bus_id, field):
    file = request.FILES.get(field)
    _, url = onibus_svc.upload(file, bus_id, field)

    return JsonResponse({"url": url})


@ajax_permission_required(roles.DAR_EMPRESTIMO)
@require_POST
def emprestar_dinheiro(request, company_id=None):
    if company_id is None:
        company_id = request.POST.get(company_id)
    form = EmprestarDinheiroForm(request.POST)
    if not form.is_valid():
        return JsonResponse(form.errors, status=400)

    form = form.cleaned_data

    emprestimo, _ = company_lending_svc_v2.create_lending(
        form["value"], form["installments"], form["annual_interest_rate"], form["start_date"], company_id, request.user
    )
    return JsonResponse(emprestimo.to_dict_json())


@ajax_roles_required(["Suporte", "Operacoes"])
@require_POST
def fechar_massivo(request):
    data = request.POST
    group_ids = json.loads(data.get("groups"))
    reason = data["reason"]
    grupo_status_svc.fechar_grupos_massivo(group_ids, reason, request.user)
    return JsonResponse({})


@ajax_roles_required(["Suporte", "Operacoes"])
@require_POST
def fechar_trechos(request: HttpRequest) -> JsonResponse:
    fechar_trechos_form = FecharTrechosForm.parse_raw(request.body)
    grupo_status_svc.fechar_trechos(fechar_trechos_form, request.user)
    return JsonResponse({})


@ajax_roles_required(["Suporte", "Operacoes"])
@require_POST
def abrir_massivo(request):
    data = request.POST
    group_ids = json.loads(data.get("groups"))
    grupo_status_svc.abrir_grupos_massivo(group_ids, request.user)
    return JsonResponse({})


# Freshchat integration
@public_endpoint  # A autorização acontece na view.
def user_preview(request):
    auth_token = request.headers.get("Authorization")
    remote_addr = request.META.get("REMOTE_ADDR")
    missing_token = not auth_token
    token_invalido = auth_token != settings.FRESHCHAT_SMARTPLUG_TOKEN
    ip_invalido = settings.FRESHCHAT_SMARTPLUG_ALLOWED_IP and settings.FRESHCHAT_SMARTPLUG_ALLOWED_IP != remote_addr
    if any([missing_token, token_invalido, ip_invalido]):
        code = 3
        if missing_token:
            code = 1
        elif token_invalido:
            code = 2
        return HttpResponse("Acesso proibido. code=%s" % code, status=403)
    phone = request.GET.get("phone")
    user_id = request.GET.get("id")
    html = freshchat_svc.get_user_preview(phone, user_id)
    return HttpResponse(html)


# Freshchat integration
@public_endpoint
@csrf_exempt
@owner(SQUAD_AQUISICAO)
def freshchat_webhook(request):
    headers = dict(request.headers)
    data = request.body.decode()
    freshchat_ips = settings.FRESHCHAT_ALLOWED_IPS
    if freshchat_ips:
        client_ip = get_client_ip(request)
        if client_ip not in freshchat_ips:
            buserlogger.info("freshchat com ip estranho ip=%s body=%s", client_ip, request.body)
            log_svc.log_freshchat_webhook({"headers": headers, "body": data, "error": "ip estranho"})
            return HttpResponse("Acesso proibido.", status=403)
    signature = headers.get("X-Freshchat-Signature")
    # https://support.freshchat.com/support/solutions/articles/239404-freshchat-webhooks-payload-structure-and-authentication
    if freshchat_svc.verify_signature(signature, request.body):
        log_svc.log_freshchat_webhook({"headers": headers, "body": data})
    else:
        buserlogger.info("freshchat webhook error signature=%s body=%s", signature, request.body)
        log_svc.log_freshchat_webhook(
            {"headers": headers, "body": data, "error": "nao foi possivel verificar assinatura"}
        )
        return HttpResponse("Acesso Proibido.", status=403)
    jsondata = json.loads(data)
    freshchat_svc.handle_webhook_async.delay(jsondata)
    return JsonResponse({})


@ajax_staff_required
def check_drivers_avaliability(request):
    params = json.loads(request.GET.get("params"))
    driver_ids = params.get("drivers")
    group_id = params.get("group_id")
    result = driver_svc.check_staff_drivers_avaliability(driver_ids, group_id)
    return JsonResponse(result)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca"])
@require_POST
def salvar_aviso_parceiro(request):
    params = json.loads(request.POST.get("aviso", "{}"))
    anexos = request.FILES.getlist("anexos", []) or []
    aviso = avisos_svc.salvar_aviso_parceiro(params=params, user=request.user, anexos=anexos)
    return JsonResponse(aviso, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca"])
@require_POST
def apagar_aviso_parceiro(request):
    id = request.POST["aviso_id"]
    aviso = avisos_svc.apagar_aviso_parceiro(id=id)
    return JsonResponse(aviso, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca"])
@require_POST
def reativar_aviso_parceiro(request):
    id = request.POST["aviso_id"]
    aviso = avisos_svc.reativar_aviso_parceiro(id=id)
    return JsonResponse(aviso, safe=False)


@ajax_staff_required
@require_GET
def get_avisos_parceiro(request):
    is_staff = True
    form = GetAvisosParceirosForm.parse_raw(request.GET.get("params", "{}"))
    avisos_qs = avisos_svc.get_avisos_parceiro(
        request.user, is_staff=is_staff, is_active=form.is_active, serialize=False
    )
    if form.has_pagination:
        paginator = Paginator(avisos_qs, form.paginator.rows_per_page)
        try:
            page = paginator.page(form.paginator.page)
        except EmptyPage:
            page = paginator.page(1)
        avisos_qs = page.object_list
        total_length = paginator.count
    else:
        total_length = len(avisos_qs)

    avisos = serializer_aviso_parceiro.serialize_parceiro(avisos_qs, is_staff=is_staff)
    return JsonResponse({"avisos": avisos, "total_length": total_length}, safe=False)


@require_POST
@ajax_permission_required(roles.ENVIAR_AVISOS_MOTORISTA)
def salvar_aviso_motorista(request):
    params = json.loads(request.POST.get("aviso", "{}"))
    imagem = request.FILES.getlist("image", []) or None
    mudar_imagem = request.POST.get("imageModifyed") == "true" or False
    if imagem:
        imagem = imagem[0]
    aviso = avisos_svc.salvar_aviso_motorista(
        params=params, imagem=imagem, mudar_imagem=mudar_imagem, user=request.user
    )
    return JsonResponse(aviso)


@require_POST
@ajax_permission_required(roles.ENVIAR_AVISOS_MOTORISTA)
def apagar_aviso_motorista(request):
    id = int(request.POST["aviso"])
    aviso = avisos_svc.apagar_aviso_motorista(id=id)
    return JsonResponse(aviso, safe=False)


@require_POST
@ajax_permission_required(roles.ENVIAR_AVISOS_MOTORISTA)
def desativar_aviso_motorista(request):
    id = int(request.POST["aviso"])
    aviso = avisos_svc.desativar_aviso_motorista(id=id)
    return JsonResponse(aviso, safe=False)


@require_POST
@ajax_permission_required(roles.ENVIAR_AVISOS_MOTORISTA)
def ativar_aviso_motorista(request):
    id = int(request.POST["aviso"])
    aviso = avisos_svc.ativar_aviso_motorista(id=id)
    return JsonResponse(aviso, safe=False)


@ajax_staff_required
@require_GET
def get_avisos_motorista(request):
    params = json.loads(request.GET.get("params", "{}"))
    ativo = params.get("is_active")
    avisos = avisos_svc.get_avisos_motorista_from_staff(request.user, ativo=ativo)
    return JsonResponse(avisos, safe=False)


@ajax_staff_required
@require_POST
def importar_ajuste_precos(request):
    params = request.POST
    csv_file = request.FILES["arquivo"]

    ajuste_preco = AjustePreco.objects.create(
        descricao=params["descricao"], status=AjustePreco.Status.PROCESSANDO, submitted_by=request.user
    )
    csv_content = csv_file.read()
    task = preco_svc.valida_e_processa_ajuste_precos_csv_task.delay(csv_content, ajuste_preco.id)
    preco_svc.save_task_metadata(ajuste_preco, task.id)
    return JsonResponse(ajuste_preco.to_dict_json(), safe=False)


@ajax_staff_required
@require_POST
def importar_ajuste_precos_parquet(request):
    params = request.POST
    parquet_file = request.FILES["arquivo"]
    parquet_content = parquet_file.read()
    ajuste_preco = AjustePreco.objects.create(
        descricao=params["descricao"], status=AjustePreco.Status.PROCESSANDO, submitted_by=request.user
    )
    task = preco_svc.valida_e_processa_ajuste_precos_parquet_task.delay(
        parquet_content, ajuste_preco.id, ajuste_preco.descricao
    )
    preco_svc.save_task_metadata(ajuste_preco, task.id)
    return JsonResponse(ajuste_preco.to_dict_json(), safe=False)


@public_endpoint
@csrf_exempt
@require_POST
def webhook_ajuste_precos(request):
    dbody = json.loads(request.body)
    sns_helper.valida_assinatura_sns(dbody)
    message = json.loads(dbody["Message"])
    buserlogger.info("ajuste_automatico_de_precos", extra=message)
    log_svc.log_sns(dbody)
    key, bucket = preco_svc.get_key_and_bucket_from_sns_message(message)
    csv_content = storage.read_bytes(key, storage=BuserDataStorage(bucket_name=bucket))
    ajuste_preco = AjustePreco.objects.create(
        descricao="ajuste de preço automático", status=AjustePreco.Status.PROCESSANDO
    )
    task = preco_svc.valida_e_processa_ajuste_precos_csv_task.delay(csv_content, ajuste_preco.id)
    preco_svc.save_task_metadata(ajuste_preco, task.id)
    return HttpResponse()


@public_endpoint
@csrf_exempt
@require_POST
def webhook_ajuste_precos_parquet(request):
    dbody = json.loads(request.body)
    log_svc.log_sns(dbody)
    sns_helper.valida_assinatura_sns(dbody)
    message = json.loads(dbody["Message"])
    key, bucket = preco_svc.get_key_and_bucket_from_sns_message(message)
    parquet_content = storage.read_bytes(key, storage=BuserDataStorage(bucket_name=bucket))
    buserlogger.info("ajuste_automatico_de_precos_parquet", extra=message)
    ajuste_preco = AjustePreco.objects.create(descricao="Em processamento...", status=AjustePreco.Status.PROCESSANDO)
    task = preco_svc.valida_e_processa_ajuste_precos_parquet_task.delay(
        parquet_content, ajuste_preco.id, ajuste_preco.descricao
    )
    preco_svc.save_task_metadata(ajuste_preco, task.id)
    return HttpResponse()


@ajax_staff_required
@require_GET
def listar_ajuste_precos(request):
    params = request.GET
    precos = preco_svc.ler_ajustes_de_precos(params)
    precos = [p.to_dict_json() for p in precos]
    return JsonResponse(precos, safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
def interromper_frete_grupo(request):
    grupo = get_object_or_404(Grupo, id=int(request.POST["grupo_id"]))
    rateio_svc.interromper_frete_grupo(grupo, user=request.user, fluxo=request.path)
    return JsonResponse({}, safe=False)


@ajax_staff_required
@require_GET
def get_logs_ajuste_preco_grupo(request, grupo_id):
    grupo = get_object_or_404(Grupo, id=int(grupo_id))
    return JsonResponse(preco_svc.get_logs_ajuste_preco_grupo(grupo))


@ajax_staff_required
@require_GET
def filtros_grupos(request):
    filtros = grupo_filtros_svc.filtros()
    return JsonResponse(filtros)


@require_POST
@ajax_roles_required(["Operacoes", "Suporte", "Risco", "PosBO", "Seguranca"])
def escalar_empresa_onibus_rotina_async(request: HttpRequest) -> HttpResponse:
    payload = EscalarEmpresaOnibusRotinaForm.parse_raw(request.body)

    operation = "escalar_empresa_onibus_rotina"
    payload_hash = hashlib.md5(
        payload.json(exclude_defaults=True, exclude={"motivo_description"}, sort_keys=True).encode()
    )
    idempotency_key = f"async_task:{operation}#{payload_hash.hexdigest()}"

    input_data = payload.dict(exclude_unset=True)
    async_task = idempotent_make_async_task(
        request, input_data, grupo_crud_svc.escalar_empresa_onibus_rotina_task, idempotency_key
    )

    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@ajax_roles_required("VendasLider")
def get_revendedores(request):
    kind = request.GET.get("kind")
    revendedores = revenda_svc.get_revendedores(kind)
    response = {"kind": kind, "revendedores": []}
    for id, first_name, last_name in revendedores:
        response["revendedores"].append({"id": id, "name": f"{first_name} {last_name}"})

    response["revendedores"] = sorted(response["revendedores"], key=lambda x: x["name"])
    return JsonResponse(response)


@ajax_roles_required("VendasLider")
@require_POST
def assign_revendedor_to_travel(request):
    user_id = request.POST.get("user_id")
    travel_id = request.POST.get("travel_id")
    travel, user_revendedor = revenda_svc.assign_revendedor_to_travel(user_id, travel_id)
    log_svc.log_atribui_travel_to_revendedor(travel, request.user, user_revendedor)
    return JsonResponse({})


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Suporte"])
def list_locais_embarque_de_grupos(request):
    form_request = CompanyIdForm.parse_obj(request.GET.dict())
    company_id = form_request.company.id
    search_pattern = form_request.search
    locais = locais_retirada_svc.list_locais_embarque_de_grupos(company_id, search_pattern)
    return JsonResponse(locais, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Suporte", "Infra"])
def verifica_se_existe_local_embarque_na_posicao(request):
    data = PdePositionForm.parse_obj(request.GET.dict())
    places = rotasadm_svc.pde_exists_in_this_position(data)

    return JsonResponse({"places_in_same_position": places}, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Suporte", "Infra"])
def list_locais_retirada_marketplace(request):
    company_id = CompanyIdForm.parse_obj(request.GET.dict()).company.id
    locais = locais_retirada_svc.list_locais_retirada_marketplace(company_id)
    return JsonResponse(locais, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Infra"])
def create_local_retirada_marketplace(request):
    new_local_retirada = LocalRetiradaMarketplaceForm.parse_raw(request.body)
    created_local = locais_retirada_svc.create_local_retirada_marketplace(new_local_retirada)
    return JsonResponse(created_local, safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Infra"])
def remove_local_retirada_marketplace(request):
    item_id = LocalRetiradaMarketplaceIdForm.parse_obj(request.POST.dict()).local_retirada.id
    return JsonResponse(locais_retirada_svc.remove_local_retirada_marketplace(item_id), safe=False)


@ajax_roles_required(["Comercial", "Qualidade", "Seguranca", "Infra"])
def edit_local_retirada_marketplace(request):
    edited_item = LocalRetiradaMarketplaceForm.parse_raw(request.body)
    return JsonResponse(locais_retirada_svc.edit_local_retirada_marketplace(edited_item), safe=False)


@ajax_staff_required
def grupo_info_marketplace(request, grupo_id):
    grupo = get_object_or_404(Grupo, id=grupo_id)
    return JsonResponse(asdict(InfoGrupoMarketplace(grupo)))


@ajax_staff_required
def create_restriction_bypass(request):
    bypass = BypassRestrictionForm.parse_raw(request.body)
    bypass_list = rotasadm_svc.create_restriction_bypass_event(
        bypass.reason,
        bypass.restrictions,
        bypass.groups,
        user=request.user,
    )
    return JsonResponse({"bypass": [{"id": bp.id} for bp in bypass_list]}, safe=False)


@ajax_staff_required
def create_parada(request):
    parada_params = CreateCheckpointParadaListForm.parse_raw(request.body)
    parada_list = checkpoint_parada_svc.create_parada(parada_params)
    return JsonResponse({"paradas": [{"id": parada.id} for parada in parada_list]}, safe=False)


@ajax_staff_required
def lista_passageiros_viagem_rodoviaria(request):
    grupo = ListPaxViagemRodoviariaForm.parse_obj(request.GET.dict()).grupo
    return JsonResponse(rodoviaria_svc.lista_passageiros_viagem(grupo.id), safe=False)


@ajax_roles_required("Marketing")
def create_page(request):
    page = request.POST.get("page")
    background_image = request.FILES.get("background_image")
    form = LandingPageForm.parse_raw(page)
    landing_page = landing_page_svc.create_page(form, background_image)
    return JsonResponse(landing_page)


@ajax_roles_required("Marketing")
def update_page(request):
    page = request.POST.get("page")
    background_image = request.FILES.get("background_image")
    form = LandingPageForm.parse_raw(page)
    landing_page = landing_page_svc.update_page(form, background_image)
    return JsonResponse(landing_page)


@ajax_staff_required
def list_landing_pages(request):
    landing_pages = landing_page_svc.list_landing_pages().to_serialize(LandingPageSerializer)
    return JsonResponse([page.serialize() for page in landing_pages], safe=False)


@ajax_staff_required
def add_pax_na_lista_rodoviaria(request):
    params = CheckPaxForm.parse_raw(request.body)
    try:
        rodoviaria_reserva_svc.emitir_passagem_nuvemzinha_staff(
            params.travel_id, params.passenger.buseiro_id, params.passenger.buyer_cpf
        )
        grupo_id = TrechoClasse.objects.filter(id=params.trechoclasse_id).values_list("grupo_id", flat=True)[0]
        return rodoviaria_svc.lista_passageiros_viagem(grupo_id)
    except Exception as ex:
        buserlogger.error(
            "Erro ao tentar emitir passagem nuvemzinha staff",
            extra={"params": params.dict(), "error": error_str(ex)},
        )
    return JsonResponse(rodoviaria_svc.add_pax_na_lista(params.dict()), safe=False)


@ajax_staff_required
def remove_pax_da_lista_rodoviaria(request):
    params = RodoviariaRemovePaxForm.parse_raw(request.body).dict()
    return JsonResponse(rodoviaria_svc.remover_pax_da_lista(params), safe=False)


@ajax_staff_required
def edit_rota_parada(request):
    parada_params = BaseParadaListForm.parse_raw(request.body)
    created_parada_list = checkpoint_parada_svc.edit_rota_parada(parada_params)
    return JsonResponse({"paradas": [{"id": parada.id} for parada in created_parada_list]})


@ajax_staff_required
@require_GET
def get_itinerarios_marketplace(request):
    company_rodoviaria_id = int(request.GET["company_rodoviaria_id"])
    filters = request.GET.get("filters", "{}")

    try:
        itinerarios = rodoviaria_svc.get_itinerarios_marketplace(company_rodoviaria_id, filters)
    except RodoviariaException as ex:
        return JsonResponse({"error": error_str(ex)}, status=422)
    if not itinerarios:
        return JsonResponse([], safe=False)

    return JsonResponse(itinerarios)


@ajax_staff_required
@require_GET
def get_rota_marketplace_para_criar(request):
    rodoviaria_rota_id = int(request.GET["rodoviaria_rota_id"])

    rota = rodoviaria_svc.get_rota_marketplace_para_criar(rodoviaria_rota_id)
    if not rota:
        return JsonResponse({}, safe=False)

    return JsonResponse(rota)


@ajax_staff_required
@require_POST
def pos_salvar_rota(request):
    data = PosSalvarRotaForm.parse_raw(request.body)
    rodoviaria_svc.pos_salvar_rota(data.params)
    return JsonResponse({}, safe=False)


@ajax_staff_required
@require_GET
def get_rotinas_rota_marketplace(request):
    rodoviaria_rota_id = int(request.GET["rodoviaria_rota_id"])
    rota_internal_id = int(request.GET["rota_internal_id"])
    start_date = datetime.strptime(f"{request.GET['start_date']} -0300", "%Y-%m-%d %z")
    end_date = datetime.strptime(f"{request.GET['end_date']} -0300", "%Y-%m-%d %z")

    rotinas = rodoviaria_svc.get_rotinas_rota_marketplace(rodoviaria_rota_id, rota_internal_id, start_date, end_date)
    return JsonResponse(rotinas, safe=False)


@ajax_staff_required
@require_GET
def tem_grupos_abertos(request):
    rota_id = int(request.GET["rota_id"])
    company_id = int(request.GET["company_id"])
    datas = request.GET["date_filter"].split(",")
    datas = [datetime.strptime(f"{data} -0300", "%Y-%m-%d %z") for data in datas]

    tem_grupos_abertos = rodoviaria_svc.tem_grupos_abertos(rota_id, company_id, datas)
    return JsonResponse(tem_grupos_abertos, safe=False)


@ajax_staff_required
@require_POST
def criar_grupos_markeplace(request):
    data = CriarGruposMarketplaceForm.parse_raw(request.body)
    grupos_criados = rodoviaria_svc.criar_grupos_markeplace(data, request.user)
    return JsonResponse({"qtde_grupos_criados": len(grupos_criados)}, safe=False)


@ajax_staff_required
@require_GET
def get_classes_e_precos_rota(request):
    rota_id = int(request.GET["rota_id"])
    classes_e_precos = rodoviaria_svc.get_classes_e_precos_rota(rota_id)
    return JsonResponse(classes_e_precos, safe=False)


@ajax_staff_required
@require_GET
def check_itinerario_rodoviaria(request):
    grupo_id = int(request.GET["grupo_id"])
    grupo = Grupo.objects.get(pk=grupo_id)

    itinerario, erros = rodoviaria_svc.itinerario(grupo)
    if not itinerario:
        return JsonResponse(None, safe=False)

    return JsonResponse(
        {
            "rota": {"itinerario": itinerario},
            "erros": erros,
        }
    )


@ajax_staff_required
@require_GET
def atualiza_itinerario_rodoviaria(request):
    grupo_id = int(request.GET["grupo_id"])
    grupo = Grupo.objects.get(pk=grupo_id)

    if not rodoviaria_svc.tenta_atualizar_trechos_do_grupo_para_itinerario(grupo_id=grupo_id, max_tentativas=3):
        return JsonResponse({"error": "Nenhum trecho integrado encontrado"}, safe=False)

    # TODO: Retirar call extra pro rodoviaria.
    fetch_rotas_response = rodoviaria_svc.fetch_rotas(grupo_id=grupo_id)
    rota = fetch_rotas_response.get("rota")
    if not rota:
        return JsonResponse(None, safe=False)

    itinerario, erros = rodoviaria_svc.itinerario(grupo)
    if not itinerario:
        return JsonResponse(None, safe=False)

    return JsonResponse(
        {
            "rota": {"itinerario": itinerario},
            "erros": erros,
        }
    )


@ajax_staff_required
def list_links_locais_embarque(request):
    params = request.GET.get("params", "{}")
    links = rodoviaria_svc.list_links_locais_embarque(json.loads(params))
    return JsonResponse(links, safe=False)


@ajax_staff_required
def update_links_locais_embarque(request):
    params = UpdateLinkLocalForm.parse_raw(request.POST.get("link"))
    link = rodoviaria_svc.update_link_local_embarque(params)
    return JsonResponse(link, safe=False)


@ajax_staff_required
def get_map_poltronas(request):
    trechoclasse_id = int(request.GET["trechoclasse_id"])
    map_poltronas = rodoviaria_svc.get_map_poltronas(trechoclasse_id)
    return JsonResponse(map_poltronas, safe=False)


@ajax_roles_required("SegurancaLider")
def liberar_1_motora(request):
    form = Liberar1MotoraForm.parse_raw(request.body)
    gruposadm_svc.liberar_1_motora([form.grupo.id])
    return JsonResponse({})


@ajax_staff_required
def list_plataformas(request):
    local_id = request.GET.get("local_id")
    plataformas = plataforma_svc.list_plataformas(local_id)
    return JsonResponse({"plataformas": [p.serialize() for p in plataformas]})


@ajax_staff_required
def set_parada_plataformas(request):
    set_parada_form = PlataformaParadaForm.parse_raw(request.body)
    plataforma_svc.set_plataforma(set_parada_form)
    updated_group = Grupo.objects.to_serialize(serializer_plataforma.PlataformaGrupoSerializer).get(
        id=set_parada_form.grupo.id
    )
    return JsonResponse(updated_group.serialize())


@ajax_staff_required
def remove_parada_plataforma(request):
    remove_parada_form = PlataformaParadaForm.parse_raw(request.body)
    updated_group = plataforma_svc.remove_parada(remove_parada_form)
    return JsonResponse(updated_group.serialize())


@ajax_staff_required
def plataformas_list_grupos(request):
    list_parada_form = PlataformaListGroup.parse_raw(request.GET.get("params", {}))
    group_list = plataforma_svc.list_groups_next_twelve_hours(list_parada_form.local)
    return JsonResponse({"grupos": [group.serialize() for group in group_list]})


@ajax_staff_required
def get_active_valores_ressarcimento(request):
    type = request.GET.get("modelo_venda")
    incidentes = json.loads(request.GET.get("incidentes") or "[]")
    user = request.user
    prefetch_related_objects([user], "user_permissions")
    valores_ressarcimento = valores_ressarcimento_svc.list_active_valores_ressarcimento(
        type, incidentes, [p.id for p in user.user_permissions.all()]
    )
    return JsonResponse([valor.to_dict_json() for valor in valores_ressarcimento], safe=False)


@ajax_staff_required
def get_all_valores_ressarcimento(request):
    valores_ressarcimento = valores_ressarcimento_svc.list_all_valores_ressarcimento()
    return JsonResponse([valor.to_dict_json() for valor in valores_ressarcimento], safe=False)


@ajax_roles_required("SuporteLider")
def create_valor_ressarcimento(request):
    motivo_ressarcimento = json.loads(request.POST.get("motivoRessarcimento"))
    valores_ressarcimento_svc.create_valor_ressarcimento(motivo_ressarcimento, user=request.user)
    return JsonResponse({})


@ajax_roles_required("SuporteLider")
def update_valor_ressarcimento(request):
    motivo_ressarcimento = json.loads(request.POST.get("motivoRessarcimento"))
    valores_ressarcimento_svc.update_valor_ressarcimento(motivo_ressarcimento, user=request.user)
    return JsonResponse({})


@ajax_roles_required("SuporteLider")
def inactivate_valor_ressarcimento(request):
    key = request.POST.get("key")
    valores_ressarcimento_svc.inactivate_valor_ressarcimento(key, user=request.user)
    return JsonResponse({}, safe=False)


@ajax_roles_required("SuporteLider")
def reactivate_valor_ressarcimento(request):
    key = request.POST.get("key")
    valores_ressarcimento_svc.reactivate_valor_ressarcimento(key, user=request.user)
    return JsonResponse({}, safe=False)


@require_GET
@ajax_roles_required("Qualidade")
def consulta_cnpj(request):
    cnpj = request.GET["cnpj"]
    consulta = doc_check_svc.check_cnpj(cnpj)
    return JsonResponse(consulta, safe=False)


@ajax_staff_required
def trechos_vendidos_rodoviaria(request):
    group_id = request.POST.get("group_id", None)
    if not group_id:
        return JsonResponse([], safe=False)
    trechos_vendidos_rodoviaria = rodoviaria_svc.trechos_vendidos_rodoviaria(group_id)
    return JsonResponse(trechos_vendidos_rodoviaria, safe=False)


@ajax_staff_required
@require_POST
def hard_stop_empresa_rodoviaria(request):
    company_id = int(request.POST.get("company_internal_id"))
    mensagem = request.POST.get("mensagem_fechamento")
    try:
        count_grupos_classes_fechados = rodoviaria_svc.hard_stop_empresa(company_id, mensagem, request.user)
        return JsonResponse({"count": count_grupos_classes_fechados}, safe=False)
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message}, status=422)


@ajax_staff_required
@require_POST
def revert_hard_stop_empresa_rodoviaria(request):
    company_id = int(request.POST.get("company_internal_id"))
    try:
        count_grupos_classes_reabertos = rodoviaria_svc.revert_hardstop_empresa(company_id, request.user)
        return JsonResponse({"count": count_grupos_classes_reabertos})
    except ValidationError as ex:
        return JsonResponse({"error": ex.message}, status=422)


@ajax_roles_required(["Comercial", "Rotas"])
@require_POST
def editar_porcentagem_repasse_taxa_servico(request):
    user = request.user
    data = EditarRepasseTaxaServicoForm.parse_raw(request.body)
    lista_grupos = data.lista_grupos
    novo_percentual = data.porcentagem
    percentual_name = data.percentual_name
    editar_todos_grupos_futuros = data.editar_todos_grupos_futuros
    try:
        resp = editar_porcentagem_repasse_taxa_servico_svc.editar(
            lista_grupos, novo_percentual, user, percentual_name, editar_todos_grupos_futuros
        )
    except ValidationError as ex:
        return JsonResponse({"error": ex.message}, safe=False, status=400)
    return JsonResponse(resp, safe=False)


@ajax_roles_required(["Risco", "Torre"])
@require_POST
def encerrar_viagem(request):
    user = request.user
    grupo_id = request.POST.get("grupo_id")
    try:
        grupos_svc.encerrar_viagem(grupo_id, user.id)
    except Exception as ex:
        return JsonResponse({"error": error_str(ex)}, safe=False, status=400)
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
@require_POST
def atualizar_permissao_taxa_servico_checkout(request):
    data = AtualizaPermissaoTaxaServicoCheckoutForm.parse_raw(request.body)
    company_id = data.company.id
    cobra_taxa_servico_checkout = data.cobra_taxa_servico_checkout
    try:
        company_svc.update_company_taxa_servico_checkout(company_id, cobra_taxa_servico_checkout)
    except Exception as ex:
        return JsonResponse({"error": ex.message}, safe=False, status=400)
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Suporte", "Qualidade"])
@require_POST
def user_forgot_password(request):
    user_id = int(request.POST.get("user_id", None))
    auth_svc.forgot_password(user_id, evento_comunicacao="views_staff.user_forgot_password")
    return JsonResponse({})


@ajax_roles_required("Rotas")
def cadastrar_grupos_hibridos_rodoviaria_params(request):
    company_id = request.GET.get("company_id", None)
    rota_id = request.GET.get("rota_id", None)
    infos = rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria_params(company_id, rota_id)
    return JsonResponse(infos, safe=False)


@ajax_roles_required(["Rotas", "Seguranca"])
@require_POST
def cadastrar_grupos_hibridos_rodoviaria(request):
    criar_grupo_hibrido_params = CadastrarGruposHibridosForm.from_params(request.POST)
    try:
        response = rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria(
            criar_grupo_hibrido_params.company_id, criar_grupo_hibrido_params.grupos_ids
        )
    except rodoviaria_svc.RotaNaoCadastradaRodoviaria as ex:
        return JsonResponse(
            {"error": ex.message, "type": ex.type, "rota_id": ex.rota_id, "company_id": ex.company_id}, status=412
        )
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
@require_POST
def save_repasse_referencia_empresa_marketplace(request, company_id):
    fromuser = request.user
    perc_repasse = Decimal(request.POST.get("percentual_repasse", -1))
    if perc_repasse <= 0:
        return JsonResponse({"error": "Parametros não são válidos"}, safe=False, status=422)

    repasse_empresa_marketplace_svc.save(company_id, perc_repasse, fromuser)
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
@require_GET
def get_repasse_referencia_empresa_marketplace(request, company_id):
    return JsonResponse(repasse_empresa_marketplace_svc.get_percentual_repasse_referencia(company_id), safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
@require_POST
def save_taxa_servico_referencia_empresa_marketplace(request, company_id):
    fromuser = request.user
    perc_taxa_servico = Decimal(request.POST.get("percentual_taxa_servico", -1))
    if perc_taxa_servico <= 0:
        return JsonResponse({"error": "Parametros não são válidos"}, safe=False, status=422)

    editar_porcentagem_repasse_taxa_servico_svc.editar_percentual_referencia_marketplace(
        company_id, perc_taxa_servico, fromuser
    )
    return JsonResponse({}, safe=False)


@ajax_roles_required(["Comercial", "Rotas"])
@require_GET
def get_taxa_servico_referencia_empresa_marketplace(request, company_id):
    return JsonResponse(
        taxa_servico_checkout_svc.get_percentual_referencia_marketplace_taxa_servico_checkout(company_id), safe=False
    )


@ajax_roles_required("Rotas")
@require_POST
def abrir_trechos(request):
    abrir_trechos_params = AbrirTrechosForm.from_params(request.POST)
    rodoviaria_svc.abrir_trechos(abrir_trechos_params.grupos_ids)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def criar_itinerario_hibrido(request):
    criar_itinerario_hibrido_params = RotaHibridoForm.from_params(request.POST)
    response = rodoviaria_svc.criar_itinerario_hibrido(criar_itinerario_hibrido_params)
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def atualizar_embarques_hibrido(request):
    atualizar_embarques_hibrido_params = RotaHibridoForm.from_params(request.POST)
    response = rodoviaria_svc.atualizar_embarques_hibrido(atualizar_embarques_hibrido_params)
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def criar_rota_hibrido(request):
    criar_rota_hibrido_params = CriarRotaHibridoForm.from_params(request.POST)
    response = rodoviaria_svc.criar_rota_hibrido(criar_rota_hibrido_params)
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def sincronizar_rota_hibrido(request):
    grupo_id = json.loads(request.POST.get("grupo_id"))
    response = rodoviaria_svc.sincronizar_rota_hibrido_por_grupo(grupo_id=grupo_id)
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_GET
def list_autorizacoes_hibrido(request):
    list_autorizacoes = autorizacao_hibrido_svc.list_autorizacoes_hibrido()
    list_autorizacoes = [a.to_dict_json() for a in list_autorizacoes]
    return JsonResponse({"autorizacoes": list_autorizacoes}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def alterar_autorizacao_grupo_hibrido(request):
    form = AlterarAutorizacaoGrupoHibrido.from_params(request.POST)
    autorizacao_hibrido_svc.alterar_autorizacao_grupo_hibrido(form.grupo_id, form.autorizacao)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_POST
def cancelar_bpes(request):
    grupo_ids = request.POST.getlist("grupo_ids")
    response = rodoviaria_svc.cancelar_bpes(grupo_ids)
    if isinstance(response, dict) and "error" in response:
        return JsonResponse(response, safe=False, status=422)
    return JsonResponse({}, safe=False)


@ajax_roles_required("Rotas")
@require_GET
def get_parametros_precificacao(request):
    classes_e_precos = globalsettings_svc.get("price_valor_ticket_por_km")
    return JsonResponse(classes_e_precos)


@ajax_roles_required("Rotas")
@require_POST
def altera_parametros_precificacao(request):
    ticket_km_classes = request.POST.dict()
    # valida se entrada está correta
    ParametrosPrecificacaoForm.parse_obj(ticket_km_classes)

    globalsettings_svc.set("price_valor_ticket_por_km", ticket_km_classes)
    return JsonResponse({})


@ajax_roles_required("Privacy")
@require_POST
@no_toast
def delete_user_data(request):
    user_id = json.loads(request.POST.get("user_id"))
    force = json.loads(request.POST.get("force"))
    privacy_svc.delete_user_data(user_id, force)
    return JsonResponse({}, safe=False)


@ajax_can_edit_communication_template
@require_POST
def create_template_comunicacao(request):
    fromuser = request.user
    problem_description = request.POST.get("problem_description")
    email = json.loads(request.POST.get("email"))
    sms = json.loads(request.POST.get("sms"))
    push = json.loads(request.POST.get("push"))
    staff_notification_svc.create_template_comunicacao(fromuser, problem_description, email, sms, push)
    return JsonResponse({})


@ajax_can_edit_communication_template
@require_POST
def edit_template_comunicacao(request):
    fromuser = request.user
    _id = request.POST.get("id")
    problem_description = request.POST.get("problem_description")
    email = json.loads(request.POST.get("email"))
    sms = json.loads(request.POST.get("sms"))
    push = json.loads(request.POST.get("push"))
    staff_notification_svc.edit_template_comunicacao(fromuser, _id, problem_description, email, sms, push)
    return JsonResponse({})


@ajax_can_edit_communication_template
@require_POST
def delete_template_comunicacao(request):
    fromuser = request.user
    _id = request.POST.get("id")
    motivo = request.POST.get("motivo")
    staff_notification_svc.delete_template_comunicacao(fromuser, _id, motivo)
    return JsonResponse({})


@ajax_roles_required("Suporte")
@require_GET
def list_templates_comunicacao(request):
    res = staff_notification_svc.list_templates_comunicacao()
    return JsonResponse(res, safe=False)


@ajax_roles_required("SuporteLider")
@require_POST
def create_canal_atendimento(request):
    canal_atendimento_nome = request.POST.get("nome_canal")
    canal_atendimento_svc.create_canal_atendimento(canal_atendimento_nome)
    return JsonResponse({})


@ajax_roles_required("SuporteLider")
@require_POST
def update_canal_atendimento(request):
    _id = json.loads(request.POST.get("id"))
    novo_nome = request.POST.get("nome_canal")
    canal_atendimento_svc.update_canal_atendimento(_id, novo_nome)
    return JsonResponse({})


@ajax_roles_required("SuporteLider")
@require_POST
def delete_canal_atendimento(request):
    _id = request.POST.get("id")
    nome_canal = request.POST.get("nome_canal")
    canal_atendimento_svc.delete_canal_atendimento(_id, nome_canal)
    return JsonResponse({})


@ajax_roles_required("SuporteLider")
@require_GET
def list_permissions(request):
    permissions = valores_ressarcimento_svc.list_permissions()
    return JsonResponse([{"id": p.id, "name": p.name, "codename": p.codename} for p in permissions], safe=False)


@ajax_roles_required("Suporte")
@require_GET
def list_all_canais_atendimento(request):
    canais_de_atendimento = canal_atendimento_svc.list_all_canais_atendimento()
    return JsonResponse([{"id": c.id, "nome": c.nome} for c in canais_de_atendimento], safe=False)


@ajax_roles_required("Suporte")
@require_GET
def atraso_trecho_grupo(request, grupo_id, trecho_id):
    atraso = grupos_svc.atraso_trecho_grupo(grupo_id, trecho_id)
    return JsonResponse(atraso if atraso else {}, safe=False)


@ajax_staff_required
@require_GET
def new_incidents_general_group_infos(request):
    form = NewIncidentsGeneralGroupInfosForm.parse_raw(request.GET.get("group_id"))
    general_group_infos = grupos_svc.new_incidents_general_group_infos(form.group_id)

    return JsonResponse(general_group_infos)


@ajax_staff_required
@require_POST
def update_geofence(request):
    feature_collection = request.POST["feature_collection"]
    Geofence.objects.update_or_create(defaults={"feature_collection": feature_collection}, pk=1)
    return JsonResponse({}, status=201)


@ajax_roles_required(["Operacoes", "VendasLider"])
@require_http_methods(["GET", "POST"])
def update_hidden_from_pax(request):
    if request.method == "POST":
        form = AlterarHiddenForPaxStatus.from_params(request.POST)
        grupo_crud_svc.update_hidden_from_pax(
            grupo_id=form.group_id, hidden_for_pax_status=form.status, user=request.user
        )
        return JsonResponse({"group_id": form.group_id, "hidden_for_pax_status": form.status})
    form = GetHiddenPaxForStatusForm.from_params(request.GET)
    group_id = form.group_id
    status = grupo_crud_svc.get_hidden_from_pax(group_id)
    return JsonResponse({"group_id": group_id, "hidden_for_pax_status": status})


@ajax_staff_required
@require_GET
def get_configuracao_pagamento(request, company_id):
    result_data = []

    for modelo_venda in Grupo.ModeloVenda:
        info_confg_pagamento = configuracao_pagamento_svc.get_configuracao_pagamento(company_id, modelo_venda)
        tipo_pagamento = configuracao_pagamento_svc.get_tipo_pagamento(company_id, modelo_venda)
        prazo_pagamento_semana = configuracao_pagamento_svc.get_prazo_pagamento_semana(company_id, modelo_venda)
        porcentagem_repasse = configuracao_pagamento_svc.get_porcentagem_repasse(company_id, modelo_venda)
        taxa_antecipacao = configuracao_pagamento_svc.get_taxa_antecipacao(company_id, modelo_venda)
        percentual_max_antecipacao = configuracao_pagamento_svc.get_percentual_max_antecipacao(company_id, modelo_venda)

        data_dict = {
            "id": info_confg_pagamento.id if info_confg_pagamento else None,
            "modelo_venda": modelo_venda,
            "tipo_pagamento": tipo_pagamento,
            "prazo_pagamento_semana": prazo_pagamento_semana,
            "porcentagem_repasse": porcentagem_repasse,
            "taxa_antecipacao": taxa_antecipacao,
            "percentual_max_antecipacao": percentual_max_antecipacao,
        }

        result_data.append(data_dict)
    return JsonResponse(result_data, safe=False)


@ajax_permission_required(roles.CAN_EDIT_CONFIGURACAO_PAGAMENTO)
@csrf_exempt
@require_POST
def edit_configuracao_pagamento(request):
    form = ConfiguracaoPagamentoForm.parse_obj(request.POST.dict())
    fromuser = request.user
    configuracao_pagamento_svc.upsert_config_pagamento(
        company_id=form.company_id,
        modelo_venda=form.modelo_venda,
        tipo_pagamento=form.tipo_pagamento,
        prazo_pagamento_semana=form.prazo_pagamento_semana,
        porcentagem_repasse=form.porcentagem_repasse,
        taxa_antecipacao=form.taxa_antecipacao,
        percentual_max_antecipacao=form.percentual_max_antecipacao,
        updated_by=fromuser,
    )
    return JsonResponse({})


@ajax_staff_required
@require_GET
def get_company_admins(request, company_id):
    admins = company_svc.get_company_admins_with_permissions(company_id)
    return JsonResponse({"admins": admins})


@ajax_staff_required
@require_GET
def get_company_profile(request, company_id):
    company_id = int(company_id)
    company = company_svc.get_company_profile(company_id)
    if not company:
        return JsonResponse({}, status=404)
    return JsonResponse(company)


@ajax_staff_required
def multas_qualidade(request: AuthenticatedHttpRequest, grupo_id: int) -> JsonResponse:
    if request.method == "GET":
        multas_qs = multa_svc.listar_multas_qualidade(grupo_id)
        return JsonResponse({"multas": [multa.serialize() for multa in multas_qs]})
    elif request.method == "POST":
        multa = request.POST.get("multa", "{}")
        anexos_evidencias = request.FILES.getlist("anexos", []) or []
        form = IndicarMultaDjangoForm.parse_raw(multa)
        multa_svc.indicar_multa(
            grupo_id,
            request.user,
            motivo_id=form.tipo,
            origem=form.origem,
            company=form.empresa,
            incidente=form.incidente,
            valor_multa=form.valor_multa,
            descricao=form.descricao,
            anexos_evidencias=anexos_evidencias,
        )
        return JsonResponse({}, status=201)
    return JsonResponse({"message": "Method not allowed."}, status=405)


@api_key_required("torre")
@csrf_exempt
@require_POST
def multas_qualidade_torre(request: AuthenticatedHttpRequest, grupo_id: int) -> JsonResponse:
    form = IndicarMultaTorreForm.parse_raw(request.body)
    multa_svc.indicar_multa(
        grupo_id,
        request.user,
        motivo_id=form.tipo,
        origem=form.origem,
        incidente=form.incidente,
        valor_multa=form.valor_multa,
    )
    return JsonResponse({}, status=201)


@ajax_staff_required
def list_motivos_multas_qualidade(request: HttpRequest):
    motivos = MotivoMulta.objects.to_serialize(MotivoMultaSerializer).filter(is_active=True)
    return JsonResponse({"motivos": [motivo.serialize() for motivo in motivos]})


@ajax_roles_required(["Qualidade", "RiscoLider"])
def aplicar_multa(request: HttpRequest, multa_id: int):
    try:
        multa_svc.aplicar_multa(multa_id, request.user)
    except ValueError as err:
        return JsonResponse({"error": error_str(err)}, status=HTTPStatus.BAD_REQUEST)

    return JsonResponse({"message": "Multa aplicada com sucesso."}, status=HTTPStatus.OK)


@ajax_roles_required(["Qualidade", "RiscoLider"])
def descontar_multa(request: HttpRequest, multa_id: int):
    try:
        multa_svc.descontar_multa(multa_id, request.user)
    except ValidationError as e:
        return JsonResponse({"error": e.message}, status=HTTPStatus.BAD_REQUEST)
    return JsonResponse({"message": "Multa descontada com sucesso."}, status=HTTPStatus.OK)


@ajax_roles_required(["Qualidade", "RiscoLider", "Risco", "Torre", "Seguranca"])
def editar_multa(request: HttpRequest, multa_id: int):
    multa = request.POST.get("multa", "{}")
    novos_anexos = request.FILES.getlist("anexos", []) or []
    form = IndicarMultaDjangoForm.parse_raw(multa)
    multa_svc.editar_multa(form, multa_id, novos_anexos)
    return JsonResponse({})


@ajax_roles_required(["Qualidade", "RiscoLider", "Risco", "Torre", "Seguranca"])
def cancelar_multa(request: HttpRequest, multa_id: int):
    multa_svc.cancelar_multa(multa_id)
    return JsonResponse({})


@ajax_staff_required
@require_GET
def get_has_pdv_associado(request):
    user_id = request.GET.get("user_id")
    revendedor = get_or_none(Revendedor, user_id=user_id)
    has_pdv = bool(revendedor and revendedor.ponto_de_venda_id)
    return JsonResponse(has_pdv, safe=False)


@ajax_staff_required
def get_rodoviaria_passagem_info(request):
    buseiro_id = request.GET.get("buseiro_id")
    travel_id = request.GET.get("travel_id")
    passagem_list = rodoviaria_svc.get_passagem_info(travel_id, buseiro_id)
    return JsonResponse(passagem_list, safe=False)


@ajax_staff_required
def get_rodoviaria_atualizacao_passagem_api_parceiro(request):
    form = AtualizacaoPassagemRodoviariaForm.parse_obj(request.GET.dict())
    try:
        passagem_list = rodoviaria_svc.get_informacoes_passagem_api_parceiro(
            form.buseiro_id, form.modelo_venda, form.travel_id
        )
        return JsonResponse(passagem_list, safe=False)
    except rodoviaria_svc.ServiceNotImplementedInRodoviaria:
        return JsonResponse(
            {"error": "Serviço ainda não implementado para essa empresa"}, status=HTTPStatus.NOT_IMPLEMENTED
        )
    except rodoviaria_svc.IntegrationErrorRodoviaria:
        return JsonResponse({"error": "Algo deu errado na integração com o sistema do marketplace"}, status=500)


@ajax_staff_required
@require_GET
def houve_reducao_de_frete(request, grupo_id):
    data_criacao_grupo = Grupo.objects.only("created_on").get(pk=grupo_id).created_on
    houve_reducao_de_frete = ActivityLog.objects.filter(
        grupo_id=grupo_id, type="reduzir_frete", created_at__gt=data_criacao_grupo
    ).exists()
    return JsonResponse({"houve_reducao_de_frete": houve_reducao_de_frete})


@ajax_staff_required
def list_integracoes_rodoviaria(request):
    try:
        integracoes = rodoviaria_svc.list_integracoes_rodoviaria()
    except Exception as ex:
        return JsonResponse({"message": str(ex)}, status=500)
    return JsonResponse(integracoes)


@ajax_staff_required
@require_GET
def get_email_content(request, channel, message_id):
    if channel == "email":
        transaction_email = get_object_or_404(TransactionEmail, id=message_id)
        return JsonResponse({"content": transaction_email.content}, status=HTTPStatus.OK)
    return JsonResponse({}, status=HTTPStatus.NOT_FOUND)


@ajax_roles_required(["Qualidade", "RiscoLider", "Risco", "Torre", "Seguranca"])
def estornar_multa(request: HttpRequest, multa_id: int):
    multa_svc.estornar_multa(multa_id, request.user)
    return JsonResponse({})


@ajax_roles_required("Marketing")
@require_POST
def create_categoria_turistica(request):
    try:
        form = CategoriaTuristicaForm.parse_obj(request.POST.dict())
        response = categoria_turistica_svc.create_categoria_turistica(form)
        return JsonResponse(response)
    except ValidationError as e:
        return JsonResponse({"error": e.message}, status=HTTPStatus.BAD_REQUEST)


@ajax_roles_required("Marketing")
@require_GET
def list_categorias_turisticas(request):
    categorias = categoria_turistica_svc.list_categorias_turisticas()
    return JsonResponse(categorias, safe=False)


@ajax_roles_required("Marketing")
@require_POST
def edit_categoria_turistica(request):
    try:
        form = CategoriaTuristicaEditForm.parse_obj(request.POST.dict())
        categoria_turistica_svc.update_categoria_turistica(form)
        return JsonResponse({})
    except ValidationError as e:
        return JsonResponse({"error": e.message}, status=HTTPStatus.BAD_REQUEST)


@ajax_roles_required("Marketing")
@require_POST
def remove_cidade_categoria(request):
    data = request.POST.get("params")
    try:
        form = CidadeCategoriaTuristicaForm.parse_raw(data)
        categoria_turistica_svc.remove_cidade_de_categoria_turistica(form)
        return JsonResponse({})
    except ValidationError as e:
        return JsonResponse({"error": e.message}, status=HTTPStatus.BAD_REQUEST)


@ajax_roles_required("Marketing")
@require_POST
def cadastra_cidade_categoria(request):
    data = request.POST.get("params")
    try:
        form = CidadeCategoriaTuristicaForm.parse_raw(data)
        categoria_turistica_svc.cadastra_cidade_em_categoria_turistica(form)
        return JsonResponse({})
    except ValidationError as e:
        return JsonResponse({"error": e.message}, status=HTTPStatus.BAD_REQUEST)


@ajax_roles_required("Marketing")
@require_GET
def lista_cidades_de_categoria(request, categoria_id):
    categorias = categoria_turistica_svc.lista_cidades_de_categoria(categoria_id)
    return JsonResponse(categorias, safe=False)


@ajax_staff_required
@require_GET
def geocode(request):
    address = request.GET["address"]
    google_maps_client: GoogleMapsClient = get_client("google_maps")
    return JsonResponse(google_maps_client.geocode(address))


@ajax_staff_required
@require_POST
def importar_leads(request):
    formato = request.POST.get("formato", "buser6anos.com.br")
    csv_file = TextIOWrapper(request.FILES["file"], encoding="iso-8859-1")

    formats = {
        "buser6anos.com.br": {
            "delimiter": ";",
            "create_user": True,
            "fieldnames": [
                "id",
                "codigo_para_indicar",
                "codigo_indicacao",
                "name",
                "cpf",
                "birth_date",
                "cep",
                "phone",
                "email",
                "aceite_contato",
                "aceite_regulamento",
                "date",
                "data_atualizacao",
            ],
            "default_utm_source": "https://buser6anos.com.br",
            "default_referrer": "buser6anos",
        },
        "Padrão": {
            "delimiter": ",",
            "create_user": False,
            "fieldnames": [
                "name",
                "email",
                "phone",
                "aceite_contato",
                "utm_source",
                "utm_medium",
                "utm_campaign",
                "date",
            ],
            "default_utm_source": None,
            "default_referrer": None,
        },
    }

    cfg = formats.get(formato)
    if cfg is None:
        return JsonResponse({"error": "Formato selecionado não é conhecido."}, status=400)

    csv_content = csv.DictReader(csv_file, fieldnames=cfg["fieldnames"], delimiter=cfg["delimiter"])

    next(csv_content)

    for group_leads in chunks(csv_content, 200):
        lead_svc.import_marketing_lead_chunk.delay(
            group_leads,
            create_user=cfg["create_user"],
            default_utm_source=cfg["default_utm_source"],
            default_referrer=cfg["default_referrer"],
        )

    return JsonResponse({"message": "CSV importado com sucesso."}, safe=False)


@ajax_roles_required(["Operacoes", "Suporte"])
@require_POST
def contabilizar_fidelidade(request, grupo_id):
    grupo = get_or_none(Grupo, id=grupo_id)
    if not grupo:
        return JsonResponse({}, status=HTTPStatus.NOT_FOUND)
    fidelidade_svc.contabilizar_travels_nos_programas_de_fidelidade(grupo=grupo)
    return JsonResponse({})


@ajax_staff_required
def get_cronograma_atualizacao_rodoviaria(request):
    params = json.loads(request.GET.get("params"))
    resp = rodoviaria_svc.get_cronograma_atualizacao_rodoviaria(params)
    return JsonResponse(resp, safe=False)


@ajax_staff_required
@require_POST
def update_cronograma_atualizacao_rodoviaria(request):
    params = json.loads(request.POST.get("params"))
    resp = rodoviaria_svc.update_cronograma_atualizacao_rodoviaria(params)
    return JsonResponse(resp, safe=False)


@ajax_staff_required
@require_GET
def get_min_rotas_integrar(request, company_id):
    try:
        response = atualizacao_operacao_completa_svc.get_min_rotas_para_integrar(company_id)
        return JsonResponse(response, safe=False)
    except ValidationError as exc:
        return JsonResponse({"error": error_str(exc)}, status=HTTPStatus.BAD_REQUEST)


@ajax_staff_required
@require_POST
def set_min_rotas_integrar(request, company_id):
    qtd_rotas = json.loads(request.POST.get("qtd_rotas"))
    try:
        qtd_min_rotas = atualizacao_operacao_completa_svc.set_min_rotas_para_integrar(company_id, qtd_rotas)
        return JsonResponse(qtd_min_rotas, safe=False)
    except ValidationError as exc:
        return JsonResponse({"error": error_str(exc)}, status=HTTPStatus.BAD_REQUEST)


@ajax_staff_required
@require_POST
def auto_integra_operacao_empresa_rodoviaria(request, company_id):
    try:
        atualizacao_operacao_completa_svc.verifica_elegibilidade_auto_integra_operacao(company_id)
    except ValidationError as exc:
        return JsonResponse({"error": error_str(exc)}, status=HTTPStatus.BAD_REQUEST)
    operation = "auto_integra_operacao_empresa_rodoviaria"
    idempotency_key = f"async_task:{operation}#{company_id}"
    async_task = idempotent_make_async_task(
        request,
        {"company_id": company_id},
        atualizacao_operacao_completa_svc.atualizar_operacao_empresa,
        idempotency_key,
    )
    company_id_executando = async_task.input_data["company_id"]
    if company_id_executando != company_id:
        return JsonResponse(
            {"error": f"Integração em execução para outra empresa ID = {company_id_executando}"},
            status=HTTPStatus.BAD_REQUEST,
        )
    return HttpResponse(headers={"Location": resolve_url(async_task), "Retry-After": 5}, status=HTTPStatus.ACCEPTED)


@ajax_roles_required(["Dev", "Marketing", "Rotas"])
@require_GET
def list_feriados(request):
    params = request.GET.get("params")
    if params:
        params = json.loads(params)
        items_per_page = params.get("items_per_page")
        which_page = params.get("page")
        search_str = params.get("search")
        feriados = feriado_adm_svc.query_feriados(search_str)
        if items_per_page:
            paginator = Paginator(feriados, per_page=items_per_page)
            page = paginator.get_page(which_page)
            response = {
                "feriados": [feriado.serialize() for feriado in page.object_list],
                "count_items": paginator.count,
                "count_pages": paginator.num_pages,
            }
        else:
            response = [feriado.serialize() for feriado in feriados]
    else:
        feriados = feriado_adm_svc.query_feriados()
        response = [feriado.serialize() for feriado in feriados]
    return JsonResponse(response, safe=False)


@require_POST
@ajax_roles_required(["Dev", "Marketing", "Rotas"])
def create_or_update_feriado(request):
    feriado = json.loads(request.POST.get("feriado", "{}"))
    background_image = request.FILES.get("background_image")
    first_section_image = request.FILES.get("first_section_image")

    form = PaginaFeriadoForm.parse_obj(feriado)
    result = feriado_adm_svc.create_or_update_feriado(form, background_image, first_section_image)
    return JsonResponse(result, safe=False, status=HTTPStatus.CREATED)


@ajax_roles_required(["Dev", "Marketing", "Rotas"])
@require_POST
def delete_feriado(request):
    feriado_code = request.POST.get("code")
    feriado_adm_svc.delete_feriado(feriado_code)
    return HttpResponse()


@ajax_staff_required
def risk_score(request, user_id):
    client: RiskScoreClient = get_client("riskscore")
    response = client.predict_user_score(user_id)[0]
    return JsonResponse(response)


@ajax_staff_required
def get_user_features(request, user_id):
    client: RiskScoreClient = get_client("riskscore")
    response = client.features_by_user(user_id)[0]
    return JsonResponse(response)


@ajax_roles_required(["Dev", "Rotas"])
@require_GET
def lista_conexoes(request):
    params = request.GET.get("params")
    params = json.loads(params)
    items_per_page = params.get("items_per_page", 20)
    which_page = params.get("page", 1)
    search = params.get("search")
    include_deleted = params.get("include_deleted", False)
    response = conexao_adm_svc.lista_conexoes(items_per_page, which_page, search, include_deleted)

    return JsonResponse(response, safe=False)


@ajax_roles_required(["Dev", "Rotas"])
@require_POST
def create_conexao(request):
    data = json.loads(request.POST.get("data", "{}"))
    try:
        data = ConexaoCreateForm.parse_obj(data)
    except ValidationError as exc:
        return JsonResponse({"error": exc.message}, status=HTTPStatus.BAD_REQUEST)
    try:
        conexao_criada = conexao_adm_svc.create_conexao(data)
        return JsonResponse(conexao_criada, safe=False, status=HTTPStatus.CREATED)
    except Exception as exc:
        return JsonResponse({"error": str(exc)}, status=400)


@ajax_roles_required(["Dev", "Rotas"])
@require_POST
def update_conexao(request):
    data = json.loads(request.POST.get("data", "{}"))
    try:
        data = ConexaoUpdateForm.parse_obj(data)
    except ValidationError as exc:
        return JsonResponse({"error": error_str(exc)}, status=HTTPStatus.BAD_REQUEST)

    try:
        if data.delete:
            conexao_adm_svc.safe_delete_conexao(data.conexao_id)
            return HttpResponse()
        conexao_atualizada = conexao_adm_svc.update_conexao(data)
        return JsonResponse(conexao_atualizada, safe=False)
    except Exception as exc:
        return JsonResponse({"error": str(exc)}, status=400)


@require_POST
@ajax_roles_required(["Qualidade", "Seguranca", "Operacoes"])
def remove_poltrona(request):
    is_user_enabled = feature_flags.is_user_enabled("marcacao_assento_release", user_id=request.user.id)

    if not is_user_enabled:
        return JsonResponse({"error": "Usuário não tem permissão para remover poltrona"}, status=401)

    form = RemovePoltronaFretamentoForm.parse_raw(request.body)
    onibus_svc.remove_poltrona(form.poltrona_id)
    return JsonResponse(None, status=204, safe=False)


@ajax_staff_required
@require_POST
def batch_update_managers(request):
    """
    View POST para atualização em lote de TrechoEstoqueManager.

    Recebe um arquivo JSON com formato:
    [{"origem_slug": str, "destino_slug": str, "companies_cnpj": list[str], "triggers": list[str]}, ...]
    """

    if "file" not in request.FILES:
        return JsonResponse({"error": "Nenhum arquivo foi enviado"}, status=400)

    uploaded_file = request.FILES["file"]

    if not uploaded_file.name.endswith(".json"):
        return JsonResponse({"error": "Arquivo deve ter extensão .json"}, status=400)

    try:
        file_content = uploaded_file.read().decode("utf-8")
        data = json.loads(file_content)
    except json.JSONDecodeError as e:
        return JsonResponse({"error": f"Erro ao parsear JSON: {str(e)}"}, status=400)
    except UnicodeDecodeError as e:
        return JsonResponse({"error": f"Erro de codificação do arquivo: {str(e)}"}, status=400)

    if not isinstance(data, list):
        return JsonResponse({"error": "O arquivo JSON deve conter uma lista de objetos"}, status=400)

    try:
        result = trecho_estoque_manager_svc.batch_update_managers(data)
    except ValidationError as e:
        return JsonResponse({"error": f"Erro de validação: {str(e)}"}, status=400)
    except Exception as e:
        capture_exception(e)
        return JsonResponse({"error": f"Erro interno: {str(e)}"}, status=500)

    return JsonResponse(
        {
            "success": True,
            "message": f"Processamento concluído com sucesso. Criados: {result['created']}, Atualizados: {result['updated']}",
        }
    )


@require_POST
@ajax_roles_required(["Rotas", "Comercial"])
@require_POST
def evento_extra_save(request: HttpRequest) -> JsonResponse:
    evento_extra_dict = json.loads(request.body)
    form = EventoExtraForm(**evento_extra_dict, updated_by_id=request.user.id)
    evento_extra = evento_extra_svc.create_or_update_evento_extra(form)
    serialized_evento_extra = EventoExtraSerializer().serialize_object(evento_extra)

    return JsonResponse(serialized_evento_extra, safe=False, status=HTTPStatus.OK)


@ajax_roles_required(["Rotas", "Comercial"])
@require_GET
def evento_extra_details(request: HttpRequest, evento_extra_id: int) -> JsonResponse:
    filters = EventoExtraDetailsFilterForm.parse_raw(request.GET.get("filters"))
    eventos_extra = evento_extra_svc.list_evento_extra_details(evento_extra_id, filters)
    serialized_evento = [evento_extra.serialize() for evento_extra in eventos_extra]
    return JsonResponse(serialized_evento, safe=False)


@require_http_methods(["DELETE"])
@ajax_roles_required(["Rotas"])
def evento_extra_delete(request: HttpRequest, evento_extra_id: int) -> JsonResponse:
    try:
        evento_extra = evento_extra_svc.delete_evento_extra(evento_extra_id)
        return JsonResponse({})

    except EventoExtra.DoesNotExist:
        return JsonResponse({"error": "Evento não encontrado."}, status=HTTPStatus.NOT_FOUND)
    except IntegrityError:
        return JsonResponse(
            {"error": error_str("Não é possível excluir evento com solicitações ativas.")},
            status=HTTPStatus.BAD_REQUEST,
        )


@ajax_roles_required(["Rotas", "Comercial"])
@require_GET
def evento_extra_list(request: HttpRequest) -> JsonResponse:
    params = EventoExtraListParamsForm.parse_raw(request.GET.get("params"))
    filters = params.filters
    paginator_form = params.paginator

    eventos_extra = evento_extra_svc.list_eventos_extra(filters, paginator_form.sort_by)
    paginator = Paginator(eventos_extra, per_page=paginator_form.rows_per_page)
    page = paginator.get_page(paginator_form.page)

    dextras = serializer_evento_extra.serialize_eventos_extra(page.object_list)
    return JsonResponse({"items": dextras, "count": paginator.count, "num_pages": paginator.num_pages}, safe=False)


@ajax_roles_required(["Rotas", "Comercial"])
@require_POST
def evento_extra_solicitacao_save(request: HttpRequest) -> JsonResponse:
    params = json.loads(request.body)
    params = EventoExtraSolicitacaoForm(**params, updated_by_id=request.user.id)

    solicitacao = evento_extra_svc.create_or_update_evento_extra_solicitacao(params)
    serialized_solicitacao = EventoExtraSolicitacaoSerializer().serialize_object(solicitacao)

    return JsonResponse(serialized_solicitacao, safe=False, status=HTTPStatus.OK)


@ajax_roles_required(["Rotas"])
@require_http_methods(["DELETE"])
def evento_extra_solicitacao_delete(request: HttpRequest, solicitacao_id: int) -> JsonResponse:
    try:
        evento_extra = evento_extra_svc.delete_evento_extra_solicitacao(solicitacao_id)
        return JsonResponse({})

    except EventoExtraSolicitacao.DoesNotExist:
        return JsonResponse({"error": f"Solicitação id {solicitacao_id} não encontrada."}, status=HTTPStatus.NOT_FOUND)


@ajax_roles_required(["Rotas", "Comercial"])
@require_POST
def evento_extra_negociacao_save(request: HttpRequest) -> JsonResponse:
    params = json.loads(request.body)
    negociacao_form = EventoExtraNegociacaoForm(**params, updated_by_id=request.user.id)
    negociacao = evento_extra_svc.create_or_update_evento_extra_negociacao(negociacao_form)
    serialized_negociacao = EventoExtraNegociacaoSerializer().serialize_object(negociacao)

    return JsonResponse(serialized_negociacao, safe=False, status=HTTPStatus.OK)


@ajax_roles_required(["Rotas"])
@require_http_methods(["DELETE"])
def evento_extra_negociacao_delete(request: HttpRequest, negociacao_id: int) -> JsonResponse:
    try:
        evento_extra = evento_extra_svc.delete_evento_extra_negociacao(negociacao_id)
        return JsonResponse({})

    except EventoExtraNegociacao.DoesNotExist:
        return JsonResponse({"error": f"Negociação id {negociacao_id} não encontrada."}, status=HTTPStatus.NOT_FOUND)


@ajax_roles_required(["Rotas", "Comercial"])
@require_POST
def evento_extra_solicitacao_perna_save(request: HttpRequest) -> JsonResponse:
    params = json.loads(request.body)
    params = SolicitacaoPernaForm(**params, updated_by_id=request.user.id)

    solicitacao_perna = evento_extra_svc.create_or_update_evento_extra_solicitacao_perna(params)
    serialized_solicitacao_perna = EventoExtraSolicitacaoPernaSerializer().serialize_object(solicitacao_perna)

    return JsonResponse(serialized_solicitacao_perna, safe=False, status=HTTPStatus.OK)


@ajax_roles_required(["Rotas"])
@require_http_methods(["DELETE"])
def evento_extra_solicitacao_perna_delete(request: HttpRequest, perna_id: int) -> JsonResponse:
    try:
        evento_extra = evento_extra_svc.delete_evento_extra_solicitacao_perna(perna_id)
        return JsonResponse({})

    except EventoExtraSolicitacaoPerna.DoesNotExist:
        return JsonResponse({"error": f"Negociação id {perna_id} não encontrada."}, status=HTTPStatus.NOT_FOUND)


@ajax_roles_required(["Rotas", "Comercial"])
@require_GET
def evento_extra_solicitacao_perna_list(request: HttpRequest) -> JsonResponse:
    paginator_form = PaginatorForm.parse_raw(request.GET.get("paginator"))
    filters = SolicitacaoPernaFiltersForm.parse_raw(request.GET.get("filters"))

    solicitacao_pernas = evento_extra_svc.list_pernas_solicitacao(filters, paginator_form.sort_by)
    paginator = Paginator(solicitacao_pernas, per_page=paginator_form.rows_per_page)
    page = paginator.get_page(paginator_form.page)
    items = [item.serialize() for item in page.object_list]
    return JsonResponse({"items": items, "count": paginator.count, "num_pages": paginator.num_pages}, safe=False)
