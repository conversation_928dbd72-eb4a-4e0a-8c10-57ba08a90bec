from datetime import datetime
from zoneinfo import ZoneInfo

from django.contrib.auth.models import User
from django.db.models import Count, Prefetch, prefetch_related_objects
from django_qserializer.serialization import BaseSerializer

from commons.serialization_utils import enforce_list
from core.models_company import Company, Onibus
from core.models_grupo import EventoExtra, EventoExtraNegociacao, EventoExtraSolicitacao, EventoExtraSolicitacaoPerna
from core.models_rota import Rota


class EventoExtraSerializer(BaseSerializer):
    def serialize_object(self, obj) -> dict:
        return {
            "id": obj.id,
            "nome": obj.nome,
            "dataInicial": obj.data_inicial,
            "dataFinal": obj.data_final,
            "status": obj.status,
            "created_at": obj.created_at,
        }


class EventoExtraNegociacaoSerializer(BaseSerializer):
    prefetch_related = [
        Prefetch("company", queryset=Company.objects.only("id", "name")),
        Prefetch("onibus", queryset=Onibus.objects.only("id", "placa", "company")),
        Prefetch("gerente_comercial", queryset=User.objects.only("id", "first_name", "last_name")),
    ]

    def serialize_object(self, obj) -> dict:
        return {
            "id": obj.id,
            "eventoExtraId": obj.evento_extra_id,
            "solicitacaoId": obj.solicitacao_extra_id,
            "gerenteComercial": {"id": obj.gerente_comercial_id, "name": obj.gerente_comercial.get_full_name()},
            "company": {"id": getattr(obj.company, "id", None), "name": getattr(obj.company, "name", None)},
            "kmPerna": obj.distancia_por_perna,
            "kmTotal": obj.distancia_total,
            "deslocamento": obj.deslocamento,
            "freteTotal": obj.frete_total,
            "freteKm": obj.frete_km,
            "cask": obj.cask,
            "ticketMedio": obj.ticket_medio,
            "cashInGMV": obj.cash_in_gmv,
            "breakeven": obj.breakeven,
            "resultadoMax": obj.resultado_max,
            "tiposAssento": obj.tipos_assento,
            "capacidade": obj.capacidade,
            "has_empresa_escalada": obj.has_empresa_escalada,
            "is_fechado_comercial": obj.is_fechado_comercial,
            "has_contrato_assinado": obj.has_contrato_assinado,
            "onibus": {"id": getattr(obj.onibus, "id", None), "placa": getattr(obj.onibus, "placa", None)},
            "createdAt": obj.created_at,
        }


class EventoExtraSolicitacaoPernaSerializer(BaseSerializer):
    prefetch_related = [
        Prefetch(
            "rota",
            queryset=Rota.objects.select_related("origem__cidade").only("id", "origem__cidade__timezone"),
        ),
        "solicitacao_extra__rota_principal",
        "solicitacao_extra",
    ]

    def _dt_to_local_tz(self, obj: EventoExtraSolicitacaoPerna) -> datetime:
        default_timezone = ZoneInfo("America/Sao_Paulo")
        if obj.rota is not None:
            default_timezone = ZoneInfo(obj.rota.origem.cidade.timezone)

        return obj.datetime_ida.astimezone(tz=default_timezone)

    def serialize_object(self, obj) -> dict:
        # nao serializar a sigla é proposital.
        # No front, é carregado pelo componente e adicionar aqui nao faz diferença
        rota = {"id": obj.rota_id, "sigla": ""}
        # how-to-prevent-date-conversion-to-local-time-zone-with-dayjs
        # https://stackoverflow.com/questions/75324638
        local_datetime_ida = self._dt_to_local_tz(obj)
        datetime_ida_without_tzinfo = local_datetime_ida.replace(tzinfo=None)
        return {
            "id": obj.id,
            "solicitacaoExtraId": obj.solicitacao_extra_id,
            "sentido": obj.sentido,
            "turno": obj.turno,
            "rota": rota,
            "rotaPrincipal": {
                "id": obj.solicitacao_extra.rota_principal_id,
                "eixo": obj.solicitacao_extra.rota_principal.eixo,
            },
            "rotaPrevista": obj.solicitacao_extra.rota_prevista,
            "datetimeIda": datetime_ida_without_tzinfo,
            "created_at": obj.created_at,
        }


def prepare_eventos_extra(eventos_extra: list[EventoExtra]):
    prefetch_objects = ["eventoextrasolicitacao_set", "eventoextranegociacao_set"]
    prefetch_related_objects(eventos_extra, *prefetch_objects)


@enforce_list
def serialize_eventos_extra(eventos_extra):
    prepare_eventos_extra(eventos_extra)
    count_solicitacao_por_evento = _get_solicitacao_por_evento(eventos_extra)
    count_negociacao_por_evento = _get_negociacao_por_evento(eventos_extra)
    return [
        _serialize_evento_extra_object(
            evento_extra,
            qtd_solicitacoes=count_solicitacao_por_evento.get(evento_extra.id, 0),
            qtd_negociacoes=count_negociacao_por_evento.get(evento_extra.id, 0),
        )
        for evento_extra in eventos_extra
    ]


def _get_solicitacao_por_evento(eventos_extra):
    eventos_count = {}
    evento_extra_ids = [ev.id for ev in eventos_extra]
    rows = EventoExtraSolicitacao.objects.filter(evento_extra_id__in=evento_extra_ids)
    if not rows:
        return eventos_count

    rows = rows.values_list("evento_extra_id").annotate(count=Count("id"))

    for r in rows:
        eventos_count[r[0]] = r[1]
    return eventos_count


def _get_negociacao_por_evento(eventos_extra):
    eventos_count = {}
    evento_extra_ids = [ev.id for ev in eventos_extra]
    rows = EventoExtraNegociacao.objects.filter(evento_extra_id__in=evento_extra_ids)
    if not rows:
        return eventos_count

    rows = rows.values_list("evento_extra_id").annotate(count=Count("id"))

    for r in rows:
        eventos_count[r[0]] = r[1]
    return eventos_count


def _serialize_evento_extra_object(evento_extra, qtd_solicitacoes, qtd_negociacoes) -> dict:
    return {
        "id": evento_extra.id,
        "nome": evento_extra.nome,
        "dataInicial": evento_extra.data_inicial,
        "dataFinal": evento_extra.data_final,
        "status": evento_extra.status,
        "qtdSolicitacoes": qtd_solicitacoes,
        "qtdNegociacoes": qtd_negociacoes,
        "created_at": evento_extra.created_at,
    }


class EventoExtraNegociacaoParam(BaseSerializer):
    prefetch_related = [
        Prefetch(
            "eventoextranegociacao_set",
            queryset=EventoExtraNegociacao.objects.to_serialize(EventoExtraNegociacaoSerializer),
            to_attr="negociacoes",
        ),
    ]

    def serialize_object(self, obj) -> dict:
        return {"negociacoes": [negociacao.serialize() for negociacao in obj.negociacoes]}


class EventoExtraSolicitacaoPernaParam(BaseSerializer):
    prefetch_related = [
        Prefetch(
            "eventoextrasolicitacaoperna_set",
            queryset=EventoExtraSolicitacaoPerna.objects.to_serialize(EventoExtraSolicitacaoPernaSerializer),
            to_attr="pernas",
        )
    ]

    def serialize_object(self, obj) -> dict:
        return {"pernas": [perna.serialize() for perna in obj.pernas]}


class EventoExtraSolicitacaoSerializer(BaseSerializer):
    extra = {"negociacao": EventoExtraNegociacaoParam, "perna": EventoExtraSolicitacaoPernaParam}
    select_related = ["rota_principal"]

    def serialize_object(self, obj):
        return {
            "id": obj.id,
            "eventoExtraId": obj.evento_extra_id,
            "rotaPrincipal": {"id": obj.rota_principal.id, "eixo": obj.rota_principal.eixo},
            "rotaPrevista": obj.rota_prevista,
            "regional": obj.regional,
            "prioridade": obj.prioridade,
            "tiposAssento": obj.tipos_assento,
            "precoEstimado": obj.preco_estimado,
            "breakevenEsperado": obj.breakeven_esperado,
            "is_fechado_rotas": obj.is_fechado_rotas,
            "has_precificacao_inicial": obj.has_precificacao_inicial,
            "has_precificacao_final": obj.has_precificacao_final,
            "is_criado_staff": obj.is_criado_staff,
            "createdAt": obj.created_at,
        }
