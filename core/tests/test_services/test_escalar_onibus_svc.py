from datetime import datetime, timed<PERSON>ta
from decimal import Decimal as D
from itertools import cycle
from unittest import mock

import pytest
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from model_bakery import baker

from commons.dateutils import now, to_default_tz_required
from commons.price_helper import round_price
from core.constants import TIPOS_ASSENTO
from core.models_grupo import GrupoClasse, TrechoClasse
from core.models_travel import AlteracaoTravel, Travel, TravelComunicada
from core.service import preco_svc, travel_comunicada_svc
from core.service.grupos_staff import escalar_onibus_svc
from core.service.remanejamento import remanejamento_svc
from core.tests import fixtures


@pytest.fixture
def grupo_datetime_ida():
    return to_default_tz_required(datetime(2025, 12, 1, 10, 0))


@pytest.fixture
def onibus_leito():
    onibus = baker.make("core.Onibus", tipo="LD")
    baker.make("core.OnibusClasse", tipo="leito", capacidade=60, capacidade_vendida=60, onibus=onibus)
    baker.make(
        "core.InfoOnibus",
        tipo="dut",
        onibus=onibus,
        status="pendente",
        data_vencimento=to_default_tz_required(datetime(2025, 12, 10, 10, 0)),
    )
    baker.make(
        "core.InfoOnibus",
        tipo="seguro",
        onibus=onibus,
        status="pendente",
        data_vencimento=to_default_tz_required(datetime(2025, 12, 10, 10, 0)),
    )
    return onibus


@pytest.fixture
def onibus_leito_e_cama():
    onibus = baker.make("core.Onibus")
    baker.make("core.OnibusClasse", tipo="leito", capacidade=60, onibus=onibus)
    baker.make("core.OnibusClasse", tipo="leito cama", capacidade=60, capacidade_vendida=60, onibus=onibus)
    return onibus


@pytest.fixture
def onibus_leito_2():
    onibus = baker.make("core.Onibus", jsondata={"features": "adaptado_contra_covid"})
    baker.make("core.OnibusClasse", tipo="leito", capacidade=50, capacidade_vendida=50, onibus=onibus)
    baker.make(
        "core.InfoOnibus", tipo="dut", onibus=onibus, status="pendente", data_vencimento=now() + timedelta(days=30)
    )
    baker.make(
        "core.InfoOnibus", tipo="seguro", onibus=onibus, status="pendente", data_vencimento=now() + timedelta(days=30)
    )

    return onibus


@pytest.fixture
def onibus_executivo():
    onibus = baker.make("core.Onibus", tipo="DD")
    baker.make("core.OnibusClasse", tipo="executivo", capacidade=20, capacidade_vendida=20, onibus=onibus)
    return onibus


@pytest.fixture
def classe_onibus_leito_2_form(onibus_leito_2):
    return list(onibus_leito_2.classes.all())


@pytest.fixture
def classe_executivo_form(onibus_executivo):
    return list(onibus_executivo.classes.all())


@pytest.fixture
def grupo_sem_onibus(rota, grupo_datetime_ida):
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota)
    return baker.make(
        "core.Grupo",
        rota=rota,
        valor_frete=500,
        rotina_onibus=rotina_onibus,
        datetime_ida=grupo_datetime_ida,
    )


@pytest.fixture
def grupo_sem_onibus_2(rota):
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota)
    return baker.make(
        "core.Grupo",
        rota=rota,
        rotina_onibus=rotina_onibus,
        datetime_ida=to_default_tz_required(datetime(2026, 12, 25, 10, 0)),
    )


@pytest.fixture
def grupo_com_onibus_executivo(rota, onibus_executivo, grupo_datetime_ida, buckets_limpos):
    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        onibus=onibus_executivo,
        datetime_ida=grupo_datetime_ida,
        valor_frete=500,
        rotina_onibus=baker.make("core.RotinaOnibus", frete_atual=1000, _fill_optional=["onibus"]),
    )
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="executivo", capacidade=80)
    tv1, tv2 = rota.get_trechos_vendidos()
    pm = baker.make("core.PriceManager")
    for bucket in buckets_limpos:
        baker.make(
            "core.PriceBucket",
            value=bucket["max_split_value"],
            tamanho=bucket["tamanho"],
            expiration_days=bucket.get("expiration_days"),
            price_manager=pm,
        )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv1,
        datetime_ida=grupo.datetime_ida,
        max_split_value=D("13.00"),
        ref_split_value=D("13.00"),
        price_manager=pm,
    )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv2,
        datetime_ida=grupo.datetime_ida + timedelta(hours=2),
        max_split_value=D("35.00"),
        ref_split_value=D("35.00"),
        price_manager=baker.make("core.PriceManager", value=D("35.00"), ref_value=D("35.00")),
    )
    baker.make(
        "core.InfoOnibus",
        tipo="dut",
        data_vencimento=to_default_tz_required(datetime(2028, 12, 25, 10, 0)),
        onibus=onibus_executivo,
    )
    baker.make(
        "core.InfoOnibus",
        tipo="seguro",
        data_vencimento=to_default_tz_required(datetime(2028, 12, 25, 10, 0)),
        onibus=onibus_executivo,
    )
    return grupo


@pytest.fixture
def travel_grupo_com_onibus_leito(grupo_com_onibus_leito, faker):
    user = baker.make(User, email="<EMAIL>")
    baker.make("core.Profile", user=user)

    return baker.make(
        Travel,
        grupo=grupo_com_onibus_leito,
        user=user,
        trecho_vendido=grupo_com_onibus_leito.rota.trechos_vendidos.all()[0],
        grupo_classe=grupo_com_onibus_leito.grupoclasse_set.all()[0],
        count_seats=1,
        trecho_classe=baker.make(
            TrechoClasse,
            datetime_ida=timezone.now(),
            trecho_vendido=grupo_com_onibus_leito.rota.trechos_vendidos.all()[0],
            grupo_classe=grupo_com_onibus_leito.grupoclasse_set.all()[0],
            grupo=grupo_com_onibus_leito,
            max_split_value=D("10"),
            ref_split_value=D("10"),
            price_manager=baker.make("core.PriceManager", value=D("10"), ref_value=D("10")),
        ),
    )


@pytest.fixture
def travel_grupo_com_onibus_leito_2(grupo_com_onibus_leito, faker):
    user = baker.make(User, email="<EMAIL>")
    baker.make("core.Profile", user=user)
    travel = baker.make(
        Travel,
        grupo=grupo_com_onibus_leito,
        user=user,
        trecho_vendido=grupo_com_onibus_leito.rota.trechos_vendidos.all()[1],
        grupo_classe=grupo_com_onibus_leito.grupoclasse_set.all()[0],
        count_seats=1,
        max_split_value=D("10"),
        trecho_classe=baker.make(
            TrechoClasse,
            datetime_ida=timezone.now(),
            trecho_vendido=grupo_com_onibus_leito.rota.trechos_vendidos.all()[1],
            grupo_classe=grupo_com_onibus_leito.grupoclasse_set.all()[0],
            grupo=grupo_com_onibus_leito,
            max_split_value=D("10"),
            ref_split_value=D("10"),
            price_manager=baker.make("core.PriceManager", value=D("10"), ref_value=D("10")),
        ),
    )
    baker.make("core.Passageiro", travel=travel)

    return travel


@pytest.fixture
def grupo_com_onibus_leito(rota, onibus_leito, grupo_datetime_ida, buckets_limpos):
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota, onibus=onibus_leito)
    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        onibus=onibus_leito,
        rotina_onibus=rotina_onibus,
        datetime_ida=grupo_datetime_ida,
    )
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito", capacidade=40)
    tv1, tv2 = rota.get_trechos_vendidos()
    pm = baker.make("core.PriceManager")
    for bucket in buckets_limpos:
        baker.make(
            "core.PriceBucket",
            value=bucket["max_split_value"],
            tamanho=bucket["tamanho"],
            expiration_days=bucket.get("expiration_days"),
            price_manager=pm,
        )
    baker.make(
        TrechoClasse,
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv1,
        datetime_ida=grupo.datetime_ida,
        max_split_value=D("12.90"),
        ref_split_value=D("12.90"),
        price_manager=pm,
    )
    baker.make(
        TrechoClasse,
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv2,
        datetime_ida=grupo.datetime_ida + timedelta(hours=2),
        max_split_value=D("34.90"),
        ref_split_value=D("34.90"),
        price_manager=baker.make("core.PriceManager", value=D("34.90"), ref_value=D("34.90")),
    )
    return grupo


@pytest.fixture
def grupo_com_onibus_double_class(rota, onibus_leito_e_cama, grupo_datetime_ida, buckets_limpos):
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota, onibus=onibus_leito_e_cama)
    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        onibus=onibus_leito_e_cama,
        rotina_onibus=rotina_onibus,
        datetime_ida=grupo_datetime_ida,
    )
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito", capacidade=80)
    grupo_classe2 = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito cama", capacidade=80)
    tv1, tv2 = rota.get_trechos_vendidos()
    pm = baker.make("core.PriceManager")
    pm2 = baker.make("core.PriceManager", value=D("42.00"), ref_value=D("42.00"))
    for bucket in buckets_limpos:
        baker.make(
            "core.PriceBucket",
            value=bucket["max_split_value"],
            tamanho=bucket["tamanho"],
            price_manager=pm,
        )
        baker.make(
            "core.PriceBucket",
            value=bucket["max_split_value"],
            tamanho=bucket["tamanho"],
            price_manager=pm2,
        )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv1,
        datetime_ida=grupo.datetime_ida,
        max_split_value=D("13.00"),
        ref_split_value=D("13.00"),
        price_manager=pm,
    )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=tv2,
        datetime_ida=grupo.datetime_ida + timedelta(hours=2),
        max_split_value=D("35.00"),
        ref_split_value=D("35.00"),
        price_manager=baker.make("core.PriceManager", value=D("35.00"), ref_value=D("35.00")),
    )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe2,
        trecho_vendido=tv1,
        datetime_ida=grupo.datetime_ida,
        max_split_value=D("42.00"),
        ref_split_value=D("42.00"),
        price_manager=pm2,
    )
    baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe2,
        trecho_vendido=tv2,
        datetime_ida=grupo.datetime_ida + timedelta(hours=2),
        max_split_value=D("60.00"),
        ref_split_value=D("60.00"),
        price_manager=baker.make("core.PriceManager", value=D("60.00"), ref_value=D("60.00")),
    )
    return grupo


@pytest.fixture
def regra_reducao_frete():
    return baker.make("BusChangeRequestRules", from_bus="DD", to="LD", reduction=10)


def _reprecifica_buckets(buckets, fator_capacidade, fator_precificacao):
    if not buckets:
        return []

    for bucket in buckets:
        novo_max_split_value = round_price(D(bucket["max_split_value"]) * (1 + fator_precificacao))
        novo_tamanho = max(round(fator_capacidade * bucket["tamanho"]), 1)
        bucket.update({"max_split_value": str(novo_max_split_value), "tamanho": novo_tamanho})
    return buckets


def test_nao_notifica_pax_quando_remove_onibus(grupo_com_onibus_leito, mailoutbox):
    grupo_com_onibus_leito.datetime_ida = now() + timedelta(days=1)
    grupo_com_onibus_leito.save()

    escalar_onibus_svc.alterar_onibus_grupo(None, grupo_com_onibus_leito, "OUTROS", "Teste", False, False)

    grupo_com_onibus_leito.refresh_from_db()

    assert grupo_com_onibus_leito.onibus is None
    assert len(mailoutbox) == 0


@pytest.mark.parametrize(
    "classes,expected",
    [
        (["leito", "leito individual"], "leito"),
        (["leito cama", "cama premium", "semi leito"], "semi leito"),
        (["executivo"], "executivo"),
    ],
)
def test_get_classe_inferior(classes, expected):
    assert escalar_onibus_svc.get_classe_inferior(classes) == expected


def test_procura_match_rateio_percentual_com_downgrade(mocker, grupo_com_onibus_leito, classe_executivo_form):
    mocker.patch("pricing.pricing_svc.get_scenario_site", return_value="mock")
    """Downgrade de leito pra executivo tem reajuste de ~ -29%"""
    match_rateio = preco_svc.procura_match_rateio(grupo_com_onibus_leito, classe_executivo_form)
    assert match_rateio != {}
    precos_originais = {
        tc.trecho_vendido_id: (tc.max_split_value, tc.ref_split_value, preco_svc.get_active_buckets(tc))
        for tc in grupo_com_onibus_leito.trechoclasse_set.all()
    }
    classe_original = grupo_com_onibus_leito.grupoclasse_set.all()[0]
    fator_capacidade = _calcula_fator_capacidade(classe_executivo_form, classe_original)
    for match in match_rateio.values():
        valor, valor_ref, buckets = precos_originais[match.trecho_vendido.id]
        taxa = D("0.6") / D("0.7")
        valor_reajustado = round_price(valor * taxa)
        valor_ref_reajustado = round_price(valor_ref * taxa)
        buckets_reajustados = _reprecifica_buckets(buckets, fator_capacidade, taxa - 1)
        assert match.valor == valor_reajustado
        assert match.valor_ref == valor_ref_reajustado
        assert match.buckets_to_dict() == buckets_reajustados


def _calcula_fator_capacidade(nova_classe_form, classe_original):
    return nova_classe_form[0].capacidade / classe_original.capacidade if classe_original.capacidade > 0 else 1


def test_procura_match_rateio_percentual_com_upgrade(mocker, grupo_com_onibus_executivo, classe_onibus_leito_2_form):
    mocker.patch("pricing.pricing_svc.get_scenario_site", return_value="mock")
    """Upgrade de executivo pra leito tem reajuste de ~ +40%"""
    match_rateio = preco_svc.procura_match_rateio(grupo_com_onibus_executivo, classe_onibus_leito_2_form)
    assert match_rateio != {}
    precos_originais = {
        tc.trecho_vendido_id: (tc.max_split_value, tc.ref_split_value, preco_svc.get_active_buckets(tc))
        for tc in grupo_com_onibus_executivo.trechoclasse_set.all()
    }
    classe_original = grupo_com_onibus_executivo.grupoclasse_set.all()[0]
    fator_capacidade = _calcula_fator_capacidade(classe_onibus_leito_2_form, classe_original)
    for match in match_rateio.values():
        valor, valor_ref, buckets = precos_originais[match.trecho_vendido.id]
        taxa = D("0.7") / D("0.6")
        valor_reajustado = round_price(valor * taxa)
        valor_ref_reajustado = round_price(valor_ref * taxa)
        buckets_reajustados = _reprecifica_buckets(buckets, fator_capacidade, taxa - 1)
        assert match.valor == valor_reajustado
        assert match.valor_ref == valor_ref_reajustado
        assert match.buckets_to_dict() == buckets_reajustados


def test_procura_match_rateio_percentual_com_mesma_classe(grupo_com_onibus_leito, classe_onibus_leito_2_form):
    match_rateio = preco_svc.procura_match_rateio(grupo_com_onibus_leito, classe_onibus_leito_2_form)
    assert match_rateio != {}
    precos_originais = {
        tc.trecho_vendido_id: (tc.max_split_value, tc.ref_split_value, preco_svc.get_active_buckets(tc))
        for tc in grupo_com_onibus_leito.trechoclasse_set.all()
    }
    classe_original = grupo_com_onibus_leito.grupoclasse_set.all()[0]
    fator_capacidade = _calcula_fator_capacidade(classe_onibus_leito_2_form, classe_original)
    for match in match_rateio.values():
        valor, valor_ref, buckets = precos_originais[match.trecho_vendido.id]
        valor_reajustado = round_price(valor * D("1"))
        valor_ref_reajustado = round_price(valor_ref * D("1"))
        buckets_reajustados = _reprecifica_buckets(buckets, fator_capacidade, D(0))
        assert match.valor == valor_reajustado
        assert match.valor_ref == valor_ref_reajustado
        assert match.buckets_to_dict() == buckets_reajustados


def test_match_rateio_mantem_tamanho_do_bucket_se_capacidade_original_for_0(
    grupo_com_onibus_leito, classe_onibus_leito_2_form
):
    GrupoClasse.objects.filter(grupo=grupo_com_onibus_leito).update(capacidade=0)
    match_rateio = preco_svc.procura_match_rateio(grupo_com_onibus_leito, classe_onibus_leito_2_form)
    assert match_rateio != {}
    precos_originais = {
        tc.trecho_vendido_id: (tc.max_split_value, tc.ref_split_value, preco_svc.get_active_buckets(tc))
        for tc in grupo_com_onibus_leito.trechoclasse_set.all()
    }
    classe_original = grupo_com_onibus_leito.grupoclasse_set.all()[0]
    fator_capacidade = _calcula_fator_capacidade(classe_onibus_leito_2_form, classe_original)
    for match in match_rateio.values():
        *_, buckets = precos_originais[match.trecho_vendido.id]
        buckets_reajustados = _reprecifica_buckets(buckets, fator_capacidade, D(0))
        assert match.buckets_to_dict() == buckets_reajustados


def test_match_rateio_de_double_class_recalcula_a_partir_da_inferior(
    mocker, grupo_com_onibus_double_class, classe_executivo_form
):
    """O grupo é leito/cama e vai mudar pra executivo, a nova precificação
    será baseada a partir dos valores da classe mais inferior (leito)
    com a taxa de leito->executivo (~ -29%)
    """
    mocker.patch("pricing.pricing_svc.get_scenario_site", return_value="mock")
    precos_originais = {
        tc.trecho_vendido_id: (tc.max_split_value, tc.ref_split_value, preco_svc.get_active_buckets(tc))
        for tc in grupo_com_onibus_double_class.trechoclasse_set.filter(grupo_classe__tipo_assento="leito")
    }
    # o tamanho total dos buckets é 40. A capacidade antiga é 80 e a capacidade nova é 20.
    match_rateio = preco_svc.procura_match_rateio(grupo_com_onibus_double_class, classe_executivo_form)
    assert match_rateio != {}
    fator_capacidade = 0.25  # 80 -> 20
    for match in match_rateio.values():
        valor, valor_ref, buckets = precos_originais[match.trecho_vendido.id]
        taxa = D("0.6") / D("0.7")
        valor_reajustado = round_price(valor * taxa)
        valor_ref_reajustado = round_price(valor_ref * taxa)
        buckets_reajustados = _reprecifica_buckets(buckets, fator_capacidade, taxa - 1)
        assert match.valor == valor_reajustado
        assert match.valor_ref == valor_ref_reajustado
        # O tamanho total dos buckets se torna 9 devido ao arredondamento para baixo.
        assert match.buckets_to_dict() == buckets_reajustados


def test_match_rateio_nao_deve_fazer_queries(
    grupo_com_onibus_double_class, classe_executivo_form, django_assert_num_queries, globalsettings_mock
):
    # cacheia o globalsettings para não afetar o teste
    globalsettings_mock("price_fator_tipo_assento", 0.1)
    globalsettings_mock("company_ids_marketplace_bucketizadas", [])
    globalsettings_mock("max_distance_allowed_one_driver", 10)
    grupo = remanejamento_svc.get_grupos_para_remanejamento([grupo_com_onibus_double_class])[0]

    # Se você quer mexer na contagem, você tá errado.
    with django_assert_num_queries(0):
        preco_svc.procura_match_rateio(grupo, classe_executivo_form)


# def test_match_rateio_deve_fazer_queries_da_pricing_rule(
#     grupo_com_onibus_double_class, classe_executivo_form, django_assert_num_queries
# ):
#     # TODO: ME prometeram que vai ter um refactory disso
#     globalsettings_svc.get("price_fator_tipo_assento")

#     grupo = remanejamento_svc.get_grupos_para_remanejamento([grupo_com_onibus_double_class])[0]
#     with django_assert_num_queries(8):
#         preco_svc.procura_match_rateio(grupo, classe_executivo_form)


def test_duplicidade_onibus_deve_lancar_validation(grupo_com_onibus_executivo, grupo_sem_onibus):
    grupo_id = grupo_com_onibus_executivo.id
    with pytest.raises(
        ValidationError,
        match=f"Pode ser que o ônibus selecionado já esteja escalado dentro do horário do grupo {grupo_id}",
    ):
        grupo_ids = [grupo_sem_onibus.id]
        onibus_id = grupo_com_onibus_executivo.onibus.id
        escalar_onibus_svc.verificar_conflito_onibus(grupo_ids, onibus_id)


def test_duplicidade_onibus_nao_deve_lancar_validation(grupo_com_onibus_executivo, grupo_sem_onibus_2):
    grupo_ids = [grupo_sem_onibus_2.id]
    onibus_id = grupo_com_onibus_executivo.onibus.id
    escalar_onibus_svc.verificar_conflito_onibus(grupo_ids, onibus_id)


@pytest.fixture
def grupos_classe_ordenados():
    exclude = {"carro", "executivo individual", "leito cama", "cama premium individual"}
    return [
        baker.prepare("core.GrupoClasse", tipo_assento=tipo_assento)
        for tipo_assento in TIPOS_ASSENTO
        if tipo_assento not in exclude
    ]


@pytest.mark.parametrize(
    "tipo_assento, expected_tipo_assento",
    [
        ("carro", "van"),
        ("cama premium individual", "cama premium"),
        ("semi leito", "semi leito"),
        ("executivo individual", "executivo"),
        ("leito cama", "leito cama individual"),
    ],
)
def test_acha_classe_mais_proxima(grupos_classe_ordenados, tipo_assento, expected_tipo_assento):
    classe_mais_proxima = preco_svc._acha_classe_mais_proxima(grupos_classe_ordenados, tipo_assento)
    assert classe_mais_proxima.tipo_assento == expected_tipo_assento


def test_troca_de_onibus_com_reducao_de_frete(grupo_com_onibus_executivo, onibus_leito, regra_reducao_frete):
    assert grupo_com_onibus_executivo.onibus.tipo == "DD"
    assert grupo_com_onibus_executivo.valor_frete == 500
    with mock.patch(
        "core.service.grupos_staff.escalar_onibus_svc.new_incidents_adapter.groups_have_incidents",
        return_value={grupo_com_onibus_executivo.id: False},
    ):
        escalar_onibus_svc.alterar_onibus_grupo(
            onibus_leito,
            grupo_com_onibus_executivo,
            "OUTROS",
            "Teste",
            False,
            True,
            causada_por=AlteracaoTravel.CausadaPor.PARCEIRO,
        )

    grupo_com_onibus_executivo.refresh_from_db()
    assert grupo_com_onibus_executivo.onibus.tipo == "LD"
    assert grupo_com_onibus_executivo.valor_frete == 450


def test_troca_de_onibus_sem_reducao_de_frete_sem_downgrade(grupo_sem_onibus, onibus_leito, regra_reducao_frete):
    assert grupo_sem_onibus.valor_frete == 500
    with mock.patch(
        "core.service.grupos_staff.escalar_onibus_svc.new_incidents_adapter.groups_have_incidents",
        return_value={grupo_sem_onibus.id: False},
    ):
        escalar_onibus_svc.alterar_onibus_grupo(onibus_leito, grupo_sem_onibus, "OUTROS", "Teste", False, True)

    grupo_sem_onibus.refresh_from_db()
    assert grupo_sem_onibus.valor_frete == 500


def test_troca_de_onibus_sem_reducao_de_frete_com_incident(
    grupo_com_onibus_executivo, onibus_leito, regra_reducao_frete
):
    assert grupo_com_onibus_executivo.onibus.tipo == "DD"
    assert grupo_com_onibus_executivo.valor_frete == 500
    with mock.patch(
        "core.service.grupos_staff.escalar_onibus_svc.new_incidents_adapter.groups_have_incidents",
        return_value={grupo_com_onibus_executivo.id: True},
    ):
        escalar_onibus_svc.alterar_onibus_grupo(
            onibus_leito, grupo_com_onibus_executivo, "OUTROS", "Teste", False, True
        )

    grupo_com_onibus_executivo.refresh_from_db()
    assert grupo_com_onibus_executivo.onibus.tipo == "LD"
    assert grupo_com_onibus_executivo.valor_frete == 500


def test_has_class_downgrade(onibus_leito_2, travel_grupo_com_onibus_leito_2, onibus_executivo):
    has_class_downgrade = escalar_onibus_svc._has_class_downgrade(
        travel_grupo_com_onibus_leito_2.grupo,
        old_bus=onibus_leito_2,
        new_bus=onibus_executivo,
    )

    assert has_class_downgrade is True


@pytest.mark.parametrize("alterar_layout", [True, False])
def test_salva_onibus_antigo_travel_comunicada(
    onibus_leito_2,
    grupo_com_onibus_leito,
    travel_grupo_com_onibus_leito,
    mocker,
    alterar_layout,
    django_capture_on_commit_callbacks,
):
    salvar_travel_comunicada_spy = mocker.spy(travel_comunicada_svc, "salvar_travel_comunicada")
    notify_bus_change = mocker.spy(remanejamento_svc, "_notify_users_bus_change")

    grupo_com_onibus_leito.datetime_ida = now() + timedelta(days=2)
    grupo_com_onibus_leito.save()
    onibus_antigo = grupo_com_onibus_leito.onibus
    travel_grupo_com_onibus_leito.trecho_classe.datetime_ida = now() + timedelta(days=3)
    travel_grupo_com_onibus_leito.trecho_classe.pessoas = 1
    travel_grupo_com_onibus_leito.trecho_classe.save()

    with django_capture_on_commit_callbacks(execute=True):
        escalar_onibus_svc.alterar_onibus_grupo(
            onibus_leito_2, grupo_com_onibus_leito, "OUTROS", "Teste", alterar_layout, False
        )

    travel_grupo_com_onibus_leito.refresh_from_db()

    salvar_travel_comunicada_spy.assert_called_once_with(travel_grupo_com_onibus_leito, onibus=onibus_antigo)
    travel_comunicada = TravelComunicada.objects.get(travel=travel_grupo_com_onibus_leito)
    assert travel_comunicada.onibus == onibus_antigo
    assert travel_comunicada.datetime_ida_mais_proximo == travel_grupo_com_onibus_leito.trecho_classe.datetime_ida
    if alterar_layout:
        notify_bus_change.assert_called_once()


def test_alterar_onibus_grupo_com_buckets_demais_classes_superiores(grupo_datetime_ida):
    """
    https://www.notion.so/buserbrasil/Discovery-Pre-os-inconsistentes-1ebf44818fc9430d9871cb04c4ef8695#2772a7a805a044f9ab3cbfd7e7046090
    """

    cidade_rj = baker.make("core.Cidade", name="Rio de Janeiro", uf="RJ", sigla="RJ")
    cidade_ntr = baker.make("core.Cidade", name="Niterói", uf="RJ", sigla="NTR")
    cidade_gup = baker.make("core.Cidade", name="Guarapari", uf="ES", sigla="GUP")
    cidade_vix = baker.make("core.Cidade", name="Vitória", uf="ES", sigla="VIX")
    local_rj_1 = baker.make("core.LocalEmbarque", cidade=cidade_rj)
    local_rj_2 = baker.make("core.LocalEmbarque", cidade=cidade_rj)
    local_ntr = baker.make("core.LocalEmbarque", cidade=cidade_ntr)
    local_gup = baker.make("core.LocalEmbarque", cidade=cidade_gup)
    local_vix = baker.make("core.LocalEmbarque", cidade=cidade_vix)
    rota = baker.make(
        "core.Rota",
        origem=local_rj_1,
        destino=local_vix,
        distancia_total=550,
        duracao_total=timedelta(hours=9, minutes=50),
    )
    itinerario = [
        baker.prepare("core.Checkpoint", duracao=timedelta(minutes=60), local=local_rj_1, rota=rota, idx=0),
        baker.prepare("core.Checkpoint", duracao=timedelta(minutes=60), local=local_rj_2, rota=rota, idx=1),
        baker.prepare("core.Checkpoint", duracao=timedelta(minutes=60), local=local_ntr, rota=rota, idx=2),
        baker.prepare("core.Checkpoint", duracao=timedelta(minutes=60), local=local_gup, rota=rota, idx=3),
        baker.prepare("core.Checkpoint", duracao=timedelta(minutes=60), local=local_vix, rota=rota, idx=4),
    ]
    rota.itinerario.bulk_create(itinerario)
    rj1_vix = baker.make("core.TrechoVendido", origem=local_rj_1, destino=local_vix, rota=rota)
    rj1_gup = baker.make("core.TrechoVendido", origem=local_rj_1, destino=local_gup, rota=rota)
    rj2_vix = baker.make("core.TrechoVendido", origem=local_rj_2, destino=local_vix, rota=rota)
    rj2_gup = baker.make("core.TrechoVendido", origem=local_rj_2, destino=local_gup, rota=rota)
    ntr_gup = baker.make("core.TrechoVendido", origem=local_ntr, destino=local_gup, rota=rota)
    ntr_vix = baker.make("core.TrechoVendido", origem=local_ntr, destino=local_vix, rota=rota)

    onibus_semileito = baker.make("core.Onibus")
    baker.make("core.OnibusClasse", onibus=onibus_semileito, tipo="semi leito", capacidade=40, capacidade_vendida=40)
    baker.make(
        "core.InfoOnibus",
        tipo=cycle(["dut", "seguro"]),
        onibus=onibus_semileito,
        status="pendente",
        data_vencimento=grupo_datetime_ida + timedelta(days=30),
        _quantity=2,
        _bulk_create=True,
    )
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota, onibus=onibus_semileito)
    grupo_semileito = baker.make(
        "core.Grupo", rota=rota, rotina_onibus=rotina_onibus, onibus=onibus_semileito, datetime_ida=grupo_datetime_ida
    )
    grupo_classe_semileito = baker.make(
        "core.GrupoClasse", grupo=grupo_semileito, tipo_assento="semi leito", capacidade=40
    )
    onibus_doubleclass = baker.make("core.Onibus")
    baker.make("core.OnibusClasse", tipo="semi leito", capacidade=40, capacidade_vendida=40, onibus=onibus_doubleclass)
    baker.make("core.OnibusClasse", tipo="leito", capacidade=6, capacidade_vendida=6, onibus=onibus_doubleclass)
    baker.make(
        "core.OnibusClasse", tipo="leito individual", capacidade=3, capacidade_vendida=3, onibus=onibus_doubleclass
    )
    baker.make(
        "core.InfoOnibus",
        tipo=cycle(["dut", "seguro"]),
        onibus=onibus_doubleclass,
        status="pendente",
        data_vencimento=grupo_datetime_ida + timedelta(days=30),
        _quantity=2,
        _bulk_create=True,
    )

    tc_rj1_vix = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=rj1_vix,
        vagas=14,
        max_split_value=D("148.90"),
        ref_split_value=D("148.90"),
    )

    tc_rj1_gup = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=rj1_gup,
        vagas=14,
        max_split_value=D("139.90"),
        ref_split_value=D("148.90"),
    )

    tc_rj2_vix = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=rj2_vix,
        vagas=14,
        max_split_value=D("148.90"),
        ref_split_value=D("148.90"),
    )

    tc_rj2_gup = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=rj2_gup,
        vagas=14,
        max_split_value=D("139.90"),
        ref_split_value=D("139.90"),
    )

    tc_ntr_gup = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=ntr_gup,
        vagas=14,
        max_split_value=D("148.90"),
        ref_split_value=D("148.90"),
    )

    tc_ntr_vix = baker.make(
        "core.TrechoClasse",
        grupo=grupo_semileito,
        grupo_classe=grupo_classe_semileito,
        datetime_ida=grupo_datetime_ida,
        trecho_vendido=ntr_vix,
        vagas=14,
        max_split_value=D("127.90"),
        ref_split_value=D("127.90"),
    )

    pm_tc_ntr_vix = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=1,
        value=cycle([D("64.90"), D("86.90"), D("107.90")]),
        ref_value=cycle([D("64.90"), D("86.90"), D("107.90")]),
        expiration_days=cycle([25, 7, 0]),
        price_manager=pm_tc_ntr_vix,
        _quantity=3,
        _bulk_create=True,
    )
    tc_ntr_vix.price_manager = pm_tc_ntr_vix
    tc_ntr_vix.price_manager.save()
    tc_ntr_vix.save()

    pm_tc_rj2_vix = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=cycle([1, 1, 1, 1, 1, 2, 3]),
        value=cycle([D("76.90"), D("81.90"), D("95.90"), D("109.90"), D("121.90"), D("129.90"), D("149.90")]),
        ref_value=cycle([D("76.90"), D("81.90"), D("95.90"), D("109.90"), D("121.90"), D("129.90"), D("149.90")]),
        expiration_days=cycle([30, 25, 25, 7, 7, 5, 0]),
        price_manager=pm_tc_rj2_vix,
        _quantity=7,
        _bulk_create=True,
    )
    tc_rj2_vix.price_manager = pm_tc_rj2_vix
    tc_rj2_vix.price_manager.save()
    tc_rj2_vix.save()

    pm_tc_rj1_vix = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=cycle([1, 1, 1, 1, 1, 2, 3]),
        value=cycle([D("76.90"), D("81.90"), D("95.90"), D("109.90"), D("121.90"), D("129.90"), D("149.90")]),
        ref_value=cycle([D("76.90"), D("81.90"), D("95.90"), D("109.90"), D("121.90"), D("129.90"), D("149.90")]),
        expiration_days=cycle([30, 25, 25, 7, 7, 5, 0]),
        price_manager=pm_tc_rj1_vix,
        _quantity=7,
        _bulk_create=True,
    )
    tc_rj1_vix.price_manager = pm_tc_rj1_vix
    tc_rj1_vix.price_manager.save()
    tc_rj1_vix.save()

    pm_tc_rj1_gup = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=1,
        value=cycle([D("96.90"), D("107.90"), D("118.90"), D("128.90"), D("139.90")]),
        ref_value=cycle([D("96.90"), D("107.90"), D("118.90"), D("128.90"), D("139.90")]),
        expiration_days=cycle([25, 15, 7, 2, 0]),
        price_manager=pm_tc_rj1_gup,
        _quantity=5,
        _bulk_create=True,
    )
    tc_rj1_gup.price_manager = pm_tc_rj1_gup
    tc_rj1_gup.price_manager.save()
    tc_rj1_gup.save()

    pm_tc_ntr_gup = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=1,
        value=cycle([D("85.90"), D("106.90"), D("117.90")]),
        ref_value=cycle([D("85.90"), D("106.90"), D("117.90")]),
        expiration_days=cycle([25, 7, 0]),
        price_manager=pm_tc_ntr_gup,
        _quantity=3,
        _bulk_create=True,
    )
    tc_ntr_gup.price_manager = pm_tc_ntr_gup
    tc_ntr_gup.price_manager.save()
    tc_ntr_gup.save()

    pm_tc_rj2_gup = baker.make("core.PriceManager")
    baker.make(
        "core.PriceBucket",
        tamanho=1,
        value=cycle([D("96.90"), D("107.90"), D("118.90"), D("128.90"), D("139.90")]),
        ref_value=cycle([D("96.90"), D("107.90"), D("118.90"), D("128.90"), D("139.90")]),
        expiration_days=cycle([25, 15, 7, 2, 0]),
        price_manager=pm_tc_rj2_gup,
        _quantity=5,
        _bulk_create=True,
    )
    tc_rj2_gup.price_manager = pm_tc_rj2_gup
    tc_rj2_gup.price_manager.save()
    tc_rj2_gup.save()

    escalar_onibus_svc.alterar_onibus_grupo(onibus_doubleclass, grupo_semileito, "OUTROS", "Teste", True, False)

    grupo_semileito.refresh_from_db()
    tcs = grupo_semileito.trechoclasse_set.all()

    tcs_rj1_vix = tcs.filter(trecho_vendido=rj1_vix).order_by("id")
    tcs_rj1_gup = tcs.filter(trecho_vendido=rj1_gup).order_by("id")
    tcs_rj2_vix = tcs.filter(trecho_vendido=rj2_vix).order_by("id")
    tcs_rj2_gup = tcs.filter(trecho_vendido=rj2_gup).order_by("id")
    tcs_ntr_gup = tcs.filter(trecho_vendido=ntr_gup).order_by("id")
    tcs_ntr_vix = tcs.filter(trecho_vendido=ntr_vix).order_by("id")

    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_rj1_vix] == [10, 2, 1]
    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_rj1_gup] == [5, 1, 0]
    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_rj2_vix] == [10, 2, 1]
    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_rj2_gup] == [5, 1, 0]
    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_ntr_gup] == [3, 0, 0]
    assert [sum(tc.price_manager.buckets.values_list("tamanho", flat=True)) for tc in tcs_ntr_vix] == [3, 0, 0]


def test_remove_marcacao_assentos_quando_layout_nao_e_alterado(grupo_factory, travel_factory, mocker):
    # fluxo feito para V0 de marcacao de assentos, onde o de-para não é executado para esse cenario
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    onibus = fixtures.gerar_onibus_com_poltronas(trecho_classe)
    grupo.onibus = onibus
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pax = travel.passageiro_set.first()

    fixtures.gerar_accop_marcacao_assento(travel, pax, 1)

    old_acoops_marcacao_assento_count = travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    old_acoops_marcacao_assento_cancelada_count = travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()

    escalar_onibus_svc.alterar_onibus_grupo(None, grupo, "OUTROS", "Teste", False, False)

    new_travel = Travel.objects.get(reservation_code=travel.reservation_code)
    new_acoops_marcacao_assento_count = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    new_acoops_marcacao_assento_cancelada_count = new_travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()
    new_pax = new_travel.passageiro_set.first()

    assert old_acoops_marcacao_assento_count == 1
    assert new_acoops_marcacao_assento_count == 1
    assert old_acoops_marcacao_assento_cancelada_count == 0
    assert new_acoops_marcacao_assento_cancelada_count == 1
    assert new_pax
    assert new_pax.poltrona
    assert travel.id == new_travel.id
