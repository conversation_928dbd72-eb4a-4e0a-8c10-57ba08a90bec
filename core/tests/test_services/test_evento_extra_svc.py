from model_bakery import baker

from core.forms.staff_forms import EventoExtraFiltersForm
from core.service import evento_extra_svc


def test_list_evento_extra_all():
    nomes = ["São João", "Carnaval", "Páscoa"]
    baker.make("core.EventoExtra", nome=iter(nomes), _quantity=3, _bulk_create=True)

    filters = EventoExtraFiltersForm()
    eventos = evento_extra_svc.list_eventos_extra(filters, "dataInicial")
    qtd_eventos = eventos.count()
    assert qtd_eventos == 3
    assert list(eventos.values_list("nome", flat=True)) == nomes


def test_list_evento_extra_filters():
    nomes = ["São João", "Carnaval", "Páscoa"]
    status = ["EM_ANDAMENTO", "EM_ANDAMENTO", "CONCLUIDO"]
    _, evento_2, _ = baker.make(
        "core.EventoExtra", nome=iter(nomes), status=iter(status), _quantity=3, _bulk_create=True
    )

    # filtra por status
    filters = EventoExtraFiltersForm(status=status[1])
    eventos = evento_extra_svc.list_eventos_extra(filters, "dataInicial")
    qtd_eventos = eventos.count()
    assert qtd_eventos == 2
    assert list(eventos.values_list("nome", flat=True)) == nomes[0:2]

    # filtra por id
    filters = EventoExtraFiltersForm(eventoId=evento_2.id)
    eventos = evento_extra_svc.list_eventos_extra(filters, "dataInicial")
    qtd_eventos = eventos.count()
    assert qtd_eventos == 1
    assert list(eventos.values_list("nome", flat=True)) == nomes[1:2]
