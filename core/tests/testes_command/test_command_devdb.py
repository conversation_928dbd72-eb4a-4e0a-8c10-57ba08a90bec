import os

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.contrib.auth.models import User

from commons.devdb import GRUPO_DATA, TRECHOCLASSE_DATA
from core.management.commands.devdb import command
from core.models_commons import Cidade
from core.models_company import Company


def test_quantidade_de_grupos():
    assert len(GRUPO_DATA) == 1445  # Formula: DIAS * QUANTIDADE DE ROTAS * GRUPOS POR DIA


def test_quantidade_de_trechos_classe():
    assert len(TRECHOCLASSE_DATA) == 2171


def test_db_host_invalid(mocker):
    mocker.patch.dict(os.environ, {"DB_HOST": "DBDEPROD"})

    runner = CliRunner()
    result = runner.invoke(command)

    assert result.exit_code == 1
    assert "Só pode rodar devdb em banco de teste" in result.output


def test_model_sem_pk(mocker):
    user_sem_pk = User()
    mocker.patch("core.management.commands.devdb.ALL_DATA", [[user_sem_pk]])

    runner = CliRunner()
    result = runner.invoke(command)

    assert result.exit_code == 1
    assert "Todas instâncias devem ter pk definida" in result.output


def test_devdb_cria_dados(mocker):
    USER_DATA = [
        User(pk=1, username="Admin"),
        User(pk=2, username="Staff"),
        User(pk=3, username="Zezinho"),
    ]
    CIDADES_DATA = [
        Cidade(pk=1, name="Belo Horizonte"),
        Cidade(pk=2, name="São Paulo"),
    ]
    COMPANY_DATA = [
        Company(pk=1, name="Company Fretamento", vinculo="fixa"),
        Company(pk=2, name="Company Marketplace", vinculo="marketplace"),
    ]
    mocker.patch("core.management.commands.devdb.ALL_DATA", [USER_DATA, CIDADES_DATA, COMPANY_DATA])

    runner = CliRunner()
    result = runner.invoke(command)

    assert result.exit_code == 0
    assert "Populando dados de User" in result.output
    assert "Populando dados de Cidade" in result.output
    assert "Populando dados de Company" in result.output

    users = User.objects.values("pk", "username").order_by("pk")
    assert list(users) == [
        {"pk": 1, "username": "Admin"},
        {"pk": 2, "username": "Staff"},
        {"pk": 3, "username": "Zezinho"},
    ]

    cidades = Cidade.objects.values("pk", "name").order_by("pk")
    assert list(cidades) == [
        {"pk": 1, "name": "Belo Horizonte"},
        {"pk": 2, "name": "São Paulo"},
    ]

    companies = Company.objects.values("pk", "name", "vinculo").order_by("pk")
    assert list(companies) == [
        {"pk": 1, "name": "Company Fretamento", "vinculo": "fixa"},
        {"pk": 2, "name": "Company Marketplace", "vinculo": "marketplace"},
    ]
