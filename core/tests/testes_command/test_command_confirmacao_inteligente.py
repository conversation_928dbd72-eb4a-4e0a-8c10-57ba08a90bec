from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import cast
from unittest import mock
from zoneinfo import ZoneInfo

import pytest
import time_machine
from django.core.management import call_command
from django.utils import timezone
from model_bakery import baker

from commons import dateutils
from commons.dateutils import now, today_at
from core.forms.remanejamento_forms import SimulaRemanejamentoAutomatico
from core.models_grupo import (
    ClosedReasons,
    ConfirmacaoInteligenteExecutionLogger,
    ConfirmacaoInteligenteLogger,
    Grupo,
    GrupoClasse,
)
from core.models_travel import Travel
from core.service.grupos_staff import confirmacao_inteligente_svc
from core.service.grupos_staff.confirmacao_inteligente_svc import solve_cenario_remanejamento
from core.service.remanejamento.solver.solver_svc import RemanejamentoSolverManager, check_constraints
from core.tests import fixtures
from optools.errors import DataInconsistency, SolverFail
from optools.forms import SolverParams
from optools.models import Cenario


@pytest.fixture
def strategy_solution():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="SOLUTION",
        coeficientes={
            "cancelamento_fator": 1.8,
            "dummie_fator": 1.6,
            "remanejamento_fator": 1,
            "remanejamento_dummy_fator": 0.6,
            "burn_pax_fator": 22,
            "burn_dummy_fator": 18,
        },
        ativo=True,
    )


@pytest.fixture
def strategy_test():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="TEST",
        coeficientes={
            "cancelamento_fator": 1.6,
            "dummie_fator": 1.4,
            "remanejamento_fator": 1.5,
            "remanejamento_dummy_fator": 0.6,
            "burn_pax_fator": 24,
            "burn_dummy_fator": 18,
        },
        ativo=True,
    )


@pytest.fixture
def strategy_simulation():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="SIMULATION",
        coeficientes={
            "cancelamento_fator": 1.33,
            "dummie_fator": 1.14,
            "remanejamento_fator": 1,
            "remanejamento_dummy_fator": 0,
            "burn_pax_fator": 18,
            "burn_dummy_fator": 18,
        },
        ativo=True,
    )


@pytest.fixture
def strategy_fator_remanejamento_15():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="FATOR_REMANEJAMENTO_15",
        coeficientes={
            "cancelamento_fator": 1.47,
            "dummie_fator": 1.4,
            "remanejamento_fator": 1.5,
            "remanejamento_dummy_fator": 0.6,
            "burn_pax_fator": 22,
            "burn_dummy_fator": 18,
        },
        ativo=True,
    )


@pytest.fixture
def strategy_pax():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="PAX",
        coeficientes={
            "cancelamento_fator": 1.47,
            "dummie_fator": 1.4,
            "remanejamento_fator": 1,
            "remanejamento_dummy_fator": 0.6,
            "burn_pax_fator": 22,
            "burn_dummy_fator": 18,
        },
        ativo=True,
    )


@pytest.fixture
def strategy_cash():
    return baker.make(
        "optools.ConfirmacaoInteligenteStrategy",
        nome="CASH",
        coeficientes={
            "cancelamento_fator": 1.26,
            "dummie_fator": 1,
            "remanejamento_fator": 0.7,
            "remanejamento_dummy_fator": 0.1,
            "burn_pax_fator": 0,
            "burn_dummy_fator": 0,
        },
        ativo=True,
    )


@pytest.fixture
def cidade_sp():
    return baker.make("core.Cidade", slug="sao-paulo-sp", sigla="SAO")


@pytest.fixture
def cidade_bh():
    return baker.make("core.Cidade", slug="belo-horizonte-sp", sigla="BHZ")


@pytest.fixture
def cidade_rj():
    return baker.make("core.Cidade", slug="rio-de-janeiro-rj", sigla="RIO")


@pytest.fixture
def local_sp(cidade_sp):
    return baker.make("core.LocalEmbarque", latitude=-23.5486, longitude=-46.6392, cidade=cidade_sp)


@pytest.fixture
def local_bh(cidade_bh):
    return baker.make("core.LocalEmbarque", latitude=-19.9238, longitude=-43.9504, cidade=cidade_bh)


@pytest.fixture
def local_rj(cidade_rj):
    return baker.make("core.LocalEmbarque", latitude=-22.8797, longitude=-43.2057, cidade=cidade_rj)


@pytest.fixture
def make_grupo(grupo_factory):
    def _make_grupo(
        data,
        rota,
        rotina_onibus,
        pessoas,
        classe,
        capacidade,
        max_split_value,
        status,
        company,
        modelo_venda,
        is_extra,
        cancelamentos,
        buckets,
        grupo_classe_closed,
        accops=False,
        addons=False,
    ):
        grupo = grupo_factory(
            data,
            classe=classe,
            rota=rota,
            capacidade=capacidade,
            status=status,
            company=company,
            modelo_venda=modelo_venda,
            is_extra=is_extra,
            grupo_classe_closed=grupo_classe_closed,
        )
        grupo.rotina_onibus = rotina_onibus
        grupo.save(update_fields=["rotina_onibus"])
        grupo_classe = grupo.grupoclasse_set.first()
        trecho_classe = grupo.trechoclasse_set.filter(trecho_vendido__origem=rota.origem).first()
        trecho_classe.pessoas = pessoas
        trecho_classe.max_split_value = max_split_value
        pm = baker.make("core.PriceManager", value=max_split_value)
        if buckets:
            baker.make(
                "core.PriceBucket",
                tamanho=iter([bucket["tamanho"] for bucket in buckets]),
                value=iter([bucket["max_split_value"] for bucket in buckets]),
                price_manager=pm,
                _quantity=len(buckets),
                _bulk_create=True,
            )
        trecho_classe.price_manager = pm
        trecho_classe.save(update_fields=["pessoas", "max_split_value", "price_manager"])
        travels, users, passageiros = [], [], []
        if pessoas:
            travels = baker.make(
                "core.Travel",
                grupo=grupo,
                grupo_classe=grupo_classe,
                trecho_classe=trecho_classe,
                trecho_vendido=trecho_classe.trecho_vendido,
                status="pending",
                max_split_value=max_split_value,
                count_seats=1,
                _quantity=pessoas,
                _bulk_create=True,
            )
            users = [t.user for t in travels]
            passageiros = baker.make("core.Passageiro", travel=iter(travels), _quantity=pessoas, _bulk_create=True)
        if cancelamentos:
            travels_canc = baker.make(
                "core.Travel",
                grupo=grupo,
                grupo_classe=grupo_classe,
                trecho_classe=trecho_classe,
                trecho_vendido=trecho_classe.trecho_vendido,
                status="canceled",
                max_split_value=max_split_value,
                count_seats=1,
                _quantity=cancelamentos,
                _bulk_create=True,
            )
            baker.make("core.Passageiro", travel=iter(travels_canc), _quantity=cancelamentos, _bulk_create=True)
        if accops and pessoas:
            baker.make(
                "core.AccountingOperation",
                source="RESERVA",
                value_real=-Decimal("100"),
                value=-Decimal("100"),
                passageiro=iter(passageiros),
                user=iter(users),
                travel=iter(travels),
                _quantity=pessoas,
                _bulk_create=True,
            )
            if addons:
                baker.make(
                    "core.AccountingOperation",
                    travel=iter(travels),
                    source="BAGAGEM_ADICIONAL",
                    value=-Decimal(20),
                    value_real=-Decimal(20),
                    _quantity=pessoas,
                    _bulk_create=True,
                )
                baker.make(
                    "core.AccountingOperation",
                    travel=iter(travels),
                    source="SEGURO_EXTRA",
                    value=-Decimal(3),
                    value_real=-Decimal(3),
                    _quantity=pessoas,
                    _bulk_create=True,
                )

        return grupo

    return _make_grupo


@pytest.fixture
def make_grupo_ida_volta(make_grupo):
    def _make_grupo_ida_volta(
        datetime_ida_grupo_ida,
        datetime_ida_grupo_volta,
        classe,
        pessoas_ida,
        pessoas_volta,
        capacidade,
        max_split_value,
        rota_ida,
        rota_volta,
        status_ida,
        status_volta,
        rotina_onibus,
        company,
        modelo_venda,
        is_extra,
        cancelamentos_ida,
        cancelamentos_volta,
        grupo_classe_closed,
        buckets=None,
        accops=False,
        addons=False,
    ):
        grupo_ida = make_grupo(
            data=datetime_ida_grupo_ida,
            rota=rota_ida,
            rotina_onibus=rotina_onibus,
            capacidade=capacidade,
            pessoas=pessoas_ida,
            classe=classe,
            max_split_value=max_split_value,
            status=status_ida,
            company=company,
            modelo_venda=modelo_venda,
            is_extra=is_extra,
            cancelamentos=cancelamentos_ida,
            buckets=buckets,
            grupo_classe_closed=grupo_classe_closed,
            accops=accops,
            addons=addons,
        )
        grupo_volta = make_grupo(
            data=datetime_ida_grupo_volta,
            rota=rota_volta,
            rotina_onibus=rotina_onibus,
            capacidade=capacidade,
            pessoas=pessoas_volta,
            classe=classe,
            max_split_value=max_split_value,
            status=status_volta,
            company=company,
            modelo_venda=modelo_venda,
            is_extra=is_extra,
            cancelamentos=cancelamentos_volta,
            buckets=buckets,
            grupo_classe_closed=grupo_classe_closed,
            accops=accops,
            addons=addons,
        )
        return grupo_ida, grupo_volta

    return _make_grupo_ida_volta


@time_machine.travel("2025-04-09T12:45:00-03:00")
@mock.patch("core.service.grupos_staff.confirmacao_inteligente_svc._confirmacao_inteligente")
def test_command_confirmacao_inteligente_solver_uma_rota_principal(_confirmacao_inteligente, strategy_solution):
    two_days_from_now = timezone.now() + timedelta(hours=48)
    start_datetime = datetime(
        two_days_from_now.year,
        two_days_from_now.month,
        two_days_from_now.day,
        13,
        0,
        tzinfo=ZoneInfo("America/Sao_Paulo"),
    )

    end_datetime = start_datetime + timedelta(hours=25)
    rota_p = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    call_command("confirmacao_inteligente_grupos")

    _confirmacao_inteligente.delay.assert_called_once_with(
        execucao_id=execucao.id,
        grupos_ida_volta_ids={},
        start_datetime_str=start_datetime.isoformat(),
        end_datetime_str=end_datetime.isoformat(),
        nome_cenario=Cenario.PRODUCAO.name,
        dry_run=False,
    )


@mock.patch("core.service.grupos_staff.confirmacao_inteligente_svc._confirmacao_inteligente")
@time_machine.travel("2025-04-09 12:45:00-0300", tick=False)
def test_command_confirmacao_inteligente_solver_varias_rotas_principais(_confirmacao_inteligente, strategy_solution):
    rota_principal = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    rota_principal_2 = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao_1 = baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal],
        estrategia_aplicada=strategy_solution,
    )
    execucao_2 = baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal_2],
        estrategia_aplicada=strategy_solution,
    )
    start_datetime = today_at(hour=13) + timedelta(hours=48)

    end_datetime = start_datetime + timedelta(hours=25)
    call_command("confirmacao_inteligente_grupos")
    assert _confirmacao_inteligente.delay.call_count == 2

    _confirmacao_inteligente.delay.assert_any_call(
        execucao_id=execucao_1.id,
        grupos_ida_volta_ids={},
        start_datetime_str=start_datetime.isoformat(),
        end_datetime_str=end_datetime.isoformat(),
        nome_cenario=Cenario.PRODUCAO.name,
        dry_run=False,
    )

    _confirmacao_inteligente.delay.assert_any_call(
        execucao_id=execucao_2.id,
        grupos_ida_volta_ids={},
        start_datetime_str=start_datetime.isoformat(),
        end_datetime_str=end_datetime.isoformat(),
        nome_cenario=Cenario.PRODUCAO.name,
        dry_run=False,
    )


@mock.patch("core.service.grupos_staff.confirmacao_inteligente_svc._confirmacao_inteligente")
def test_command_confirmacao_inteligente_solver_nenhuma_rota_principal(_confirmacao_inteligente):
    call_command("confirmacao_inteligente_grupos")
    assert _confirmacao_inteligente.delay.call_count == 0


@time_machine.travel("1999-12-01T14:00:00-03:00")
def test_confirmacao_inteligente_numero_de_queries(
    mocker, django_assert_num_queries, local_bh, local_sp, make_grupo_ida_volta, strategy_solution
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("3000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    buckets = [{"max_split_value": Decimal("50.00"), "tamanho": 1}, {"max_split_value": Decimal("60.00"), "tamanho": 1}]

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        buckets=buckets,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=7,
        pessoas_volta=9,
        capacidade=10,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        buckets=buckets,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido = grupo_ida_hibrido.trechoclasse_set.first()
    tc_grupo_volta_hibrido = grupo_volta_hibrido.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido.id: {
                (tc_grupo_ida_hibrido.trecho_vendido_id, tc_grupo_ida_hibrido.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido.id: {
                (tc_grupo_volta_hibrido.trecho_vendido_id, tc_grupo_volta_hibrido.grupo_classe.tipo_assento): 1
            },
        },
    )
    # 83 -> 86 (+3 globalsettings) -> item adicional (87) -> globalsetting (88) -> item adicional no remanejamento (89)
    # -> +1 accops -> 92 add tabelas optools -> 119 add qtd pax hibrido -> 117 tidy applies_solution -> 118 globalsetting solver_num_workers
    # -> 119 globalsetting solver_max_time_in_seconds -> 117 remove globalsettings ml_models_svc -> 118 marcacao assento no remanejamento
    with django_assert_num_queries(118):
        call_command("confirmacao_inteligente_grupos")


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_pending_na_janela(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: pending, dentro da janela
    # volta: pending, dentro da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)

    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_1, grupo_volta_1 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=50),
        datetime_ida_grupo_volta=now() + timedelta(hours=55),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos))
    assert len(list_of_grupos) == 2


@time_machine.travel("1999-12-01T14:00:00-03:00", tick=False)
def test_confirmacao_inteligente_remanejamentos_possiveis_viagem_curta(cidade_bh, cidade_sp, strategy_solution):
    """ """
    company = baker.make("core.Company", name="nome")
    local_bh_1 = baker.make("core.LocalEmbarque", latitude=-19.92383480, longitude=-43.95049580, cidade=cidade_bh)
    # Dentro do raio de 9 km
    local_bh_2 = baker.make("core.LocalEmbarque", latitude=-19.9, longitude=-43.9504958, cidade=cidade_bh)
    # Fora do raio de 9 km
    local_bh_3 = baker.make("core.LocalEmbarque", latitude=-10, longitude=-43.9504958, cidade=cidade_bh)

    local_sp_1 = baker.make("core.LocalEmbarque", latitude=-23.5486, longitude=-46.6392, cidade=cidade_sp)

    rota1_ida = fixtures._mock_rota2(local_bh_1, local_sp_1)
    trecho_vendido_1 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_1, destino=local_sp_1)
    trecho_vendido_2 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_2, destino=local_sp_1)
    trecho_vendido_3 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_3, destino=local_sp_1)

    duracao_ida = timedelta(hours=1, minutes=30)

    tc = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        trecho_vendido=trecho_vendido_1,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_1.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe possível. Uma hora de atraso de uma viagem de 1:30 com um deslocamento menor que 9 Km
    tcp_1 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 19:00"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 19:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Três horas de atraso de uma viagem de 1:30 com um deslocamento menor que 9 Km
    tcp_2 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:00"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Uma hora de atraso de uma viagem de 1:30 com um deslocamento maior que 9 Km
    tcp_3 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:00"),
        trecho_vendido=trecho_vendido_3,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_3.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    assert check_constraints(tc, tcp_1) is True
    assert check_constraints(tc, tcp_2) is False
    assert check_constraints(tc, tcp_3) is False


@time_machine.travel("1999-12-01T14:00:00-03:00")
def test_confirmacao_inteligente_remanejamentos_possiveis_viagem_muito_longa(cidade_bh, cidade_sp, strategy_solution):
    """ """
    company = baker.make("core.Company", name="nome")
    local_bh_1 = baker.make("core.LocalEmbarque", latitude=-19.92383480, longitude=-43.95049580, cidade=cidade_bh)
    # Dentro do raio de 12 km
    local_bh_2 = baker.make("core.LocalEmbarque", latitude=-19.82, longitude=-43.9504958, cidade=cidade_bh)
    # Fora do raio de 12 km
    local_bh_3 = baker.make("core.LocalEmbarque", latitude=-19.78, longitude=-43.9504958, cidade=cidade_bh)

    local_sp_1 = baker.make("core.LocalEmbarque", latitude=-23.5486, longitude=-46.6392, cidade=cidade_sp)

    rota1_ida = fixtures._mock_rota2(local_bh_1, local_sp_1)
    trecho_vendido_1 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_1, destino=local_sp_1)
    trecho_vendido_2 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_2, destino=local_sp_1)
    trecho_vendido_3 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_3, destino=local_sp_1)

    duracao_ida = timedelta(hours=15, minutes=0)

    tc = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        trecho_vendido=trecho_vendido_1,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_1.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe possível. 4h30min de atraso de uma viagem de 15:00 com um deslocamento menor que 15 Km
    tcp_1 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 22:30"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 22:30"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Cinco horas de atraso de uma viagem de 15h com um deslocamento menor que 15 Km
    tcp_2 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 23:00"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 23:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Duas horas de atraso de uma viagem de 15h com um deslocamento maior que 15 Km
    tcp_3 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 20:00"),
        trecho_vendido=trecho_vendido_3,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=timedelta(hours=1, minutes=30),
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_3.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 20:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    assert check_constraints(tc, tcp_1) is True
    assert check_constraints(tc, tcp_2) is False
    assert check_constraints(tc, tcp_3) is False


@time_machine.travel("1999-12-01T14:00:00-03:00")
def test_confirmacao_inteligente_remanejamentos_possiveis_viagem_longa(cidade_bh, cidade_sp, strategy_solution):
    """ """
    company = baker.make("core.Company", name="nome")
    local_bh_1 = baker.make("core.LocalEmbarque", latitude=-19.92383480, longitude=-43.95049580, cidade=cidade_bh)
    # Dentro do raio de 12 km
    local_bh_2 = baker.make("core.LocalEmbarque", latitude=-19.82, longitude=-43.9504958, cidade=cidade_bh)
    # Fora do raio de 12 km
    local_bh_3 = baker.make("core.LocalEmbarque", latitude=-19.8, longitude=-43.9504958, cidade=cidade_bh)

    local_sp_1 = baker.make("core.LocalEmbarque", latitude=-23.5486, longitude=-46.6392, cidade=cidade_sp)

    rota1_ida = fixtures._mock_rota2(local_bh_1, local_sp_1)
    trecho_vendido_1 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_1, destino=local_sp_1)
    trecho_vendido_2 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_2, destino=local_sp_1)
    trecho_vendido_3 = baker.make("core.TrechoVendido", rota=rota1_ida, origem=local_bh_3, destino=local_sp_1)

    duracao_ida = timedelta(hours=7, minutes=30)

    tc = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        trecho_vendido=trecho_vendido_1,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_1.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 18:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe possível. 3h30min de atraso de uma viagem de 7:30 com um deslocamento menor que 12 Km
    tcp_1 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:30"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 21:30"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Quatro horas de atraso de uma viagem de 7:30 com um deslocamento menor que 12 Km
    tcp_2 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 22:00"),
        trecho_vendido=trecho_vendido_2,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=duracao_ida,
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_2.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 22:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    ## Trecho classe impossível. Duas horas de atraso de uma viagem de 7:30 com um deslocamento maior que 12 Km
    tcp_3 = baker.make(
        "core.TrechoClasse",
        datetime_ida=dateutils.to_default_tz_required("2020-10-06 20:00"),
        trecho_vendido=trecho_vendido_3,
        grupo_classe__capacidade=25,
        max_split_value=Decimal("25"),
        duracao_ida=timedelta(hours=1, minutes=30),
        grupo__status="pending",
        grupo__rota_id=trecho_vendido_3.rota_id,
        grupo__datetime_ida=dateutils.to_default_tz_required("2020-10-06 20:00"),
        grupo_classe__tipo_assento="leito",
        grupo__company=company,
        grupo__modelo_venda=Grupo.ModeloVenda.BUSER,
        buckets=None,
        vagas=5,
        price_manager=None,
    )

    assert check_constraints(tc, tcp_1) is True
    assert check_constraints(tc, tcp_2) is False
    assert check_constraints(tc, tcp_3) is False


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_considera_grupos_com_grupo_classe_fechado(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: pending, dentro da janela
    # volta: pending, dentro da janela
    # grupos com grupos classe fechado são desconsiderados
    """

    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)

    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_1, grupo_volta_1 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=50),
        datetime_ida_grupo_volta=now() + timedelta(hours=55),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
    )
    # usar mesmo datetime ida dos grupos anteriores buga o mapa de grupos ida x volta.
    grupo_ida_2, grupo_volta_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=60),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos))
    assert len(list_of_grupos) == 4


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_pending_dentro_fora_janela(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: pending, dentro da janela
    # volta: pending, fora da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=50),
        datetime_ida_grupo_volta=now() + timedelta(hours=80),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos)) == 2


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_pending_fora_janela(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: pending, fora da janela
    # volta: pending, fora da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=98),
        datetime_ida_grupo_volta=now() + timedelta(hours=100),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 0


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_confirmados_fora_janela(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: travel_confirmed, fora da janela
    # volta: travel_confirmed, fora da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=30),
        datetime_ida_grupo_volta=now() + timedelta(hours=40),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 0


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_confirmados_dentro_fora_janela_1(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: travel_confirmed, fora da janela
    # volta: travel_confirmed, dentro da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_5, grupo_volta_5 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=40),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos))
    assert len(list_of_grupos) == 0
    # Aqui como a IDA está fora da janela e a VOLTA está dentro, não deve estar no check


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_confirmados_dentro_fora_janela_2(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta.
    # ida: travel_confirmed, dentro da janela
    # volta: travel_confirmed, fora da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_6, grupo_volta_6 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=70),
        datetime_ida_grupo_volta=now() + timedelta(hours=80),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos))
    assert len(list_of_grupos) == 2


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_no_company_dentro_janela(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para par de grupos ida e volta. Deve selecionar mesmo aqueles sem empresa escalada.
    # ida: travel_confirmed, dentro da janela
    # volta: travel_confirmed, fora da janela
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )
    # dado dois pares de grupos ida e volta, sendo um par com Empresa escalada e outro não
    grupo_ida_6, grupo_volta_6 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=50),
        datetime_ida_grupo_volta=now() + timedelta(hours=55),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_7, grupo_volta_7 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=60),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    # espero que todos os grupos sejam selecionados, mesmo aqueles sem company.
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == len(set(list_of_grupos))
    assert len(list_of_grupos) == 4
    assert list_of_grupos == [grupo_ida_6.id, grupo_ida_7.id, grupo_volta_6.id, grupo_volta_7.id]


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_status_grupos(make_grupo_ida_volta, local_bh, local_sp, strategy_solution):
    """
    Testa filtro para pares de grupos ida e volta, com os 4 status possíveis dos grupos
    - travel_confirmed: ok
    - pending: ok
    - done: não deve aparecer
    - canceled: não deve aparecer
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_done, grupo_volta_done = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="done",
        status_volta="done",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_canceled, grupo_volta_canceled = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=66),
        datetime_ida_grupo_volta=now() + timedelta(hours=76),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="canceled",
        status_volta="canceled",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_pending, grupo_volta_canceled = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=66),
        datetime_ida_grupo_volta=now() + timedelta(hours=76),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="canceled",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 0


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_modelo_venda_grupos(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para pares de grupos ida e volta, com os 3 modelos de venda possíveis dos grupos
    - buser: ok
    - marketplace: não deve aparecer
    - hibrido: ok
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=51),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_mktplace, grupo_volta_mktplace = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=52),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="marketplace",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina,
        company=company,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 4
    assert {grupo_ida_buser.id, grupo_volta_buser.id, grupo_ida_hibrido.id, grupo_volta_hibrido.id} == set(
        list_of_grupos
    )


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_frete_grupos(make_grupo_ida_volta, local_bh, local_sp, strategy_solution):
    """
    Testa filtro de pares de grupos ida e volta, com e sem frete associado na rotina.
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_frete, grupo_volta_frete = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    rotina_not_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=None,
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )
    grupo_ida_not_frete, grupo_volta_not_frete = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_not_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 2
    assert {grupo_ida_frete.id, grupo_volta_frete.id} == set(list_of_grupos)


@pytest.mark.parametrize(
    "valor_frete, expected_custo_assento",
    [(None, 4000 / ((4 + 4) * 2)), (2400, 2400 / (4 + 4))],
)
@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_custo_assento(
    valor_frete,
    expected_custo_assento,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    strategy_solution,
):
    """
    Testa filtro para pares de grupos ida e volta, com e sem frete associado na rotina.
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_frete, grupo_volta_frete = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_frete.valor_frete = valor_frete
    grupo_ida_frete.save()
    grupo_volta_frete.valor_frete = valor_frete
    grupo_volta_frete.save()

    baker.make(
        "core.GrupoClasse",
        capacidade=196,
        grupo=grupo_ida_frete,
        closed_by=None,
        closed_at=timezone.now(),
        closed_reason=ClosedReasons.CLEANUP_OLD_CLASSES,
        _quantity=2,
        _bulk_create=True,
    )

    baker.make(
        "core.GrupoClasse",
        capacidade=4,
        grupo=grupo_ida_frete,
    )

    form = SimulaRemanejamentoAutomatico(
        grupo_ids=[grupo_ida_frete.id, grupo_volta_frete.id],
        grupos_ida_volta={grupo_ida_frete: grupo_volta_frete},
        marketplace=False,
        hibrido=False,
        downgrade=True,
        grupo_fechado=False,
        range_minutos=240,
        range_km=20,
    )

    solver_params = SolverParams(**strategy_solution.coeficientes)
    rsm = RemanejamentoSolverManager.from_grupos_check_de_cancelamento(form=form, solver_params=solver_params)

    assert rsm._grupos_dict[grupo_ida_frete.id].custo_assento == expected_custo_assento


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_gmv_previsto(make_grupo_ida_volta, local_bh, local_sp, strategy_solution):
    """
    Valida que gmv_previsto no input e otimizado é o mesmo, quando não há cancelamento de travels ou remanejamento.
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")

    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )
    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )
    # addons=True implica em travels com bagagem extra, seguro, etc.
    # Esse valor deve ser considerado na otimização.
    grupo_ida_frete, grupo_volta_frete = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=40,
        pessoas_volta=40,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
        addons=True,
    )

    baker.make(
        "core.GrupoClasse",
        capacidade=40,
        grupo=grupo_ida_frete,
    )

    form = SimulaRemanejamentoAutomatico(
        grupo_ids=[grupo_ida_frete.id, grupo_volta_frete.id],
        grupos_ida_volta={grupo_ida_frete: grupo_volta_frete},
        marketplace=False,
        hibrido=False,
        downgrade=True,
        grupo_fechado=False,
        range_minutos=240,
        range_km=20,
    )

    solver_params = SolverParams(**strategy_solution.coeficientes)
    rsm = RemanejamentoSolverManager.from_grupos_check_de_cancelamento(form=form, solver_params=solver_params)

    _now = now()
    start_datetime, end_datetime = _now + timedelta(hours=48), _now + timedelta(hours=73)
    solver_input_form = rsm.build_solver_input_form(start_datetime, end_datetime)
    solution, _ = solve_cenario_remanejamento(solver_input_form, solver_params, strategy_solution.nome)
    # O gmv previsto no input e o otimizado devem ser o mesmo quando nao houver cancelamento de travel ou remanejamento
    # e deve considerar as receitas extras.
    # max_split_value = 100 | Bagagem_extra = 20 | Seguro Extra = 3
    assert {t.receita_agregada for t in solver_input_form.travels} == {Decimal("123.00")}
    assert {g.gmv_previsto for g in solution.cancelar_grupos} == {40 * Decimal("123.00")}


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_extras_agora_eles_contam(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para pares de grupos ida e volta, sendo ou não grupos extras
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida, grupo_volta = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=60),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_extra, grupo_volta_extra = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=61),
        datetime_ida_grupo_volta=now() + timedelta(hours=66),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=True,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 4
    assert {grupo_ida.id, grupo_volta.id, grupo_ida_extra.id, grupo_volta_extra.id} == set(list_of_grupos)


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_rotina_extra(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    """
    Testa filtro para pares de grupos ida e volta, sendo ou não grupos extras
    """
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    rotina_extra = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
        is_extra=True,
    )

    grupo_ida, grupo_volta = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    grupo_ida_rotina_extra, grupo_volta_rotina_extra = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_extra,
        company=company,
        modelo_venda="buser",
        is_extra=True,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 2
    assert {grupo_ida.id, grupo_volta.id} == set(list_of_grupos)


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_rotina_threshold_nulo(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=None,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida, grupo_volta = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 0


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_filtra_grupos_rotina_threshold_none(
    make_grupo_ida_volta, local_bh, local_sp, strategy_solution
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_frete = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=None,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )

    grupo_ida, grupo_volta = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=65),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_frete,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    list_of_grupos = list(all_grupos.keys()) + list(all_grupos.values())
    assert len(list_of_grupos) == 0


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_solver_grupos_dentro_da_janela(
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1, company_2, company_3, company_4 = baker.make("core.Company", _quantity=4, _bulk_create=True)
    rotina_1, rotina_2, rotina_3, rotina_4 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=iter([company_1, company_2, company_3, company_4]),
        base_operacional=local_origem.cidade,
        _quantity=4,
        _bulk_create=True,
    )
    default_params = {
        "classe": "executivo",
        "pessoas_ida": 3,
        "pessoas_volta": 1,
        "capacidade": 4,
        "max_split_value": Decimal("100"),
        "rota_ida": rota1_ida,
        "rota_volta": rota1_volta,
        "status_ida": "pending",
        "status_volta": "pending",
        "modelo_venda": "buser",
        "is_extra": False,
        "cancelamentos_ida": 0,
        "cancelamentos_volta": 0,
        "grupo_classe_closed": False,
    }

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        rotina_onibus=rotina_1,
        company=company_1,
        **default_params,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        rotina_onibus=rotina_2,
        company=company_2,
        **default_params,
    )

    grupo_ida_buser_antes_janela, grupo_volta_buser_depois_janela = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=47),
        datetime_ida_grupo_volta=now() + timedelta(hours=74),
        rotina_onibus=rotina_3,
        company=company_3,
        **default_params,
    )

    grupo_ida_hibrido_antes_janela, grupo_volta_hibrido_depois_janela = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=47),
        datetime_ida_grupo_volta=now() + timedelta(hours=74),
        rotina_onibus=rotina_4,
        company=company_4,
        **default_params,
    )

    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    assert all_grupos == {grupo_ida_buser.id: grupo_volta_buser.id, grupo_ida_hibrido.id: grupo_volta_hibrido.id}


@pytest.mark.parametrize(
    "side_effect",
    [
        SolverFail(status_code="INVALID_SOLUTION", message="falhou em achar uma solução"),
        DataInconsistency(status_code="DATA_ERROR", message="solução com dados inconsistentes"),
    ],
)
@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_solver_nao_aplica_estrategia_se_ocorrer_alguma_exception(
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    side_effect,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    mock_simular_solver = mocker.patch(
        "core.service.grupos_staff.confirmacao_inteligente_svc.solve_cenario_remanejamento",
        return_value=None,
    )
    mock_aplicar_solver = mocker.patch(
        "core.service.grupos_staff.confirmacao_inteligente_svc.RemanejamentoSolverManager.applies_solution",
        return_value=None,
    )
    mock_simular_solver.side_effect = side_effect

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("3000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido = grupo_ida_hibrido.trechoclasse_set.first()
    tc_grupo_volta_hibrido = grupo_volta_hibrido.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido.id: {
                (tc_grupo_ida_hibrido.trecho_vendido_id, tc_grupo_ida_hibrido.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido.id: {
                (tc_grupo_volta_hibrido.trecho_vendido_id, tc_grupo_volta_hibrido.grupo_classe.tipo_assento): 1
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")

    mock_simular_solver.assert_called_once()
    mock_aplicar_solver.assert_not_called()
    log = ConfirmacaoInteligenteExecutionLogger.objects.first()
    assert log
    assert log.error == side_effect.message
    grupos_id = [grupo_ida_buser.id, grupo_volta_buser.id, grupo_ida_hibrido.id, grupo_volta_hibrido.id]
    statuses = Grupo.objects.filter(id__in=grupos_id).values_list("status", flat=True)
    assert all(status == Grupo.Status.PENDING for status in statuses)


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_simula_confirmacao_inteligente_solver_loga_quando_ocorre_exception(
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
    strategy_test,
):
    mocker.patch(
        "optools.remanejamento.is_possible_solution",
        return_value=False,
    )
    mocker.patch(
        "core.service.grupos_staff.confirmacao_inteligente_svc.RemanejamentoSolverManager.applies_solution",
        return_value=None,
    )
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_p],
        estrategias_simulacao=[strategy_test],
        estrategia_aplicada=strategy_solution,
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("3000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=4,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido = grupo_ida_hibrido.trechoclasse_set.first()
    tc_grupo_volta_hibrido = grupo_volta_hibrido.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido.id: {
                (tc_grupo_ida_hibrido.trecho_vendido_id, tc_grupo_ida_hibrido.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido.id: {
                (tc_grupo_volta_hibrido.trecho_vendido_id, tc_grupo_volta_hibrido.grupo_classe.tipo_assento): 1
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")
    call_command("simula_confirmacao_inteligente_grupos")
    assert ConfirmacaoInteligenteExecutionLogger.objects.count() == 2


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_com_forecast_trecho(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido = grupo_ida_hibrido.trechoclasse_set.first()
    tc_grupo_volta_hibrido = grupo_volta_hibrido.trechoclasse_set.first()

    mock_trechos_forecast = mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido.id: {
                (tc_grupo_ida_hibrido.trecho_vendido_id, tc_grupo_ida_hibrido.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido.id: {
                (tc_grupo_volta_hibrido.trecho_vendido_id, tc_grupo_volta_hibrido.grupo_classe.tipo_assento): 1
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")
    mock_trechos_forecast.assert_called_once()
    mock_remanejamento_automatico.assert_called_once()


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_completo(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido, grupo_volta_hibrido = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido = grupo_ida_hibrido.trechoclasse_set.first()
    tc_grupo_volta_hibrido = grupo_volta_hibrido.trechoclasse_set.first()

    mock_trechos_forecast = mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido.id: {
                (tc_grupo_ida_hibrido.trecho_vendido_id, tc_grupo_ida_hibrido.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido.id: {
                (tc_grupo_volta_hibrido.trecho_vendido_id, tc_grupo_volta_hibrido.grupo_classe.tipo_assento): 1
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")
    mock_trechos_forecast.assert_called_once()
    mock_remanejamento_automatico.assert_called_once()
    # TODO: Checar remanejamento nos objetos do django


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_completo_com_input_particionado(
    mock_remanejamento_automatico, make_grupo_ida_volta, mocker, strategy_solution
):
    rota_p = baker.make("core.RotaPrincipal", eixo="CPC-FLN")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_p],
        estrategia_aplicada=strategy_solution,
        particionado=True,
    )

    cidade_cpc = baker.make("core.Cidade", slug="chapeco-sc", sigla="CPC")
    cidade_fln = baker.make("core.Cidade", slug="florianopolis-sc", sigla="FLN")
    local_origem = baker.make("core.LocalEmbarque", latitude=-27.09, longitude=-52.6, cidade=cidade_cpc)
    local_destino = baker.make("core.LocalEmbarque", latitude=-27.06, longitude=-48.55, cidade=cidade_fln)
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_2,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido_2, grupo_volta_hibrido_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=56),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_buser_3, grupo_volta_buser_3 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_hibrido_4, grupo_volta_hibrido_4 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=56),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="hibrido",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()
    tc_grupo_ida_hibrido_2 = grupo_ida_hibrido_2.trechoclasse_set.first()
    tc_grupo_volta_hibrido_2 = grupo_volta_hibrido_2.trechoclasse_set.first()
    tc_grupo_ida_buser_3 = grupo_ida_buser_3.trechoclasse_set.first()
    tc_grupo_volta_buser_3 = grupo_volta_buser_3.trechoclasse_set.first()
    tc_grupo_ida_hibrido_4 = grupo_ida_hibrido_2.trechoclasse_set.first()
    tc_grupo_volta_hibrido_4 = grupo_volta_hibrido_2.trechoclasse_set.first()

    mock_trechos_forecast = mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido_2.id: {
                (tc_grupo_ida_hibrido_2.trecho_vendido_id, tc_grupo_ida_hibrido_2.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido_2.id: {
                (tc_grupo_volta_hibrido_2.trecho_vendido_id, tc_grupo_volta_hibrido_2.grupo_classe.tipo_assento): 1
            },
            grupo_ida_buser_3.id: {
                (tc_grupo_ida_buser_3.trecho_vendido_id, tc_grupo_ida_buser_3.grupo_classe.tipo_assento): 1
            },
            grupo_volta_buser_3.id: {
                (tc_grupo_volta_buser_3.trecho_vendido_id, tc_grupo_volta_buser_3.grupo_classe.tipo_assento): 1
            },
            grupo_ida_hibrido_4.id: {
                (tc_grupo_ida_hibrido_4.trecho_vendido_id, tc_grupo_ida_hibrido_4.grupo_classe.tipo_assento): 1
            },
            grupo_volta_hibrido_4.id: {
                (tc_grupo_volta_hibrido_4.trecho_vendido_id, tc_grupo_volta_hibrido_4.grupo_classe.tipo_assento): 1
            },
        },
    )

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={
            grupo_ida_buser.id: 1,
            grupo_volta_buser.id: 1,
            grupo_ida_hibrido_2.id: 1,
            grupo_volta_hibrido_2.id: 1,
            grupo_ida_buser_3.id: 1,
            grupo_volta_buser_3.id: 1,
            grupo_ida_hibrido_4.id: 1,
            grupo_volta_hibrido_4.id: 1,
        },
    )
    call_command("confirmacao_inteligente_grupos")

    assert mock_trechos_forecast.call_count == 2
    assert mock_remanejamento_automatico.call_count == 2


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_ida_volta(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    execucao = baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    cidade_fln = baker.make("core.Cidade", slug="florianopolis-sc", sigla="FLN")
    local_fln = baker.make("core.LocalEmbarque", latitude=-27.06, longitude=-48.55, cidade=cidade_fln)
    rota1_ida = fixtures._mock_rota_3_locais(local_origem, local_destino, local_fln)
    rota1_volta = fixtures._mock_rota_3_locais(local_fln, local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_destino.cidade,
    )
    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
    )
    # retorna um dict no formato {grupo_ida_id: grupo_volta_id}
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    map_grupo_ida_volta = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    assert map_grupo_ida_volta[grupo_ida_buser.id] == grupo_volta_buser.id


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@mock.patch("core.service.notifications.user_notification_svc.viagem_cancelada_pela_buser")
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_completo_conexoes(
    mock_n,
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    local_rj,
    cidade_bh,
    cidade_sp,
    cidade_rj,
    mocker,
    strategy_solution,
):
    rota_p_bh_sp = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    rota_p_sp_rj = baker.make("core.RotaPrincipal", eixo="SAO-RIO")

    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p_bh_sp], estrategia_aplicada=strategy_solution
    )
    rota_bh_sp_ida = fixtures._mock_rota2(local_bh, local_sp)
    rota_sp_rj_ida = fixtures._mock_rota2(local_sp, local_rj)
    rota_sp_bh_volta = fixtures._mock_rota2(local_sp, local_bh)
    rota_rj_sp_volta = fixtures._mock_rota2(local_rj, local_sp)

    company_1 = baker.make("core.Company", name="company_1")
    company_2 = baker.make("core.Company", name="company_2")

    rotina_bh_sp_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota_bh_sp_ida,
        rota_principal=rota_p_bh_sp,
        company=company_1,
        base_operacional=local_bh.cidade,
    )
    rotina_sp_rj = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota_sp_rj_ida,
        rota_principal=rota_p_sp_rj,
        company=company_1,
        base_operacional=local_rj.cidade,
    )
    rotina_bh_sp_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota_bh_sp_ida,
        rota_principal=rota_p_bh_sp,
        company=company_2,
        base_operacional=local_bh.cidade,
    )

    grupo_ida_sera_confirmado, grupo_volta_sera_confirmado = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_bh_sp_ida,
        rota_volta=rota_sp_bh_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_bh_sp_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=1,
        cancelamentos_volta=1,
        grupo_classe_closed=False,
        accops=True,
    )

    # grupos não serão confirmados
    grupo_ida_conexao_1, grupo_volta_conexao_1 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49, minutes=30),
        datetime_ida_grupo_volta=now() + timedelta(hours=50, minutes=30),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_bh_sp_ida,
        rota_volta=rota_sp_bh_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_bh_sp_2,
        company=company_2,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    grupo_ida_conexao_2, grupo_volta_conexao_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now()
        + timedelta(hours=49, minutes=30)
        + timedelta(hours=6),  # adiciona a chegada do grupo anterior
        datetime_ida_grupo_volta=now() + timedelta(hours=50, minutes=30) - timedelta(hours=6),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_sp_rj_ida,
        rota_volta=rota_rj_sp_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_sp_rj,
        company=company_2,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    # criando as conexões
    conexao = baker.make("Conexao", cidade_origem=cidade_bh, cidade_destino=cidade_rj)
    travel_conexao = baker.make("TravelConexao", conexao=conexao)
    trecho_conexao_1 = baker.make(
        "TrechoConexao",
        idx=0,
        conexao=conexao,
        origem=cidade_bh,
        destino=cidade_sp,
        modelo_venda="buser",
    )
    trecho_conexao_2 = baker.make(
        "TrechoConexao",
        idx=1,
        conexao=conexao,
        origem=cidade_sp,
        destino=cidade_rj,
        modelo_venda="buser",
    )
    travel_conexao_1 = grupo_ida_conexao_1.travel_set.first()
    travel_conexao_1.travel_conexao = travel_conexao
    travel_conexao_1.trecho_conexao = trecho_conexao_1
    travel_conexao_1.save(update_fields=["travel_conexao", "trecho_conexao"])

    travel_conexao_2 = grupo_ida_conexao_2.travel_set.first()
    travel_conexao_2.travel_conexao = travel_conexao
    travel_conexao_2.trecho_conexao = trecho_conexao_2
    travel_conexao_2.save(update_fields=["travel_conexao", "trecho_conexao"])

    grupo_ida_sera_confirmado.trechoclasse_set.first()
    grupo_volta_sera_confirmado.trechoclasse_set.first()
    grupo_ida_conexao_1.trechoclasse_set.first()
    grupo_volta_conexao_1.trechoclasse_set.first()
    grupo_ida_conexao_2.trechoclasse_set.first()
    grupo_volta_conexao_2.trechoclasse_set.first()

    call_command("confirmacao_inteligente_grupos")

    travel_conexao_1.refresh_from_db()
    travel_conexao_2.refresh_from_db()
    grupo_ida_sera_confirmado.refresh_from_db()
    grupo_ida_conexao_1.refresh_from_db()
    grupo_ida_conexao_2.refresh_from_db()

    assert travel_conexao_1.status == Travel.Status.CANCELED
    assert travel_conexao_2.status == Travel.Status.CANCELED

    assert grupo_ida_sera_confirmado.status == Grupo.Status.TRAVEL_CONFIRMED
    assert grupo_ida_conexao_1.status == Grupo.Status.PENDING
    assert grupo_ida_conexao_2.status == Grupo.Status.TRAVEL_CONFIRMED


@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@mock.patch("core.service.notifications.user_notification_svc.viagem_cancelada_pela_buser")
def test_confirmacao_inteligente_nao_cancela_grupos(
    mock_n,
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=0,
        solver_threshold=0,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company_1,
        base_operacional=local_origem.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
        accops=True,
    )

    grupo_ida_buser_2, grupo_volta_buser_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=53),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_buser_3, grupo_volta_buser_3 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_buser.trechoclasse_set.first()
    grupo_volta_buser.trechoclasse_set.first()
    grupo_ida_buser_2.trechoclasse_set.first()
    grupo_volta_buser_2.trechoclasse_set.first()
    grupo_ida_buser_3.trechoclasse_set.first()
    grupo_volta_buser_3.trechoclasse_set.first()

    call_command("confirmacao_inteligente_grupos")

    grupo_ida_buser.refresh_from_db()
    grupo_volta_buser.refresh_from_db()
    assert grupo_ida_buser.status != Grupo.Status.CANCELED
    assert grupo_volta_buser.status != Grupo.Status.CANCELED


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_varios_eixos(
    mock_remanejamento_automatico, make_grupo_ida_volta, local_bh, local_sp, local_rj, mocker
):
    rota_bhz_sao = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    rota_rio_sao = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    execucao = baker.make("optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_bhz_sao, rota_rio_sao])

    local_origem = local_bh
    local_destino = local_sp
    local_origem_2 = local_rj
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)

    company_1 = baker.make("core.Company", name="company_1")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_bhz_sao,
        company=company_1,
        base_operacional=local_origem.cidade,
    )

    rota2_ida = fixtures._mock_rota2(local_origem_2, local_destino)
    rota2_volta = fixtures._mock_rota2(local_destino, local_origem_2)
    company_2 = baker.make("core.Company", name="company_2")
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota2_ida,
        rota_principal=rota_rio_sao,
        company=company_2,
        base_operacional=local_origem_2.cidade,
    )

    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
    )

    grupo_ida_buser_2, grupo_volta_buser_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=53),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota2_ida,
        rota_volta=rota2_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    idas = []
    voltas = []
    start_date = now() + timedelta(hours=48)
    end_date = now() + timedelta(hours=73)
    all_grupos = confirmacao_inteligente_svc._filtra_grupos(execucao, start_date, end_date)
    for ida_id, volta_id in all_grupos.items():
        idas.append(ida_id)
        voltas.append(volta_id)
    assert grupo_ida_buser.id in idas
    assert grupo_volta_buser.id in voltas
    assert grupo_ida_buser.id not in voltas
    assert grupo_volta_buser.id not in idas
    assert grupo_ida_buser_2.id in idas
    assert grupo_volta_buser_2.id in voltas
    assert grupo_ida_buser_2.id not in voltas
    assert grupo_volta_buser_2.id not in idas
    assert len(all_grupos) == 2
    assert len(idas) == 2


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_confirma_grupos_com_breakeven(
    local_rj, local_sp, make_grupo_ida_volta, mocker, strategy_solution
):
    rota_principal = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal],
        estrategia_aplicada=strategy_solution,
    )

    rota_ida = fixtures._mock_rota2(local_rj, local_sp)
    rota_volta = fixtures._mock_rota2(local_sp, local_rj)

    company = baker.make("core.Company")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=1,
        rota=rota_ida,
        rota_principal=rota_principal,
        company=company,
        base_operacional=local_rj.cidade,
    )
    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina,
        company=company,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 30
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 30
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")

    grupos = Grupo.objects.all()
    assert all(grupo.status == Grupo.Status.TRAVEL_CONFIRMED for grupo in grupos)


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_cancela_grupos_fora_da_base(
    local_rj, local_sp, make_grupo_ida_volta, mocker, strategy_solution
):
    rota_principal = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal],
        estrategia_aplicada=strategy_solution,
    )

    rota_ida = fixtures._mock_rota2(local_rj, local_sp)
    rota_volta = fixtures._mock_rota2(local_sp, local_rj)
    company = baker.make("core.Company")
    company2 = baker.make("core.Company")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=1,
        rota=rota_ida,
        rota_principal=rota_principal,
        company=company,
        base_operacional=local_rj.cidade,
    )
    rotina2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=1,
        rota=rota_ida,
        rota_principal=rota_principal,
        company=company2,
        base_operacional=local_rj.cidade,
    )
    grupo_ida_1, grupo_volta_1 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=25,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina,
        company=company,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    grupo_ida_2, grupo_volta_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=69),
        datetime_ida_grupo_volta=now() + timedelta(hours=70),
        classe="executivo",
        pessoas_ida=1,
        pessoas_volta=15,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina,
        company=company,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    grupo_ida_3, grupo_volta_3 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=20,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina2,
        company=company2,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_1 = grupo_ida_1.trechoclasse_set.first()
    tc_grupo_volta_1 = grupo_volta_1.trechoclasse_set.first()
    tc_grupo_ida_2 = grupo_ida_2.trechoclasse_set.first()
    tc_grupo_volta_2 = grupo_volta_2.trechoclasse_set.first()
    tc_grupo_ida_3 = grupo_ida_3.trechoclasse_set.first()
    tc_grupo_volta_3 = grupo_volta_3.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_1.id: {(tc_grupo_ida_1.trecho_vendido_id, tc_grupo_ida_1.grupo_classe.tipo_assento): 30},
            grupo_volta_1.id: {(tc_grupo_volta_1.trecho_vendido_id, tc_grupo_volta_1.grupo_classe.tipo_assento): 7},
            grupo_ida_2.id: {(tc_grupo_ida_2.trecho_vendido_id, tc_grupo_ida_2.grupo_classe.tipo_assento): 9},
            grupo_volta_2.id: {(tc_grupo_volta_2.trecho_vendido_id, tc_grupo_volta_2.grupo_classe.tipo_assento): 30},
            grupo_ida_3.id: {(tc_grupo_ida_3.trecho_vendido_id, tc_grupo_ida_3.grupo_classe.tipo_assento): 28},
            grupo_volta_3.id: {(tc_grupo_volta_3.trecho_vendido_id, tc_grupo_volta_3.grupo_classe.tipo_assento): 21},
        },
    )
    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={
            grupo_ida_1.id: 30,
            grupo_volta_1.id: 7,
            grupo_ida_2.id: 9,
            grupo_volta_2.id: 30,
            grupo_ida_3.id: 28,
            grupo_volta_3.id: 21,
        },
    )
    call_command("confirmacao_inteligente_grupos")

    grupos_map = Grupo.objects.filter(
        id__in=[
            grupo_ida_1.id,
            grupo_volta_1.id,
            grupo_ida_2.id,
            grupo_volta_2.id,
            grupo_ida_3.id,
            grupo_volta_3.id,
        ]
    ).in_bulk()

    assert grupos_map[grupo_ida_1.id].status == Grupo.Status.TRAVEL_CONFIRMED
    assert grupos_map[grupo_volta_1.id].status == Grupo.Status.PENDING
    assert grupos_map[grupo_ida_2.id].status == Grupo.Status.PENDING
    assert grupos_map[grupo_volta_2.id].status == Grupo.Status.TRAVEL_CONFIRMED


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_soh_cancela_grupos_no_company_sem_pax_ida_e_volta(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    # se grupo ida e volta não tem pax nem company, então cancela.
    grupo_ida_cancelar, grupo_volta_cancelar = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
    )

    grupo_ida_com_pax, grupo_volta_com_pax = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=53),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_com_company, grupo_volta_com_company = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    # a confirmação inteligente deve manter os grupos com pax ou com company
    # mas os grupos sem pax e sem company devem ser cancelados.
    call_command("confirmacao_inteligente_grupos")
    # uma query pra atualizar todo mundo
    grupos_map = Grupo.objects.filter(
        id__in=[
            grupo_ida_cancelar.id,
            grupo_volta_cancelar.id,
            grupo_ida_com_pax.id,
            grupo_volta_com_pax.id,
            grupo_ida_com_company.id,
            grupo_volta_com_company.id,
        ]
    ).in_bulk()
    # grupos sem pax e sem company são cancelados
    assert grupos_map[grupo_ida_cancelar.id].status == Grupo.Status.CANCELED
    assert grupos_map[grupo_volta_cancelar.id].status == Grupo.Status.CANCELED
    # grupos com pax e company são confirmados
    assert grupos_map[grupo_ida_com_pax.id].status == Grupo.Status.TRAVEL_CONFIRMED
    assert grupos_map[grupo_volta_com_pax.id].status == Grupo.Status.TRAVEL_CONFIRMED
    # grupos sem pax e com company são fechados. Status == PENDING e grupoclasse.closed==True
    assert grupos_map[grupo_ida_com_company.id].status == Grupo.Status.PENDING
    assert grupos_map[grupo_volta_com_company.id].status == Grupo.Status.PENDING
    assert all(grupo_ida_com_company.grupoclasse_set.all().values_list("closed", flat=True))
    assert all(grupo_volta_com_company.grupoclasse_set.all().values_list("closed", flat=True))


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_cancela_grupos_e_registra_dias_parados_e_frete_zerado(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    # dado que a rota principal possui remanejamentos possíveis e um par de grupos ida e volta sem company
    # e sem pax, passível de cancelamento
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    # se grupo ida e volta não tem pax nem company, então cancela.
    grupo_ida_cancelar, grupo_volta_cancelar = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
        accops=True,
    )

    grupo_ida_com_pax, grupo_volta_com_pax = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=53),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_com_company, grupo_volta_com_company = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    # ao executar a confirmação inteligente
    call_command("confirmacao_inteligente_grupos")

    grupo_ida_cancelar.refresh_from_db()
    grupo_volta_cancelar.refresh_from_db()

    # espero que esse grupos, além de cancelados, estejam com os atributos abaixo nos valores especificados.
    assert grupo_ida_cancelar.status == Grupo.Status.CANCELED
    assert grupo_ida_cancelar.contar_dia_parado is True
    assert grupo_ida_cancelar.valor_frete == Decimal("0.00")
    assert (
        grupo_ida_cancelar.observacao_dia_parado.split("]")[1]
        == "Cancelamento confirmação inteligente. NO_COMPANY e NO_PAX"
    )

    assert grupo_volta_cancelar.status == Grupo.Status.CANCELED
    assert grupo_volta_cancelar.contar_dia_parado is True
    assert grupo_volta_cancelar.valor_frete == Decimal("0.00")
    assert (
        grupo_volta_cancelar.observacao_dia_parado.split("]")[1]
        == "Cancelamento confirmação inteligente. NO_COMPANY e NO_PAX"
    )


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_loga_por_grupo(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
    strategy_simulation,
    strategy_fator_remanejamento_15,
):
    # dado alguns grupos válidos na janela da confirmação inteligente.
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_p],
        estrategia_aplicada=strategy_solution,
        estrategias_simulacao=[strategy_simulation, strategy_fator_remanejamento_15],
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=None,
        base_operacional=local_origem.cidade,
    )
    # se grupo ida e volta não tem pax nem company, então cancela.
    grupo_ida_cancelar, grupo_volta_cancelar = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=None,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=True,
        accops=True,
    )

    grupo_ida_com_pax, grupo_volta_com_pax = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=53),
        datetime_ida_grupo_volta=now() + timedelta(hours=54),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_com_company, grupo_volta_com_company = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=55),
        datetime_ida_grupo_volta=now() + timedelta(hours=57),
        classe="executivo",
        pessoas_ida=0,
        pessoas_volta=0,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    tc_ida = grupo_ida_cancelar.trechoclasse_set.first()
    tc_volta = grupo_volta_cancelar.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_cancelar.id: {(tc_ida.trecho_vendido_id, tc_ida.grupo_classe.tipo_assento): 10},
            grupo_volta_cancelar.id: {(tc_volta.trecho_vendido_id, tc_volta.grupo_classe.tipo_assento): 30},
        },
    )

    # ao executar a rotina de confirmação
    call_command("confirmacao_inteligente_grupos")
    call_command("simula_confirmacao_inteligente_grupos")

    grupos_id = [
        grupo_ida_cancelar.id,
        grupo_volta_cancelar.id,
        grupo_ida_com_pax.id,
        grupo_volta_com_pax.id,
        grupo_ida_com_company.id,
        grupo_volta_com_company.id,
    ]

    logs = ConfirmacaoInteligenteLogger.objects.filter(grupo_id__in=grupos_id)
    # sera salvo um log para cada grupo na simulação
    assert logs.filter(solver_strategy=strategy_simulation.nome).count() == 6
    # e um log para cada grupo durante a resolução
    assert logs.filter(solver_strategy=strategy_solution.nome).count() == 6
    assert set(logs.values_list("grupo_id", flat=True)) == set(grupos_id)
    assert logs.filter(grupo_id=grupo_ida_cancelar.id).values_list("forecast_trechoclasse", flat=True).first() == {
        str(tc_ida.id): 10
    }


@mock.patch("core.service.remanejamento.solver.solver_svc.remanejamento_automatico", return_value=None)
@time_machine.travel("1999-12-01T13:00:00-03:00", tick=False)
def test_confirmacao_inteligente_cancela_grupos_cancelados(
    mock_remanejamento_automatico,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    mocker,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_1 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0.7,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
    )
    # se grupo ida e volta tem status="canceled", então cancela.
    grupo_ida_cancelar, grupo_volta_cancelar = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=30,
        pessoas_volta=30,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="canceled",
        status_volta="canceled",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
    )

    # a confirmação inteligente deve cancelar grupos que estão com status = 'canceled'
    call_command("confirmacao_inteligente_grupos")
    # uma query pra atualizar todo mundo
    grupos_map = Grupo.objects.filter(
        id__in=[
            grupo_ida_cancelar.id,
            grupo_volta_cancelar.id,
        ]
    ).in_bulk()
    # grupos com status = 'canceled' são cancelados
    assert grupos_map[grupo_ida_cancelar.id].status == Grupo.Status.CANCELED
    assert grupos_map[grupo_volta_cancelar.id].status == Grupo.Status.CANCELED


@time_machine.travel("2024-12-26T12:45:00-03:00")
def test_confirmacao_inteligente_executa_em_dry_run(
    local_rj, local_sp, make_grupo_ida_volta, mocker, strategy_solution
):
    rota_principal = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal],
        estrategia_aplicada=strategy_solution,
    )

    rota_ida = fixtures._mock_rota2(local_rj, local_sp)
    rota_volta = fixtures._mock_rota2(local_sp, local_rj)
    company = baker.make("core.Company")
    rotina = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=1,
        rota=rota_ida,
        rota_principal=rota_principal,
        company=company,
        base_operacional=local_rj.cidade,
    )
    grupo_ida_buser, grupo_volta_buser = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="executivo",
        pessoas_ida=3,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina,
        company=company,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_buser = grupo_ida_buser.trechoclasse_set.first()
    tc_grupo_volta_buser = grupo_volta_buser.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_buser.id: {
                (tc_grupo_ida_buser.trecho_vendido_id, tc_grupo_ida_buser.grupo_classe.tipo_assento): 30
            },
            grupo_volta_buser.id: {
                (tc_grupo_volta_buser.trecho_vendido_id, tc_grupo_volta_buser.grupo_classe.tipo_assento): 30
            },
        },
    )
    mock_confirm_groups = mocker.patch("core.service.remanejamento.solver.solver_svc.confirm_groups")
    mock_applie_solution_cancel_travel = mocker.patch(
        "core.service.remanejamento.solver.solver_svc.reserva_svc.grupo_cancel_travels_conexao"
    )
    call_command("confirmacao_inteligente_grupos", dry_run=True)
    mock_confirm_groups.assert_not_called()
    mock_applie_solution_cancel_travel.assert_not_called()


@time_machine.travel("2025-05-27T12:45:00-03:00", tick=False)
def test_confirmacao_inteligente_nao_remaneja_considerando_forecast(
    mocker,
    make_grupo_ida_volta,
    local_bh,
    local_sp,
    strategy_solution,
):
    rota_p = baker.make("core.RotaPrincipal", eixo="BHZ-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao", rotas_principais=[rota_p], estrategia_aplicada=strategy_solution
    )

    local_origem = local_bh
    local_destino = local_sp
    rota1_ida = fixtures._mock_rota2(local_origem, local_destino)
    rota1_volta = fixtures._mock_rota2(local_destino, local_origem)
    company = baker.make("core.Company", name="nome")
    rotina_1, rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=iter([Decimal("2000"), Decimal("8000")]),
        solver_threshold=0,
        rota=rota1_ida,
        rota_principal=rota_p,
        company=company,
        base_operacional=local_origem.cidade,
        _quantity=2,
        _bulk_create=True,
    )

    grupo_ida_1, grupo_volta_1 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="semi leito",
        pessoas_ida=20,
        pessoas_volta=40,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="travel_confirmed",
        status_volta="travel_confirmed",
        rotina_onibus=rotina_1,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    grupo_ida_2, grupo_volta_2 = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="semi leito",
        pessoas_ida=20,
        pessoas_volta=20,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota1_ida,
        rota_volta=rota1_volta,
        status_ida="pending",
        status_volta="pending",
        rotina_onibus=rotina_2,
        company=company,
        modelo_venda="buser",
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_1 = grupo_ida_1.trechoclasse_set.first()
    tc_grupo_volta_1 = grupo_volta_1.trechoclasse_set.first()
    tc_grupo_ida_2 = grupo_ida_2.trechoclasse_set.first()
    tc_grupo_volta_2 = grupo_volta_2.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.forecast_prediction_svc.get_grupo_predictions",
        return_value={grupo_ida_1.id: 40, grupo_volta_1.id: 40, grupo_ida_2.id: 20, grupo_volta_2.id: 20},
    )

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_1.id: {(tc_grupo_ida_1.trecho_vendido_id, tc_grupo_ida_1.grupo_classe.tipo_assento): 40},
            grupo_volta_1.id: {(tc_grupo_volta_1.trecho_vendido_id, tc_grupo_volta_1.grupo_classe.tipo_assento): 40},
            grupo_ida_2.id: {(tc_grupo_ida_2.trecho_vendido_id, tc_grupo_ida_2.grupo_classe.tipo_assento): 20},
            grupo_volta_2.id: {(tc_grupo_volta_2.trecho_vendido_id, tc_grupo_volta_2.grupo_classe.tipo_assento): 20},
        },
    )

    call_command("confirmacao_inteligente_grupos")

    output = cast(dict, ConfirmacaoInteligenteExecutionLogger.objects.all()[0].output)

    # geral
    assert output["solver_status"] == "OPTIMAL"
    assert output["total_grupos"] == 2
    assert output["grupos_cancelados"] == 2
    assert output["total_dummies"] == 20
    assert output["total_pessoas"] == 60
    assert output["occ_media_antes"] == 0.625
    assert output["occ_media_depois"] == 0.75
    assert output["economia_frete"] == Decimal(8000)
    assert output["receita_forecast"] == Decimal(12000)
    assert output["receita_travels_mantidas"] == Decimal(6000)
    # custos
    assert output["custo_frete"] == Decimal(2000)
    assert output["custo_cancelamento"] == Decimal(3200)
    assert output["custo_assento_vazio"] == Decimal(0)
    # cancelar_grupos
    assert len(output["cancelar_grupos"]) == 4
    # remanejamentos e cancelamentos
    assert output["pessoas_canceladas"] == 40
    assert output["pessoas_remanejadas"] == 0
    assert output["total_dummies_cancelados"] == 0
    assert output["total_dummies_remanejados"] == 0
    assert output["receita_dummies_cancelados"] == Decimal(0)
    assert output["receita_pessoas_canceladas"] == Decimal(4000)
    # grupos e grupos classe
    assert list(Grupo.objects.values_list("id", "status").order_by("id")) == [
        (grupo_ida_1.id, Grupo.Status.TRAVEL_CONFIRMED),
        (grupo_volta_1.id, Grupo.Status.TRAVEL_CONFIRMED),
        (grupo_ida_2.id, Grupo.Status.PENDING),
        (grupo_volta_2.id, Grupo.Status.PENDING),
    ]
    assert GrupoClasse.objects.filter(closed=True, closed_reason=ClosedReasons.SOLVER).count() == 2


@time_machine.travel("2025-05-27T12:45:00-03:00")
def test_confirmacao_inteligente_remaneja_grupos_fracos_para_fortes(
    local_rj, local_sp, make_grupo_ida_volta, mocker, strategy_solution
):
    rota_principal = baker.make("core.RotaPrincipal", eixo="RIO-SAO")
    baker.make(
        "optools.ConfirmacaoInteligenteExecucao",
        rotas_principais=[rota_principal],
        estrategia_aplicada=strategy_solution,
    )
    rota_ida, rota_volta = fixtures._mock_rota2(local_rj, local_sp), fixtures._mock_rota2(local_sp, local_rj)
    company_1, company_2 = baker.make("core.Company", _quantity=2, _bulk_create=True)
    rotina_1, rotina_2 = baker.make(
        "core.RotinaOnibus",
        frete_atual=Decimal("4000"),
        solver_threshold=0,
        rota=rota_ida,
        rota_principal=rota_principal,
        company=iter([company_2, company_2]),
        base_operacional=local_rj.cidade,
        _quantity=2,
        _bulk_create=True,
    )
    grupo_ida_forte, grupo_volta_forte = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=49),
        datetime_ida_grupo_volta=now() + timedelta(hours=50),
        classe="semi leito",
        pessoas_ida=39,
        pessoas_volta=39,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina_1,
        company=company_1,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )
    grupo_ida_fraco, grupo_volta_fraco = make_grupo_ida_volta(
        datetime_ida_grupo_ida=now() + timedelta(hours=51),
        datetime_ida_grupo_volta=now() + timedelta(hours=52),
        classe="semi leito",
        pessoas_ida=1,
        pessoas_volta=1,
        capacidade=40,
        max_split_value=Decimal("100"),
        rota_ida=rota_ida,
        rota_volta=rota_volta,
        status_ida=Grupo.Status.PENDING,
        status_volta=Grupo.Status.PENDING,
        rotina_onibus=rotina_2,
        company=company_2,
        modelo_venda=Grupo.ModeloVenda.BUSER,
        is_extra=False,
        cancelamentos_ida=0,
        cancelamentos_volta=0,
        grupo_classe_closed=False,
        accops=True,
    )

    tc_grupo_ida_forte = grupo_ida_forte.trechoclasse_set.first()
    tc_grupo_volta_forte = grupo_volta_forte.trechoclasse_set.first()
    tc_grupo_ida_fraco = grupo_ida_fraco.trechoclasse_set.first()
    tc_grupo_volta_fraco = grupo_volta_fraco.trechoclasse_set.first()

    mocker.patch(
        "core.service.remanejamento.solver.solver_svc.get_cached_trechos_forecast_by_grupo_id_trecho_vendido_tipo_assento",
        return_value={
            grupo_ida_forte.id: {
                (tc_grupo_ida_forte.trecho_vendido_id, tc_grupo_ida_forte.grupo_classe.tipo_assento): 39
            },
            grupo_volta_forte.id: {
                (tc_grupo_volta_forte.trecho_vendido_id, tc_grupo_volta_forte.grupo_classe.tipo_assento): 39
            },
            grupo_ida_fraco.id: {
                (tc_grupo_ida_fraco.trecho_vendido_id, tc_grupo_ida_fraco.grupo_classe.tipo_assento): 2
            },
            grupo_volta_fraco.id: {
                (tc_grupo_volta_fraco.trecho_vendido_id, tc_grupo_volta_fraco.grupo_classe.tipo_assento): 2
            },
        },
    )

    call_command("confirmacao_inteligente_grupos")

    output = cast(dict, ConfirmacaoInteligenteExecutionLogger.objects.all()[0].output)

    # geral
    assert output["solver_status"] == "OPTIMAL"
    assert output["total_grupos"] == 2
    assert output["grupos_cancelados"] == 2
    assert output["total_dummies"] == 2
    assert output["total_pessoas"] == 80
    assert output["occ_media_antes"] == 0.5
    assert output["occ_media_depois"] == 1
    assert output["economia_frete"] == Decimal(4000)
    assert output["receita_forecast"] == Decimal(8200)
    assert output["receita_travels_mantidas"] == Decimal(8000)
    # custos
    assert output["custo_frete"] == Decimal(4000)
    assert output["custo_cancelamento"] == Decimal(0)
    assert output["custo_assento_vazio"] == Decimal(0)
    # cancelar_grupos
    assert len(output["cancelar_grupos"]) == 4
    # remanejamentos e cancelamentos
    assert output["pessoas_canceladas"] == 0
    assert output["pessoas_remanejadas"] == 2
    assert output["total_dummies_cancelados"] == 2
    assert output["total_dummies_remanejados"] == 0
    assert output["receita_dummies_cancelados"] == Decimal(200)
    assert output["receita_pessoas_canceladas"] == Decimal(0)
    # grupos e grupos classe
    assert list(Grupo.objects.values_list("id", "status").order_by("id")) == [
        (grupo_ida_forte.id, Grupo.Status.TRAVEL_CONFIRMED),
        (grupo_volta_forte.id, Grupo.Status.TRAVEL_CONFIRMED),
        (grupo_ida_fraco.id, Grupo.Status.PENDING),
        (grupo_volta_fraco.id, Grupo.Status.PENDING),
    ]
    assert GrupoClasse.objects.filter(closed=True, closed_reason=ClosedReasons.SOLVER).count() == 2
