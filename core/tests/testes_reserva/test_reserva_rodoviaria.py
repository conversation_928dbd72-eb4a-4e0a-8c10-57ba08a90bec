from unittest.mock import Mock

import pytest
import requests
from model_bakery import baker

from core.models_grupo import <PERSON><PERSON><PERSON>, TrechoClasse
from core.service.reserva.reserva_svc import BlockedSeatReserva, bloqueia_poltronas
from core.service.selecao_assento import NotEnoughSeats, SeatSelectionConnectionError
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat


def test_bloqueia_poltronas_sem_poltronas(mocker):
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mock_escolhe_e_bloqueia_poltronas = mocker.patch.object(
        MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas", side_effect=NotEnoughSeats(0, "")
    )
    tc = baker.make(TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE)

    # Create a mock evento that matches the expected interface
    mock_evento = Mock()
    mock_evento.passengers = [{"name": "jose", "id": 456}, {"name": "joao", "id": 457}]
    mock_evento.categoria_especial = ""
    mock_evento.travels = [{"trechoclasse_id": tc.id}]
    mock_evento.trechos = [tc]

    with pytest.raises(NotEnoughSeats):
        bloqueia_poltronas(evento=mock_evento)
    mock_escolhe_e_bloqueia_poltronas.assert_called_once()


def test_bloqueia_poltronas(mocker):
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mock_block = BlockedSeat(poltrona=Assento(numero=1, x=0, y=0, tipo_assento="any"), tempo_limite_bloqueio=None)
    mock_escolhe_e_bloqueia_poltronas = mocker.patch.object(
        MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas", return_value=[mock_block] * 2
    )
    tc = baker.make(TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE)

    # Create a mock evento that matches the expected interface
    mock_evento = Mock()
    mock_evento.passengers = [{"name": "jose", "id": 456}, {"name": "joao", "id": 457}]
    mock_evento.categoria_especial = ""
    mock_evento.travels = [{"trechoclasse_id": tc.id}]
    mock_evento.trechos = [tc]

    poltronas = bloqueia_poltronas(evento=mock_evento)

    assert len(poltronas) == 2
    blocked_seat = poltronas[0]
    assert blocked_seat == BlockedSeatReserva(
        trecho_classe_id=tc.id, blocked_seat=mock_block, pax_key=None, tipo=BlockedSeatReserva.Tipo.AUTOMATICO
    )
    mock_escolhe_e_bloqueia_poltronas.assert_called_once()


@pytest.mark.parametrize("erro", [SeatSelectionConnectionError(), requests.Timeout()])
def test_escolhe_e_bloqueia_poltronas_connection_error(mocker, erro):
    get_layout = mocker.patch.object(
        MarketplaceSeatsController,
        "get_layout_onibus",
        side_effect=erro,
    )
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    controller = MarketplaceSeatsController(
        trecho_classe=baker.make(TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    )
    with pytest.raises(type(erro)):
        controller.escolhe_e_bloqueia_poltronas(quantidade_poltronas=1, categoria_especial="normal")

    get_layout.assert_called_once()


def test_bloqueia_poltronas_todas_poltronas_selecionadas(mocker):
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mock_escolhe_e_bloqueia_poltronas = mocker.patch.object(MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas")
    tc = baker.make(TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE)

    # Create a mock evento that matches the expected interface
    mock_evento = Mock()
    mock_evento.passengers = [{"name": "jose", "id": 456}, {"name": "joao", "id": 457}]
    mock_evento.categoria_especial = ""
    mock_evento.travels = [{"trechoclasse_id": tc.id, "assentos_selecionados": {456: 1, 457: 2}, "trechoclasse": tc}]
    mock_evento.trechos = [tc]

    poltronas = bloqueia_poltronas(evento=mock_evento)

    assert len(poltronas) == 2
    blocked_seat = poltronas[0]
    expected_blocked_seat = BlockedSeat(
        poltrona=Assento(numero=1, x=0, y=0, tipo_assento="any"),
        force_expired=True,
    )
    assert blocked_seat == BlockedSeatReserva(
        trecho_classe_id=tc.id, blocked_seat=expected_blocked_seat, pax_key=456, tipo=BlockedSeatReserva.Tipo.MANUAL
    )
    mock_escolhe_e_bloqueia_poltronas.assert_not_called()
