import json
from decimal import Decimal as D
from http import HTTPStatus
from typing import SupportsFloat
from unittest import mock

from django.core import mail
from django.core.exceptions import ValidationError
from django.test import TestCase

from adapters.mock import mock_stark_adapter_requests as starkmocker
from commons.utils import hashint
from core.models_grupo import Grupo
from core.models_travel import Buseiro, Pagamento, Passageiro
from core.service.pagamento.pagamento_svc import calcula_parcela
from core.service.reserva.reserva_svc import BlockedSeatReserva, _get_poltrona
from core.service.selecao_assento import Assento, BlockedSeat
from core.tests import fixtures


def _mock_verify_signature(body, signature):
    return True


class TestReservaBase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user_ze = fixtures.user_ze()
        cls.b = fixtures.grupos_e_trechos()

    def visualiza_detalhe_viagem(self, client_ze, travel_id, extrato_esperado={}, expectvagas=22):
        r = client_ze.get("/api/travels/%s" % travel_id)
        self.assertEqual(200, r.status_code)
        travel = json.loads(r.content.decode("utf-8"), parse_float=D)
        self.assertTrue("id" in travel)
        self.assertTrue("status" in travel)
        self.assertTrue("vagas" in travel["grupo"])
        self.assertTrue("payment" in travel)
        self.assertEqual(travel["grupo"]["vagas"], expectvagas)
        self.assertIsNotNone(travel["grupo"]["destino"]["mapurl"])
        self.assert_first_dict_in_second(extrato_esperado, travel["extrato"])
        return travel

    def adiciona_contato_travel(self, client, travel_id, phone, error_expected=None):
        r = client.post("/api/travels/add_contact_phone", {"id": travel_id, "phone": phone})
        if not error_expected:
            self.assertEqual(200, r.status_code)
        else:
            res = r.json()
            self.assertEqual(400, r.status_code)
            self.assertEqual(error_expected, res["error"])

    def cancela_viagem(self, client_ze, travel_id, expectcanceled=1, expect_error=False):
        r = client_ze.post("/api/travels/%s/cancel" % travel_id)
        if expect_error:
            self.assertEqual(400, r.status_code)
        else:
            self.assertEqual(200, r.status_code)

    def remove_passageiro(self, client_ze, travel_id=None, pid=None):
        if not pid:
            pid = Passageiro.objects.filter(travel_id=travel_id).values_list("id", flat=True).first()

        r = client_ze.post("/api/removepassenger/%s" % pid)
        self.assertEqual(200, r.status_code)

    def _hash_ids(self, data):
        for g in data["groups"]:
            if "id" in g:
                g["id"] = hashint(g["id"])

    def _hash_ids_conexao(self, data):
        for g in data["groups"]:
            if "id" in g:
                g["id"] = ":".join([hashint(_id) for _id in g["id"].split(":")])

    def purchase(
        self,
        client_ze,
        dgroups,
        payment,
        pay,
        expected_error,
        count_emails=1,
        parcela_count=1,
        net_value=None,
        source=None,
        source_id=None,
        conexao=False,
    ):
        data = json.loads(json.dumps(dgroups))
        if not conexao:
            self._hash_ids(data)
        else:
            self._hash_ids_conexao(data)
        ob = mail.outbox if count_emails is not None else []
        ob.clear()
        r2 = self.execute_purchase_request(client_ze, data, net_value, parcela_count, pay, payment, source, source_id)

        self.assertEqual(200, r2.status_code)
        travels = json.loads(r2.content.decode("utf-8"), parse_float=D)
        if expected_error:
            self.assertTrue(expected_error in travels["error"])
        else:
            if ob:
                self.assertEqual(count_emails, len(ob))

            if isinstance(travels, dict):
                self.assertIsNone(travels.get("error"))
            travel = travels[0]
            self.assertTrue("error" not in travel)
            if payment:
                self.assertIsNotNone(travel["payment"])
                self.assertIsNotNone(travel["payment"]["status"])
                self.assertIsNotNone(travel["payment"]["value"])
                if ob and payment["payment_method"] == "boleto":
                    self.assertIn(ob[0].subject, ["Reserva aguardando pagamento", "Prepare-se para sua viagem"])
            else:
                self.assertIsNone(travel.get("payment"))

            if payment:
                travel_payment_value = D(travels[0]["payment"]["value"])
                self.assertTrue(abs(travel_payment_value - pay) < D("0.01"))
        if ob:
            ob.clear()
        return travels

    def execute_purchase_request(self, client, data, net_value, parcela_count, pay, payment, source, source_id):
        # data['phone'] = '12981440013'
        # data['email'] = 'x@y.z'
        if source:
            data["source"] = source
        if source_id:
            data["source_id"] = source_id
        if payment:
            if net_value is None:
                net_value = pay
            data["payment"] = payment.copy()
            data["payment"]["value"] = pay
            data["payment"]["net_value"] = net_value
            data["payment"]["parcela_count"] = parcela_count

        r2 = client.post("/api/purchase", {"data": json.dumps(data)}, **{"HTTP_client": data.get("client")})
        return r2

    def remarcar(
        self,
        client_ze,
        travel_id,
        grupo,
        payment=None,
        pay=None,
        net_value=None,
        count_emails=1,
        parcela_count=1,
        expecterror=None,
    ):
        if net_value is None:
            net_value = pay

        data = dict(travel_id=travel_id, groups=[dict(id=grupo)])
        if payment:
            payment = payment.copy()
            payment.update(
                value=pay,
                net_value=net_value,
                parcela_count=parcela_count,
            )
            data["payment"] = payment
        try:
            r = client_ze.post("/api/remarcar", {"data": json.dumps(data)})
        except (Exception, ValidationError) as e:
            self.assertEqual(str(e), expecterror)
            return
        self.assertEqual(200, r.status_code)
        travels = r.json(parse_float=D)
        if expecterror:
            self.assertIn(expecterror, travels["error"])
        else:
            if isinstance(travels, dict):
                self.assertIsNone(travels.get("error"))
            travel = travels[0]
            self.assertTrue("error" not in travel)

        return travels

    def estorna(self, client, ids=None, status_code=HTTPStatus.OK, qtd_estornos=2):
        req = client.post("/api/estorno", {"params": json.dumps({"ids": ids})})
        self.assertEqual(status_code, req.status_code)

        if status_code != HTTPStatus.OK:
            return

        res = json.loads(req.content.decode("utf-8"))
        self.assertFalse("error" in res)

    def entra_no_grupo_sem_cpf(self, client_ze, payment, pay=0.0, expecterror=None):
        data = {
            "passengers": _passenger_sem_cpf(),
            "groups": [
                {
                    "id": str(self.b.trecho_classe_bhsp.id),
                }
            ],
            "payment": payment,
        }
        return self.purchase(client_ze, data, payment, pay, expecterror)

    def entra_no_grupo_com_acceptance(
        self, client_ze, group_id, payment, pay=0.0, passengers=None, parcela_count=1, expecterror=False
    ):
        data = {
            "passengers": passengers if passengers is not None else _passengers2(),
            "groups": [
                {
                    "id": str(group_id),
                }
            ],
            "payment": payment,
            "acceptance": True,
        }
        return self.execute_entra_no_grupo_request(client_ze, data, expecterror, parcela_count, pay, payment)

    def entra_no_grupo(
        self, client_ze, group_id, payment, pay=D("0"), passengers=None, parcela_count=1, expecterror=False
    ):
        data = {
            "passengers": passengers if passengers is not None else _passengers2(),
            "groups": [
                {
                    "id": str(group_id),
                }
            ],
            "payment": payment,
        }
        return self.execute_entra_no_grupo_request(client_ze, data, expecterror, parcela_count, pay, payment)

    def execute_entra_no_grupo_request(
        self, client_ze, data, expecterror, parcela_count, pay, payment, provider=Pagamento.Provider.MERCADOPAGO
    ):
        if expecterror:
            return self.purchase(client_ze, data, payment, pay, expecterror, parcela_count=parcela_count)
        value = (
            pay
            if parcela_count == 1
            else calcula_parcela(pay, payment["bank_identification_number"], parcela_count, provider).total_parcelado
        )
        return self.purchase(client_ze, data, payment, value, expecterror, parcela_count=parcela_count, net_value=pay)[
            0
        ]

    def entra_no_grupo_n_pessoas(self, client_ze, payment, pessoas=2, split_value=D("88.00"), pay_value=None):
        passengers = []
        for i in range(pessoas):
            passengers.append(_passenger_com_cpf()[0])
        data = {
            "passengers": passengers,
            "groups": [
                {
                    "id": str(self.b.trecho_classe_bhsp.id),
                }
            ],
            "payment": payment,
        }
        pay = pay_value if pay_value is not None else D(0) if not payment else pessoas * split_value
        return self.purchase(client_ze, data, payment, pay, expected_error=False, count_emails=1)[0]

    def entra_no_grupo_soida(
        self,
        client_ze,
        payment,
        pay: SupportsFloat = 0.0,
        expected_error=None,
        new_cpf=False,
        id=None,
        count_email=1,
        return_multiple_passengers=True,
        passengers=None,
        cupom=None,
        parcela_count=1,
        net_value=None,
        source=None,
        source_id=None,
        user_revenda=None,
        user_revendedor=None,
    ):
        data = {
            "passengers": passengers
            or _passengers2(new_cpf=new_cpf, return_multiple_passengers=return_multiple_passengers),
            "groups": [
                {
                    "id": str(self.b.trecho_classe_bhsp.id if not id else id),
                    "promoCode": cupom,
                }
            ],
            "user_revenda": user_revenda,
            "user_revendedor": user_revendedor,
        }
        res = self.purchase(
            client_ze,
            data,
            payment,
            pay,
            expected_error,
            count_email,
            parcela_count=parcela_count,
            net_value=net_value,
            source=source,
            source_id=source_id,
        )

        return res if expected_error else res[0]

    def entra_no_grupo_conexao(
        self,
        client_ze,
        payment,
        pay=0.0,
        expected_error=None,
        new_cpf=False,
        trechos_classe_id=None,
        count_email=1,
        return_multiple_passengers=True,
        passengers=None,
        parcela_count=1,
        net_value=None,
        source=None,
        source_id=None,
        user_revenda=None,
        user_revendedor=None,
    ):
        data = {
            "passengers": passengers
            or _passengers2(new_cpf=new_cpf, return_multiple_passengers=return_multiple_passengers),
            "groups": [{"id": ":".join(map(str, trechos_classe_id or [])), "promoCode": None}],
            "user_revenda": user_revenda,
            "user_revendedor": user_revendedor,
        }
        res = self.purchase(
            client_ze,
            data,
            payment,
            pay,
            expected_error,
            count_email,
            parcela_count=parcela_count,
            net_value=net_value,
            source=source,
            source_id=source_id,
            conexao=True,
        )
        return res if expected_error else res[0]

    def entra_no_grupo_soida_1_passageiro(
        self, client_ze, payment, pay=0.0, expecterror=None, new_cpf=False, id=None, count_email=1, cupom=None
    ):
        data = {
            "passengers": _passengers1(new_cpf=new_cpf),
            "groups": [{"id": str(self.b.trecho_classe_bhsp.id if not id else id), "promoCode": cupom}],
        }
        res = self.purchase(client_ze, data, payment, pay, expecterror, count_email)
        return res if expecterror else res[0]

    def entra_no_grupo_ida_e_volta_separadas(
        self,
        client_ze,
        payment,
        pay=D(0),
        count_email=1,
        grupo_ida=None,
        grupo_volta=None,
        passengers=None,
        cupom=None,
        expecterror=False,
        parcela_count=1,
        net_value=None,
        cpf_da_promo=None,
    ):
        grupo_ida = self.b.trecho_classe_bhsp.id if grupo_ida is None else grupo_ida
        grupo_volta = self.b.trecho_classe_spbh.id if grupo_volta is None else grupo_volta
        passengers = _passengers2() if passengers is None else passengers
        data = {
            "passengers": passengers,
            "groups": [
                {
                    "id": str(grupo_ida),
                    "promoCode": cupom,
                },
                {
                    "id": str(grupo_volta),
                    "promoCode": cupom,
                },
            ],
        }

        if cpf_da_promo:
            data["cpf_da_promo"] = cpf_da_promo

        return self.purchase(
            client_ze,
            data,
            payment,
            pay,
            expected_error=expecterror,
            count_emails=count_email,
            parcela_count=parcela_count,
            net_value=net_value,
        )

    def assert_meio_pagamento_criado(self, client_ze, created):
        r = client_ze.get("/api/payment_methods")
        self.assertEqual(200, r.status_code)
        cards = r.json()
        self.assertEqual(len(cards) > 0, created)
        if created:
            self.assertTrue("brand" in cards[0])
            self.assertTrue("id" in cards[0])
            self.assertTrue("last_digits" in cards[0])
            return cards[0]["id"]
        return None

    def assert_grupo_e_travel_count(self, client_ze, grupos, travels_prior, travels_next, vagas=22):
        self.assertEqual(grupos, Grupo.objects.all().count())
        r1 = client_ze.get("/api/travels", {"kind": "prior", "only_valid": True})
        r2 = client_ze.get("/api/travels", {"kind": "next", "only_valid": True})
        self.assertEqual(200, r1.status_code)
        self.assertEqual(200, r2.status_code)
        t_prior = r1.json()
        t_next = r2.json()
        self.assertEqual(travels_prior, len(t_prior))
        self.assertEqual(travels_next, len(t_next))
        travels = t_prior + t_next
        if len(travels) > 0:
            travel = travels[0]
            self.assertTrue("id" in travel)
            self.assertTrue("status" in travel)
            self.assertTrue("vagas" in travel["grupo"])
            self.assertEqual(travel["grupo"]["vagas"], vagas)
        return travels

    def assert_travel_limited_count(self, client_ze, limit):
        r = client_ze.get("/api/travels", {"kind": "next", "only_valid": True, "limit": limit})
        self.assertEqual(200, r.status_code)
        t_next = r.json()
        self.assertEqual(limit, len(t_next))

    def assert_count_pessoas(self, count, group_id=None):
        if not group_id:
            group_id = self.b.grupo_classe_bhsp.grupo.id
        grupo = Grupo.objects.get(pk=group_id)
        self.assertEqual(count, grupo.count_pessoas())

    def assert_count_buseiros(self, count):
        buseiros = Buseiro.objects.filter(user=self.user_ze).all()
        self.assertEqual(count, len(buseiros))

    def logout(self, client):
        r = client.post("/api/logout")
        self.assertEqual(200, r.status_code)
        r = client.get("/api/whoami")
        self.assertEqual(200, r.status_code)
        res = r.json()
        self.assertFalse(res["authenticated"])

    def assert_first_dict_in_second(self, d1, d2):
        if isinstance(d1, dict):
            for k in d1:
                self.assert_first_dict_in_second(d1[k], d2[k])
        elif isinstance(d1, list | tuple):
            self.assertEqual(len(d1), len(d2))
            for v1, v2 in zip(d1, d2):
                self.assert_first_dict_in_second(v1, v2)
        else:
            self.assertEqual(d1, d2)

    def _get_and_assert_extrato(self, client, reservation_data, extrato_esperado):
        r2 = client.get("/api/extratoreserva", {"data": json.dumps(reservation_data)})
        self.assertEqual(200, r2.status_code)
        result = json.loads(r2.content.decode("utf-8"), parse_float=D)
        self.assert_first_dict_in_second(extrato_esperado, result)
        self.assertIsNotNone(result["parcelamento_options"])
        self.assertIsNotNone(result.get("extrato_reserva"))
        return result

    def _get_and_assert_extrato_existente(self, client, travel_id, extrato_esperado):
        r2 = client.get(f"/api/travels/{travel_id}")
        self.assertEqual(200, r2.status_code)
        result = json.loads(r2.content.decode("utf-8"), parse_float=D)["extrato"]
        self.assert_first_dict_in_second(extrato_esperado, result)
        return result

    def _get_and_assert_extrato_remarcacao(
        self, client, travel_id, group_id, extrato_esperado, error=None, status_code=200
    ):
        group = dict(id=group_id)
        data = dict(groups=[group], travel_id=travel_id)
        try:
            r = client.get("/api/extratoremarcacao", {"data": json.dumps(data)})
        except Exception as e:
            self.assertEqual(str(e), error)
            return
        self.assertEqual(status_code, r.status_code)
        if status_code == 400:
            return
        result = r.json(parse_float=D)
        if not error:
            self.assert_first_dict_in_second(extrato_esperado, result)
            self.assertIsNotNone(result["parcelamento_options"])
            self.assertIsNotNone(result.get("extrato_cancelamento"))
            self.assertIsNotNone(result.get("extrato"))
        else:
            self.assertEqual(result, dict(error=error, help=["Tente remarcar novamente"]))
        return result

    def _reservation_data(self, ida_e_volta=False, passengers=None, grupo_id=None, cupom=None):
        data = {
            "passengers": passengers or _passengers2(),
            "groups": [
                {
                    "id": grupo_id or str(self.b.trecho_classe_bhsp.id),
                    "promoCode": cupom,
                }
            ],
        }
        if ida_e_volta:
            data["groups"].append(
                {
                    "id": str(self.b.trecho_classe_spbh.id),
                    "promoCode": cupom,
                }
            )

        for g in data["groups"]:
            if "id" in g:
                g["id"] = hashint(g["id"])

        return data

    def _reservation_data_simples(self, group):
        data = {"passengers": _passengers2(), "groups": [{"id": group.id}]}
        for g in data["groups"]:
            if "id" in g:
                g["id"] = hashint(g["id"])
        return data

    @mock.patch("adapters.stark_adapter.verify_signature", _mock_verify_signature)
    def postback_pix_paid(self, m, pagamento):
        starkmocker.mock_get_pix(m, status="paid", amount=17600)
        starkmocker.mock_detalhes_pix(m)
        data = json.dumps(starkmocker.pix_postback(status="paid"))
        headers = {
            "content_type": "application/json",
            "HTTP_DIGITAL_SIGNATURE": "fakesignature",
        }
        r = self.client.post("/api/starkpostback/invoice", data=data, **headers)
        self.assertEqual(200, r.status_code)
        pagamento.refresh_from_db()
        self.assertEqual("paid", pagamento.status)

    @mock.patch("adapters.stark_adapter.verify_signature", _mock_verify_signature)
    def postback_pix_falha_estorno(self, m, pagamento):
        starkmocker.mock_get_pix(m, status="paid", amount=17600)
        data = json.dumps(starkmocker.pix_postback(status="paid"))
        headers = {
            "content_type": "application/json",
            "HTTP_DIGITAL_SIGNATURE": "fakesignature",
        }
        r = self.client.post("/api/starkpostback/invoice", data=data, **headers)
        self.assertEqual(200, r.status_code)
        pagamento.refresh_from_db()
        self.assertEqual("paid", pagamento.status)


def _passengers1(assento=False, new_cpf=False):
    d = {
        "name": "TONY CALLERI FRANCA",
        "cpf": "186.269.630-68" if new_cpf else "858.913.473-34",
        "rg_number": "97002613134",
        "rg_orgao": "SSP/SP",
        "tipo_documento": "RG",
        "birthday": "10/06/1981",
    }
    if assento:
        d["seat"] = {"position": {"y": 0, "x": 0, "z": 0}, "label": "1"}
    return [d]


def _passengers2(assento=False, new_cpf=False, return_multiple_passengers=True):
    d = [
        {
            "name": "Samantha Lobato",
            "rg_number": "12345678",
            "rg_orgao": "SSP/SP",
            "tipo_documento": "RG",
            "cpf": "699.922.830-25" if new_cpf else "96038267077",
            "birthday": "10/10/2018",
        }
    ]
    if assento:
        d[0]["seat"] = {"position": {"y": 0, "x": 1, "z": 0}, "label": "2"}
    return _passengers1(assento=assento, new_cpf=new_cpf) + d if return_multiple_passengers else d


def _passenger_sem_cpf(assento=False):
    d = {
        "name": "TONY CALLERI FRANCA",
        "email": "<EMAIL>",
        "rg_number": "97891261314",
        "birthday": "10/06/1981",
        "phone": "12981440013",
        "tipo_documento": "RG",
    }
    if assento:
        d["seat"] = {"position": {"y": 0, "x": 0, "z": 0}, "label": "1"}
    return [d]


def _passenger_com_cpf(assento=False):
    d = {
        "name": "TONY CALLERI FRANCA",
        "email": "<EMAIL>",
        "rg_number": fixtures.fake.rg(),
        "cpf": fixtures.fake.cpf(),
        "birthday": "10/06/1981",
        "phone": "12981440013",
    }
    if assento:
        d["seat"] = {"position": {"y": 0, "x": 0, "z": 0}, "label": "1"}
    return [d]


def test_get_poltrona():
    blocked_seat = BlockedSeat(poltrona=Assento(x=1, y=1, andar=1, numero=5, tipo_assento="executivo"))

    pax1 = {"id": 1, "rg_number": "12345678"}
    seat_pax1 = BlockedSeatReserva(
        trecho_classe_id=123, pax_key=pax1["rg_number"], tipo=BlockedSeatReserva.Tipo.MANUAL, blocked_seat=blocked_seat
    )

    pax2 = {"id": 2, "rg_number": "145"}
    seat_pax2 = BlockedSeatReserva(
        trecho_classe_id=123, pax_key=None, tipo=BlockedSeatReserva.Tipo.AUTOMATICO, blocked_seat=blocked_seat
    )

    poltronas_bloqueadas = [seat_pax1, seat_pax2]
    # Trecho classe errado, sem pop
    assert _get_poltrona(poltronas_bloqueadas, pax1, 124) is None
    assert len(poltronas_bloqueadas) == 2

    # Popa a primeira, que tem pax selecionado
    assert _get_poltrona(poltronas_bloqueadas, pax1, 123) == seat_pax1
    assert len(poltronas_bloqueadas) == 1

    # Popa a segunda que não tem nenhum pax
    assert _get_poltrona(poltronas_bloqueadas, pax2, 123) == seat_pax2
    assert len(poltronas_bloqueadas) == 0

    # Vazio e retorna None sem erro
    assert _get_poltrona(poltronas_bloqueadas, pax1, 123) is None
