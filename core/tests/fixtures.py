import itertools
import json
import random
import string
from datetime import date, datetime, timedelta
from decimal import Decimal as D
from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from unittest.mock import MagicMock
from urllib.parse import quote

from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import Permission, User
from django.contrib.gis.geos import Point
from django.utils import timezone
from faker import Faker
from model_bakery import baker

from accounting.service import account_operation_svc
from buser import roles
from commons import dateutils, guard
from commons.bunch import Bunch
from commons.dateutils import now, to_default_tz_required, to_tz
from commons.utils import random_code, random_number
from core.constants import TipoDocumento
from core.models_commons import Cidade, GlobalSetting, Lead, Profile, Revendedor, WhatsappTemplate
from core.models_company import Company, InfoOnibus, NotaFiscal, Onibus, OnibusClasse, StatusSefaz
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse
from core.models_rota import (
    CAPACIDADE_MAP,
    Checkpoint,
    LocalEmbarque,
    Rota,
    TrechoImportante,
    TrechoVendido,
    calc_max_split,
)
from core.models_travel import Buseiro, ItemAdicional, Reserva, TravelSurvey
from core.service import device_svc, grupos_svc, multitrecho_svc
from core.service.pagamento import pagamento_svc
from core.service.pagamento.order_info_factory_svc import order_info_factory
from core.service.reserva import reserva_svc
from core.service.reserva.reserva_eventos_svc import ReservaDict
from core.tests import whatsapp_templates
from telemetria.models import ViagemTelemetria

_2days = timedelta(days=2)
fake = Faker(["pt-BR"])


def set_global_setting(key, value):
    try:
        s = GlobalSetting.objects.get(key=key)
    except GlobalSetting.DoesNotExist:
        s = GlobalSetting(key=key)

    s.parsed_value = value
    s.save()


def set_cookie(client, cookie_name, cookie_value):
    cookies = dict(**client.cookies)
    cookies[cookie_name] = quote(json.dumps(cookie_value))
    client.cookies = SimpleCookie(cookies)


def user_ze():
    user = user_factory(
        username="ze",
        first_name="Jose",
        last_name="Silva",
        email="<EMAIL>",
        password="password",
        profile__cpf="57188327068",
        profile__cell_phone="88923282383",
        profile__cell_phone_confirmation_code="123546",
        profile__invitecode=fake.pystr_format("??????", letters=string.ascii_uppercase),
        lead=True,
    )
    device_svc.update_or_create_device(user.profile, "xyz", kind=device_svc.FCM)
    return user


def user_joao():
    return user_factory(
        username="joao",
        first_name="João",
        last_name="Silva",
        email="<EMAIL>",
        password="password",
        profile__cpf="21406070076",
        profile__cell_phone="89932891238",
        profile__cell_phone_confirmation_code="123546",
        profile__invitecode=fake.pystr_format("??????", letters=string.ascii_uppercase),
        lead=True,
    )


def user_factory(
    *,
    roles=(),
    permissions=(),
    profile=False,
    lead=False,
    password=None,  # O password precisa ser convertido em hash.
    **kwargs,
):
    """
    Constrói um usuário simples.

    Atributos do profile são definidos por `profile__*`. Se quiser apenas que
    seja criado profile, use `profile=True`.

    Atributos do lead são definidos por `lead__*`. Se quiser apenas que seja
    criado lead, use `lead=True`.

    Roles e permissions são associadas com o usuário criado.
    """
    user_kwargs = {
        "password": make_password(password),
    }
    profile_kwargs = {}
    lead_kwargs = {}

    for kwarg, kwvalue in kwargs.items():
        if kwarg.startswith("profile__"):
            profile_kwargs[kwarg[9:]] = kwvalue
            profile = True
        elif kwarg.startswith("lead__"):
            lead_kwargs[kwarg[6:]] = kwvalue
            lead = True
        else:
            user_kwargs[kwarg] = kwvalue

    user = baker.make(User, **user_kwargs)

    if profile:
        user.profile = baker.make(Profile, user=user, **profile_kwargs)

    if lead:
        user.lead = baker.make(Lead, user=user, **lead_kwargs)

    for role in roles:
        guard.assign_role(user, role)

    for permission in permissions:
        guard.set_permission(user, permission)

    return user


def user_fulano_sem_senha():
    return user_factory(
        username="fulano",
        first_name="Fulano",
        last_name="Silva",
        email="<EMAIL>",
        password=None,
        profile__cell_phone="89933841238",
        lead=True,
    )


def user_staff(*, roles=(), permissions=(), **kwargs):
    props = {
        "username": "staff",
        "first_name": "Staffanie",
        "last_name": "Silva",
        "email": "<EMAIL>",
        "password": "password",
        "is_staff": True,
        "profile__cell_phone": "119" + random_number(8),
    }
    props.update(kwargs)
    return user_factory(
        roles=["staff"] + list(roles),
        permissions=permissions,
        **props,
    )


def user_admin():
    return user_factory(
        roles=["Dev"],
        username="admin",
        first_name="Administrador",
        last_name="Fodão",
        email="<EMAIL>",
        password="password",
        is_superuser=True,
        is_staff=True,
        profile__cell_phone="119" + random_number(8),
    )


def user_accountant():
    return user_factory(
        roles=("financial", roles.CAN_GIVE_REAIS),
        username="accountant",
        first_name="Contadonilson",
        last_name="Novofidocontado",
        email="<EMAIL>",
        password="password",
    )


def user_ops():
    return user_factory(
        roles=["ops"],
        username="ops",
        first_name="Opswaldo",
        last_name="Zoelho",
        email="<EMAIL>",
        password="password",
    )


def user_revendedor():
    revendedor = user_factory(
        roles=["Revendedor"],
        username="revendedor",
        first_name="Revendildo",
        last_name="Silva",
        email="<EMAIL>",
        password="password",
        profile__cell_phone="119" + random_number(8),
        profile__cpf=fake.cpf(),
    )
    Revendedor.objects.create(user=revendedor, active=True)
    return revendedor


def user_suporte(lider=False, can_give_reais=False, can_concluir_grupos=False):
    username = "suporteLider" if lider else "suporte"
    user_roles = [
        "staff",
        "SuporteLider" if lider else "Suporte",
    ]
    if can_give_reais:
        user_roles.append(roles.CAN_GIVE_REAIS)
    if can_concluir_grupos:
        user_roles.append(roles.CAN_CONCLUIR_GRUPOS)

    return user_factory(
        roles=user_roles,
        username=username,
        first_name="Filvs",
        last_name="Boss",
        email="<EMAIL>",
        password="password",
    )


def user_rotas():
    return user_factory(
        roles=["Rotas"],
        username="rotas",
        first_name="Ricardo",
        last_name="Barrosito",
        email="<EMAIL>",
        password="password",
    )


def user_infra():
    return user_factory(
        roles=["Infra"],
        username="Infra",
        first_name="Matheus",
        last_name="Baraduas",
        email="<EMAIL>",
        password="password",
    )


def user_comercial():
    return user_factory(
        roles=["Comercial"],
        username="comercial",
        first_name="Sabados",
        last_name="Baratissimo",
        email="<EMAIL>",
        password="password",
    )


def user_qualidade():
    return user_factory(
        roles=["Qualidade"],
        username="qualidade",
        first_name="Jesska",
        last_name="Ramires",
        email="<EMAIL>",
        password="password",
    )


def user_seguranca():
    return user_factory(
        roles=["Seguranca"],
        username="seguranca",
        first_name="Rodrigones",
        last_name="Zousa",
        email="<EMAIL>",
        password="password",
    )


def user_marketing():
    return user_factory(
        roles=["Marketing"],
        username="marketing",
        first_name="marke",
        last_name="ting",
        email="<EMAIL>",
        password="password",
    )


def user_escritor():
    return user_factory(
        roles=["Escritor"],
        username="escritor",
        first_name="Machado",
        last_name="de Assis",
        email="<EMAIL>",
        password="password",
    )


def user_driver(company=None):
    driver = user_factory(
        roles=["driver"],
        username="driver",
        first_name="Drivenildo",
        last_name="Silva",
        email="<EMAIL>",
        password="password",
        is_staff=False,
        profile__company=company,
        profile__cpf="00000000191",
        profile__driver_docs_status="aprovado",
        profile__cell_phone="1111111111",
        profile__cnh_expire_date=dateutils.now() + timedelta(days=30),
        profile__curso_expire_date=dateutils.now() + timedelta(days=30),
        motoristaaprovacao_set__status="aprovado",
    )
    return driver


def user_driver2(company=None):
    driver = user_factory(
        roles=["driver"],
        username="driver2",
        first_name="Dominic",
        last_name="Toreto",
        email="<EMAIL>",
        password="password",
        is_staff=False,
        profile__company=company,
        profile__cpf="00000000272",
        profile__driver_docs_status="aprovado",
        profile__cell_phone="2222222222",
        profile__cnh_expire_date=dateutils.now() + timedelta(days=30),
        profile__curso_expire_date=dateutils.now() + timedelta(days=30),
        motoristaaprovacao_set__status="aprovado",
    )
    return driver


def user_empresario(company, permissions=(), username="empresario"):
    return user_factory(
        roles=["company"],
        permissions=permissions,
        username=username,
        first_name="Senhor",
        last_name="Empresario",
        email="<EMAIL>",
        password="password",
        profile__company=company,
        profile__fullname="Senhor Empresario",
    )


def user_coupon_creator():
    user = user_factory(
        username="coupon_creator",
        first_name="Paul",
        last_name="Haver",
        email="<EMAIL>",
        password="password",
        roles=("staff",),
    )
    # Essa permission não faz parte do django-role-permissions.
    permission = Permission.objects.get(codename="criar_cupom")
    user.user_permissions.add(permission)
    return user


def user_dev():
    return user_factory(
        roles=["Dev"],
        username="zuck",
        first_name="Mark",
        last_name="Zuckerberg",
        email="<EMAIL>",
        password="password",
    )


def motivo_alteracao():
    return baker.make("core.MotivoAlteracao")


def onibus(
    company=None,
    name="Azul Cometinha",
    placa=None,
    capacidade_leito_cama=24,
    capacidade_leito=0,
    info=True,
    infoonibus_status="pendente",
    ano=2020,
    create_classes=True,
    **kwargs,
):
    if not placa:
        placa = "".join(random.choice(string.ascii_uppercase + string.digits) for _ in range(7))
    if not company:
        company = baker.make("core.company")
    o = Onibus.objects.create(
        company=company,
        name=name,
        placa=placa,
        ano=ano,
        numero_eixos=4,
        ano_fabricacao=2020,
        tipo="DD",
        **kwargs,
    )
    if create_classes:
        OnibusClasse.objects.bulk_create(
            (
                OnibusClasse(
                    tipo="leito cama",
                    capacidade=capacidade_leito_cama,
                    capacidade_vendida=capacidade_leito_cama,
                    onibus_id=o.id,
                ),
                OnibusClasse(
                    tipo="leito",
                    capacidade=capacidade_leito,
                    capacidade_vendida=capacidade_leito,
                    onibus_id=o.id,
                ),
            )
        )
    if info:
        create_infoonibus(o, infoonibus_status)
    return o


def create_infoonibus(onibus, infoonibus_status="pendente"):
    for tipo_info in InfoOnibus.get_types():
        baker.make(
            InfoOnibus,
            onibus=onibus,
            tipo=tipo_info,
            status=infoonibus_status,
            data_vencimento=now() + timedelta(days=30),
        )


def onibus_executivo(company):
    o = Onibus(company=company, name="Cometinha reserva", placa="BUS9999", ano=2020, tipo="LD")
    o.save()
    for type_choice, _ in InfoOnibus.INFO_TYPE_CHOICES:
        baker.make(
            InfoOnibus, tipo=type_choice, onibus=o, status="pendente", data_vencimento=now() + timedelta(days=30)
        )

    OnibusClasse.objects.create(tipo="executivo", capacidade=24, capacidade_vendida=24, onibus=o)
    return o


def onibus_leito(company):
    o = Onibus(company=company, name="Cometinha", placa="BUS7777", ano=2020, tipo="DD")
    o.save()
    for type_choice, _ in InfoOnibus.INFO_TYPE_CHOICES:
        baker.make(
            InfoOnibus, tipo=type_choice, onibus=o, status="pendente", data_vencimento=now() + timedelta(days=30)
        )

    OnibusClasse.objects.create(tipo="leito", capacidade=24, capacidade_vendida=24, onibus=o)
    return o


def dar_reais(user, fromuser, value, reason=None):
    if reason is None:
        reason = "reais pelo admin"
    account_operation_svc.create_account_operation(
        user=user,
        fromuser=fromuser,
        source="BONUS_REAIS",
        value=value,
        value_real=value,
        reason=reason,
    )


def cidades(b=None):
    b = b or Bunch()
    b.cidade_bh = baker.make(
        Cidade,
        name="Belo Horizonte",
        sigla="BHZ",
        uf="MG",
        city_code_ibge="3106200",
        uf_code_ibge="31",
        slug="belo-horizonte-mg",
    )
    b.cidade_sp = baker.make(
        Cidade,
        name="São Paulo",
        sigla="SAO",
        uf="SP",
        city_code_ibge="3550308",
        uf_code_ibge="35",
        slug="sao-paulo-sp",
    )
    b.cidade_ipatinga = baker.make(
        Cidade,
        name="Ipatinga",
        sigla="IPA",
        uf="MG",
        city_code_ibge="3131307",
        uf_code_ibge="31",
        slug="ipating-mg",
    )
    b.cidade_montes = baker.make(
        Cidade,
        name="Montes Claros",
        sigla="MCO",
        uf="MG",
        city_code_ibge="3143302",
        uf_code_ibge="31",
        slug="montes-claros-mg",
    )
    b.cidade_uberlandia = baker.make(
        Cidade,
        name="Uberlandia",
        sigla="UBR",
        uf="MG",
        city_code_ibge="3170206",
        uf_code_ibge="31",
        slug="uberlandia-mg",
    )
    b.cidade_campogrande = baker.make(
        Cidade,
        name="Campo Grande",
        sigla="CGR",
        uf="MS",
        uf_code_ibge="50",
        timezone="America/Campo_Grande",
        city_code_ibge="5002704",
        slug="campo-grande-ms",
    )
    b.cidade_curitiba = baker.make(
        Cidade,
        name="Curitiba",
        sigla="CWB",
        uf="PR",
        city_code_ibge="4106902",
        uf_code_ibge="41",
        slug="curitiba-pr",
    )
    b.cidade_floripa = baker.make(
        Cidade,
        name="Florianópolis",
        sigla="FLN",
        uf="SC",
        city_code_ibge="4205407",
        uf_code_ibge="41",
        slug="floripa-sc",
    )
    b.cidade_mongagua = baker.make(
        Cidade,
        name="Mongaguá",
        sigla="MO",
        uf="SP",
        city_code_ibge="3531100",
        uf_code_ibge="35",
        slug="mongagua-sp",
    )
    return b


def locais(b=None):
    b = b or Bunch()
    b.local_sp_se = baker.make(
        LocalEmbarque,
        endereco="Praça da Sé - Sé",
        endereco_slug="praca-da-se",
        cidade=b.cidade_sp,
        nickname="Praça da Sé",
        nickname_slug="nick-praca-da-se",
        endereco_bairro="Sé",
        bairro_slug="se",
        endereco_logradouro="Praça da Sé",
        latitude=-23.5486,
        longitude=-46.6392,
        ativo=True,
        slug="nick-praca-da-se-sao-paulo-sp",
    )

    b.local_sp_voluntarios = baker.make(
        LocalEmbarque,
        endereco="Voluntarios da patria 344",
        endereco_slug="voluntarios-patria",
        cidade=b.cidade_sp,
        nickname="Voluntarios da patria",
        nickname_slug="nick-voluntarios-da-patria",
        endereco_bairro="Santana",
        bairro_slug="santana",
        endereco_logradouro="Voluntarios da patria",
        latitude=-23.765486,
        longitude=-46.13464,
        ativo=True,
        slug="nick-voluntarios-da-patria-sao-paulo-sp",
    )

    b.local_mc_montesshopping = baker.make(
        LocalEmbarque,
        endereco="Av. Donato Quintino, 90 - Cidade Nova",
        cidade=b.cidade_montes,
        nickname="Montes Claros Shopping",
        nickname_slug="nick-montes-claros-shopping",
        endereco_bairro="Cidade Nova",
        bairro_slug="cidade-nova",
        endereco_logradouro="Av. Donato Quintino",
        endereco_numero=90,
        latitude=-16.742289,
        longitude=-43.870628,
        ativo=True,
        slug="nick-montes-claros-shopping-montes-claros-mg",
    )

    b.local_bh_pampulha = baker.make(
        LocalEmbarque,
        endereco="Rua do Pampulha Center, 123 - Pampulha",
        cidade=b.cidade_bh,
        nickname="Pampulha Center",
        nickname_slug="nick-pampulha-center",
        endereco_bairro="Pampulha",
        bairro_slug="pampulha",
        endereco_slug="rua-do-pampulha-center",
        endereco_logradouro="Rua do Pampulha Center",
        endereco_numero=123,
        latitude=-23.2754481,
        longitude=-46.5856553,
        ativo=True,
        slug="nick-pampulha-center-belo-horizonte-mg",
    )

    b.local_bh_lagoapatos = baker.make(
        LocalEmbarque,
        endereco="Lagoa dos patos",
        endereco_slug="lagoa-dos-patos",
        cidade=b.cidade_bh,
        nickname="Lagoa dos patos",
        nickname_slug="nick-lagoa-dos-patos",
        endereco_bairro="Tio Patinhas",
        bairro_slug="tio-patinhas",
        latitude=-19.826175,
        longitude=-43.986793,
        ativo=True,
        slug="nick-lagoa-dos-patos-belo-horizonte-mg",
    )

    b.local_bh_lagoapatos2 = baker.make(
        LocalEmbarque,
        endereco="Lagoa dos patos 2",
        endereco_slug="lagoa-dos-patos-2",
        cidade=b.cidade_bh,
        nickname="Lagoa dos patos 2",
        nickname_slug="lagoa-dos-patos-2",
        endereco_bairro="Tio Patinhas",
        bairro_slug="tio-patinhas",
        latitude=-19.826175,
        longitude=-44.0,
        ativo=True,
        slug="lagoa-dos-patos-2-belo-horizonte-mg",
    )

    b.local_ip_pqipanema = baker.make(
        LocalEmbarque,
        endereco="Rua do Pq. Ipanema, 1 - Bairro Pq. Ipanema",
        cidade=b.cidade_ipatinga,
        nickname="Parque Ipanema",
        nickname_slug="parque-ipanema",
        endereco_bairro="Bairro Pq. Ipanema",
        bairro_slug="bairro-pq-ipanema",
        endereco_logradouro="Rua do Pq. Ipanema",
        endereco_slug="parque-ipanema",
        endereco_numero=1,
        latitude=-19.470134,
        longitude=-42.542027,
        ativo=True,
        slug="parque-ipanema-ipating-mg",
    )

    b.local_ip_pqciencia = baker.make(
        LocalEmbarque,
        endereco="Rua Pq. Ciencia, 100 - Veneza",
        endereco_slug="parque-da-ciencia",
        cidade=b.cidade_ipatinga,
        nickname="Parque da Ciência",
        nickname_slug="parque-da-ciencia",
        endereco_bairro="Veneza",
        bairro_slug="veneza",
        endereco_logradouro="Rua Pq. Ciencia",
        endereco_numero=100,
        latitude=-19.4683191,
        longitude=-42.5392051,
        ativo=True,
        slug="parque-da-ciencia-ipating-mg",
    )
    b.local_ub_uberpoint = baker.make(
        LocalEmbarque,
        endereco="Av. Uber Point, 10 - Centro",
        endereco_slug="uber-point",
        cidade=b.cidade_uberlandia,
        nickname="Uber Point",
        nickname_slug="uber-point",
        endereco_bairro="Centro",
        bairro_slug="centro",
        endereco_logradouro="Av. Uber Point",
        endereco_numero=10,
        latitude=-18.470134,
        longitude=-43.542027,
        ativo=True,
        slug="uber-point-uberlandia-mg",
    )

    b.local_cwb_castelo = baker.make(
        LocalEmbarque,
        endereco="Pç. Castelo de Vidro - Jardim Botânico",
        endereco_slug="castelo-de-vidro",
        cidade=b.cidade_curitiba,
        nickname="Castelo de Vidro",
        nickname_slug="castelo-de-vidro",
        endereco_bairro="Jardim Botânico",
        bairro_slug="jardim-botanico",
        endereco_logradouro="Pç. Castelo de Vidro",
        latitude=-17.470134,
        longitude=-42.542027,
        ativo=True,
        slug="castelo-de-vidro-curitiba-pr",
    )

    b.local_fln_aero = baker.make(
        LocalEmbarque,
        endereco="Aeroporto de Floripa",
        endereco_slug="aeroporto-de-floripa",
        cidade=b.cidade_floripa,
        nickname="Aero Floripa",
        nickname_slug="aeroporto-florianopolis",
        endereco_bairro="ilha",
        bairro_slug="se",
        endereco_logradouro="Aeroporto Hercilio Luz",
        latitude=-27.593500,
        longitude=-48.558540,
        slug="aeroporto-de-floripa-floripa-sc",
    )
    return b


def grupos_e_trechos(
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    confirming_probability="very_low",
):
    dtbase = to_default_tz_required(dtbase)
    b = Bunch()
    cidades(b)
    locais(b)

    b.rota_bhsp = _mock_rota2(b.local_bh_lagoapatos, b.local_sp_se)
    b.rotina_bhsp = baker.make("core.RotinaOnibus", nome="BHZ-SAO", rota=b.rota_bhsp)
    b.rota_spbh = _mock_rota2(b.local_sp_se, b.local_bh_lagoapatos)
    b.rotina_spbh = baker.make("core.RotinaOnibus", nome="SAO-BHZ", rota=b.rota_spbh)
    b.rota_bhipa = _mock_rota2(b.local_bh_lagoapatos, b.local_ip_pqipanema)
    b.rotina_bhipa = baker.make("core.RotinaOnibus", nome="BHZ-IPA", rota=b.rota_bhipa)
    b.rota_ipau = _mock_rota2(
        b.local_ip_pqipanema,
        b.local_ub_uberpoint,
        tvargs=dict(preco_rodoviaria=D("100.00")),
    )
    b.rotina_ipau = baker.make("core.RotinaOnibus", nome="IPA-UDI", rota=b.rota_ipau)
    b.rota_uipa = _mock_rota2(
        b.local_ub_uberpoint,
        b.local_ip_pqipanema,
        tvargs=dict(preco_rodoviaria=D("120.00")),
    )
    b.rotina_uipa = baker.make("core.RotinaOnibus", nome="UDI-IPA", rota=b.rota_uipa)
    b.grupo_classe_bhsp, b.trecho_classe_bhsp = grupo_classe_bhsp(
        b, dtbase, pessoasnogrupo, status, confirming_probability
    )
    return b


def rota_bhmoc(b):
    return _mock_rota2(b.local_bh_lagoapatos, b.local_mc_montesshopping)


def grupo_classe_bhmoc(b, dtbase=None, pessoasnogrupo=0, status="pending", confirming_probability="very_low"):
    if dtbase is None:
        dtbase = now() + timedelta(days=12)
    rota = rota_bhmoc(b)
    rotina = baker.make("core.RotinaOnibus", nome="BHZ-MCO", rota=rota)
    grupo_classe_bhmoc, trecho_classe_bhmoc = _mock_grupo_classe(
        rota=rota,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
        status=status,
        confirming_probability=confirming_probability,
        rotina=rotina,
    )
    return grupo_classe_bhmoc, trecho_classe_bhmoc


def grupo_classe_bhsp(b, dtbase=None, pessoasnogrupo=0, status="pending", confirming_probability="very_low"):
    if dtbase is None:
        dtbase = now() + timedelta(days=12)
    grupo_classe_bhsp, trecho_classe_bhsp = _mock_grupo_classe(
        rota=b.rota_bhsp,
        rotina=b.rotina_bhsp,
        datetime_ida=to_default_tz_required(dtbase),
        status=status,
        closed=False,
        pessoas=pessoasnogrupo,
        confirming_probability=confirming_probability,
    )
    trecho_classe_bhsp.duracao_ida = timedelta(hours=8, minutes=30)
    trecho_classe_bhsp.save(update_fields=["duracao_ida"])
    return grupo_classe_bhsp, trecho_classe_bhsp


def grupos_e_trechos_artesp(
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    confirming_probability="very_low",
):
    b = grupos_e_trechos(pessoasnogrupo, dtbase, status, confirming_probability)
    b.cidade_sjc = baker.make(
        Cidade,
        name="São José dos Campos",
        sigla="SJK",
        uf="SP",
        city_code_ibge="3549904",
        uf_code_ibge="35",
    )
    b.local_sjc = baker.make(
        LocalEmbarque,
        endereco="D Pascoal - Dutra",
        endereco_slug="d-pascoal-dutra",
        cidade=b.cidade_sjc,
        latitude=-23.2313809,
        longitude=-45.9140568,
    )
    b.rota_sjcsp = _mock_rota2(b.local_sjc, b.local_sp_se)
    b.grupo_classe_sjcsp, b.trecho_classe_sjcsp = _mock_grupo_classe(
        rota=b.rota_sjcsp,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
        status=status,
    )
    return b


def trechos_importantes(b=Bunch()):
    b.cidade_importante_x = baker.make(
        Cidade,
        name="Cidade X",
        sigla="CX1",
        uf="MG",
        city_code_ibge="9001",
        uf_code_ibge="31",
    )
    b.cidade_importante_y = baker.make(
        Cidade,
        name="Cidade Y",
        sigla="CY1",
        uf="MG",
        city_code_ibge="9002",
        uf_code_ibge="31",
    )
    b.cidade_importante_z = baker.make(
        Cidade,
        name="Cidade Z",
        sigla="CZ1",
        uf="MG",
        city_code_ibge="9003",
        uf_code_ibge="31",
    )
    b.trecho_importante_bh_sp = baker.make(TrechoImportante, origem=b.cidade_bh, destino=b.cidade_sp)
    b.trecho_importante_sp_bh = baker.make(TrechoImportante, origem=b.cidade_sp, destino=b.cidade_bh)
    b.trecho_importante_x_y = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_x,
        destino=b.cidade_importante_y,
    )
    b.trecho_importante_x_z = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_x,
        destino=b.cidade_importante_z,
    )
    b.trecho_importante_y_z = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_y,
        destino=b.cidade_importante_z,
    )
    b.trecho_importante_y_x = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_y,
        destino=b.cidade_importante_x,
    )
    b.trecho_importante_z_y = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_z,
        destino=b.cidade_importante_y,
    )
    b.trecho_importante_z_x = baker.make(
        TrechoImportante,
        origem=b.cidade_importante_z,
        destino=b.cidade_importante_x,
    )


def rota_campo_grande_bh(b):
    b.local_campo_grande = baker.make(
        LocalEmbarque,
        endereco="Praça Ary Coelho",
        endereco_slug="praca-ary-coelho",
        cidade=b.cidade_campogrande,
        latitude=-20.463395,
        longitude=-54.616021,
    )
    b.rota_cgbh = _mock_rota2(b.local_campo_grande, b.local_bh_pampulha)


def rota_fln_aero_sp_se(b, ida_e_volta=False):
    b.rota_fln_aero_sp_se = _mock_rota2(b.local_fln_aero, b.local_sp_se)
    if ida_e_volta:
        b.rota_sp_se_fln_aero = _mock_rota2(b.local_sp_se, b.local_fln_aero)


def grupoclasses_saindo_de_ipatinga(b, pessoasnogrupo=0, dtbase=now() + timedelta(days=12), status="pending"):
    b.grupo_classe_ipau, b.trecho_classe_ipau = _mock_grupo_classe(
        rota=b.rota_ipau,
        datetime_ida=to_default_tz_required(dtbase),
        status=status,
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_uipa, b.trecho_classe_uipa = _mock_grupo_classe(
        rota=b.rota_uipa,
        rotina=b.rotina_uipa,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
        status=status,
    )


def rota3(b):
    rota = baker.make(
        Rota,
        ativo=True,
        origem=b.local_bh_pampulha,
        destino=b.local_ip_pqipanema,
        distancia_total=176,
        duracao_total=timedelta(minutes=210),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_bh_pampulha,
        idx=0,
        distancia_km=None,
        duracao=timedelta(minutes=0),
        tempo_embarque=timedelta(minutes=0),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_mc_montesshopping,
        idx=1,
        distancia_km=111,
        duracao=timedelta(minutes=120),
        tempo_embarque=timedelta(minutes=20),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_ip_pqipanema,
        idx=2,
        distancia_km=65,
        duracao=timedelta(minutes=70),
        tempo_embarque=timedelta(minutes=0),
    )
    b.trecho_vendido_rota3_bhipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_bh_pampulha,
        destino=b.local_ip_pqipanema,
        preco_rodoviaria=D("100.00"),
    )
    b.trecho_vendido_rota3_mcipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_mc_montesshopping,
        destino=b.local_ip_pqipanema,
        preco_rodoviaria=D("90.00"),
    )
    b.rota3 = rota
    return rota


def rota4(b):
    rota = baker.make(
        Rota,
        ativo=True,
        origem=b.local_bh_lagoapatos,
        destino=b.local_ip_pqciencia,
        distancia_total=176,
        duracao_total=timedelta(minutes=210),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_bh_lagoapatos,
        idx=0,
        distancia_km=None,
        duracao=None,
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_mc_montesshopping,
        idx=1,
        distancia_km=111,
        duracao=timedelta(minutes=120),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_ip_pqciencia,
        idx=2,
        distancia_km=65,
        duracao=timedelta(minutes=70),
    )
    b.trecho_vendido_rota4_bhipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_bh_lagoapatos,
        destino=b.local_ip_pqciencia,
        preco_rodoviaria=D("100.00"),
    )
    b.trecho_vendido_rota4_mcipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_mc_montesshopping,
        destino=b.local_ip_pqciencia,
        preco_rodoviaria=D("90.00"),
    )
    b.rota4 = rota
    return rota


def rota5(b):
    rota = baker.make(
        Rota,
        ativo=True,
        origem=b.local_bh_pampulha,
        destino=b.local_ip_pqciencia,
        distancia_total=176,
        duracao_total=timedelta(minutes=210),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_bh_pampulha,
        idx=0,
        distancia_km=None,
        duracao=None,
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_mc_montesshopping,
        idx=1,
        distancia_km=111,
        duracao=timedelta(minutes=120),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_ip_pqciencia,
        idx=2,
        distancia_km=65,
        duracao=timedelta(minutes=70),
    )
    b.trecho_vendido_rota5_bhipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_bh_pampulha,
        destino=b.local_ip_pqciencia,
        preco_rodoviaria=D("100.00"),
    )
    b.trecho_vendido_rota5_mcipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_mc_montesshopping,
        destino=b.local_ip_pqciencia,
        preco_rodoviaria=D("90.00"),
    )
    b.rota5 = rota
    return rota


def rota_bh_bh_ipa(b):
    rota = baker.make(
        Rota,
        ativo=True,
        ufs_intermediarios="GO",
        origem=b.local_bh_lagoapatos2,
        destino=b.local_ip_pqipanema,
        distancia_total=1001,
        duracao_total=timedelta(minutes=75),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_bh_lagoapatos2,
        idx=0,
        distancia_km=0,
        duracao=timedelta(minutes=0),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_bh_lagoapatos,
        idx=1,
        distancia_km=1,
        duracao=timedelta(minutes=5),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=b.local_ip_pqipanema,
        idx=2,
        distancia_km=1000,
        duracao=timedelta(hours=8, minutes=30),
    )
    b.trecho_vendido_rota_bh_bh_ipa_trecho_bhipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_bh_lagoapatos2,
        destino=b.local_ip_pqipanema,
    )
    b.trecho_vendido_rota_bh_bh_ipa_trecho_bh2ipa = baker.make(
        TrechoVendido,
        rota=rota,
        origem=b.local_bh_lagoapatos,
        destino=b.local_ip_pqipanema,
    )

    b.rota_bh_bh_ipa = rota
    return rota


def grupo_classe_bhipa(b, pessoasnogrupo=0, dtbase=now() + timedelta(days=12), status="pending"):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_bhipa, b.trecho_classe_bhipa = _mock_grupo_classe(
        rota=b.rota_bhipa,
        rotina=b.rotina_bhipa,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_bhipa.grupo.status = status
    b.grupo_classe_bhipa.grupo.save(update_fields=["status"])
    b.trecho_classe_bhipa.duracao_ida = timedelta(hours=8, minutes=30)
    b.trecho_classe_bhipa.save(update_fields=["duracao_ida"])
    return b


def grupo_classe_spcwb(
    rota_spcwb,
    pessoasnogrupo=0,
    dtbase=None,
    status="pending",
    capacidade=None,
    marketplace=False,
    rotina=None,
):
    if dtbase is None:
        dtbase = now() + timedelta(days=12)

    dtbase = to_default_tz_required(dtbase)
    return _mock_grupo_classe(
        rota=rota_spcwb,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
        capacidade=capacidade,
        marketplace=marketplace,
        rotina=rotina,
    )


def grupo_classe_cwbsp(rota_cwbsp, dtbase=None, rotina=None):
    dtbase = now() + timedelta(days=12)
    return _mock_grupo_classe(rota=rota_cwbsp, datetime_ida=dtbase, rotina=rotina)


def grupo_classe_bhipa_rota3(b, pessoasnogrupo=0, dtbase=now() + timedelta(days=12), status="pending"):
    (
        b.grupo_classe_bhipa_rota3,
        b.trecho_classe_bhipa_rota3,
    ) = _mock_grupo_classe(
        rota=b.rota3,
        rotina=b.rotina_bhipa,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_bhipa.grupo.status = status
    b.grupo_classe_bhipa.grupo.save()
    return b


def grupo_classe_spbh(b, pessoasnogrupo=0, dtbase=now() + timedelta(days=12), status="pending", capacidade=None):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_spbh, b.trecho_classe_spbh = _mock_grupo_classe(
        rota=b.rota_spbh,
        status=status,
        datetime_ida=to_default_tz_required(dtbase + timedelta(minutes=2)),
        closed=False,
        pessoas=pessoasnogrupo,
        capacidade=capacidade,
    )
    b.grupo_classe_spbh.grupo.status = status
    b.grupo_classe_spbh.grupo.save()
    return b


def grupo_classe_bhsp2(b, pessoasnogrupo=0, dtbase=now(), status="pending"):
    b.grupo_classe_bhsp2, b.trecho_classe_bhsp2 = _mock_grupo_classe(
        rota=b.rota_bhsp,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_bhsp2.grupo.status = status
    b.grupo_classe_bhsp2.grupo.save()
    return b


def grupo_classe_cgbh(b, pessoasnogrupo=0, dtbase=now() + timedelta(days=12), status="pending"):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_cgbh, b.trecho_classe_cgbh = _mock_grupo_classe(
        rota=b.rota_cgbh,
        status=status,
        datetime_ida=to_tz(dtbase, "America/Campo_Grande"),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_cgbh.grupo.status = status
    b.grupo_classe_cgbh.grupo.save()
    return b


def grupo_classe_ipamco(
    b,
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    rotina=None,
):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_ipamco, b.trecho_classe_ipamco = _mock_grupo_classe(
        rota=b.rota_ipamco,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_ipamco.grupo.status = status
    b.grupo_classe_ipamco.grupo.rotina_onibus = rotina
    b.grupo_classe_ipamco.grupo.company = b.company_granero
    b.grupo_classe_ipamco.grupo.save()
    return b


def grupo_classe_mcoube(
    b,
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    rotina=None,
):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_mcoube, b.trecho_classe_mcoube = _mock_grupo_classe(
        rota=b.rota_mcoube,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_mcoube.grupo.status = status
    b.grupo_classe_mcoube.grupo.rotina_onibus = rotina
    b.grupo_classe_mcoube.grupo.company = b.company_granero
    b.grupo_classe_mcoube.grupo.save()
    return b


def grupo_classe_ubemco(
    b,
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    rotina=None,
):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_ubemco, b.trecho_classe_ubemco = _mock_grupo_classe(
        rota=b.rota_ubemco,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_ubemco.grupo.status = status
    b.grupo_classe_ubemco.grupo.rotina_onibus = rotina
    b.grupo_classe_ubemco.grupo.company = b.company_granero
    b.grupo_classe_ubemco.grupo.save()
    return b


def grupo_classe_mcoipa(
    b,
    pessoasnogrupo=0,
    dtbase=now() + timedelta(days=12),
    status="pending",
    rotina=None,
):
    dtbase = to_default_tz_required(dtbase)
    b.grupo_classe_mcoipa, b.trecho_classe_mcoipa = _mock_grupo_classe(
        rota=b.rota_mcoipa,
        status=status,
        datetime_ida=to_default_tz_required(dtbase),
        closed=False,
        pessoas=pessoasnogrupo,
    )
    b.grupo_classe_mcoipa.grupo.status = status
    b.grupo_classe_mcoipa.grupo.rotina_onibus = rotina
    b.grupo_classe_mcoipa.grupo.company = b.company_granero
    b.grupo_classe_mcoipa.grupo.save()
    return b


def viagens(b, trecho_classe=None, method="boleto", provider="mercadopago"):
    if not trecho_classe:
        trecho_classe = b.trecho_classe_bhsp

    cpfs = [
        "097.626.200-20",
        "401.562.530-70",
        "393.062.860-07",
        "101.734.580-50",
        "259.945.280-56",
        "795.138.650-29",
    ]
    users = create_random_users(4)
    b.users = users
    b.travels = []
    for idx, user in enumerate(users):
        travels = reserva(user, trecho_classe, cpf=cpfs[idx], method=method, provider=provider)
        b.travels.extend(travels)
    return b


def create_random_users(count):
    users = []
    for _ in range(count):
        email = fake.unique.safe_email()
        user = user_factory(
            email=email,
            first_name=fake.first_name(),
            last_name=fake.last_name(),
            password=fake.password(),
            username=email,
            profile__cell_phone="119" + random_number(8),
            profile__rg=random_number(9),
            profile__cpf=fake.cpf(),
            lead=True,
        )
        device_svc.update_or_create_device(user.profile, random_code(32), kind=device_svc.FCM)
        users.append(user)
    return users


def travels_rota3(b, provider="mercadopago"):
    users = create_random_users(8)
    b.users = users
    b.travels = b.travels or []
    trechos_classes = TrechoClasse.objects.filter(grupo__rota=b.rota3)
    for i, tc in enumerate(trechos_classes):
        for j in range(2):
            idx = 2 * i + j
            user = users[idx]
            travels = reserva2(user, cpf=fake.cpf(), trecho_classe=tc, provider=provider)
            b.travels.extend(travels)
    return b


def grupo_com_configuracao_especifica(
    b,
    classes,
    companion=False,
    voucher=None,
    pay_value=None,
    rota=None,
    dtbase=None,
    confirming_probability="very_low",
    method="boleto",
    saldo=None,
    provider=None,
    grupo=None,
):
    dtbase = dtbase or now() + timedelta(days=12)
    if not grupo:
        grupo = _mock_grupo(
            b,
            dtbase=dtbase,
            rota=rota,
            confirming_probability=confirming_probability,
        )
    for classe in classes:
        _mock_grupo_classe_with_travels(
            b, grupo, classe, companion, voucher, pay_value, method=method, saldo=saldo, provider=provider
        )

    return grupo


def feedbacks(b):
    feedback = {
        "feedback": [
            {"text": "Pontualidade", "value": False},
            {"text": "Qualidade do ônibus", "value": True},
            {"text": "Direção", "value": True},
        ],
        "comment": "Sangue bom",
    }
    travel = b.travels[0]
    feedback["rating"] = 4
    grupos_svc.travel_feedback(travel.id, feedback=feedback)

    travel = b.travels[1]
    feedback["rating"] = 3
    grupos_svc.travel_feedback(travel.id, feedback=feedback)


def reserva(
    user,
    trecho_classe,
    name=None,
    cpf="858.913.473-34",
    companion=True,
    pay_value=None,
    voucher=None,
    method="boleto",
    user_revendedor=None,
    tipo_documento=TipoDocumento.RG,
    birthday="10/06/1981",
    provider="mercadopago",
):
    set_global_setting("metodos_pagamento", ["credit_card", "pix", "boleto", "dinheiro"])
    name = name or user.get_full_name()
    if method == "credit_card":
        pagamento_handler = _payment_cartao
    elif method == "pix":
        pagamento_handler = _payment_pix
    else:
        pagamento_handler = _payment_boleto

    pagamento = pagamento_handler(2 if companion else 1, pay_value, provider)
    d = {
        "groups": [
            {
                "id": str(trecho_classe.id),
                "companions": [],
                "promoCode": voucher,
            }
        ],
        "passengers": [
            {
                "name": name,
                "social_name": name,
                "email": user.email,
                "cpf": cpf,
                "rg_number": str(random_number(9)),
                "rg_orgao": str(random_code(3)),
                "birthday": birthday,
                "phone": "***********",
                "tipo_documento": tipo_documento,
            }
        ],
        "payment": pagamento,
        "user_revendedor": user_revendedor,
    }
    if companion:
        d["passengers"].append(
            {
                "name": "Samantha Lobato",
                "social_name": "Samantha Lobato",
                "rg_number": str(random_number(9)),
                "rg_orgao": str(random_code(3)),
                "birthday": "04/02/1982",
                "cpf": fake.cpf(),
            }
        )
    travels, _ = reserva_svc.efetuar_reserva_sincrona(d, user)
    return travels


def reserva2(user, name=None, cpf="858.913.473-34", trecho_classe=None, voucher=None, provider="mercadopago"):
    name = name or user.get_full_name()

    pagamento = {
        "value": D("176.00"),
        "net_value": D("176.00"),
        "payment_method": "boleto",
        "name": "Tony Calleri França",
        "email": "<EMAIL>",
        "cpf": "***********",
        "phone": "***********",
        "provider": provider,
        "expiration_date": datetime(1990, 1, 1),
    }
    d = {
        "groups": [
            {
                "id": str(trecho_classe.id),
                "companions": [],
                "promoCode": voucher,
            }
        ],
        "passengers": [
            {
                "name": name,
                "social_name": name,
                "email": user.email,
                "cpf": cpf,
                "rg_number": str(random_number(9)),
                "rg_orgao": str(random_code(3)),
                "birthday": "10/06/1981",
                "phone": "***********",
                "tipos_deficiencia": [],
            },
            {
                "name": "Samantha Lobato",
                "social_name": "Samantha Lobato",
                "rg_number": str(random_number(9)),
                "rg_orgao": str(random_code(3)),
                "birthday": "04/02/1982",
                "cpf": fake.cpf(),
                "tipos_deficiencia": [],
            },
        ],
        "payment": pagamento,
        "extrato": {
            "user": user,
            "travel_ida": {
                "trechoclasse_id": str(trecho_classe.id),
                "trechoclasse": trecho_classe,
                "trecho_vendido": trecho_classe.trecho_vendido,
                "promoCode": None,
                "tipo_assento": "leito",
                "max_split_value": D("88.00"),
                "count_seats": 2,
                "passengers": [
                    {"id": None, "name": "Eduardo Ramos", "cpf": "097.626.200-20", "removed": False},
                    {"id": None, "name": "Samantha Lobato", "cpf": "302.169.548-51", "removed": False},
                ],
                "extrato": {
                    "split_value": D("88.00"),
                    "value": D("176.00"),
                    "reais_utilizados": D("0"),
                    "valor_pagamento": D("176.00"),
                },
            },
            "travel_volta": {},
            "staff_user": None,
            "pagamento_total": D("176.00"),
            "total_parcelado": D("176.00"),
            "parcela_value": D("176.00"),
            "juros_factor": D("1.0000"),
            "parcela_count": 1,
            "boleto": {
                "allow": True,
                "notallowed_reason": None,
                "expiration_date": date(2021, 9, 3),
                "indebted": False,
                "indebted_reason": None,
            },
        },
    }

    for passenger in d["passengers"]:
        buseiro = Buseiro(user=user)
        buseiro.update_from_dict(passenger)  # aqui ja faz um save()

    order_info = order_info_factory(user, pagamento)
    reserva = Reserva.objects.create(status=Reserva.Status.CRIADA, is_async=False, user=user)
    pagamento = pagamento_svc._gerar_pagamento_chain(user, order_info, reserva)
    evento = MagicMock(
        payment=d["payment"],
        passengers=d["passengers"],
        trechos_ida=[trecho_classe],
        trechos_volta=[],
        extrato=ReservaDict(d["extrato"]),
        data=ReservaDict(d),
        user=user,
        conexao_ida=None,
        conexao_volta=None,
    )
    travels = reserva_svc._cria_travels(evento, pagamento)

    # os testes que eu to mexendo agora não usam accounting operations pra nada
    # for travel in travels:
    #     accounting_svc._create_op(user=travel.user,
    #                 source='RESERVA',
    #                 travel=travel,
    #                 value_real=-D('88.00'),
    #                 value=-D('88.00'))
    trecho_classe.grupo.refresh_from_db()
    multitrecho_svc.atualiza_vagas_trechos_classe_do_grupo(trecho_classe.grupo)
    reserva.status = Reserva.Status.CONCLUIDA
    reserva.save()

    return travels


def company_granero(b, cnpj="**************", vinculo="temporaria"):
    b.company_granero = Company.objects.create(name="Granero")
    b.company_granero.has_accepted_contract = False
    b.company_granero.cnpj = cnpj
    b.company_granero.vinculo = vinculo
    b.company_granero.inscricao_estadual = "*********"
    b.company_granero.razao_social = "Granero Fretamento LTDA"
    b.company_granero.nome_fantasia = "Granero Fretamento"
    b.company_granero.endereco_logradouro = "Rua dos Fretadores"
    b.company_granero.endereco_numero = "S/N"
    b.company_granero.endereco_bairro = "Bairro do Fretes"
    b.company_granero.endereco_cep = "********"
    b.company_granero.phone = "***********"
    b.company_granero.taf = "************"
    b.company_granero.cidade = b.cidade_bh
    b.company_granero.rating = 4.5
    b.company_granero.emissao_nf_enabled = True
    b.company_granero.has_sent_digital_certificate = True
    b.company_granero.datetime_expiracao_certificado = now() + timedelta(weeks=100)
    b.company_granero.bank_account = baker.make(
        "core.BankAccount",
        agencia="0001",
        agencia_dv="0",
        bank_code="260",
        conta="132144",
        conta_dv="3",
    )
    b.company_granero.save()
    b.grupo_classe_bhsp.grupo.company = b.company_granero
    b.grupo_classe_bhsp.grupo.save()
    b.grupo_classe_bhsp.save()
    return b.company_granero


def nota_fiscal(b, company_id, s3key="s3key", xml_s3key="xmlS3Key", emissao_auto=True):
    today = now()
    first = today.date().replace(day=1)
    last_day_last_month = first - timedelta(days=1)
    b.nota_fiscal = NotaFiscal.objects.create(valor=100)
    b.nota_fiscal.company_id = company_id
    b.nota_fiscal.s3key = s3key
    b.nota_fiscal.xml_s3key = xml_s3key
    b.nota_fiscal.created_at = to_default_tz_required(str(last_day_last_month))
    b.nota_fiscal.provider = "upload"
    if emissao_auto:
        b.nota_fiscal.status_sefaz = StatusSefaz.AUTORIZADA
        b.nota_fiscal.cteos_key = "13245"
        b.nota_fiscal.provider = "tecnospeed"
        b.nota_fiscal.data_emissao = now() - timedelta(minutes=10)
    b.nota_fiscal.save()
    return b.nota_fiscal


def travel_survey(b=None):
    b = b or Bunch()
    b.travel_survey = TravelSurvey.objects.create(
        question="Qual o motivo da sua viagem?",
        choices="Trabalho,Lazer,Família,Outro",
    )
    return b.travel_survey


def company_santa_maria(b):
    b.company_santa_maria = Company.objects.create(name="Santa Maria")
    b.company_santa_maria.has_accepted_contract = False
    b.company_santa_maria.cnpj = "09547990000157"
    b.company_santa_maria.inscricao_estadual = "*********"
    b.company_santa_maria.razao_social = "Santa Maria Turismo LTDA"
    b.company_santa_maria.nome_fantasia = "Sama Tur"
    b.company_santa_maria.endereco_logradouro = "Rua dos Fretadores"
    b.company_santa_maria.endereco_numero = "S/N"
    b.company_santa_maria.endereco_bairro = "Bairro do Fretes"
    b.company_santa_maria.endereco_cep = "********"
    b.company_santa_maria.phone = "***********"
    b.company_santa_maria.taf = "************"
    b.company_santa_maria.cidade = b.cidade_campogrande
    b.company_santa_maria.rating = 4.5
    b.company_santa_maria.cteos_serie = 3
    b.company_santa_maria.emissao_nf_enabled = True
    b.company_santa_maria.has_sent_digital_certificate = True
    b.company_santa_maria.datetime_expiracao_certificado = now() + timedelta(weeks=100)
    b.company_santa_maria.tecnospeed_handle = 4598
    b.company_santa_maria.datetime_expiracao_certificado = timezone.now() + timedelta(days=30)
    b.company_santa_maria.save()
    b.grupo_classe_bhsp.grupo.company = b.company_santa_maria
    b.grupo_classe_bhsp.grupo.save()
    b.grupo_classe_bhsp.save()
    return b.company_santa_maria


def golsat_tracking(grupo):
    def _steps(_local_list):
        steps = 5

        for index in range(len(_local_list) - 1):
            # o primeiro step é gerado antes do start e o ultimo step é gerado
            # após o end, para testar os estados iniciais

            start, end = _local_list[index], _local_list[index + 1]

            start_x, start_y = start.coords
            end_x, end_y = end.coords

            step_x = (end_x - start_x) / steps
            step_y = (end_y - start_y) / steps

            for i in range(steps + 1):
                yield start_x + (i - 1) * step_x, start_y + (i - 1) * step_y, True

    local_list = (grupo.rota.origem, grupo.rota.destino)
    objs = []
    for n, (lat, long, ignition) in enumerate(_steps(local_list)):
        obj = ViagemTelemetria(
            onibus_placa=grupo.onibus.placa,
            origin="fixture_origin",
            datetime_medicao=grupo.datetime_ida + (n - 2) * timedelta(minutes=40),
            speed=75 + n,
            position=Point(long, lat, srid=4326),
            ignition=ignition,
        )
        objs.append(obj)
    ViagemTelemetria.objects.bulk_create(objs)


def _mock_rota2(local_origem, local_destino, tvargs=None):
    tvargs = tvargs or {}
    rota = baker.make(
        Rota,
        ativo=True,
        ufs_intermediarios="GO",
        origem=local_origem,
        destino=local_destino,
        distancia_total=1000,
        duracao_total=timedelta(hours=8, minutes=30),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=local_origem,
        idx=0,
        tempo_embarque=timedelta(minutes=0),
        distancia_km=0,
        duracao=timedelta(minutes=0),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=local_destino,
        idx=1,
        tempo_embarque=timedelta(minutes=0),
        distancia_km=1000,
        duracao=timedelta(hours=8, minutes=30),
    )
    baker.make(
        TrechoVendido,
        rota=rota,
        origem=local_origem,
        destino=local_destino,
        **tvargs,
    )
    return rota


def _mock_rota_3_locais(local_1, local_2, local_3):
    rota = baker.make(
        Rota,
        ativo=True,
        origem=local_1,
        destino=local_3,
        distancia_total=900,
        duracao_total=timedelta(hours=12, minutes=40),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=local_1,
        idx=0,
        distancia_km=0,
        duracao=timedelta(minutes=0),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=local_2,
        idx=1,
        distancia_km=300,
        duracao=timedelta(hours=4),
    )
    baker.make(
        Checkpoint,
        rota=rota,
        local=local_3,
        idx=2,
        distancia_km=600,
        duracao=timedelta(hours=8),
    )
    baker.make(TrechoVendido, rota=rota, origem=local_1, destino=local_2)
    baker.make(TrechoVendido, rota=rota, origem=local_1, destino=local_3)
    baker.make(TrechoVendido, rota=rota, origem=local_2, destino=local_3)
    return rota


def grupo_extra(b, dtbase=None, rota=None):
    rota = rota or b.rota_bhsp
    grupo = baker.make(
        Grupo,
        rota=rota,
        status="pending",
        datetime_ida=dtbase or now() + _2days,
        is_extra=True,
    )
    return grupo


def _mock_grupo(b, dtbase=None, rota=None, confirming_probability="very_low"):
    rota = rota or b.rota_bhsp
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota)
    grupo = baker.make(
        Grupo,
        rota=rota,
        status="pending",
        rotina_onibus=rotina_onibus,
        confirming_probability=confirming_probability,
        datetime_ida=dtbase or now() + _2days,
        is_extra=False,
    )
    return grupo


def _mock_grupo_classe(
    rota,
    datetime_ida=None,
    status="pending",
    tipo_assento="leito",
    capacidade=None,
    closed=False,
    pessoas=0,
    confirming_probability="very_low",
    marketplace=False,
    company=None,
    rotina=None,
    gc: GrupoClasse | None = None,
    grupo: Grupo | None = None,
):
    capacidade = capacidade or CAPACIDADE_MAP[tipo_assento]
    if not grupo:
        grupo = baker.make(
            Grupo,
            rota=rota,
            datetime_ida=datetime_ida,
            status=status,
            confirming_probability=confirming_probability,
            is_extra=False,
            modelo_venda=Grupo.ModeloVenda.MARKETPLACE if marketplace else Grupo.ModeloVenda.BUSER,
            company=company,
            rotina_onibus=rotina,
            one_driver_exception=False,
            hidden_for_pax=False,
        )
    if not gc:
        gc = baker.make(
            GrupoClasse,
            grupo=grupo,
            tipo_assento=tipo_assento,
            capacidade=capacidade,
            pessoas=pessoas,
            closed=closed,
        )
    tc = None
    for trechovendido in rota.get_trechos_vendidos():
        max_split_value = calc_max_split(trechovendido.preco_rodoviaria, tipo_assento)
        tc = baker.make(
            TrechoClasse,
            grupo=gc.grupo,
            datetime_ida=datetime_ida,
            grupo_classe=gc,
            trecho_vendido=trechovendido,
            preco_rodoviaria=trechovendido.preco_rodoviaria,
            max_split_value=max_split_value,
            ref_split_value=max_split_value,
            duracao_ida=timedelta(hours=4),
            pessoas=pessoas,
            vagas=gc.capacidade,
            price_manager=baker.make("core.PriceManager", value=max_split_value, ref_value=max_split_value),
        )
    return gc, tc


def _mock_grupo_classe_with_existing_grupo(grupo, closed=False):
    return baker.make(
        GrupoClasse,
        grupo=grupo,
        closed=closed,
    )


def _mock_grupo_classe_with_travels(
    b, grupo, params, companion, voucher=None, pay_value=None, method="boleto", saldo=None, provider=None
):
    tc = None
    gc = baker.make(
        GrupoClasse,
        grupo=grupo,
        tipo_assento=params["tipo_assento"],
        capacidade=params["max_capacity"],
    )
    for trechovendido in grupo.rota.get_trechos_vendidos():
        max_split_value = params["trechos"][0]["max_split_value"]
        tc = baker.make(
            TrechoClasse,
            grupo=grupo,
            grupo_classe=gc,
            datetime_ida=grupo.datetime_ida,
            trecho_vendido=trechovendido,
            preco_rodoviaria=trechovendido.preco_rodoviaria,
            max_split_value=max_split_value,
            ref_split_value=max_split_value,
            duracao_ida=timedelta(hours=1),
            pessoas=0,
            vagas=gc.capacidade,
            price_manager=baker.make("core.PriceManager", value=max_split_value, ref_value=max_split_value),
        )
        _mock_viagens(
            b, tc, params["pessoas"], companion, voucher, pay_value, method=method, saldo=saldo, provider=provider
        )
    return tc


def _mock_viagens(
    b,
    trecho_classe,
    quantity,
    companion,
    voucher=None,
    pay_value=None,
    method="boleto",
    saldo=None,
    birthday="10/06/1981",
    tipo_documento=TipoDocumento.RG,
    users=None,
    provider=None,
):
    if not pay_value:
        pay_value = trecho_classe.max_split_value * (2 if companion else 1)
    pay_value = pay_value - (saldo or D(0))
    count_users_to_retrieve = int(quantity / (2 if companion else 1))
    if users is None:
        users = create_random_users(count_users_to_retrieve)
    else:
        users = [next(users) for _ in range(count_users_to_retrieve)]
    for idx, user in enumerate(users):
        if saldo:
            staff = User.objects.filter(username="staff").first()
            dar_reais(user, staff, saldo, reason="ADMIN_REAIS")
        reserva(
            user,
            trecho_classe,
            companion=companion,
            name=fake.name(),
            cpf=fake.cpf(),
            voucher=voucher,
            pay_value=pay_value,
            method=method,
            birthday=birthday,
            tipo_documento=tipo_documento,
            provider=provider,
        )
    return b


def _payment_boleto(count=1, value=None, provider=None):
    value = value or (D("88.00") * count)
    return {
        "value": value,
        "net_value": value,
        "payment_method": "boleto",
        "name": "Tony Calleri França",
        "email": "<EMAIL>",
        "cpf": "***********",
        "phone": "***********",
        "provider": provider,
    }


def _payment_pix(count=1, value=None, provider=None):
    value = value or (D("88.00") * count)
    return {
        "value": value,
        "net_value": value,
        "payment_method": "pix",
        "name": "Tony Calleri França",
        "email": "<EMAIL>",
        "cpf": "***********",
        "phone": "***********",
    }


def _payment_cartao(count=1, value=None, provider="mercadopago"):
    value = value or (D("88.00") * count)
    return {
        "value": value,
        "parcela_value": value,
        "net_value": value,
        "payment_method": "credit_card",
        "parcela_count": 1,
        "provider": provider,
        "bank_identification_number": "423564",
        "card_hash": "HASH",
        "name": "Tony Calleri França",
        "card_name": "TONY CALLERI FRANCA",
        "email": "<EMAIL>",
        "cpf": "***********",
        "phone": "***********",
        "street": "Rua Aracuai",
        "streetnum": "89",
        "cep": "********",
        "state": "SP",
        "city": "São José dos Campos",
        "neighborhood": "Bosque dos Eucaliptos",
        "card_brand": "visa",
        "juros_factor": D("1.0"),
    }


def make_dummy_travels(thumb=False):
    xuxa = ["A", "B", "C", "D", "E"]
    cidade_list = [
        baker.make(
            "core.Cidade",
            name=f"CIDADE {letra}",
            uf=letra * 2,
            sigla=letra * 3,
        )
        for letra in xuxa
    ]
    for cidade in cidade_list:
        cidade_info = baker.make("core.CidadeInfo", cidade=cidade)
        baker.make("core.WikipediaInfo", cidade=cidade)
        if thumb:
            baker.make("core.Imagem", cidade_info=cidade_info, tipo="thumb", image_file=f"{cidade.slug}.jpg")
    qtdy_map = {4: 2, 5: 2, 13: 3, 6: 1, 14: 4, 0: 5, 9: 2, 2: 3, 10: 1}
    localembarque_list = [baker.make(LocalEmbarque, cidade=cidade) for cidade in cidade_list]
    localembarque_a = localembarque_list.pop(0)

    trecho_vendido_list = _make_dummy_trechos_vendidos(localembarque_a, localembarque_list)
    trecho_classe_list = _make_dummy_trecho_classes(trecho_vendido_list)

    for i, tc in enumerate(trecho_classe_list):
        if qtdy_map.get(i) is not None:
            baker.make(
                "core.Travel",
                trecho_vendido=tc.trecho_vendido,
                trecho_classe=tc,
                _quantity=qtdy_map.get(i),
            )


def _make_dummy_trechos_vendidos(main_localembarque, localembarque_list):
    tv_saindo_de = [
        baker.make(
            TrechoVendido,
            origem=main_localembarque,
            destino=destino,
            rota=_make_dummy_rota(main_localembarque, destino),
        )
        for destino in localembarque_list
    ]

    tv_indo_para = [
        baker.make(
            TrechoVendido,
            origem=origem,
            destino=main_localembarque,
            rota=_make_dummy_rota(origem, main_localembarque),
        )
        for origem in localembarque_list
    ]
    return tv_saindo_de + tv_indo_para


def _make_dummy_trecho_classes(trecho_vendido_list):
    max_split_value_list = [
        D("250.00"),
        D("200.00"),
        D("150.00"),
        D("110.00"),
        D("100.00"),
        D("75.00"),
        D("50.00"),
        D("30.00"),
    ]

    tc_lista1 = [
        baker.make(
            TrechoClasse,
            trecho_vendido=trecho_vendido,
            max_split_value=max_split_value,
            ref_split_value=max_split_value,
            grupo=_make_dummy_group(trecho_vendido.rota),
            datetime_ida=timezone.now() + timedelta(days=3),
            vagas=30,
            price_manager=baker.make("core.PriceManager", value=max_split_value, ref_value=max_split_value),
        )
        for trecho_vendido, max_split_value in zip(trecho_vendido_list, max_split_value_list)
    ]
    tc_lista2 = [
        baker.make(
            TrechoClasse,
            trecho_vendido=trecho_vendido,
            max_split_value=max_split_value,
            ref_split_value=max_split_value,
            grupo=_make_dummy_group(trecho_vendido.rota),
            datetime_ida=timezone.now() + timedelta(days=3),
            vagas=30,
            price_manager=baker.make("core.PriceManager", value=max_split_value, ref_value=max_split_value),
        )
        for trecho_vendido, max_split_value in zip(trecho_vendido_list, reversed(max_split_value_list))
    ]

    return tc_lista1 + tc_lista2


def _make_dummy_rota(origem, destino):
    rota = baker.make(Rota, origem=origem, destino=destino)
    baker.make("core.Checkpoint", local=origem, rota=rota)
    baker.make("core.Checkpoint", local=destino, rota=rota)
    return rota


def _make_dummy_group(rota):
    return baker.make("core.Grupo", rota=rota)


def rotina_3_cidades(b, minimo_reservas=None, minimo_reservas_prev=None, rota_principal=None):
    b.rotina_tripla = baker.make(
        "RotinaOnibus",
        base_operacional=b.cidade_ipatinga,
        minimo_reservas=minimo_reservas,
        minimo_reservas_prev=minimo_reservas_prev,
        rota_principal=rota_principal,
    )
    b.rota_ipamco = _mock_rota2(b.local_ip_pqipanema, b.local_mc_montesshopping)
    b.rota_mcoube = _mock_rota2(b.local_mc_montesshopping, b.local_ub_uberpoint)
    b.rota_ubemco = _mock_rota2(b.local_ub_uberpoint, b.local_mc_montesshopping)
    b.rota_mcoipa = _mock_rota2(b.local_mc_montesshopping, b.local_ip_pqipanema)
    return b.rotina_tripla


def rotina_onibus(b):
    rota_principal_bhz_sao = baker.make("RotaPrincipal", eixo="BHZ-SAO")
    b.rotina_bhsp_2030 = baker.make(
        "RotinaOnibus",
        nome="BHZ-SAO 20:30",
        frete_atual=1000,
        rota_principal_id=rota_principal_bhz_sao.id,
    )
    b.rotina_spbh_2030 = baker.make("RotinaOnibus", nome="SAO-BHZ 20:30", frete_atual=2000)

    b.grupo_classe_bhsp.grupo.rotina_onibus = b.rotina_bhsp_2030
    b.grupo_classe_bhsp.grupo.save()


def rotina_onibus_com_onibus(b):
    rota_principal_bhz_sao = baker.make("RotaPrincipal", eixo="BHZ-SAO")
    b.rotina_bhsp_2030 = baker.make(
        "RotinaOnibus",
        nome="BHZ-SAO 20:30",
        frete_atual=1000,
        rota_principal_id=rota_principal_bhz_sao.id,
        _fill_optional=["onibus"],
    )
    b.rotina_spbh_2030 = baker.make("RotinaOnibus", nome="SAO-BHZ 20:30", frete_atual=2000, _fill_optional=["onibus"])

    b.grupo_classe_bhsp.grupo.rotina_onibus = b.rotina_bhsp_2030
    b.grupo_classe_bhsp.grupo.save()


def rotina_onibus_fln_sp(b):
    rota_fln_aero_sp_se(b, True)
    b.rotina_fln_sp = baker.make("RotinaOnibus", nome="aero floripa", base_operacional=b.cidade_floripa)
    gc_ida, tc_ida = _mock_grupo_classe(
        b.rota_fln_aero_sp_se,
        datetime_ida=to_default_tz_required(now() + timedelta(hours=2)),
        status="travel_confirmed",
    )
    gc_volta, tc_volta = _mock_grupo_classe(
        b.rota_sp_se_fln_aero,
        datetime_ida=to_default_tz_required(now() + timedelta(hours=8)),
        status="travel_confirmed",
    )
    gc_ida.grupo.rotina_onibus = b.rotina_fln_sp
    gc_ida.grupo.save()
    gc_volta.grupo.rotina_onibus = b.rotina_fln_sp
    gc_volta.grupo.save()
    b.trecho_classe_fln_sp = tc_ida
    b.trecho_classe_sp_fln = tc_volta


def promo():
    now = dateutils.now()
    return {
        "code": "JOVEM210",
        "value": None,
        "fixed_value": None,
        "discount": D("100"),
        "description": "Cupom divulgação na jovem pan de curitiba no dia 20/20/10 texto super longo n dá vontade de ler.",
        "start_date": now.strftime("%Y-%m-%d"),
        "due_date": (now + timedelta(days=20)).strftime("%Y-%m-%d"),
        "datetime_ida_start": (now + timedelta(days=10)).strftime("%Y-%m-%d"),
        "datetime_ida_end": (now + timedelta(days=20)).strftime("%Y-%m-%d"),
        "type": "DESCONTO_PERCENTUAL",
        "is_volta": True,
        "is_apenas_reserva": False,
        "rotas": [],
        "cidades_origem": [],
        "cidades_destino": [],
        "valor_limite": None,
        "exclusivo_app": False,
    }


def promo_valor_fixo():
    return {
        "code": "JOVEM211",
        "value": None,
        "fixed_value": D("10"),
        "discount": None,
        "description": "Cupom divulgação na jovem pan de curitiba no dia 20/20/10 texto super longo n dá vontade de ler.",
        "start_date": timezone.now().strftime("%Y-%m-%d"),
        "due_date": (timezone.now() + timedelta(days=20)).strftime("%Y-%m-%d"),
        "datetime_ida_start": (timezone.now() + timedelta(days=10)).strftime("%Y-%m-%d"),
        "datetime_ida_end": (timezone.now() + timedelta(days=20)).strftime("%Y-%m-%d"),
        "type": "VALOR_FIXO",
        "is_volta": True,
        "is_apenas_reserva": False,
        "rotas": [],
        "valor_limite": None,
    }


def promo_with_date_restrictions():
    now = dateutils.now()
    return {
        "code": "JOVEM211",
        "value": None,
        "fixed_value": D("10"),
        "discount": None,
        "description": "Cupom divulgação na jovem pan de curitiba no dia 20/20/10 texto super longo n dá vontade de ler.",
        "start_date": to_default_tz_required(timezone.now()).strftime("%Y-%m-%d"),
        "due_date": to_default_tz_required(now + timedelta(days=20)).strftime("%Y-%m-%d"),
        "datetime_ida_start": to_default_tz_required(now + timedelta(days=10)).strftime("%Y-%m-%d"),
        "datetime_ida_end": to_default_tz_required(now + timedelta(days=20)).strftime("%Y-%m-%d"),
        "type": "VALOR_FIXO",
        "is_volta": True,
        "is_apenas_reserva": False,
        "rotas": [],
        "valor_limite": None,
        "date_restrictions": [
            {
                "date_start": to_default_tz_required(now + timedelta(days=15)).isoformat(),
                "date_end": to_default_tz_required(now + timedelta(days=16)).isoformat(),
            }
        ],
    }


def promo_desconto_absoluto():
    return {
        "code": "JOVEM212",
        "value": D("20"),
        "fixed_value": None,
        "discount": None,
        "description": "Cupom divulgação na jovem pan de curitiba no dia 20/20/10 texto super longo n dá vontade de ler.",
        "start_date": timezone.now().strftime("%Y-%m-%d"),
        "due_date": (timezone.now() + timedelta(days=20)).strftime("%Y-%m-%d"),
        "datetime_ida_start": (timezone.now() + timedelta(days=10)).strftime("%Y-%m-%d"),
        "datetime_ida_end": (timezone.now() + timedelta(days=20)).strftime("%Y-%m-%d"),
        "type": "DESCONTO_ABSOLUTO",
        "is_volta": True,
        "is_apenas_reserva": False,
        "rotas": [],
        "valor_limite": None,
    }


def whatsapp_template_factory(msgid="", from_sender="suporte"):
    if WhatsappTemplate.objects.filter(msgid=msgid).exists():
        return

    template = whatsapp_templates.TEMPLATES[msgid]
    baker.make(
        WhatsappTemplate,
        msgid=msgid,
        from_sender=from_sender,
        content=template["content"],
        type=template["type"],
        is_active=template["is_active"],
        inactive_reason=template["inactive_reason"],
    )


def gerar_onibus_com_poltronas(trecho_classe, tipo_assento=None):
    grupo = trecho_classe.grupo
    onibus = baker.make("core.Onibus")
    poltronas = []
    for poltrona, (x, y, z) in enumerate(itertools.product(range(4), range(10), range(1, 2)), start=1):
        poltronas.append(
            baker.prepare(
                "core.PoltronaOnibus",
                onibus=onibus,
                poltrona=poltrona,
                linha=x,
                coluna=y,
                tipo=tipo_assento or trecho_classe.grupo_classe.tipo_assento,
                andar=z,
                ativo=True,
            )
        )
    onibus.poltronas.bulk_create(poltronas)
    grupo.onibus = onibus
    grupo.save()
    return onibus


def gerar_accop_marcacao_assento(travel, pax, poltrona=1):
    pax.poltrona = 1
    pax.save(update_fields=["poltrona"])

    baker.make(
        "core.AccountingOperation",
        travel=travel,
        source="MARCACAO_ASSENTO",
        value=-D("14.9"),
        value_real=-D("14.9"),
        passageiro=pax,
    )

    baker.make(
        "accounting.AccountingOperation",
        travel=travel,
        source="MARCACAO_ASSENTO",
        value_real=-D("14.9"),
        passageiro=pax,
    )

    baker.make(
        "core.ItemAdicional",
        travel=travel,
        pax=pax,
        tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO,
        status=ItemAdicional.StatusItemAdicional.CONCLUIDO,
        valor=D("14.9"),
        quantidade=1,
        is_upsell=False,
        poltrona=None,
    )
    return travel
