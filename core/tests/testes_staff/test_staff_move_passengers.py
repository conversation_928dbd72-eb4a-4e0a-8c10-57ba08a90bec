import json
from decimal import Decimal
from decimal import Decimal as D
from unittest import mock

import pytest
from django.core import mail
from django.core.exceptions import ValidationError
from model_bakery import baker

from accounting.service import accounting_svc
from adapters.mock.mock_mercadopago_adapter import mock_mercadopago
from core.forms.remanejamento_forms import MoverBuseiroForm
from core.models_commons import ActivityLog
from core.models_company import PoltronaOnibus
from core.models_grupo import Grupo
from core.models_travel import HistoricoRemanejamento, ItemAdicional, Pagamento, Travel
from core.service.remanejamento import remanejamento_svc
from core.tests import fixtures
from integrations.rodoviaria_client.exceptions import RodoviariaMoverBuseiroException


@pytest.fixture(autouse=True)
def mock_cartao():
    with mock_mercadopago(value=176, refunded_value=176):
        yield


@pytest.fixture
def bunch():
    b = fixtures.grupos_e_trechos()
    fixtures.viagens(b, method="credit_card", provider=Pagamento.Provider.MERCADOPAGO)
    return b


@pytest.fixture
def trecho_classe_bhmoc(bunch):
    grupo_classe, trecho_classe = fixtures.grupo_classe_bhmoc(bunch)
    return trecho_classe


@pytest.fixture
def tc_leito(trecho_classe_bhmoc):
    tc = trecho_classe_bhmoc
    tc.max_split_value = D("88")
    tc.price_manager.value = D("88")
    tc.grupo_classe.tipo_assento = "leito"
    tc.grupo_classe.save()
    tc.price_manager.save()
    tc.save()
    return tc


@pytest.fixture
def tc_leito_cama(trecho_classe_bhmoc):
    tc = trecho_classe_bhmoc
    tc.max_split_value = D("125")
    tc.price_manager.value = D("125")
    tc.grupo_classe.tipo_assento = "leito cama"
    tc.grupo_classe.save()
    tc.price_manager.save()
    tc.save()
    return tc


@pytest.fixture
def tc_executivo(trecho_classe_bhmoc):
    tc = trecho_classe_bhmoc
    tc.max_split_value = D("69")
    tc.price_manager.value = D("69")
    tc.grupo_classe.tipo_assento = "executivo"
    tc.grupo_classe.save()
    tc.price_manager.save()
    tc.save()
    return tc


@pytest.fixture
def travels_leito(bunch):
    return list(bunch.grupo_classe_bhsp.travel_set.all())


@pytest.fixture
def travels_to_move(travels_leito):
    travels_to_move = [t for t in travels_leito if t.grupo == travels_leito[0].grupo][:2]
    Pagamento.objects.filter(id__in=[t.pagamento_id for t in travels_to_move]).update(status="paid")
    return travels_to_move


@pytest.fixture
def travel_com_marcacao_assento(travels_leito):
    travel = travels_leito[0]
    pax = travel.passageiro_set.first()
    pax.poltrona = 1
    pax.save(update_fields=["poltrona"])

    baker.make(
        "core.AccountingOperation",
        travel=travel,
        source="MARCACAO_ASSENTO",
        value=-Decimal("14.9"),
        value_real=-Decimal("14.9"),
        passageiro=pax,
    )

    baker.make(
        "accounting.AccountingOperation",
        travel=travel,
        source="MARCACAO_ASSENTO",
        value_real=-Decimal("14.9"),
        passageiro=pax,
    )

    baker.make(
        "core.ItemAdicional",
        travel=travel,
        pax=pax,
        tipo=ItemAdicional.TipoItemAdicional.MARCACAO_ASSENTO,
        status=ItemAdicional.StatusItemAdicional.CONCLUIDO,
        valor=Decimal("14.9"),
        quantidade=1,
        is_upsell=False,
        poltrona=None,
    )
    fixtures.gerar_onibus_com_poltronas(travel.trecho_classe)
    return travel


@pytest.fixture
def trecho_classe_buser_marcacao_destino(trecho_classe_bhmoc):
    tc = trecho_classe_bhmoc
    tc.max_split_value = D("125")
    tc.price_manager.value = D("125")
    tc.grupo_classe.tipo_assento = "leito cama"
    tc.grupo_classe.save()
    tc.price_manager.save()
    tc.id = None
    tc.pk = None
    tc.save()
    return tc


@pytest.fixture
def travels_sem_pagamento(travels_leito):
    travels_to_move = [t for t in travels_leito if t.grupo == travels_leito[0].grupo][:2]
    Travel.objects.filter(id__in=[t.id for t in travels_to_move]).update(pagamento=None)
    return travels_to_move


def test_erro_move_pax_sem_cancelar_viagem_antiga(travels_to_move):
    travel = travels_to_move[0]
    assert travel.status != "canceled"
    with pytest.raises(ValidationError):
        accounting_svc.cria_evento_reserva_remanejamento(travel, Travel(), [], {})


def test_staff_move_passenger_sem_reembolso(
    client_with_logged_staff, tc_leito, travels_to_move, django_capture_on_commit_callbacks
):
    saldos = {}
    usuarios_afetados = {t.user for t in travels_to_move}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)

    grupo_origem = travels_to_move[0].grupo
    assert grupo_origem.count_pessoas() == 8
    assert tc_leito.grupo.count_pessoas() == 0

    with django_capture_on_commit_callbacks(execute=True):
        _move_passengers(client_with_logged_staff, tc_leito.id, travels_to_move)
    assert grupo_origem.count_pessoas() == 4
    assert tc_leito.grupo.count_pessoas() == 4
    _assert_travels_canceladas(travels_to_move)
    for user in usuarios_afetados:
        assert saldos[user] == accounting_svc.get_saldo(user)


@pytest.mark.parametrize("modelo_venda", [Grupo.ModeloVenda.BUSER, Grupo.ModeloVenda.MARKETPLACE])
def test_staff_move_passenger_com_downgrade(
    modelo_venda: Grupo.ModeloVenda,
    client_with_logged_staff,
    tc_executivo,
    travels_to_move,
    django_capture_on_commit_callbacks,
):
    Grupo.objects.filter(id=tc_executivo.grupo_id).update(modelo_venda=modelo_venda)
    saldos = {}
    usuarios_afetados = {t.user for t in travels_to_move}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)
    grupo_origem = travels_to_move[0].grupo

    # modelo Buser: [preço da passagem] 88 * ( 0.6 - 0.7 [fatores de classe]) = 8.8, como são duas passagens: 17,60
    # modelo Marketplace: (88 * 2) - (69 * 2) = 38 -> diferença de passagens (38) é maior que downgrade (35.20) então considera 38
    downgrade_simulacao = D("17.60") if modelo_venda == Grupo.ModeloVenda.BUSER else D("38.00")

    # Valor efetivo é diferente da simulação para modelos Buser, pois agora que temos o ressarcimento unico,
    # ressarcimos somente após a viagem começar, evitando um tira e põe de dinheiro caso o pax seja remanejado muitas vezes.
    downgrade_efetivo = D("0.00") if modelo_venda == Grupo.ModeloVenda.BUSER else downgrade_simulacao

    mapa_reembolso = {}

    receita_gerada_marketplace = (
        travels_to_move[0].max_split_value * travels_to_move[0].count_seats
        if modelo_venda == Grupo.ModeloVenda.MARKETPLACE
        else D("0")
    )
    mapa_reembolso[travels_to_move[0].reservation_code] = dict(
        reais=downgrade_simulacao,
        buedas=0,
        receita_gerada_mktplace=receita_gerada_marketplace,
        divergencia_repasse=0,
    )
    mapa_reembolso[travels_to_move[1].reservation_code] = dict(
        reais=downgrade_simulacao,
        buedas=0,
        receita_gerada_mktplace=receita_gerada_marketplace,
        divergencia_repasse=0,
    )
    assert grupo_origem.count_pessoas() == 8
    assert tc_executivo.grupo.count_pessoas() == 0
    _assert_simulacao(client_with_logged_staff, tc_executivo.id, travels_to_move, mapa_reembolso)
    with django_capture_on_commit_callbacks(execute=True):
        _move_passengers(
            client_with_logged_staff, tc_executivo.id, travels_to_move, send_zap=True, send_push_inbox_sms_email=True
        )
    assert grupo_origem.count_pessoas() == 4
    assert tc_executivo.grupo.count_pessoas() == 4
    historico = HistoricoRemanejamento.objects.first()
    assert historico
    assert historico.antigo_tipo_assento != historico.novo_tipo_assento
    _assert_travels_canceladas(travels_to_move)
    for user in usuarios_afetados:
        saldo_pos = accounting_svc.get_saldo(user)
        assert saldo_pos["reais"] == saldos[user]["reais"] + downgrade_efetivo
        # Se mktplace, damos o downgrade como diferença de preço.
        # Se Buser, damos só após começar de fato a viagem, através do ressarcimento único, como explicado acima
        sacavel = D("0")
        assert saldo_pos["sacavel"] == saldos[user]["sacavel"] + sacavel
        assert saldo_pos["estornavel"] == saldos[user]["estornavel"] + downgrade_efetivo


@pytest.mark.parametrize("modelo_venda", [Grupo.ModeloVenda.BUSER, Grupo.ModeloVenda.MARKETPLACE])
def test_staff_move_passenger_com_downgrade_so_saldo(
    modelo_venda: Grupo.ModeloVenda,
    client_with_logged_staff,
    tc_executivo,
    travels_sem_pagamento,
    django_capture_on_commit_callbacks,
):
    Grupo.objects.filter(id=tc_executivo.grupo_id).update(modelo_venda=modelo_venda)
    saldos = {}
    usuarios_afetados = {t.user for t in travels_sem_pagamento}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)
    grupo_origem = travels_sem_pagamento[0].grupo

    # modelo Buser: [preço da passagem] 88 * ( 0.6 - 0.7 [fatores de classe]) = 8.8, como são duas passagens: 17,60
    # modelo Marketplace: (88 * 2) - (69 * 2) = 38 -> diferença de passagens (38) é maior que downgrade (35.20) então considera 38
    downgrade_simulacao = D("17.60") if modelo_venda == Grupo.ModeloVenda.BUSER else D("38.00")

    # Valor efetivo é diferente da simulação para modelos Buser, pois agora que temos o ressarcimento unico,
    # ressarcimos somente após a viagem começar, evitando um tira e põe de dinheiro caso o pax seja remanejado muitas vezes.
    downgrade_efetivo = D("0.00") if modelo_venda == Grupo.ModeloVenda.BUSER else downgrade_simulacao

    mapa_reembolso = {}
    receita_gerada_marketplace = (
        travels_sem_pagamento[0].max_split_value * travels_sem_pagamento[0].count_seats
        if modelo_venda == Grupo.ModeloVenda.MARKETPLACE
        else D("0")
    )
    mapa_reembolso[travels_sem_pagamento[0].reservation_code] = dict(
        reais=downgrade_simulacao,
        buedas=0,
        receita_gerada_mktplace=receita_gerada_marketplace,
        divergencia_repasse=0,
    )
    mapa_reembolso[travels_sem_pagamento[1].reservation_code] = dict(
        reais=downgrade_simulacao,
        buedas=0,
        receita_gerada_mktplace=receita_gerada_marketplace,
        divergencia_repasse=0,
    )
    assert grupo_origem.count_pessoas() == 8
    assert tc_executivo.grupo.count_pessoas() == 0
    _assert_simulacao(client_with_logged_staff, tc_executivo.id, travels_sem_pagamento, mapa_reembolso)
    with django_capture_on_commit_callbacks(execute=True):
        _move_passengers(
            client_with_logged_staff, tc_executivo.id, travels_sem_pagamento, send_push_inbox_sms_email=True
        )
    assert grupo_origem.count_pessoas() == 4
    assert tc_executivo.grupo.count_pessoas() == 4
    _assert_travels_canceladas(travels_sem_pagamento)
    for user in usuarios_afetados:
        saldo_pos = accounting_svc.get_saldo(user)
        assert saldo_pos["reais"] == saldos[user]["reais"] + downgrade_efetivo
        # Se mktplace, damos o downgrade como diferença de preço.
        # Se Buser, damos só após começar de fato a viagem, através do ressarcimento único, como explicado acima
        sacavel = D("0")
        assert saldo_pos["sacavel"] == saldos[user]["sacavel"] + sacavel
        assert saldo_pos["estornavel"] == saldos[user]["estornavel"] + downgrade_efetivo


def test_staff_move_passenger_pagamento_nao_finalizado(client_with_logged_staff, tc_executivo, travels_to_move):
    Pagamento.objects.filter(id__in=[t.pagamento_id for t in travels_to_move]).update(status="waiting_payment")
    saldos = {}
    usuarios_afetados = {t.user for t in travels_to_move}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)
    _move_passengers(client_with_logged_staff, tc_executivo.id, travels_to_move)
    for user in usuarios_afetados:
        saldo_pos = accounting_svc.get_saldo(user)
        novo_saldo_reais = D("88.00") * 2
        assert saldo_pos["buedas"] == saldos[user]["buedas"]
        assert saldo_pos["reais"] == saldos[user]["reais"] - novo_saldo_reais
        assert saldo_pos["sacavel"] == saldos[user]["sacavel"] - novo_saldo_reais
        assert saldo_pos["estornavel"] == saldos[user]["estornavel"] - novo_saldo_reais


@mock.patch("core.service.notifications.user_notification_svc.travel_comunicada_svc.salvar_travel_comunicada")
def test_staff_move_passenger_com_upgrade(
    mock_travel_comunicada,
    client_with_logged_staff,
    travels_to_move,
    tc_leito_cama,
    django_capture_on_commit_callbacks,
):
    saldos = {}
    usuarios_afetados = {t.user for t in travels_to_move}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)
    grupo_origem = travels_to_move[0].grupo
    # Upgrade bacana, mas não devemos cobrar a mais dos pax!
    mapa_reembolso = {}
    mapa_reembolso[travels_to_move[0].reservation_code] = dict(
        reais=0, buedas=0, receita_gerada_mktplace=0, divergencia_repasse=0
    )
    mapa_reembolso[travels_to_move[1].reservation_code] = dict(
        reais=0, buedas=0, receita_gerada_mktplace=0, divergencia_repasse=0
    )
    assert grupo_origem.count_pessoas() == 8
    assert tc_leito_cama.grupo.count_pessoas() == 0
    _assert_simulacao(client_with_logged_staff, tc_leito_cama.id, travels_to_move, mapa_reembolso)
    with django_capture_on_commit_callbacks(execute=True):
        _move_passengers(client_with_logged_staff, tc_leito_cama.id, travels_to_move, send_zap=True)
    assert mock_travel_comunicada.call_count == 2
    assert grupo_origem.count_pessoas() == 4
    assert tc_leito_cama.grupo.count_pessoas() == 4
    _assert_travels_canceladas(travels_to_move)
    for user in usuarios_afetados:
        assert saldos[user] == accounting_svc.get_saldo(user)


def test_staff_move_passenger_receita_gerada_marketplace(
    client_with_logged_staff,
    travels_to_move,
    tc_leito_cama,
    whatsapp_template_factory,
):
    whatsapp_template_factory("reservation_updatev2")
    saldos = {}
    usuarios_afetados = {t.user for t in travels_to_move}
    for user in usuarios_afetados:
        saldos[user] = accounting_svc.get_saldo(user)
    grupo_destino = tc_leito_cama.grupo
    grupo_destino.modelo_venda = Grupo.ModeloVenda.MARKETPLACE
    grupo_destino.percentual_repasse = 50
    grupo_destino.save()
    # Upgrade bacana, mas não devemos cobrar a mais dos pax!
    mapa_reembolso = {}
    receita_gerada_mktplace = D("51")
    divergencia_repasse = D("37")
    reembolso_esperado = dict(
        reais=0, buedas=0, receita_gerada_mktplace=receita_gerada_mktplace, divergencia_repasse=divergencia_repasse
    )
    mapa_reembolso[travels_to_move[0].reservation_code] = reembolso_esperado
    mapa_reembolso[travels_to_move[1].reservation_code] = reembolso_esperado
    _assert_simulacao(client_with_logged_staff, tc_leito_cama.id, travels_to_move, mapa_reembolso)


def test_staff_move_passenger_log(client_with_logged_staff, travels_to_move, tc_leito):
    _move_passengers(
        client_with_logged_staff, tc_leito.id, travels_to_move, send_zap=False, send_push_inbox_sms_email=False
    )

    log = ActivityLog.objects.filter(type="move_passengers")
    assert log.count() == 1

    jsondata = log.values_list("jsondata")[0][0]
    log_dict = json.loads(jsondata)

    # TODO: assert datetime. Precisa corrigir tz UTC que vem da fixture
    assert log_dict["dist_origem"] == 0.0
    assert log_dict["dist_destino"] == 810.05


def test_staff_move_passenger_com_deficit_de_vagas_sem_bypass(client_with_logged_staff, travels_to_move, tc_leito_cama):
    url = "/api/staff/groups/mover_buseiros"
    params = {
        "params": json.dumps(
            {
                "trecho_classe_id": tc_leito_cama.id,
                "travel_ids": [t.id for t in travels_to_move],
                "motivo": "Comprou ida no lugar da volta",
                "bypass": False,
            }
        )
    }
    with mock.patch("core.service.remanejamento.remanejamento_svc.simula_mover_buseiros") as m:
        m.return_value = [], 3
        resposta = client_with_logged_staff.post(url, params)
        warning = "O remanejamento vai causar overbooking no trecho classe de destino de 3 assento(s)"
        assert resposta.json()["warning"] == warning


def _assert_simulacao(client_staff, trecho_classe_id, travels, mapa_reembolso_esperado):
    url_simulacao = "/api/staff/groups/simula_mover_buseiros"
    d = {
        "trecho_classe_id": trecho_classe_id,
        "travel_ids": [t.id for t in travels],
        "motivo": "Comprou ida no lugar da volta",
    }
    params = {"params": json.dumps(d)}
    resposta_simulacao = client_staff.post(url_simulacao, params)
    impactos = resposta_simulacao.json()

    mapa_impactos = {impacto.get("reserva"): impacto for impacto in impactos}
    qtd_buseiros = sum([travel.passageiro_set.count() for travel in travels])
    qtd_buseiros_impactados = sum([impacto.get("buseiros") for impacto in impactos])

    assert 200 == resposta_simulacao.status_code
    assert len(travels) == len(impactos)
    for impacto in impactos:
        assert "buseiros" in impacto
        assert "classe_de" in impacto
        assert "classe_para" in impacto
        assert "horario_de" in impacto
        assert "horario_para" in impacto
        assert "custo_reais" in impacto
        assert "custo_buedas" in impacto
        assert impacto["dist_origem"] == 0
        assert impacto["dist_destino"] == 810.05
    for travel in travels:
        assert travel.reservation_code in mapa_impactos
        if mapa_reembolso_esperado:
            esperado = mapa_reembolso_esperado[travel.reservation_code]
            assert esperado["reais"] == D(str(mapa_impactos[travel.reservation_code]["custo_reais"]))
            assert esperado["buedas"] == D(str(mapa_impactos[travel.reservation_code]["custo_buedas"]))
            assert (
                esperado["receita_gerada_mktplace"]
                == mapa_impactos[travel.reservation_code]["receita_gerada_marketplace"]
            )
            assert esperado["divergencia_repasse"] == mapa_impactos[travel.reservation_code]["divergencia_repasse"]

    assert qtd_buseiros == qtd_buseiros_impactados


def _move_passengers(client_staff, trecho_classe_id, travels, send_zap=False, send_push_inbox_sms_email=False):
    url = "/api/staff/groups/mover_buseiros"
    params = {
        "params": json.dumps(
            {
                "trecho_classe_id": trecho_classe_id,
                "travel_ids": [t.id for t in travels],
                "motivo": "Comprou ida no lugar da volta",
                "whatsapp": send_zap,
                "push_inbox_sms_email": send_push_inbox_sms_email,
            }
        )
    }
    resposta = client_staff.post(url, params)
    assert 200 == resposta.status_code


def _assert_email_notificacao(moved_travels):
    notificados = []
    for travel in moved_travels:
        notificados.append(travel.user.email)
        notificados.extend([pax.buseiro.email for pax in travel.passageiro_set.all() if pax.buseiro.email is not None])
    emails_notificacao = [email for email in mail.outbox if "Sua viagem precisou ser alterada" == email.subject]
    assert len(emails_notificacao) == len(set(notificados))
    mail.outbox.clear()


def _assert_travels_canceladas(travels):
    for travel in travels:
        travel.refresh_from_db()
        assert travel.status == "canceled"


@pytest.mark.parametrize("capacidade_gc_destino", [4, 1])
def test_simula_com_deficit_de_vagas(travels_to_move, tc_leito_cama, capacidade_gc_destino):
    tc_leito_cama.grupo_classe.capacidade = capacidade_gc_destino
    tc_leito_cama.grupo_classe.save()
    qtd_pax = sum(t.count_seats for t in travels_to_move)
    data = MoverBuseiroForm(
        travel_ids=[t.id for t in travels_to_move], trecho_classe_id=tc_leito_cama.id, simulacao=True
    )
    _, deficit_de_vagas = remanejamento_svc.simula_mover_buseiros(data)
    assert deficit_de_vagas == max(qtd_pax - capacidade_gc_destino, 0)


def test_simula_marketplace(travels_to_move, tc_leito_cama):
    data = MoverBuseiroForm(
        travel_ids=[t.id for t in travels_to_move], trecho_classe_id=tc_leito_cama.id, simulacao=True
    )
    with mock.patch(
        "core.service.rodoviaria_svc.simula_mover_buseiro_marketplace"
    ) as mock_simula_mover_buseiro_marketplace:
        remanejamento_svc.simula_mover_buseiros(data)
    mock_simula_mover_buseiro_marketplace.assert_called_once()
    assert mock_simula_mover_buseiro_marketplace.call_args[0][1] == tc_leito_cama
    assert sorted(mock_simula_mover_buseiro_marketplace.call_args[0][0], key=lambda k: k.id) == sorted(
        travels_to_move, key=lambda k: k.id
    )


def test_simula_marketplace_view(client_with_logged_staff):
    url_simulacao = "/api/staff/groups/simula_mover_buseiros"
    d = {
        "trecho_classe_id": 3123,
        "travel_ids": [532, 325],
        "motivo": "Comprou ida no lugar da volta",
    }
    params = {"params": json.dumps(d)}
    with mock.patch(
        "core.service.remanejamento.remanejamento_svc.simula_mover_buseiros",
        side_effect=RodoviariaMoverBuseiroException("Erro na API do parceiro"),
    ):
        resposta_simulacao = client_with_logged_staff.post(url_simulacao, params)
    resposta_simulacao = resposta_simulacao.json()
    assert resposta_simulacao["message"] == "Erro na API do parceiro (mover_buseiro_bloqueado)"
    assert resposta_simulacao["blocked"] is True


def test_simula_mover_buseiro_travels_canceladas(travels_to_move, tc_leito_cama):
    for t in travels_to_move:
        t.status = "canceled"
        t.save()
    data = MoverBuseiroForm(
        travel_ids=[t.id for t in travels_to_move], trecho_classe_id=tc_leito_cama.id, motivo="", simulacao=True
    )
    with pytest.raises(ValidationError, match="Sem travels para mover"):
        remanejamento_svc.simula_mover_buseiros(data)


def test_mover_buseiros_cria_historico_remanejamento_com_motivo(travels_to_move, tc_leito_cama):
    data = MoverBuseiroForm(
        travel_ids=[t.id for t in travels_to_move], trecho_classe_id=tc_leito_cama.id, motivo="OUTROS"
    )

    remanejamento_svc.mover_buseiros(data)

    motivos = HistoricoRemanejamento.objects.values_list("motivo", flat=True).distinct()
    assert list(motivos) == ["OUTROS"]


def test_gerar_de_para_poltronas_compradas_com_marcacao_assento_paga(
    travel_com_marcacao_assento, tc_leito_cama, mocker
):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)

    data = MoverBuseiroForm(
        travel_ids=[travel_com_marcacao_assento.id], trecho_classe_id=tc_leito_cama.id, motivo="OUTROS"
    )

    with mock.patch("core.service.remanejamento.remanejamento_svc._gerar_de_para_poltronas_compradas") as mock_gerar:
        mock_gerar.return_value = {1: 15}

        remanejamento_svc.mover_buseiros(data)

        mock_gerar.assert_called()


def test_atribui_poltronas_novos_passageiros_do_remanejamento(travel_com_marcacao_assento, tc_leito_cama, mocker):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)

    data = MoverBuseiroForm(
        travel_ids=[travel_com_marcacao_assento.id], trecho_classe_id=tc_leito_cama.id, motivo="OUTROS"
    )

    with mock.patch(
        "core.service.remanejamento.remanejamento_svc._atribui_poltronas_novos_passageiros_do_remanejamento"
    ) as mock_atribuir_poltronas:
        remanejamento_svc.mover_buseiros(data)

        mock_atribuir_poltronas.assert_called()


def test_remanejamento_com_marcacao_assento_ok(travel_com_marcacao_assento, tc_leito_cama, mocker, globalsettings_mock):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    globalsettings_mock("novo_remanejamento_contabil", True)

    fixtures.gerar_onibus_com_poltronas(tc_leito_cama)

    reservation_code = travel_com_marcacao_assento.reservation_code
    pax_com_marcacao_paga = travel_com_marcacao_assento.passageiro_set.get(poltrona=1)
    pax_sem_marcacao = travel_com_marcacao_assento.passageiro_set.get(poltrona=None)

    data = MoverBuseiroForm(
        travel_ids=[travel_com_marcacao_assento.id], trecho_classe_id=tc_leito_cama.id, motivo="OUTROS"
    )

    remanejamento_svc.mover_buseiros(data)

    old_accops_reserva_marcacao = travel_com_marcacao_assento.accountingoperation_set.filter(source="MARCACAO_ASSENTO")
    old_accops_reserva_marcacao_cancelada = travel_com_marcacao_assento.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    )
    new_travel = Travel.objects.get(reservation_code=reservation_code)
    new_accops_marcacao = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO")
    new_accops_marcacao_cancelada = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO_CANCELADA")
    new_pax_com_marcacao_paga = new_travel.passageiro_set.filter(buseiro=pax_com_marcacao_paga.buseiro).first()
    new_pax_sem_marcacao = new_travel.passageiro_set.filter(buseiro=pax_sem_marcacao.buseiro).first()
    new_item_adicional_pax_com_marcacao_paga = ItemAdicional.objects.filter(pax=new_pax_com_marcacao_paga).first()
    new_item_adicional_pax_sem_marcacao = ItemAdicional.objects.filter(pax=new_pax_sem_marcacao).first()

    old_accops_marcacao = list(old_accops_reserva_marcacao) + list(old_accops_reserva_marcacao_cancelada)

    assert new_travel.id != travel_com_marcacao_assento.id
    assert new_item_adicional_pax_com_marcacao_paga
    assert new_item_adicional_pax_sem_marcacao is None
    assert new_pax_com_marcacao_paga and new_pax_com_marcacao_paga.poltrona
    assert new_pax_sem_marcacao and new_pax_sem_marcacao.poltrona
    assert len(new_accops_marcacao) == 1
    assert len(old_accops_reserva_marcacao) == 1
    assert len(old_accops_reserva_marcacao_cancelada) == 1
    assert not len(new_accops_marcacao_cancelada)
    assert sum(accop.value_real for accop in old_accops_marcacao) == 0


def test_remanejamento_com_marcacao_assento_sem_manter_poltronas(
    travel_com_marcacao_assento, tc_leito_cama, mocker, globalsettings_mock
):
    mocker.patch("commons.feature_flags.is_user_enabled", return_value=True)
    globalsettings_mock("novo_remanejamento_contabil", True)
    fixtures.gerar_onibus_com_poltronas(tc_leito_cama)

    # removendo a poltrona equivalante a do pax que tem marcacao paga
    PoltronaOnibus.objects.filter(onibus=tc_leito_cama.grupo.onibus, poltrona=1).update(ativo=False)

    reservation_code = travel_com_marcacao_assento.reservation_code
    pax_com_marcacao_paga = travel_com_marcacao_assento.passageiro_set.get(poltrona=1)
    pax_sem_marcacao = travel_com_marcacao_assento.passageiro_set.get(poltrona=None)

    data = MoverBuseiroForm(
        travel_ids=[travel_com_marcacao_assento.id], trecho_classe_id=tc_leito_cama.id, motivo="OUTROS"
    )

    remanejamento_svc.mover_buseiros(data)

    old_accops_reserva_marcacao = travel_com_marcacao_assento.accountingoperation_set.filter(source="MARCACAO_ASSENTO")
    old_accops_reserva_marcacao_cancelada = travel_com_marcacao_assento.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    )

    new_travel = Travel.objects.get(reservation_code=reservation_code)
    new_accops_marcacao = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO")
    new_accops_marcacao_cancelada = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO_CANCELADA")
    new_pax_com_marcacao_paga_na_travel_antiga = new_travel.passageiro_set.filter(
        buseiro=pax_com_marcacao_paga.buseiro
    ).first()
    new_pax_sem_marcacao = new_travel.passageiro_set.filter(buseiro=pax_sem_marcacao.buseiro).first()
    new_item_adicional_pax_com_marcacao_paga = ItemAdicional.objects.filter(
        pax=new_pax_com_marcacao_paga_na_travel_antiga
    )
    new_item_adicional_pax_sem_marcacao = ItemAdicional.objects.filter(pax=new_pax_sem_marcacao)

    old_accops_marcacao = list(old_accops_reserva_marcacao) + list(old_accops_reserva_marcacao_cancelada)

    assert new_travel.id != travel_com_marcacao_assento.id
    assert not len(new_item_adicional_pax_com_marcacao_paga)
    assert not len(new_item_adicional_pax_sem_marcacao)
    assert new_pax_com_marcacao_paga_na_travel_antiga and new_pax_com_marcacao_paga_na_travel_antiga.poltrona
    assert new_pax_sem_marcacao and new_pax_sem_marcacao.poltrona
    assert not len(new_accops_marcacao)
    assert len(old_accops_reserva_marcacao) == 1
    assert len(old_accops_reserva_marcacao_cancelada) == 1
    assert not len(new_accops_marcacao_cancelada)
    assert sum(accop.value_real for accop in old_accops_marcacao) == 0
