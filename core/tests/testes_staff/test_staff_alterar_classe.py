import json
from datetime import datetime, timedelta
from decimal import Decimal
from unittest import mock

import pytest
from django.test.client import Client
from model_bakery import baker
from pytest_mock import MockerFixture
from requests_mock.mocker import Mo<PERSON>

from accounting.service import accounting_svc
from adapters.mock import mock_stark_adapter_requests as starkmocker
from adapters.mock.mock_mercadopago_adapter import (
    mock_mercadopago,
)
from adapters.mock.mock_payment_adapter import (
    create_stark_provider_mock,
    gen_BOLETO_PAGO,
)
from commons.dateutils import now, to_default_tz_required
from commons.price_helper import round_price
from core import views_staff
from core.models_commons import ActivityLog
from core.models_company import OnibusClasse
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse
from core.models_rota import FATOR_MAP
from core.models_travel import (
    AlteracaoTravel,
    CupomLead,
    Pagamento,
    RessarcimentoDowngrade,
    Travel,
    TravelComunicada,
)
from core.service import multitrecho_svc, preco_svc, rotas_svc
from core.service.grupos_staff.escalar_onibus_svc import simular_escalar_onibus_task
from core.service.pagamento import estorno_svc
from core.service.remanejamento import remanejamento_svc
from core.service.remanejamento.remanejamento_svc import _get_travels_por_prioridade
from core.service.ressarcimento_downgrade_svc import get_downgrade_value
from core.tests import fixtures
from core.tests.common_operations import assert_price_buckets, clear_outbox
from core.tests.fixtures import fake
from core.tests.prevent_useless_logs import prevent_request_warnings
from core.tests.testes_reserva.test_reserva_base import TestReservaBase


@pytest.fixture
def enable_mercadopago_pix(globalsettings_mock):
    globalsettings_mock("pct_stark_pix", 0)
    globalsettings_mock("pct_mercadopago_pix", 100)


@Mocker()
class TestStaffAlterarClasse(TestReservaBase):
    @classmethod
    def setUpTestData(cls):
        b = cls.b = fixtures.grupos_e_trechos(pessoasnogrupo=1)
        cls.user_staff = fixtures.user_staff(roles=["Operacoes"])

        trecho_vendido = b.trecho_classe_bhsp.trecho_vendido
        cls.MAX_SPLIT_VALUE_CAMA = round_price(trecho_vendido.preco_rodoviaria * FATOR_MAP["leito cama"])
        cls.CLASSE_CAMA = {
            "max_capacity": 2,
            "tipo_assento": "leito cama",
            "trechos": [
                {
                    "max_split_value": round_price(cls.MAX_SPLIT_VALUE_CAMA),
                    "ref_split_value": round_price(cls.MAX_SPLIT_VALUE_CAMA),
                }
            ],
        }

        cls.MAX_SPLIT_VALUE_LEITO = round_price(trecho_vendido.preco_rodoviaria * FATOR_MAP["leito"])
        cls.CLASSE_LEITO = {
            "max_capacity": 2,
            "tipo_assento": "leito",
            "trechos": [
                {
                    "max_split_value": round_price(cls.MAX_SPLIT_VALUE_LEITO),
                    "ref_split_value": round_price(cls.MAX_SPLIT_VALUE_LEITO),
                }
            ],
        }

        cls.MAX_SPLIT_VALUE_SEMI = round_price(trecho_vendido.preco_rodoviaria * FATOR_MAP["semi leito"])
        cls.CLASSE_SEMI = {
            "max_capacity": 2,
            "tipo_assento": "semi leito",
            "trechos": [
                {
                    "max_split_value": round_price(cls.MAX_SPLIT_VALUE_SEMI),
                    "ref_split_value": round_price(cls.MAX_SPLIT_VALUE_SEMI),
                }
            ],
        }

        cls.MAX_SPLIT_VALUE_EXECUTIVO = round_price(trecho_vendido.preco_rodoviaria * FATOR_MAP["executivo"])
        cls.CLASSE_EXECUTIVO = {
            "max_capacity": 2,
            "tipo_assento": "executivo",
            "trechos": [
                {
                    "max_split_value": round_price(cls.MAX_SPLIT_VALUE_EXECUTIVO),
                    "ref_split_value": round_price(cls.MAX_SPLIT_VALUE_EXECUTIVO),
                }
            ],
        }

    def setUp(self):
        self.client_staff = self.client
        self.client_staff.force_login(self.user_staff)

    def _get_params(self, classes, grupo, class_change_reason=None, capacidade=2):
        classes_form = [
            OnibusClasse(
                tipo=classe["tipo_assento"],
                capacidade=classe["max_capacity"] or capacidade,
                capacidade_vendida=classe["max_capacity"] or capacidade,
            )
            for classe in classes
        ]
        matches = preco_svc.procura_match_rateio(grupo, classes_form)
        split_values = [match.valor for match in matches.values()]
        params = {
            "notify_users_class_changed": True,
            "classes": [
                classe_com_trechos(classe, self.b, split_value, classe["max_capacity"])
                for classe, split_value in zip(classes, split_values)
            ],
        }
        if class_change_reason is not None:
            params["class_change_reason"] = class_change_reason
        return params

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_activity_log_tem_new_classe_id(self, m, mock_n):
        initial_classes_cama = [classe_com_pessoas(self.CLASSE_CAMA, 2)]
        initial_classes_leito = [classe_com_pessoas(self.CLASSE_LEITO, 2)]
        with mock_mercadopago(value=68.9):
            g = fixtures.grupo_com_configuracao_especifica(
                self.b,
                initial_classes_cama,
                method="credit_card",
                saldo=Decimal(30),
                provider=Pagamento.Provider.MERCADOPAGO,
            )
        with mock_mercadopago(value=57.9):
            g = fixtures.grupo_com_configuracao_especifica(
                self.b,
                initial_classes_leito,
                method="credit_card",
                saldo=Decimal(30),
                provider=Pagamento.Provider.MERCADOPAGO,
                grupo=g,
            )
        novas_classes = [self.CLASSE_LEITO, self.CLASSE_SEMI]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        expected_allocation = {"leito": 2, "semi leito": 2}
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation, assert_log=True)

    @mock_mercadopago(value=176)
    def test_downgrade_cama_para_leito_e_leito_para_semi_so_com_saldo(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 2), classe_com_pessoas(self.CLASSE_LEITO, 2)]
        saldo = Decimal(200)
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, saldo=saldo, method="pix")
        novas_classes = [self.CLASSE_LEITO, self.CLASSE_SEMI]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        expected_allocation = {"leito": 2, "semi leito": 2}
        t_cama = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito cama")
        t_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito")
        user_cama = t_cama[0].user
        user_leito = t_leito[0].user
        expected_transfers = {
            "leito>semi leito": {
                "pessoas": 2,
                "custo": sum(
                    get_downgrade_value(t, self.CLASSE_LEITO["tipo_assento"], self.CLASSE_SEMI["tipo_assento"])
                    for t in t_leito
                ),
            },
            "leito cama>leito": {
                "pessoas": 2,
                "custo": sum(
                    get_downgrade_value(t, self.CLASSE_CAMA["tipo_assento"], self.CLASSE_LEITO["tipo_assento"])
                    for t in t_cama
                ),
            },
        }
        self._simular_remanejamento(self.client_staff, g.id, params, expected_allocation, expected_transfers)
        self._change_and_assert_grupo_classe(
            self.client_staff, g.id, params, expected_allocation, estorno_downgrade=True
        )

        SALDO_FINAL_CAMA = saldo - self.MAX_SPLIT_VALUE_CAMA
        SALDO_FINAL_LEITO = saldo - self.MAX_SPLIT_VALUE_LEITO
        self._assert_saldo(user_cama, SALDO_FINAL_CAMA, Decimal(0))
        self._assert_saldo(user_leito, SALDO_FINAL_LEITO, Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_upgrade_leito_para_cama_e_semi_para_leito(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_LEITO, 2), classe_com_pessoas(self.CLASSE_SEMI, 2)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
        novas_classes = [self.CLASSE_CAMA, self.CLASSE_LEITO]
        params = self._get_params(novas_classes, g)
        expected_allocation = {"leito cama": 2, "leito": 2}
        user_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito").first().user
        user_semi = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="semi leito").first().user
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)
        assert CupomLead.objects.filter(lead=user_leito.lead).count() == 0
        assert CupomLead.objects.filter(lead=user_semi.lead).count() == 0
        self._assert_saldo(user_semi, Decimal(0), Decimal(0))
        self._assert_saldo(user_leito, Decimal(0), Decimal(0))
        travel_semi_para_leito = Travel.objects.filter(user=user_semi).first()
        travel_leito_para_cama = Travel.objects.filter(user=user_leito).first()
        self.assertEqual(travel_semi_para_leito.grupo_classe.tipo_assento, "leito")
        self.assertEqual(travel_leito_para_cama.grupo_classe.tipo_assento, "leito cama")

    @pytest.mark.usefixtures("pix_chain_use_stark_first")
    def test_cama_mantem_leito_downgrade_para_executivo_estorno_pix(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 2), classe_com_pessoas(self.CLASSE_LEITO, 2)]
        starkmocker.mock_gerar_pix(m, amount=17600, status="paid")
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, method="pix")
        novas_classes = [self.CLASSE_CAMA, self.CLASSE_EXECUTIVO]
        params = self._get_params(novas_classes, g)
        expected_allocation = {"leito cama": 2, "executivo": 2}
        user_cama = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito cama").first().user
        user_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito").first().user
        starkmocker.mock_estornar_pix(m, 17600, 0)
        self._change_and_assert_grupo_classe(
            self.client_staff,
            g.id,
            params,
            expected_allocation,
            estorno_downgrade=True,
        )
        self._assert_saldo(user_cama, Decimal(0), Decimal(0))
        registros_contabeis = self._assert_saldo(user_leito, Decimal(0), Decimal(0))

        # Não estornamos mais após um downgrade
        assert "ESTORNO" not in {op["source"] for op in registros_contabeis}

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_downgrade_quem_reservou_primeiro_tem_prioridade_para_ficar_na_classe_original(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 2), classe_com_pessoas(self.CLASSE_LEITO, 2)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
        self.CLASSE_LEITO["max_capacity"] = 1
        self.CLASSE_SEMI["max_capacity"] = 5
        novas_classes = [self.CLASSE_LEITO, self.CLASSE_SEMI]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        expected_allocation = {"leito": 1, "semi leito": 3}
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_upgrade_executivo_para_leito_e_semi_para_cama(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_EXECUTIVO, 2), classe_com_pessoas(self.CLASSE_SEMI, 2)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
        novas_classes = [self.CLASSE_LEITO, self.CLASSE_CAMA]
        params = self._get_params(novas_classes, g)
        expected_allocation = {"leito": 2, "leito cama": 2}
        user_executivo = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="executivo").first().user
        user_semi = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="semi leito").first().user
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)
        self._assert_saldo(user_executivo, Decimal(0), Decimal(0))
        self._assert_saldo(user_semi, Decimal(0), Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_nao_muda_nada_se_sobrar_gente(self, m):
        with prevent_request_warnings():
            initial_classes = [classe_com_pessoas(self.CLASSE_EXECUTIVO, 2), classe_com_pessoas(self.CLASSE_SEMI, 2)]
            g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
            params = self._get_params([self.CLASSE_LEITO], g)
            expected_allocation = {"executivo": 2, "semi leito": 2}
            user_executivo = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="executivo").first().user
            user_semi = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="semi leito").first().user
            self._change_and_assert_grupo_classe(
                self.client_staff, g.id, params, expected_allocation, expected_error="Tem mais passageiros que vagas!"
            )

            self._assert_saldo(user_executivo, Decimal(0), Decimal(0))
            self._assert_saldo(user_semi, Decimal(0), Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_nao_deixa_remanejar_pra_onibus_com_menos_vagas_que_occ_por_trecho(self, m):
        with prevent_request_warnings():
            initial_classes = [classe_com_pessoas(self.CLASSE_EXECUTIVO, 2), classe_com_pessoas(self.CLASSE_SEMI, 2)]
            g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
            self.CLASSE_LEITO["max_capacity"] = 1
            params = self._get_params([self.CLASSE_LEITO], g)

            expected_allocation = {}
            self._change_and_assert_grupo_classe(
                self.client_staff, g.id, params, expected_allocation, expected_error="Tem mais passageiros que vagas!"
            )

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_downgrade_travel_com_dois_pax(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 4), classe_com_pessoas(self.CLASSE_LEITO, 4)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, companion=True, provider="stark")
        self.CLASSE_LEITO["max_capacity"] = 3
        self.CLASSE_SEMI["max_capacity"] = 8
        novas_classes = [self.CLASSE_LEITO, self.CLASSE_SEMI]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        expected_allocation = {"leito": 2, "semi leito": 6}
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_nao_conseguiu_remanejar_travel_agregado(self, m):
        with prevent_request_warnings():
            initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 4), classe_com_pessoas(self.CLASSE_LEITO, 4)]
            g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, companion=True, provider="stark")
            self.CLASSE_LEITO["max_capacity"] = 3
            self.CLASSE_SEMI["max_capacity"] = 5
            novas_classes = [self.CLASSE_LEITO, self.CLASSE_SEMI]
            params = self._get_params(novas_classes, g, "por que deu ruim no busao")
            expected_allocation = {"leito cama": 4, "leito": 4}
            user_cama = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito cama").first().user
            user_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito").first().user
            self._change_and_assert_grupo_classe(
                self.client_staff, g.id, params, expected_allocation, expected_error="Não foi possível remanejar"
            )
            self._assert_saldo(user_cama, Decimal(0), Decimal(0))
            self._assert_saldo(user_leito, Decimal(0), Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(create_boleto_value=gen_BOLETO_PAGO()),
    )
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remanejar_travels_cancelados(self, mock_n, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_EXECUTIVO, 2), classe_com_pessoas(self.CLASSE_SEMI, 2)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider=Pagamento.Provider.STARK)
        self._cancelar_metade_das_viagens(g)
        g.refresh_from_db()
        novas_classes = [self.CLASSE_LEITO]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        expected_allocation = {"leito": 2}
        user_executivo = (
            Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="executivo", status="pending").first().user
        )
        user_semi = (
            Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="semi leito", status="pending").first().user
        )
        user_executivo_canceled = (
            Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="executivo", status="canceled").first().user
        )
        user_semi_canceled = (
            Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="semi leito", status="canceled").first().user
        )
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)
        self._assert_saldo(user_executivo, Decimal(0), Decimal(0))
        self._assert_saldo(user_semi, Decimal(0), Decimal(0))
        self._assert_saldo(user_executivo_canceled, self.MAX_SPLIT_VALUE_EXECUTIVO, Decimal(0))
        self._assert_saldo(user_semi_canceled, self.MAX_SPLIT_VALUE_SEMI, Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_erro_tipo_assento_duplicado(self, m):
        with prevent_request_warnings():
            initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 2), classe_com_pessoas(self.CLASSE_LEITO, 2)]
            g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
            novas_classes = [self.CLASSE_SEMI, self.CLASSE_SEMI, self.CLASSE_EXECUTIVO]
            params = self._get_params(novas_classes, g, "por que deu ruim no busao")
            expected_allocation = {"leito cama": 2, "leito": 2}
            user_cama = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito cama").first().user
            user_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito").first().user
            self._change_and_assert_grupo_classe(
                self.client_staff, g.id, params, expected_allocation, expected_error="tipo_assento duplicado"
            )
            self._assert_saldo(user_cama, Decimal(0), Decimal(0))
            self._assert_saldo(user_leito, Decimal(0), Decimal(0))

    @mock.patch(
        "core.payment.providers.stark.StarkProvider",
        create_stark_provider_mock(),
    )
    def test_erro_trecho_vendido_duplicado(self, m):
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 2), classe_com_pessoas(self.CLASSE_LEITO, 2)]
        g = fixtures.grupo_com_configuracao_especifica(self.b, initial_classes, provider="stark")
        self.CLASSE_SEMI["max_capacity"] = 20
        novas_classes = [self.CLASSE_SEMI]
        params = self._get_params(novas_classes, g, "por que deu ruim no busao")
        # duplica a classe
        params["classes"].append(params["classes"][0])
        expected_allocation = {"leito cama": 2, "leito": 2}
        user_cama = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito cama").first().user
        user_leito = Travel.objects.filter(grupo=g, grupo_classe__tipo_assento="leito").first().user
        self._change_and_assert_grupo_classe(
            self.client_staff, g.id, params, expected_allocation, expected_error="tipo_assento duplicado"
        )
        self._assert_saldo(user_cama, Decimal(0), Decimal(0))
        self._assert_saldo(user_leito, Decimal(0), Decimal(0))

    @mock_mercadopago(value=176)
    def test_downgrade_de_viagem_gratis_soh_da_voucher(self, m):
        voucher_discount_100porcento = _gera_voucher(discount=Decimal(1))
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 1)]
        g = fixtures.grupo_com_configuracao_especifica(
            self.b, initial_classes, voucher=voucher_discount_100porcento, pay_value=Decimal(0), method="pix"
        )
        novas_classes = [self.CLASSE_LEITO]
        params = self._get_params(novas_classes, g)
        expected_allocation = {"leito": 1}
        t_cama = Travel.objects.get(grupo=g, grupo_classe__tipo_assento="leito cama")
        user_cama = t_cama.user
        expected_transfers = {
            "leito cama>leito": {
                "pessoas": 1,
                "custo": get_downgrade_value(
                    t_cama, self.CLASSE_CAMA["tipo_assento"], self.CLASSE_LEITO["tipo_assento"]
                ),
            },
        }
        self._simular_remanejamento(
            self.client_staff,
            g.id,
            params,
            expected_allocation,
            expected_transfers,
        )
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)
        self._assert_saldo(user_cama, value_real=Decimal(0), value_bueda=Decimal("0"))

    @mock_mercadopago(value=176)
    @mock.patch("core.service.pagamento.pagamento_svc._get_provider_pix", return_value=Pagamento.Provider.MERCADOPAGO)
    def test_downgrade_de_viagem_preco_fixo(self, m_mp, m_provider):
        voucher_fixed_value_10 = _gera_voucher(fixed_value=Decimal(10))
        initial_classes = [classe_com_pessoas(self.CLASSE_CAMA, 1)]
        g = fixtures.grupo_com_configuracao_especifica(
            self.b, initial_classes, voucher=voucher_fixed_value_10, pay_value=Decimal(10), method="pix"
        )
        novas_classes = [self.CLASSE_LEITO]
        params = self._get_params(novas_classes, g)
        expected_allocation = {"leito": 1}
        t_cama = Travel.objects.get(grupo=g, grupo_classe__tipo_assento="leito cama")
        expected_transfers = {
            "leito cama>leito": {
                "pessoas": 1,
                "custo": get_downgrade_value(
                    t_cama, self.CLASSE_CAMA["tipo_assento"], self.CLASSE_LEITO["tipo_assento"]
                ),
            },
        }
        self._simular_remanejamento(self.client_staff, g.id, params, expected_allocation, expected_transfers)
        self._change_and_assert_grupo_classe(self.client_staff, g.id, params, expected_allocation)

    def _cancelar_metade_das_viagens(self, grupo):
        travels_executivo = Travel.objects.filter(grupo=grupo, grupo_classe__tipo_assento="executivo").order_by(
            "-created_on"
        )[:1]
        travels_semi = Travel.objects.filter(grupo=grupo, grupo_classe__tipo_assento="semi leito").order_by(
            "-created_on"
        )[:1]
        for travel in list(travels_executivo) + list(travels_semi):
            client = Client()
            client.force_login(travel.user)
            self.cancela_viagem(client, travel.id)
        clear_outbox()

    def _change_and_assert_grupo_classe(
        self,
        client,
        grupo_id,
        params,
        expected_allocation,
        expected_error=None,
        assert_log=False,
        estorno_downgrade=False,
    ):
        params["groups"] = [grupo_id]
        params["estorno_downgrade_automatico"] = estorno_downgrade
        with self.captureOnCommitCallbacks(execute=True):
            r = client.post("/api/staff/groups/bulkchangeclass", {"params": json.dumps(params)})
        if assert_log:
            log = ActivityLog.objects.get(type="admin_remaneja_group")
            self.assertTrue(all(transfer["new_classe_id"] for transfer in log.jsondata_dict["transfers"]))
        self.assertEqual(400 if expected_error else 200, r.status_code)
        res = r.json()
        data = {"group_id": grupo_id}
        r1 = client.get("/api/staff/efops/groups/group", {"params": json.dumps(data)})

        group = r1.json(parse_float=Decimal)
        if expected_error:
            self.assertTrue(expected_error in res["error"])
        else:
            self.assertIsNotNone(group["id"])
            trechos_classes = group["trechos_classes"]
            classes = json.loads(json.dumps(params["classes"]), parse_float=Decimal)
            for classe in classes:
                classe_criada = next(c for c in group["classes"] if c["tipo_assento"] == classe["tipo_assento"])
                for tv_key in trechos_classes:
                    for c_key in trechos_classes[tv_key]:
                        tc = trechos_classes[tv_key][c_key]
                        if tc["tipo_assento"] == classe["tipo_assento"]:
                            classe_criada.setdefault("trechos", []).append(
                                {
                                    "id": int(tv_key),
                                    "max_split_value": tc["max_split_value"],
                                    "ref_split_value": tc["ref_split_value"],
                                }
                            )
                for param in classe:
                    self.assertEqual(classe[param], classe_criada[param])
        # validar alocação final
        for tipo_assento in expected_allocation.keys():
            classe = next(c for c in group["classes"] if c["tipo_assento"] == tipo_assento)
            self.assertEqual(expected_allocation[tipo_assento], classe["pessoas"])
        return group

    def _simular_remanejamento(self, client, grupo_id, params, expected_allocation, expected_transfers):
        clear_outbox()
        data = {"group_id": grupo_id}
        grupo_antes = client.get("/api/staff/efops/groups/group", {"params": json.dumps(data)}).content.decode("utf-8")
        params["groups"] = [grupo_id]
        r = client.post("/api/staff/groups/simularremanejamentomassivo", {"params": json.dumps(params)})
        grupo_depois = client.get("/api/staff/efops/groups/group", {"params": json.dumps(data)}).content.decode("utf-8")
        self.assertEqual(grupo_antes, grupo_depois)
        self.assertEqual(200, r.status_code)
        res = r.json(parse_float=Decimal)
        self._validar_transfers(res["transfers"], expected_transfers)
        return res

    def _validar_transfers(self, computed_transfers, expected_transfers):
        self.assertEqual(len(computed_transfers), len(expected_transfers))
        # validar transfers
        for transfer in expected_transfers:
            expected_transfer = expected_transfers[transfer]
            computed_transfer = computed_transfers[transfer]
            self.assertEqual(expected_transfer["pessoas"], computed_transfer["pessoas"])
            self.assertEqual(expected_transfer["custo"], Decimal(str(computed_transfer["custo"])))

    def _assert_saldo(self, user, value_real, value_bueda):
        operations = accounting_svc.get_extrato(user)["doperations"]
        saldo = accounting_svc.get_saldo(user)
        self.assertEqual(value_real, sum([op["value_real"] for op in operations]))
        self.assertEqual(value_bueda, sum([op["value_bueda"] for op in operations]))
        self.assertEqual(value_real, saldo["reais"])
        self.assertEqual(value_bueda, saldo["buedas"])
        return operations


def classe_com_trechos(classe, b, split_value, capacidade=2):
    c = {**classe, "max_capacity": capacidade}
    c["trechos"] = c["trechos"].copy()
    c["trechos"][0]["id"] = b.trecho_classe_bhsp.trecho_vendido.id
    c["trechos"][0]["max_split_value"] = split_value
    c["trechos"][0]["ref_split_value"] = split_value
    return c


def classe_com_pessoas(classe, pessoas):
    return {**classe, "pessoas": pessoas, "max_capacity": max(pessoas, classe["max_capacity"])}


@pytest.fixture
def grupo():
    rota = baker.make("core.Rota", duracao_total=timedelta(minutes=60), distancia_total=100)
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota)
    return baker.make(
        "core.Grupo",
        rota=rota,
        rotina_onibus=rotina_onibus,
        datetime_ida=to_default_tz_required(datetime(2020, 9, 22, 23, 30)),
    )


@pytest.fixture
def itinerario(grupo):
    return [
        baker.make(
            "core.Checkpoint", idx=0, rota=grupo.rota, tempo_embarque=timedelta(minutes=0), duracao=timedelta(minutes=0)
        ),
        # Essa aqui vai sair meia noite.
        baker.make(
            "core.Checkpoint",
            idx=1,
            rota=grupo.rota,
            tempo_embarque=timedelta(minutes=10),
            duracao=timedelta(minutes=20),
        ),
        baker.make(
            "core.Checkpoint",
            idx=2,
            rota=grupo.rota,
            tempo_embarque=timedelta(minutes=0),
            duracao=timedelta(minutes=10),
        ),
    ]


@pytest.fixture
def grupo_classe(grupo):
    return baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito", capacidade=10)


@pytest.fixture
def grupo_classe_fechado(grupo):
    user_rotas = fixtures.user_rotas()
    return baker.make(
        "core.GrupoClasse",
        grupo=grupo,
        closed=True,
        tipo_assento="executivo",
        closed_reason="Alguém quis fechar",
        closed_by=user_rotas,
        capacidade=10,
    )


@pytest.fixture
def trecho_vendido(db, grupo, itinerario):
    return baker.make(
        "core.TrechoVendido",
        rota=grupo.rota,
        origem=itinerario[1].local,
        destino=itinerario[2].local,
        origem__cidade__timezone="America/Sao_Paulo",
        origem__cidade__sigla="SAO",
        destino__cidade__sigla="RIO",
    )


@pytest.fixture
def trecho_classe(grupo, grupo_classe, trecho_vendido):
    return baker.make(
        "core.TrechoClasse",
        datetime_ida=grupo.datetime_ida.replace(hour=00, minute=00),
        max_split_value=Decimal("10.00"),
        ref_split_value=Decimal("10.00"),
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        price_manager=baker.make("core.PriceManager", value=Decimal("10.00"), ref_value=Decimal("10.00")),
    )


@pytest.fixture
def trecho_classe_gc_fechado(grupo, grupo_classe_fechado, trecho_vendido):
    return baker.make(
        "core.TrechoClasse",
        datetime_ida=grupo.datetime_ida.replace(hour=23, minute=59),
        max_split_value=Decimal("10.00"),
        ref_split_value=Decimal("10.00"),
        grupo=grupo,
        grupo_classe=grupo_classe_fechado,
        trecho_vendido=trecho_vendido,
        price_manager=baker.make("core.PriceManager", value=Decimal("10.00"), ref_value=Decimal("10.00")),
    )


@pytest.fixture
def trecho_classe_gc_aberto(trecho_vendido, grupo_classe, grupo):
    return baker.make(
        "core.TrechoClasse",
        datetime_ida=grupo.datetime_ida.replace(hour=23, minute=59),
        max_split_value=Decimal("10.00"),
        ref_split_value=Decimal("10.00"),
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        price_manager=baker.make("core.PriceManager", value=Decimal("10.00"), ref_value=Decimal("10.00")),
    )


@pytest.fixture
def trecho_classe_bucketizado(db, trecho_classe_gc_aberto):
    pm = baker.make("core.PriceManager")
    baker.make("core.PriceBucket", value=Decimal("10.0"), tamanho=2, price_manager=pm)
    baker.make("core.PriceBucket", value=Decimal("12.0"), tamanho=3, price_manager=pm)
    baker.make("core.PriceBucket", value=Decimal("14.0"), tamanho=5, price_manager=pm)
    trecho_classe_gc_aberto.price_manager = pm
    trecho_classe_gc_aberto.save(update_fields=["price_manager", "updated_on"])
    return trecho_classe_gc_aberto


def test_meia_noite_nunca_mais(trecho_classe):
    grupo = trecho_classe.grupo
    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )
    trecho_classe_novo = grupo.trechoclasse_set.first()

    # Criou um novo trecho_classe mesmo.
    assert trecho_classe.id != trecho_classe_novo.id

    expected = to_default_tz_required("2020-09-22T23:59:00")
    assert to_default_tz_required(trecho_classe_novo.datetime_ida) == expected


def test_bulk_change_class_preenche_alteracao_travel(grupo_factory, travel_factory):
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trechos_classe = list(grupo.trechoclasse_set.all())
    travels = [
        travel_factory(
            grupo_da_travel=grupo,
            qtd_pax=qtd_pax,
            trecho_vendido=trechos_classe[0].trecho_vendido,
            trecho_classe=trechos_classe[0],
            travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
        )
        for qtd_pax in range(1, 4)
    ]

    params = {
        "groups": [grupo.id],
        "classes": _classe_params("executivo", grupo, max_capacity=10),
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )

    historicos_alteracao = AlteracaoTravel.objects.all()
    historico_alteracao_map = {hist.travel.id: hist for hist in historicos_alteracao}
    assert len(historicos_alteracao) == 3

    for travel in travels:
        alteracao = historico_alteracao_map[travel.id]
        assert alteracao
        assert grupo_classe
        assert alteracao.antigo_tipo_assento == grupo_classe.tipo_assento
        assert alteracao.novo_tipo_assento == "executivo"
        assert alteracao.tipo == AlteracaoTravel.TipoAlteracao.MUDANCA_DE_CLASSE


def test_classe_mantida_nao_preenche_alteracao_travel(grupo_factory, travel_factory, rota_multitrecho):
    rotina_onibus = baker.make("core.RotinaOnibus", rota=rota_multitrecho)
    grupo_exec = grupo_factory(data=now(), classe="executivo", rota=rota_multitrecho, rotina_onibus=rotina_onibus)
    grupo_semi = grupo_factory(data=now(), classe="semi leito", rota=rota_multitrecho, rotina_onibus=rotina_onibus)
    trecho_vendido = rota_multitrecho.get_trechos_vendidos()[0]

    for grupo in [grupo_exec, grupo_semi]:
        trecho_classe = TrechoClasse.objects.filter(grupo=grupo, trecho_vendido=trecho_vendido).first()
        grupo_classe = trecho_classe.grupo_classe
        travel_factory(
            grupo_da_travel=grupo,
            qtd_pax=1,
            trecho_vendido=trecho_classe.trecho_vendido,
            trecho_classe=trecho_classe,
            travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
        )

    params = {
        "groups": [grupo.id],
        "classes": _classe_params("executivo", grupo, max_capacity=10),
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )

    historicos_alteracao = AlteracaoTravel.objects.all()
    assert len(historicos_alteracao) == 1


def classe_params(tipo_assento, trecho_vendido_id, max_capacity=None, max_split_value=None):
    return {
        "max_capacity": max_capacity or fake.pyint(1, 54),
        "tipo_assento": tipo_assento,
        "trechos": [
            {
                "id": trecho_vendido_id,
                "max_split_value": max_split_value
                or round(fake.pydecimal(positive=True, max_value=Decimal("1000.00")), 2),
            }
        ],
    }


def _classe_params(tipo_assento, grupo, max_capacity=None, max_split_value=None):
    classe = {
        "max_capacity": max_capacity or fake.pyint(1, 54),
        "tipo_assento": tipo_assento,
        "trechos": [],
    }
    for trecho_classe in grupo.trechoclasse_set.all():
        classe["trechos"].append(
            {
                "id": trecho_classe.trecho_vendido_id,
                "max_split_value": max_split_value or round(fake.pydecimal(positive=True, max_value=Decimal("259")), 2),
            }
        )
    return [classe]


def test_classe_nova_em_grupo_fechado_vem_fechada_com_closed_reason_original(trecho_classe_gc_fechado):
    grupo = trecho_classe_gc_fechado.grupo
    closed_reason_original = trecho_classe_gc_fechado.grupo_classe.closed_reason
    # executivo (fechado) -> leito  (fechado)
    classes = [classe_params("leito", trecho_classe_gc_fechado.trecho_vendido_id)]
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )
    novo_grupo_classe_leito = grupo.grupoclasse_set.filter(tipo_assento="leito").values_list(
        "closed", "closed_reason", "closed_by"
    )[0]
    assert novo_grupo_classe_leito == (True, closed_reason_original, None)


def test_classe_bucketizada_mantem_bucketizacao(trecho_classe_bucketizado):
    grupo = trecho_classe_bucketizado.grupo
    params = {
        "groups": [grupo.id],
        "classes": _classe_params("executivo", grupo, max_capacity=20),
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )
    novo_trecho_classe = grupo.trechoclasse_set.first()
    # vai arredondar os valores dos buckets para a nova classe
    buckets_pos_remanejamento = [
        {"tamanho": 4, "max_split_value": "7.90"},
        {"tamanho": 6, "max_split_value": "9.90"},
        {"tamanho": 10, "max_split_value": "11.90"},
    ]
    assert_price_buckets(novo_trecho_classe, buckets_pos_remanejamento)


@mock.patch("core.service.remanejamento.remanejamento_svc._validate_classes")
@mock.patch("core.service.remanejamento.remanejamento_svc._get_travels_por_prioridade")
@mock.patch("core.service.remanejamento.remanejamento_svc._classes_novas_por_tipo_assento")
@mock.patch("core.service.remanejamento.remanejamento_svc._get_new_trechos_classes")
def test_numero_queries_simular_remanejamento(
    mock_get_new_trechos,
    mock_classes_novas,
    mock_get_travels,
    mock_validate_classes,
    grupo_com_rota_varios_trechos_vendidos,
    travel_factory,
    django_assert_num_queries,
):
    grupo = grupo_com_rota_varios_trechos_vendidos
    user = baker.make("User")

    tcs = TrechoClasse.objects.all()
    for tc in tcs:
        tc.closed = True
        tc.closed_reason = "alguma razao"
        tc.closed_by_id = user.id
    TrechoClasse.objects.bulk_update(tcs, ["closed", "closed_reason", "closed_by_id"])

    trechos_vendidos = grupo.rota.get_trechos_vendidos().values_list(flat=True)

    trechos = [
        {
            "id": tv,
            "max_split_value": "10.00",
        }
        for tv in trechos_vendidos
    ]
    classes = [
        {"max_capacity": 42, "tipo_assento": assento, "trechos": trechos} for assento in ("cama", "convencional")
    ]

    params = {
        "class_change_reason": "porque não encontramos ônibus disponível da classe que você desejava",
        "notify_users_class_changed": True,
        "groups": [grupo.id],
        "classes": classes,
    }
    grupos = remanejamento_svc.get_grupos_para_remanejamento(params["groups"])
    grupo = grupos[0]
    grupo_cm = multitrecho_svc.prepare_capacity_manager_from_grupo(grupo)
    trechos_vendidos_map = remanejamento_svc._build_trechos_vendidos_map(params["classes"])
    with django_assert_num_queries(0):
        remanejamento_svc.simular_remanejamento(grupo, params, trechos_vendidos_map, grupo_cm)


def test_get_travels_por_prioridade(travel_factory):
    # Dado travels de um mesmo grupo classe
    t1 = travel_factory(qtd_pax=7)
    t2 = travel_factory(grupo_da_travel=t1.grupo, qtd_pax=1)
    t3 = travel_factory(grupo_da_travel=t1.grupo, qtd_pax=2)
    t4 = travel_factory(grupo_da_travel=t1.grupo, qtd_pax=5)

    # ao ordenar as travels por prioridade
    resp = _get_travels_por_prioridade(t1.grupo)
    # retornar as travels ordenadas por quantidade de assentos (reverso)
    assert resp == [t1, t4, t3, t2]


def test_alterar_classe_preserva_estado_de_fechamento_das_classes(trecho_classe_gc_fechado, trecho_classe_gc_aberto):
    user_rotas = trecho_classe_gc_fechado.grupo_classe.closed_by
    grupo = trecho_classe_gc_fechado.grupo
    classes = [
        classe_params("executivo", trecho_classe_gc_fechado.trecho_vendido_id),
        classe_params("leito", trecho_classe_gc_aberto.trecho_vendido_id),
    ]
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    remanejamento_svc.bulk_change_class(
        params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
    )
    grupo_classe_novo = grupo.grupoclasse_set.filter(tipo_assento="executivo").values_list(
        "closed", "closed_reason", "closed_by"
    )[0]
    grupo_classe_novo_leito = grupo.grupoclasse_set.filter(tipo_assento="leito").values_list(
        "closed", "closed_reason", "closed_by"
    )[0]
    assert grupo_classe_novo == (True, "Alguém quis fechar", user_rotas.id)
    assert grupo_classe_novo_leito == (False, None, None)


def _gera_voucher(fixed_value=None, value=None, discount=None):
    voucher = baker.make(
        "Voucher",
        code=fixtures.fake.pystr_format("#" * 6),
        cupom__fixed_value=fixed_value,
        cupom__value=value,
        cupom__discount=discount,
        cupom__due_date=to_default_tz_required(now() + timedelta(days=3)),
    )
    return voucher.code


def test_remanejamento_massivo(tres_grupos_com_classes_distintas_e_travels, rf, user_ops):
    grupo_1, grupo_2, grupo_3 = tres_grupos_com_classes_distintas_e_travels

    trechos_vendidos = list(grupo_1.rota.get_trechos_vendidos())
    trechos_vendidos_id = [tv.id for tv in trechos_vendidos]
    trecho_vendido = trechos_vendidos[0]
    max_split_value_executivo = trecho_vendido.preco_rodoviaria * FATOR_MAP["executivo"]
    classe_executivo = {
        "max_capacity": 2,
        "tipo_assento": "executivo",
        "trechos": [
            {
                "max_split_value": max_split_value_executivo,
                "ref_split_value": max_split_value_executivo,
            }
        ],
    }
    classes = _param_classes_multi_trechos(classe_executivo, trechos_vendidos_id)
    params = {
        "groups": [grupo_1.id, grupo_2.id, grupo_3.id],
        "class_change_reason": "por que deu ruim no busao",
        "notify_users_class_changed": True,
        "classes": [classes],
    }

    req_1 = rf.post("/api/staff/groups/simularremanejamentomassivo", {"params": json.dumps(params)})
    req_1.user = user_ops
    r_1 = views_staff.simular_remanejamento_massivo(req_1)
    assert 200 == r_1.status_code
    resp_1 = json.loads(r_1.content)

    # grupo_1: leito -> 2 pax/semi leito  -> 2 pax
    # grupo_2: leito -> 2 pax/leito cama -> 2 pax
    # grupo_3: semi leito-> 4 pax
    expected = {"leito>executivo": 4, "semi leito>executivo": 6, "leito cama>executivo": 2}
    assert len(resp_1["transfers"].keys()) > 0
    for key in resp_1["transfers"]:
        assert resp_1["transfers"][key]["pessoas"] == expected[key]

    req_2 = rf.post("/api/staff/groups/bulkchangeclass", {"params": json.dumps(params)})
    req_2.user = user_ops
    r_2 = views_staff.bulk_change_class(req_2)
    assert 200 == r_2.status_code
    assert "error" not in r_2


def test_remanejamento_massivo_params_string(tres_grupos_com_classes_distintas_e_travels, rf, user_ops):
    grupo_1, grupo_2, grupo_3 = tres_grupos_com_classes_distintas_e_travels
    """"""
    trechos_vendidos = list(grupo_1.rota.get_trechos_vendidos())
    trechos_vendidos_id = [tv.id for tv in trechos_vendidos]
    params = {
        "groups": [grupo_1.id, grupo_2.id, grupo_3.id],
        "class_change_reason": "por que deu ruim no busao",
        "notify_users_class_changed": True,
        "classes": [
            {
                "max_capacity": "10",
                "tipo_assento": "executivo",
                "trechos": [
                    {
                        "max_split_value": Decimal("65.0000"),
                        "ref_split_value": Decimal("65.0000"),
                        "id": trechos_vendidos_id[0],
                    },
                    {
                        "max_split_value": Decimal("65.0000"),
                        "ref_split_value": Decimal("65.0000"),
                        "id": trechos_vendidos_id[1],
                    },
                    {
                        "max_split_value": Decimal("65.0000"),
                        "ref_split_value": Decimal("65.0000"),
                        "id": trechos_vendidos_id[2],
                    },
                ],
            }
        ],
    }
    req_1 = rf.post("/api/staff/groups/simularremanejamentomassivo", {"params": json.dumps(params)})
    req_1.user = user_ops
    r_1 = views_staff.simular_remanejamento_massivo(req_1)
    assert 200 == r_1.status_code


@pytest.fixture
def tres_grupos_com_classes_distintas_e_travels(grupo_factory, travel_factory):
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo_1 = grupo_factory(now() + timedelta(minutes=10), classe="leito", capacidade=2, rotina_onibus=rotina_onibus)
    _add_classe_ao_grupo(grupo_1, classe="semi leito")
    grupo_2 = grupo_factory(now() + timedelta(minutes=15), classe="leito", capacidade=2, rotina_onibus=rotina_onibus)
    _add_classe_ao_grupo(grupo_2, classe="leito cama")
    grupo_3 = grupo_factory(
        now() + timedelta(minutes=20), classe="semi leito", capacidade=4, rotina_onibus=rotina_onibus
    )

    grupos_list = [grupo_1, grupo_2, grupo_3]
    tcs = TrechoClasse.objects.select_related("grupo_classe", "trecho_vendido").filter(grupo__in=grupos_list)

    tv = tcs.first().trecho_vendido

    for tc in tcs.filter(trecho_vendido=tv, grupo__in=grupos_list):
        for pax in range(tc.grupo_classe.capacidade):  # vai criar 1 pax por travel até o limite da capacidade
            travel_factory(
                grupo_da_travel=tc.grupo,
                qtd_pax=1,
                trecho_vendido=tv,
                trecho_classe=tc,
                travel_kwargs={"grupo_classe": tc.grupo_classe, "max_split_value": tc.max_split_value},
            )
    return grupos_list


def _add_classe_ao_grupo(grupo, classe="leito", capacidade=2):
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento=classe, capacidade=capacidade)
    for trecho_vendido in grupo.rota.get_trechos_vendidos():
        baker.make(
            TrechoClasse,
            grupo=grupo,
            trecho_vendido=trecho_vendido,
            grupo_classe=grupo_classe,
            max_split_value=Decimal(79),
            price_manager=baker.make("core.PriceManager", value=Decimal(79)),
        )

    rotas_svc.update_grupo_trechos_classes_horarios(grupo)


def _classe_com_trechos(classe, trecho_vendido_id, capacidade=2):
    c = {**classe, "max_capacity": capacidade}
    c["trechos"] = c["trechos"].copy()
    c["trechos"][0]["id"] = trecho_vendido_id
    return c


def _param_classes_multi_trechos(classe, trechos_vendidos_id):
    classes = _classe_com_trechos(classe, trechos_vendidos_id[0], "10")
    trechos = []
    trecho = classes["trechos"][0].copy()
    for id in trechos_vendidos_id:
        trecho["id"] = id
        trechos.append(trecho.copy())
    classes["trechos"] = trechos
    return classes


def test_bulk_change_class_num_de_queries(
    grupo_factory, travel_factory, django_assert_num_queries, django_capture_on_commit_callbacks, globalsettings_mock
):
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    assert grupo_classe is not None
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    assert trecho_classe is not None
    [
        travel_factory(
            grupo_da_travel=grupo,
            qtd_pax=qtd_pax,
            trecho_vendido=trecho_classe.trecho_vendido,
            trecho_classe=trecho_classe,
            travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
        )
        for qtd_pax in range(1, 4)
    ]
    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    globalsettings_mock("company_ids_marketplace_bucketizadas", [])

    with django_assert_num_queries(59), django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
        )


def test_bulk_change_class_num_de_queries_motivo_id(
    grupo_factory, travel_factory, django_assert_num_queries, django_capture_on_commit_callbacks, globalsettings_mock
):
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    assert grupo_classe is not None
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    assert trecho_classe is not None
    travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    motivo_alteracao = fixtures.motivo_alteracao()
    globalsettings_mock("company_ids_marketplace_bucketizadas", [])

    with django_assert_num_queries(59), django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=motivo_alteracao.id,
        )


def test_bulk_change_class_dont_downgrade(
    grupo_factory, travel_factory, django_capture_on_commit_callbacks, mocker: MockerFixture
):
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pagamento = baker.make(
        "core.Pagamento",
        value=100,
        method=Pagamento.Method.PIX,
        status="paid",
        user=travel.user,
        net_value=50,
        jsondata=json.dumps({}),
    )
    travel.pagamento = pagamento
    travel.save(update_fields=["pagamento"])

    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "estorno_downgrade_automatico": True}
    estornar_downgrade = mocker.spy(estorno_svc, "estornar_downgrade")

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=None,
        )

    assert estornar_downgrade.call_count == 0  # Não estornamos mais na troca de classe
    assert RessarcimentoDowngrade.objects.count() == 0


@pytest.mark.parametrize(
    "max_split_value, modelo_venda, preco_rodoviaria, distancia_km, expected",
    [
        pytest.param(
            Decimal("100"),
            Grupo.ModeloVenda.BUSER,
            Decimal("50"),
            100,
            Decimal("100"),
            id="max_split_value pre setado buser",
        ),
        pytest.param(
            Decimal("100"),
            Grupo.ModeloVenda.MARKETPLACE,
            Decimal("50"),
            100,
            Decimal("100"),
            id="max_split_value pre setado marketplace",
        ),
        pytest.param(
            None, Grupo.ModeloVenda.MARKETPLACE, Decimal("50"), 100, Decimal("50"), id="marketplace valor default"
        ),
        pytest.param(
            None,
            Grupo.ModeloVenda.BUSER,
            Decimal("50"),
            100,
            Decimal("31.9"),
            id="buser valor default distancia setada manualmente",
        ),
        pytest.param(
            None,
            Grupo.ModeloVenda.BUSER,
            Decimal("50"),
            None,
            Decimal("63.9"),
            id="buser valor default distancia do itinerario",
        ),
    ],
)
def test_max_split_value_create(
    pre_travel_setup_factory, max_split_value, modelo_venda, preco_rodoviaria, distancia_km, expected
):
    data = pre_travel_setup_factory(valor=max_split_value, modelo_venda=modelo_venda)
    trecho_vendido = data["trecho_vendido"]
    trecho_vendido.preco_rodoviaria = preco_rodoviaria
    trecho_vendido.save()
    trecho_classe = data["trecho_classe"]
    novo_trecho_classe = TrechoClasse.create(
        trecho_vendido=trecho_vendido,
        grupo_classe=trecho_classe.grupo_classe,
        max_split_value=max_split_value,
        distancia_km=distancia_km,
    )
    assert novo_trecho_classe.max_split_value == expected


def test_bulk_change_class_grava_travel_comunicada(
    grupo_factory, travel_factory, mocker, django_capture_on_commit_callbacks
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)
    datetime = now() + timedelta(days=2)
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=datetime, rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    travels = [
        travel_factory(
            grupo_da_travel=grupo,
            qtd_pax=qtd_pax,
            trecho_vendido=trecho_classe.trecho_vendido,
            trecho_classe=trecho_classe,
            travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
        )
        for qtd_pax in range(1, 4)
    ]
    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "notify_users_class_changed": True}
    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
        )
    for travel in travels:
        travel_comunicada = TravelComunicada.objects.get(travel=travel)
        assert travel_comunicada.datetime_ida_mais_proximo == datetime
        assert travel_comunicada.tipo_assento == "leito"


def test_bulk_change_class_por_alteracao_onibus_grava_travel_comunicada(
    grupo_factory, travel_factory, mocker, django_capture_on_commit_callbacks
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)
    datetime = now() + timedelta(days=3)
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=datetime, rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    travels = [
        travel_factory(
            grupo_da_travel=grupo,
            qtd_pax=1,
            trecho_vendido=trecho_classe.trecho_vendido,
            trecho_classe=trecho_classe,
            travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
        )
    ]
    onibus_antigo = baker.make("core.Onibus")
    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {
        "groups": [grupo.id],
        "classes": classes,
        "notify_users_class_changed": True,
        "onibus_antigo": onibus_antigo,
    }
    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params, origem_alteracao_placa="teste", causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO, motivo_id=None
        )
    for travel in travels:
        travel_comunicada = TravelComunicada.objects.get(travel=travel)
        assert travel_comunicada.datetime_ida_mais_proximo == datetime
        assert travel_comunicada.tipo_assento is not None
        assert travel_comunicada.onibus == onibus_antigo


def test_simular_remanejamento_massivo_reprecifica_buckets(trecho_classe_bucketizado):
    grupo = trecho_classe_bucketizado.grupo
    grupo_classe = trecho_classe_bucketizado.grupo_classe
    trecho_vendido = trecho_classe_bucketizado.trecho_vendido
    baker.make(
        "core.Travel",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        trecho_classe=trecho_classe_bucketizado,
        max_split_value=Decimal("10.00"),
        count_seats=4,
    )
    classes = [classe_params("executivo", trecho_vendido.id, 10, 10)]
    classes[0]["max_capacity"] = 20
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    res = remanejamento_svc.simular_remanejamento_massivo(params)

    valor_original = res["transfers"]["leito>executivo"]["valor_tc_original"]
    valor_novo = res["transfers"]["leito>executivo"]["valor_tc_novo"]

    assert valor_original == Decimal("10.00") and valor_novo == Decimal("7.90")


def test_simular_remanejamento_massivo_e_simular_escalar_onibus_task_mesma_precificacao(trecho_classe):
    grupo = trecho_classe.grupo
    grupo_classe = trecho_classe.grupo_classe
    trecho_vendido = trecho_classe.trecho_vendido
    baker.make(
        "core.Travel",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        trecho_classe=trecho_classe,
        max_split_value=Decimal("10.00"),
        count_seats=4,
    )
    classes = [classe_params("executivo", trecho_vendido.id, 10, 10)]
    params = {
        "groups": [grupo.id],
        "classes": classes,
    }
    res_1 = remanejamento_svc.simular_remanejamento_massivo(params)
    valor_novo_simular_remanejamento_massivo = res_1["transfers"]["leito>executivo"]["valor_tc_novo"]

    onibus = baker.make("core.Onibus", tipo="DD")
    baker.make("core.OnibusClasse", onibus=onibus, tipo="executivo", capacidade=45)
    async_task = baker.make("core.AsyncTask", input_data={"group_ids": [grupo.id], "onibus_id": onibus.id})

    res_2 = simular_escalar_onibus_task(async_task.id)
    valor_novo_simular_escalar_onibus_task = res_2[0]["transfers"]["leito>executivo"]["valor_tc_novo"]

    assert valor_novo_simular_remanejamento_massivo == valor_novo_simular_escalar_onibus_task


def test_bulk_change_class_mantem_marcacao_assento(
    grupo_factory, travel_factory, django_capture_on_commit_callbacks, mocker
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)

    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    onibus = fixtures.gerar_onibus_com_poltronas(trecho_classe, tipo_assento="leito cama")
    grupo.onibus = onibus
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pax = travel.passageiro_set.first()
    fixtures.gerar_accop_marcacao_assento(travel, pax, 1)

    classes = [classe_params("leito cama", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "estorno_downgrade_automatico": True}

    old_acoops_marcacao_assento_count = travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    old_acoops_marcacao_assento_cancelada_count = travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=None,
        )

    new_travel = Travel.objects.get(reservation_code=travel.reservation_code)
    new_acoops_marcacao_assento_count = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    new_acoops_marcacao_assento_cancelada_count = new_travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()
    new_pax = new_travel.passageiro_set.first()

    assert old_acoops_marcacao_assento_count == 1
    assert new_acoops_marcacao_assento_count == 1
    assert old_acoops_marcacao_assento_cancelada_count == 0
    assert new_acoops_marcacao_assento_cancelada_count == 0
    assert new_pax
    assert new_pax.poltrona
    assert travel.id == new_travel.id
    assert travel.trecho_classe.id != new_travel.trecho_classe.id


def test_bulk_change_class_nao_mantem_marcacao_assento(
    grupo_factory, travel_factory, django_capture_on_commit_callbacks, mocker
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)
    mocker.patch(
        "core.service.remanejamento.remanejamento_svc.get_poltronas_disponiveis_por_tc",
        return_value={},
    )
    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    onibus = fixtures.gerar_onibus_com_poltronas(trecho_classe)
    grupo.onibus = onibus
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pax = travel.passageiro_set.first()
    fixtures.gerar_accop_marcacao_assento(travel, pax, 1)

    classes = [classe_params("leito", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "estorno_downgrade_automatico": True}

    old_acoops_marcacao_assento_count = travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    old_acoops_marcacao_assento_cancelada_count = travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=None,
        )

    new_travel = Travel.objects.get(reservation_code=travel.reservation_code)
    new_acoops_marcacao_assento_count = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    new_acoops_marcacao_assento_cancelada_count = new_travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()
    new_pax = new_travel.passageiro_set.first()

    assert old_acoops_marcacao_assento_count == 1
    assert new_acoops_marcacao_assento_count == 1
    assert old_acoops_marcacao_assento_cancelada_count == 0
    assert new_acoops_marcacao_assento_cancelada_count == 1
    assert new_pax
    assert new_pax.poltrona
    assert travel.id == new_travel.id
    assert travel.trecho_classe.id != new_travel.trecho_classe.id


def test_bulk_change_class_mantem_marcacao_assento_se_tiver_upgrade(
    grupo_factory, travel_factory, django_capture_on_commit_callbacks, mocker
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)

    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    onibus = fixtures.gerar_onibus_com_poltronas(trecho_classe, tipo_assento="leito cama")
    grupo.onibus = onibus
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pax = travel.passageiro_set.first()
    fixtures.gerar_accop_marcacao_assento(travel, pax, 1)

    classes = [classe_params("leito cama", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "estorno_downgrade_automatico": True}

    old_acoops_marcacao_assento_count = travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    old_acoops_marcacao_assento_cancelada_count = travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=None,
        )

    new_travel = Travel.objects.get(reservation_code=travel.reservation_code)
    new_acoops_marcacao_assento_count = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    new_acoops_marcacao_assento_cancelada_count = new_travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()
    new_pax = new_travel.passageiro_set.first()

    assert old_acoops_marcacao_assento_count == 1
    assert new_acoops_marcacao_assento_count == 1
    assert old_acoops_marcacao_assento_cancelada_count == 0
    assert new_acoops_marcacao_assento_cancelada_count == 0
    assert new_pax
    assert new_pax.poltrona
    assert travel.trecho_classe.grupo_classe.tipo_assento == "leito"
    assert new_travel.trecho_classe.grupo_classe.tipo_assento == "leito cama"
    assert travel.id == new_travel.id
    assert travel.trecho_classe.id != new_travel.trecho_classe.id


def test_bulk_change_class_nao_mantem_marcacao_assento_se_tiver_downgrade(
    grupo_factory, travel_factory, django_capture_on_commit_callbacks, mocker
):
    mocker.patch("commons.feature_flags.is_enabled", return_value=True)

    rotina_onibus = baker.make("core.RotinaOnibus")
    grupo = grupo_factory(data=now(), rotina_onibus=rotina_onibus)
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    onibus = fixtures.gerar_onibus_com_poltronas(trecho_classe, tipo_assento="executivo")
    grupo.onibus = onibus
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )
    pax = travel.passageiro_set.first()
    fixtures.gerar_accop_marcacao_assento(travel, pax, 1)

    classes = [classe_params("executivo", trecho_classe.trecho_vendido_id)]
    classes[0]["max_capacity"] = 10
    params = {"groups": [grupo.id], "classes": classes, "estorno_downgrade_automatico": True}

    old_acoops_marcacao_assento_count = travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    old_acoops_marcacao_assento_cancelada_count = travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()

    with django_capture_on_commit_callbacks(execute=True):
        remanejamento_svc.bulk_change_class(
            params,
            origem_alteracao_placa="teste",
            causada_por=AlteracaoTravel.CausadaPor.AUTOMATICO,
            motivo_id=None,
        )

    new_travel = Travel.objects.get(reservation_code=travel.reservation_code)
    new_acoops_marcacao_assento_count = new_travel.accountingoperation_set.filter(source="MARCACAO_ASSENTO").count()
    new_acoops_marcacao_assento_cancelada_count = new_travel.accountingoperation_set.filter(
        source="MARCACAO_ASSENTO_CANCELADA"
    ).count()
    new_pax = new_travel.passageiro_set.first()

    assert old_acoops_marcacao_assento_count == 1
    assert new_acoops_marcacao_assento_count == 1
    assert old_acoops_marcacao_assento_cancelada_count == 0
    assert new_acoops_marcacao_assento_cancelada_count == 1
    assert new_pax
    assert new_pax.poltrona
    assert travel.trecho_classe.grupo_classe.tipo_assento == "leito"
    assert new_travel.trecho_classe.grupo_classe.tipo_assento == "executivo"
    assert travel.id == new_travel.id
    assert travel.trecho_classe.id != new_travel.trecho_classe.id
