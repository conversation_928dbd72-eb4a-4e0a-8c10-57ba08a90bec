from decimal import Decimal
from unittest import mock

import pytest
from model_bakery import baker

from commons.dateutils import now
from core import tasks
from core.constants import DOWNGRADE_FATOR_TIPO_ASSENTO
from core.models_contabil import AccountingOperation
from core.models_grupo import GrupoClasse, TrechoClasse
from core.models_travel import Passageiro, RessarcimentoDowngrade, Travel
from core.service import ressarcimento_downgrade_svc
from core.service.ressarcimento_downgrade_svc import _get_valor_ja_pago_map, bulk_ressarcimento_unico_downgrade


@pytest.fixture
def passageiro_com_historico_remanejamento():
    grupo_original = baker.make("core.Grupo")
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="leito")
    travel_original = baker.make(
        "core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=1, grupo=grupo_original
    )

    grupo_atual = baker.make("core.Grupo")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="executivo")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=100,
        count_seats=1,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)
    historico = baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="leito",
        novo_tipo_assento="executivo",
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    return passageiro, historico


def test_create_ressarcimento_downgrade(travel_factory):
    grupo = baker.make("core.Grupo")
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        travel_kwargs={"max_split_value": 100},
    )
    pax = baker.make(Passageiro, travel=travel)
    with mock.patch("accounting.service.accounting_svc.reserva_downgrade") as mock_accounting:
        ressarcimento_downgrade_svc.create_ressarcimento_downgrade(
            Decimal("55"), pax, travel, RessarcimentoDowngrade.ReasonsDowngrade.MOVE_TRAVEL
        )
    mock_accounting.assert_called_once_with(
        Decimal("55"), pax, travel, reason_key=RessarcimentoDowngrade.ReasonsDowngrade.MOVE_TRAVEL
    )
    ressarcimento_downgrade = RessarcimentoDowngrade.objects.get(passenger=pax)
    assert ressarcimento_downgrade.reason_key == RessarcimentoDowngrade.ReasonsDowngrade.MOVE_TRAVEL
    assert ressarcimento_downgrade.value == Decimal("55")


def test_create_ressarcimento_downgrade_valor_0():
    travel = baker.make(Travel)
    pax = baker.make(Passageiro, travel=travel)
    with mock.patch("accounting.service.accounting_svc.reserva_downgrade") as mock_accounting:
        ressarcimento_downgrade_svc.create_ressarcimento_downgrade(
            Decimal("0"), pax, travel, RessarcimentoDowngrade.ReasonsDowngrade.MOVE_TRAVEL
        )
    mock_accounting.assert_not_called()
    assert not RessarcimentoDowngrade.objects.filter(passenger=pax).exists()


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_sem_historico_remanejamento_nem_alteracao():
    grupo_classe = baker.make("core.GrupoClasse", tipo_assento="executivo")
    travel = baker.make("core.Travel", grupo_classe=grupo_classe, count_seats=1)
    passageiro = baker.make("core.Passageiro", travel=travel)
    baker.make(
        "core.AccountingOperation",
        travel=travel,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_com_historico_remanejamento(
    passageiro_com_historico_remanejamento,
):
    passageiro, historico = passageiro_com_historico_remanejamento
    antigo_tipo_assento = historico.antigo_tipo_assento
    novo_tipo_assento = historico.novo_tipo_assento
    fator_diff = (
        DOWNGRADE_FATOR_TIPO_ASSENTO[novo_tipo_assento] / DOWNGRADE_FATOR_TIPO_ASSENTO[antigo_tipo_assento]
    ) - 1
    downgrade_value = abs(fator_diff) * passageiro.travel.max_split_value

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro)
    assert ressarcimento.value == round(downgrade_value, 2)


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_com_alteracao_travel():
    grupo_classe = baker.make("core.GrupoClasse", tipo_assento="executivo")
    travel = baker.make("core.Travel", grupo_classe=grupo_classe, max_split_value=100, count_seats=1)
    passageiro = baker.make("core.Passageiro", travel=travel)
    alteracao_travel = baker.make(
        "core.AlteracaoTravel",
        travel=travel,
        antigo_tipo_assento="leito",
        novo_tipo_assento="executivo",
        tipo="change_class",
    )
    accop = baker.make(
        "core.AccountingOperation",
        travel=travel,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    fator_diff = (
        DOWNGRADE_FATOR_TIPO_ASSENTO[alteracao_travel.novo_tipo_assento]
        / DOWNGRADE_FATOR_TIPO_ASSENTO[alteracao_travel.antigo_tipo_assento]
    ) - 1
    valor_downgrade = fator_diff * accop.value_real

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro)
    assert ressarcimento.value == round(valor_downgrade, 2)


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_com_historico_remanejamento_e_alteracao_travel(
    passageiro_com_historico_remanejamento_e_alteracao_travel,
):
    passageiro, historico_1 = passageiro_com_historico_remanejamento_e_alteracao_travel()
    antigo_tipo_assento = historico_1.antigo_tipo_assento
    novo_tipo_assento = passageiro.travel.grupo_classe.tipo_assento  # o assento atual está na travel
    fator_diff = (
        DOWNGRADE_FATOR_TIPO_ASSENTO[novo_tipo_assento] / DOWNGRADE_FATOR_TIPO_ASSENTO[antigo_tipo_assento]
    ) - 1
    downgrade_value = abs(fator_diff) * Decimal(str(historico_1.travel_antiga.max_split_value))

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro)
    assert ressarcimento.value == downgrade_value


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_com_alteracao_travel_antes_historico_remanejamento():
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=1)

    # Alteração de classe 1 - Downgrade
    grupo_classe_1 = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_original.grupo_classe = grupo_classe_1
    travel_original.save(update_fields=["grupo_classe"])
    alteracao_travel = baker.make(
        "core.AlteracaoTravel",
        travel=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="leito cama individual",
        tipo="change_class",
    )

    # Remanejamento - Downgrade
    grupo_classe_2 = baker.make("core.GrupoClasse", tipo_assento="leito cama")
    travel_atual = baker.make(
        "core.Travel", grupo_classe=grupo_classe_2, count_seats=1, movido_de=travel_original.grupo
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="leito cama individual",
        novo_tipo_assento="leito cama",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)

    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )

    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    antigo_tipo_assento = alteracao_travel.antigo_tipo_assento
    novo_tipo_assento = passageiro.travel.grupo_classe.tipo_assento  # o assento atual está na travel
    fator_diff = (
        DOWNGRADE_FATOR_TIPO_ASSENTO[novo_tipo_assento] / DOWNGRADE_FATOR_TIPO_ASSENTO[antigo_tipo_assento]
    ) - 1
    downgrade_value = abs(fator_diff) * Decimal(str(travel_original.max_split_value))

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro)
    assert ressarcimento.value == downgrade_value


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_seguido_upgrade_com_historico_remanejamento():
    # Remanejamento 1 - Downgrade
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=1)
    grupo_classe_1 = baker.make("core.GrupoClasse", tipo_assento="cama premium")
    travel_1 = baker.make("core.Travel", grupo_classe=grupo_classe_1, count_seats=1, movido_de=travel_original.grupo)
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_1,
        travel_antiga=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="cama premium",
    )

    # Remanejamento 2 - Upgrade
    travel_atual = baker.make(
        "core.Travel", grupo_classe=grupo_classe_original, count_seats=1, movido_de=travel_1.grupo
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_1,
        antigo_tipo_assento="cama premium",
        novo_tipo_assento="cama premium individual",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_1,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_1,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_seguido_upgrade_com_alteracao_travel():
    # Alteração de classe 1 - Downgrade
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=2)
    grupo_classe_1 = baker.make("core.GrupoClasse", tipo_assento="cama premium")
    travel_original.grupo_classe = grupo_classe_1
    travel_original.save(update_fields=["grupo_classe"])
    baker.make(
        "core.AlteracaoTravel",
        travel=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="cama premium",
        tipo="change_class",
    )

    # Alteração de classe 2 - Upgrade
    travel_original.grupo_classe = grupo_classe_original
    travel_original.save(update_fields=["grupo_classe"])
    baker.make(
        "core.AlteracaoTravel",
        travel=travel_original,
        antigo_tipo_assento="cama premium",
        novo_tipo_assento="cama premium individual",
        tipo="change_class",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_original)
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_seguido_upgrade_com_historico_remanejamento_e_alteracao_travel():
    # Remanejamento 1 - Downgrade
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=3)
    grupo_classe_1 = baker.make("core.GrupoClasse", tipo_assento="cama premium")
    travel_1 = baker.make("core.Travel", grupo_classe=grupo_classe_1, count_seats=3, movido_de=travel_original.grupo)
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_1,
        travel_antiga=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="cama premium",
    )

    # Alteração de classe 1 - Downgrade
    grupo_classe_2 = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_1.grupo_classe = grupo_classe_2
    travel_1.save(update_fields=["grupo_classe"])
    baker.make(
        "core.AlteracaoTravel",
        travel=travel_1,
        antigo_tipo_assento="cama premium",
        novo_tipo_assento="leito cama individual",
        tipo="change_class",
    )

    # Remanejamento 2 - Upgrade
    travel_atual = baker.make("core.Travel", grupo_classe=grupo_classe_1, count_seats=3, movido_de=travel_1.grupo)
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_1,
        antigo_tipo_assento="leito cama individual",
        novo_tipo_assento="cama premium",
    )

    # Alteração de classe 2 - Upgrade
    travel_atual.grupo_classe = grupo_classe_original
    travel_atual.save(update_fields=["grupo_classe"])
    baker.make(
        "core.AlteracaoTravel",
        travel=travel_atual,
        antigo_tipo_assento="cama premium cama",
        novo_tipo_assento="cama premium individual",
        tipo="change_class",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_1,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_1,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("accounting.service.accounting_svc.reserva_downgrade")
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_ja_ressarcido_pelo_ressarcimento_unico(mock_reserva_downgrade):
    # Remanejamento - Downgrade
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, count_seats=1)
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito cama")
    travel_atual = baker.make(
        "core.Travel", grupo_classe=grupo_classe_atual, count_seats=1, movido_de=travel_original.grupo
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="leito cama individual",
        novo_tipo_assento="leito cama",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)
    baker.make(
        "core.RessarcimentoDowngrade",
        passenger=passageiro,
        value=500,
        reason_key=RessarcimentoDowngrade.ReasonsDowngrade.RESSARCIMENTO_UNICO,
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])
    ressarcimentos = RessarcimentoDowngrade.objects.filter(passenger=passageiro)

    assert len(ressarcimentos) == 1
    assert ressarcimentos[0].value == 500
    mock_reserva_downgrade.assert_not_called()


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_sem_downgrade_com_alteracao_travel():
    # Alteração de classe 1 - Sem alteração de classe
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=2)
    baker.make(
        "core.AlteracaoTravel",
        travel=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="cama premium individual",
        tipo="change_class",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_original)
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_pax_ja_ressarcido_do_modo_antigo():
    # Remanejamento - já ressarcido parcialmente
    grupo_original = baker.make("core.Grupo")
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make(
        "core.Travel", grupo_classe=grupo_classe_original, max_split_value=100, count_seats=1, grupo=grupo_original
    )
    grupo_atual = baker.make("core.Grupo")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=70,
        count_seats=1,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="leito",
    )

    passageiro = baker.make("core.Passageiro", travel=travel_atual)
    baker.make(
        "core.RessarcimentoDowngrade",
        passenger=passageiro,
        value=15,
        reason_key=RessarcimentoDowngrade.ReasonsDowngrade.TROCA_ONIBUS,
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])

    assert RessarcimentoDowngrade.objects.count() == 1


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_com_cupom_em_reais():
    # Remanejamento - Downgrade de 10%
    grupo_original = baker.make("core.Grupo")
    cupom = baker.make("core.Cupom", value=Decimal("10"), discount=None)
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_original,
        cupom=cupom,
        max_split_value=100,
        count_seats=3,
        grupo=grupo_original,
    )
    grupo_atual = baker.make("core.Grupo")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=90,
        count_seats=3,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="leito cama individual",
    )

    passageiro_1 = baker.make("core.Passageiro", travel=travel_atual)
    passageiro_2 = baker.make("core.Passageiro", travel=travel_atual)
    passageiro_3 = baker.make("core.Passageiro", travel=travel_atual)
    # Pagou R$270,00 na viagem
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_1,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_2,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_3,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        source="PROMOCAO",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_1,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_2,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_3,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        source="PROMOCAO_CANCELADA",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    # Quando grupo não é marketplace, as novas reservas são feitas com mesmo valor da inicial no
    # remanejamento
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_1,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_2,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_3,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        source="PROMOCAO",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    bulk_ressarcimento_unico_downgrade([passageiro_1.id, passageiro_2.id, passageiro_3.id])

    ressarcimento_1 = RessarcimentoDowngrade.objects.get(passenger=passageiro_1)
    ressarcimento_2 = RessarcimentoDowngrade.objects.get(passenger=passageiro_2)
    ressarcimento_3 = RessarcimentoDowngrade.objects.get(passenger=passageiro_3)
    ressarcimento_travel = ressarcimento_1.value + ressarcimento_2.value + ressarcimento_3.value

    assert ressarcimento_travel == 27.00


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_com_cupom_em_porcentagem():
    # Remanejamento - Downgrade de 10%
    grupo_original = baker.make("core.Grupo")
    cupom = baker.make("core.Cupom", value=None, discount=0.1)
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="cama premium individual")
    travel_original = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_original,
        cupom=cupom,
        max_split_value=100,
        count_seats=3,
        grupo=grupo_original,
    )
    grupo_atual = baker.make("core.Grupo")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=90,
        count_seats=3,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )
    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="cama premium individual",
        novo_tipo_assento="leito cama individual",
    )

    passageiro_1 = baker.make("core.Passageiro", travel=travel_atual)
    passageiro_2 = baker.make("core.Passageiro", travel=travel_atual)
    passageiro_3 = baker.make("core.Passageiro", travel=travel_atual)
    # Pagou R$90,00 na viagem
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_1,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_2,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_3,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        source="PROMOCAO",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_1,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_2,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_3,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        source="PROMOCAO_CANCELADA",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    # Quando grupo não é marketplace, as novas reservas são feitas com mesmo valor da inicial no
    # remanejamento
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_1,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_2,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_3,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        source="PROMOCAO",
        value_real=Decimal("30"),
        promocao="PROMO_CUPOM",
    )
    bulk_ressarcimento_unico_downgrade([passageiro_1.id, passageiro_2.id, passageiro_3.id])

    ressarcimento_1 = RessarcimentoDowngrade.objects.get(passenger=passageiro_1)
    ressarcimento_2 = RessarcimentoDowngrade.objects.get(passenger=passageiro_2)
    ressarcimento_3 = RessarcimentoDowngrade.objects.get(passenger=passageiro_3)
    ressarcimento_travel = ressarcimento_1.value + ressarcimento_2.value + ressarcimento_3.value

    assert ressarcimento_travel == 27.00


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_passageiro_downgrade_num_queries(
    django_assert_num_queries, passageiro_com_historico_remanejamento_e_alteracao_travel
):
    passageiro, _ = passageiro_com_historico_remanejamento_e_alteracao_travel()
    pax_ids = [passageiro.id for _ in range(3)]
    with django_assert_num_queries(37):
        bulk_ressarcimento_unico_downgrade(pax_ids)


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_buser_dois_pax_com_um_cancelado_travel_mais_cara():
    # 2 pax compraram -> foram remanejados -> 1 dos pax cancelou
    grupo_original = baker.make("core.Grupo", modelo_venda="buser")
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="leito individual")
    travel_original = baker.make(
        "core.Travel", grupo_classe=grupo_classe_original, max_split_value=179.9, count_seats=2, grupo=grupo_original
    )

    passageiro_original_que_vai_cancelar = baker.make("core.Passageiro", travel=travel_original)
    passageiro_original_que_vai_viajar = baker.make("core.Passageiro", travel=travel_original)

    grupo_atual = baker.make("core.Grupo", modelo_venda="buser")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=397.99,
        count_seats=1,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )

    passageiro_cancelou_atual = baker.make("core.Passageiro", travel=travel_atual, removed=True)
    passageiro_viajou_atual = baker.make("core.Passageiro", travel=travel_atual)

    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="leito individual",
        novo_tipo_assento="leito",
    )
    #: Fizeram a compra dos 2 pax na mesma travel
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_original_que_vai_cancelar,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_original_que_vai_viajar,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    #: Foram remanejados 2 pax
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_original_que_vai_cancelar,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_original_que_vai_viajar,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("179.9"),
    )
    #: O remanejamento faz a reserva dos 2 pax
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_viajou_atual,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    #: Um dos pax cancelou
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("179.9"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro_viajou_atual.id, passageiro_cancelou_atual.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro_viajou_atual)

    assert not RessarcimentoDowngrade.objects.filter(passenger=passageiro_cancelou_atual).exists()
    assert ressarcimento.value == Decimal("11.99")


@mock.patch("commons.feature_flags.is_user_enabled", lambda *args, **kwargs: True)
@mock.patch("core.tasks.passageiro_ressarcido", lambda *args, **kwargs: None)
def test_bulk_ressarcimento_unico_downgrade_marketplace_dois_pax_com_um_cancelado_travel_mais_cara():
    grupo_original = baker.make("core.Grupo", modelo_venda="marketplace")
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="leito individual")
    travel_original = baker.make(
        "core.Travel", grupo_classe=grupo_classe_original, max_split_value=179.9, count_seats=2, grupo=grupo_original
    )

    passageiro_cancelou_original = baker.make("core.Passageiro", travel=travel_original)
    passageiro_viajou_original = baker.make("core.Passageiro", travel=travel_original)

    grupo_atual = baker.make("core.Grupo", modelo_venda="marketplace")
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito")
    travel_atual = baker.make(
        "core.Travel",
        grupo_classe=grupo_classe_atual,
        max_split_value=397.99,
        count_seats=1,
        grupo=grupo_atual,
        movido_de=grupo_original,
    )

    passageiro_cancelou_atual = baker.make("core.Passageiro", travel=travel_atual, removed=True)
    passageiro_viajou_atual = baker.make("core.Passageiro", travel=travel_atual)

    baker.make(
        "core.HistoricoRemanejamento",
        travel_nova=travel_atual,
        travel_antiga=travel_original,
        antigo_tipo_assento="leito individual",
        novo_tipo_assento="leito",
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_cancelou_original,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_viajou_original,
        source="RESERVA",
        value_real=-Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_cancelou_original,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro_viajou_original,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("179.9"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="RESERVA",
        value_real=-Decimal("397.99"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_viajou_atual,
        source="RESERVA",
        value_real=-Decimal("397.99"),
    )
    # Quando grupo é marketplace, as novas reservas são feitas com o valor do max_split_value
    # e a diferença é compensada com uma PROMOCAO
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="PROMOCAO",
        value_real=Decimal("218.09"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_viajou_atual,
        source="PROMOCAO",
        value_real=Decimal("218.09"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="PROMOCAO_CANCELADA",
        value_real=-Decimal("218.09"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro_cancelou_atual,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("397.99"),
    )
    fator_diff = (
        DOWNGRADE_FATOR_TIPO_ASSENTO[travel_atual.grupo_classe.tipo_assento]
        / DOWNGRADE_FATOR_TIPO_ASSENTO[travel_original.grupo_classe.tipo_assento]
    ) - 1
    valor_downgrade = abs(fator_diff) * Decimal(str(travel_original.max_split_value))

    bulk_ressarcimento_unico_downgrade([passageiro_viajou_atual.id, passageiro_cancelou_atual.id])

    ressarcimento = RessarcimentoDowngrade.objects.get(passenger=passageiro_viajou_atual)
    assert not RessarcimentoDowngrade.objects.filter(passenger=passageiro_cancelou_atual).exists()
    assert ressarcimento.value == round(valor_downgrade, 2)


def test_create_ressarcimento_downgrade_notificacao(mocker, grupo_factory, travel_factory):
    mock_notification = mocker.patch("core.tasks.passageiro_ressarcido")
    mocker.patch("core.service.ressarcimento_downgrade_svc.create_ressarcimento_downgrade")

    grupo = grupo_factory(data=now())
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    assert grupo_classe is not None
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    assert trecho_classe is not None
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )

    user = travel.user
    user.email = "teste.com.br"
    user.save(update_fields=["email"])

    tasks.create_ressarcimento_downgrade(
        value=100, pax=travel.passageiro_set.first(), user=user, travel=travel, reason_key="RESSARC_UNICO"
    )

    mock_notification.assert_called_once()


def test_create_ressarcimento_downgrade_cria_accops_quase_identica(mocker, grupo_factory, travel_factory):
    """ "
    O problema surgiu porque a accounting operation não deixava criar um valor que os
    user_id, source. value, value_real, travel_id, passageiro_id fossem iguais, mas isso pode acontecer,
    então usamos o reason_key para permitir que possamos fazer ressarcimento único com mesmo valor
    que um ressarcimento por remanejamento já foi feito anteriormente
    """
    mock_notification = mocker.patch("core.tasks.passageiro_ressarcido")

    grupo = grupo_factory(data=now())
    grupo_classe = GrupoClasse.objects.filter(grupo=grupo).first()
    trecho_classe = TrechoClasse.objects.filter(grupo=grupo).first()
    travel = travel_factory(
        grupo_da_travel=grupo,
        qtd_pax=1,
        trecho_vendido=trecho_classe.trecho_vendido,
        trecho_classe=trecho_classe,
        travel_kwargs={"grupo_classe": grupo_classe, "max_split_value": 100},
    )

    user = travel.user
    user.email = "teste.com.br"
    user.save(update_fields=["email"])

    user_id = user.id
    value = 100
    source = "RESERVA_DOWNGRADE"
    travel_id = travel.id
    pax_id = travel.passageiro_set.first().id

    # accops já existente
    baker.make(
        "core.AccountingOperation",
        user_id=user_id,
        source=source,
        value=value,
        value_real=value,
        travel_id=travel_id,
        passageiro_id=pax_id,
    )

    tasks.create_ressarcimento_downgrade(
        value=value, pax=travel.passageiro_set.first(), user=user, travel=travel, reason_key="RESSARC_UNICO"
    )

    accops_iguais = AccountingOperation.objects.filter(
        user_id=user_id, source=source, value=value, value_real=value, travel_id=travel_id, passageiro_id=pax_id
    )
    accops_ressarc_unico = accops_iguais.filter(reason_key="RESSARC_UNICO")

    assert len(accops_iguais) == 2
    assert accops_ressarc_unico.exists()
    mock_notification.assert_called_once()


def test_get_valor_ja_pago_map():
    """
    Esse problema aconteceu por causa do Django ORM, que não estava agrupando a soma de valores
    """

    passageiro = baker.make("core.Passageiro")
    baker.make("core.RessarcimentoDowngrade", passenger=passageiro, value=100)
    baker.make("core.RessarcimentoDowngrade", passenger=passageiro, value=50)

    response = _get_valor_ja_pago_map([passageiro])

    assert response == {passageiro.id: Decimal("150")}


@mock.patch("accounting.service.accounting_svc.reserva_downgrade")
def test_bulk_ressarcimento_unico_downgrade_passageiro_user_buser_nao_recebe(mock_reserva_downgrade):
    # Remanejamento - Downgrade
    grupo_classe_original = baker.make("core.GrupoClasse", tipo_assento="leito cama individual")
    travel_original = baker.make("core.Travel", grupo_classe=grupo_classe_original, count_seats=1)
    grupo_classe_atual = baker.make("core.GrupoClasse", tipo_assento="leito cama")
    travel_atual = baker.make(
        "core.Travel", grupo_classe=grupo_classe_atual, count_seats=1, movido_de=travel_original.grupo
    )
    baker.make("core.HistoricoRemanejamento", travel_nova=travel_atual, travel_antiga=travel_original)

    passageiro = baker.make("core.Passageiro", travel=travel_atual, buseiro__user__email="<EMAIL>")
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_original,
        passageiro=passageiro,
        source="RESERVA_CANCELADA_PARCIAL",
        value_real=Decimal("100"),
    )
    baker.make(
        "core.AccountingOperation",
        travel=travel_atual,
        passageiro=passageiro,
        source="RESERVA",
        value_real=-Decimal("100"),
    )

    bulk_ressarcimento_unico_downgrade([passageiro.id])
    RessarcimentoDowngrade.objects.filter(passenger=passageiro)
    assert RessarcimentoDowngrade.objects.filter(passenger=passageiro).exists() is False
    mock_reserva_downgrade.assert_not_called()
