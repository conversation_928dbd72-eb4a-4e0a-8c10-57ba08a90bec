from django.urls import path

from core import views, views_staff, views_staff_remanejamento, views_staff_rotina

app_name = "staff"

urlpatterns = [
    path(
        "api/staff/<anomalia_type>/anomalias/<int:anomalia_id>/resolve",
        views_staff.resolve_anomalia,
        name="resolve-anomalia",
    ),
    path("api/staff/<anomalia_type>/anomalias/<int:anomalia_id>/unresolve", views_staff.unresolve_anomalia),
    path("api/staff/anomalias/get_alert_events/<int:anomalia_id>", views_staff.get_alert_events),
    path("api/staff/anomalias/indicators/agrupado", views_staff.list_indicators_anomalias_agrupado),
    path("api/staff/anomalias/indicators/v2/<int:grupo_id>", views_staff.list_indicators_anomalias_v2),
    path("api/staff/ajuste_precos", views_staff.listar_ajuste_precos),
    path("api/staff/ajuste_precos/importar", views_staff.importar_ajuste_precos),
    path("api/staff/ajuste_precos/importar/parquet", views_staff.importar_ajuste_precos_parquet),
    path("api/staff/ajuste_precos/notificar", views_staff.webhook_ajuste_precos),
    path("api/staff/ajuste_precos/notificar/parquet/", views_staff.webhook_ajuste_precos_parquet),
    path("api/staff/alerta/annotate_description", views_staff.alerta_annotate_description),
    path("api/staff/alerta/annotate_description/multiple", views_staff.alertas_annotate_description),
    path("api/staff/anttdrivers", views_staff.list_antt_drivers),  # todo: deprecated
    path(
        "api/staff/async-tasks/<int:async_task_id>",
        views_staff.async_task_detail,
        name="async-task",
    ),
    path("api/staff/avisos-motorista", views_staff.get_avisos_motorista),
    path("api/staff/avisos-motorista/apagar", views_staff.apagar_aviso_motorista),
    path("api/staff/avisos-motorista/desativar", views_staff.desativar_aviso_motorista),
    path("api/staff/avisos-motorista/reativar", views_staff.ativar_aviso_motorista),
    path("api/staff/avisos-motorista/save", views_staff.salvar_aviso_motorista),
    path("api/staff/avisos-parceiro", views_staff.get_avisos_parceiro),
    path("api/staff/avisos-parceiro/apagar", views_staff.apagar_aviso_parceiro),
    path("api/staff/avisos-parceiro/reativar", views_staff.reativar_aviso_parceiro),
    path("api/staff/avisos-parceiro/save", views_staff.salvar_aviso_parceiro),
    path("api/staff/buseiro/<int:buseiro_id>", views_staff.get_buseiro_details, kwargs={"tags": False}),
    path("api/staff/buseiro/<int:buseiro_id>/invitations", views_staff.get_buseiro_invitations),  # em transição
    path("api/staff/buseiro/<int:buseiro_id>/tags", views_staff.get_buseiro_details, kwargs={"tags": True}),
    path("api/staff/buseiro/invitations", views_staff.get_buseiro_invitations),
    path("api/staff/buseiro/saldo", views_staff.get_buseiro_saldo),
    path("api/staff/buseiro/travels", views_staff.get_buseiro_travels),
    path("api/staff/get_passengers", views_staff.get_passengers),
    path("api/staff/cancelar_ressarcimento", views_staff.cancelar_ressarcimento),
    path("api/staff/categoria_turistica/<int:categoria_id>/list_cidades", views_staff.lista_cidades_de_categoria),
    path("api/staff/categoria_turistica/cadastra_cidade", views_staff.cadastra_cidade_categoria),
    path("api/staff/categoria_turistica/create", views_staff.create_categoria_turistica),
    path("api/staff/categoria_turistica/list_all", views_staff.list_categorias_turisticas),
    path("api/staff/categoria_turistica/remove_cidade", views_staff.remove_cidade_categoria),
    path("api/staff/categoria_turistica/update", views_staff.edit_categoria_turistica),
    path("api/staff/cities_bases_ops", views_staff.list_cidades_bases_operacionais),
    path("api/staff/cities_info", views_staff.list_cidades_with_info),
    path("api/staff/city", views_staff.get_cidade),
    path("api/staff/companies", views_staff.list_companies),
    path("api/staff/companies-keyset", views_staff.list_companies_keyset),
    path("api/staff/companies-simple", views_staff.list_simple_companies),
    path("api/staff/companies/<int:company_id>/emprestar-dinheiro", views_staff.emprestar_dinheiro),  # em transição
    path("api/staff/companies/<int:company_id>/profile", views_staff.get_company_profile),
    path("api/staff/companies/consulta_cnpj", views_staff.consulta_cnpj),
    path("api/staff/companies/emprestar-dinheiro", views_staff.emprestar_dinheiro),
    path("api/staff/companies/save", views_staff.save_company),
    path("api/staff/configuracao_pagamento/<int:company_id>", views_staff.get_configuracao_pagamento),
    path(
        "api/staff/configuracao_pagamento/update",
        views_staff.edit_configuracao_pagamento,
    ),
    path("api/staff/create_new_admin", views_staff.create_new_admin),
    path("api/staff/create_template_comunicacao", views_staff.create_template_comunicacao),
    path("api/staff/create_user", views_staff.create_user),
    path("api/staff/create_whatsapp_template", views_staff.create_whatsapp_template),
    path("api/staff/creategroup", views_staff.creategroup),
    path("api/staff/createlocal", views_staff.create_local),
    path("api/staff/delete_template_comunicacao", views_staff.delete_template_comunicacao),
    path("api/staff/details_estorno", views_staff.details_estorno),
    path("api/staff/drivers", views_staff.list_drivers),
    path("api/staff/drivers-keyset", views_staff.list_drivers_keyset),
    path("api/staff/drivers/<int:driver_id>", views_staff.get_driver_details),
    path("api/staff/drivers/check_timeline", views_staff.check_drivers_avaliability),
    path("api/staff/drivers/save", views_staff.save_driver),
    path("api/staff/edit_poltrona", views_staff.edit_poltrona),
    path("api/staff/edit_template_comunicacao", views_staff.edit_template_comunicacao),
    path("api/staff/edit_user", views_staff.edit_user),
    path("api/staff/efops/bulk-remove-bus", views_staff.bulk_remove_bus),
    path("api/staff/efops/bulk-remove-drivers", views_staff.bulk_remove_drivers),
    path("api/staff/efops/check-user-info", views_staff.check_user_info),
    path("api/staff/efops/cities", views_staff.list_cidades),
    path("api/staff/efops/drivers_feedbacks", views_staff.list_drivers_feedbacks),
    path("api/staff/efops/get/route/routines", views_staff_rotina.get_route_routines),
    path("api/staff/efops/groups/alterarrota", views_staff.alterar_rota_de_grupos),
    path("api/staff/efops/groups/alterarrota/async", views_staff.alterar_rota_de_grupos_async),
    path("api/staff/efops/groups/group", views_staff.get_group_details),
    path("api/staff/efops/groups/liberar_1_motora", views_staff.liberar_1_motora),
    path("api/staff/efops/groups/simularalteracaoderota", views_staff.simular_alteracao_rota),
    path("api/staff/efops/locais_select_options", views_staff.list_locais_select_options),
    path("api/staff/efops/remove-users-info", views_staff.remove_users_info),
    path("api/staff/efops/rotas", views_staff.list_rotas),
    path("api/staff/efops/rotas_select_options", views_staff.list_rotas_select_options),
    path("api/staff/efops/rotina-onibus/alterar_campo_rotinas", views_staff_rotina.alterar_campo_rotinas),
    path("api/staff/efops/user_forgot_password", views_staff.user_forgot_password),
    path("api/staff/extrato_user_v2", views_staff.get_extrato_usuario_v2),
    path("api/staff/feedbacks", views_staff.list_feedbacks),
    path("api/staff/geocode", views_staff.geocode),
    path("api/staff/gerar_pagamento_divida", views_staff.gerar_pagamento_divida),
    path("api/staff/gestores", views_staff.list_gestores),
    path("api/staff/get-company-admins/<int:company_id>", views_staff.get_company_admins),
    path("api/staff/get_all_unverified_users", views_staff.get_all_unverified_users),
    path("api/staff/get_helpquestions", views_staff.get_helpquestions),
    path("api/staff/get_user_cupom_lead", views_staff.get_user_cupom_lead),
    path("api/staff/get_user_verification_data/<int:user_id>", views_staff.get_user_verification_data),
    path("api/staff/give_voucher", views_staff.give_voucher),
    path("api/staff/groups", views_staff.list_groups),
    path("api/staff/groups/<int:group_id>/antt", views_staff.gerar_antt),  # todo: deprecated
    path("api/staff/groups/<int:group_id>/finish", views_staff.finish_group),  # em transição
    path("api/staff/groups/<int:group_id>/printantt", views_staff.imprimir_antt),
    path("api/staff/groups/<int:grupo_id>/contabilizar-fidelidade", views_staff.contabilizar_fidelidade),
    path("api/staff/groups/<int:grupo_id>/multas-qualidade", views_staff.multas_qualidade),
    path("api/staff/groups/<int:grupo_id>/multas-qualidade-torre", views_staff.multas_qualidade_torre),
    path("api/staff/groups/<int:grupo_id>/notification/list", views_staff.list_group_notifications_pigeon),
    path("api/staff/groups/<int:grupo_id>/notification/list_logs", views_staff.list_group_notifications),
    path("api/staff/groups/<int:grupo_id>/trecho/<int:trecho_id>/atraso", views_staff.atraso_trecho_grupo),
    path("api/staff/groups/<int:grupo_id>/update", views_staff.update_group),  # em transição
    path("api/staff/groups/abrirmassivo", views_staff.abrir_massivo),
    path("api/staff/groups/alterar_ocasiao", views_staff.alterar_ocasiao),
    path("api/staff/groups/alterar_probabilidade", views_staff.alterar_probabilidade),
    path("api/staff/groups/alterar_valor_encomenda", views_staff.alterar_valor_encomenda),
    path("api/staff/groups/bulk_escalar_onibus", views_staff.bulk_escalar_onibus),
    path("api/staff/groups/bulk_escalar_onibus/simular-async", views_staff.simular_bulk_escalar_onibus_async),
    path("api/staff/groups/bulkchangeclass", views_staff.bulk_change_class),
    path("api/staff/groups/cancel", views_staff.cancel_groups),
    path("api/staff/groups/cancel/get_canceled_reasons", views_staff.get_group_canceled_reasons),
    path("api/staff/groups/cancel/simularcancelamentogrupos", views_staff.simular_cancelamento_grupo),
    path("api/staff/groups/get_closed_reasons", views_staff.get_closed_reasons),
    path("api/staff/groups/confirm", views_staff.confirm_groups),
    path(
        "api/staff/groups/editar-porcentagem-repasse-taxa-servico", views_staff.editar_porcentagem_repasse_taxa_servico
    ),
    path("api/staff/groups/encerrar_viagem", views_staff.encerrar_viagem),
    path("api/staff/groups/escalar_empresa_onibus_rotina_async", views_staff.escalar_empresa_onibus_rotina_async),
    path("api/staff/groups/esconder_grupo", views_staff.update_hidden_from_pax),
    path("api/staff/groups/fecharmassivo", views_staff.fechar_massivo),
    path("api/staff/groups/fechartrechos", views_staff.fechar_trechos),
    path("api/staff/groups/filters", views_staff.filtros_grupos),
    path("api/staff/groups/finish", views_staff.finish_group),
    path("api/staff/groups/forecast_e_gmv_previsto_grupos", views_staff.forecast_e_gmv_previsto_grupos),
    path("api/staff/groups/get_motivos_remanejamento", views_staff.get_motivos_remanejamento),
    path("api/staff/groups/list_dates_related_to_route", views_staff.list_group_dates_related_to_route),
    path("api/staff/groups/mover_buseiros", views_staff_remanejamento.mover_buseiros),
    path("api/staff/groups/precos", views_staff.alterar_precos),
    path("api/staff/groups/previous_and_next", views_staff.get_previous_and_next_groups),
    path("api/staff/groups/recalculaprecosclasse", views_staff.recalcula_precos_classe),
    path("api/staff/groups/remanejamento_automatico", views_staff_remanejamento.remanejamento_automatico),
    path("api/staff/groups/simula_mover_buseiros", views_staff_remanejamento.simula_mover_buseiros),
    path("api/staff/groups/simula_remanejamento_automatico", views_staff_remanejamento.simula_remanejamento_automatico),
    path("api/staff/groups/simularremanejamentomassivo", views_staff.simular_remanejamento_massivo),
    path("api/staff/groups/update", views_staff.update_group),
    path("api/staff/groups/updatedeparture", views_staff.update_departure_hour),
    path("api/staff/groups/verificar_conflito_onibus", views_staff.verificar_conflito_onibus),
    path("api/staff/grupos/<int:grupo_id>/logs-ajustepreco-grupo", views_staff.get_logs_ajuste_preco_grupo),
    path("api/staff/grupos/<int:grupo_id>/marketplace-info", views_staff.grupo_info_marketplace),
    path("api/staff/grupos/locais_embarque", views_staff.list_locais_embarque_de_grupos),
    path("api/staff/check_position_local_embarque", views_staff.verifica_se_existe_local_embarque_na_posicao),
    path("api/staff/has_pdv_associado", views_staff.get_has_pdv_associado),
    path("api/staff/helpquestions/publish_version", views_staff.publish_faq_version),
    path("api/staff/helpquestions/save", views_staff.save_helpquestion),
    path("api/staff/helpquestions/update_indexes", views_staff.update_helpquestion_indexes),
    path("api/staff/hibrido/alterar-autorizacao-hibrido", views_staff.alterar_autorizacao_grupo_hibrido),
    path("api/staff/hibrido/listar-autorizacoes-hibrido", views_staff.list_autorizacoes_hibrido),
    path("api/staff/historico_alteracao_embarque", views_staff.historico_alteracao_embarque),
    path("api/staff/importar_leads", views_staff.importar_leads),
    path("api/staff/inactivate_whatsapp_template", views_staff.inactivate_whatsapp_template),
    path("api/staff/integracao_rodoviaria/abrir-trechos", views_staff.abrir_trechos),
    path("api/staff/integracao_rodoviaria/addpassenger", views_staff.add_pax_na_lista_rodoviaria),
    path("api/staff/integracao_rodoviaria/atualiza_itinerario", views_staff.atualiza_itinerario_rodoviaria),
    path("api/staff/integracao_rodoviaria/atualizar-embarques-hibrido", views_staff.atualizar_embarques_hibrido),
    path("api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos", views_staff.cadastrar_grupos_hibridos_rodoviaria),
    path(
        "api/staff/integracao_rodoviaria/cadastrar-grupos-hibridos-params",
        views_staff.cadastrar_grupos_hibridos_rodoviaria_params,
    ),
    path("api/staff/integracao_rodoviaria/cadastrar-trecho", views_staff.cadastrar_trecho_rodoviaria),
    path(
        "api/staff/integracao_rodoviaria/cadastrar-trechos-por-grupo",
        views_staff.cadastrar_trechos_rodoviaria_por_grupo,
    ),
    path("api/staff/integracao_rodoviaria/cancelar-bpes", views_staff.cancelar_bpes),
    path("api/staff/integracao_rodoviaria/classes_e_precos_rota", views_staff.get_classes_e_precos_rota),
    path("api/staff/integracao_rodoviaria/company/create", views_staff.create_rodoviaria_company),
    path("api/staff/integracao_rodoviaria/company/list", views_staff.list_companies_rodoviaria),
    path("api/staff/integracao_rodoviaria/company/login", views_staff.get_rodoviaria_company_login),
    path("api/staff/integracao_rodoviaria/company/update", views_staff.update_rodoviaria_company),
    path("api/staff/integracao_rodoviaria/criar-itinerario-hibrido", views_staff.criar_itinerario_hibrido),
    path("api/staff/integracao_rodoviaria/criar-rota-hibrido", views_staff.criar_rota_hibrido),
    path("api/staff/integracao_rodoviaria/criar_grupos_markeplace", views_staff.criar_grupos_markeplace),
    path(
        "api/staff/integracao_rodoviaria/cronograma_atualizacao/list", views_staff.get_cronograma_atualizacao_rodoviaria
    ),
    path(
        "api/staff/integracao_rodoviaria/cronograma_atualizacao/update",
        views_staff.update_cronograma_atualizacao_rodoviaria,
    ),
    path(
        "api/staff/marketplace/managers/batch_update",
        views_staff.batch_update_managers,
    ),
    path("api/staff/integracao_rodoviaria/empresas/<int:company_id>", views_staff.get_integracao_empresa_rodoviaria),
    path("api/staff/integracao_rodoviaria/features", views_staff.list_all_possible_company_features),
    path(
        "api/staff/integracao_rodoviaria/fetch_external_totalbus_companies",
        views_staff.fetch_external_totalbus_companies,
    ),
    path(
        "api/staff/integracao_rodoviaria/fetch_rodoviaria_formas_pagamento",
        views_staff.fetch_rodoviaria_formas_pagamento,
    ),
    path(
        "api/staff/integracao_rodoviaria/fetch_trechos_vendidos_por_rota",
        views_staff.fetch_rodoviaria_trechos_vendidos_por_rota,
    ),
    path("api/staff/integracao_rodoviaria/get-empresas-integradas", views_staff.get_empresas_integradas_rodoviaria),
    path(
        "api/staff/integracao_rodoviaria/get-repasse-referencia-empresa/<int:company_id>",
        views_staff.get_repasse_referencia_empresa_marketplace,
    ),
    path("api/staff/integracao_rodoviaria/get-rotinas-integradas", views_staff.get_rotinas_integradas_rodoviaria),
    path(
        "api/staff/integracao_rodoviaria/get-taxa-servico-referencia-empresa/<int:company_id>",
        views_staff.get_taxa_servico_referencia_empresa_marketplace,
    ),
    path(
        "api/staff/integracao_rodoviaria/get_min_rotas_integrar/<int:company_id>",
        views_staff.get_min_rotas_integrar,
    ),
    path(
        "api/staff/integracao_rodoviaria/set_min_rotas_integrar/<int:company_id>",
        views_staff.set_min_rotas_integrar,
    ),
    path("api/staff/integracao_rodoviaria/get_auth_key_ti_sistemas", views_staff.get_auth_key_ti_sistemas),
    path(
        "api/staff/integracao_rodoviaria/get_informacoes_passagem_api_parceiro",
        views_staff.get_rodoviaria_atualizacao_passagem_api_parceiro,
    ),
    path("api/staff/integracao_rodoviaria/get_map_poltronas", views_staff.get_map_poltronas),
    path("api/staff/integracao_rodoviaria/get_passagem_info", views_staff.get_rodoviaria_passagem_info),
    path(
        "api/staff/integracao_rodoviaria/hard_stop_empresa",
        views_staff.hard_stop_empresa_rodoviaria,
    ),
    path("api/staff/integracao_rodoviaria/integracao/list", views_staff.list_integracoes_rodoviaria),
    path("api/staff/integracao_rodoviaria/itinerario", views_staff.check_itinerario_rodoviaria),
    path("api/staff/integracao_rodoviaria/itinerarios_marketplace", views_staff.get_itinerarios_marketplace),
    path("api/staff/integracao_rodoviaria/list_empresas_api", views_staff.lista_empresas_api),
    path("api/staff/integracao_rodoviaria/listpassengers", views_staff.lista_passageiros_viagem_rodoviaria),
    path("api/staff/integracao_rodoviaria/locais/fetch", views_staff.fetch_rodoviaria_locais),
    path("api/staff/integracao_rodoviaria/locais_retirada", views_staff.get_rodoviaria_locais_retirada),
    path("api/staff/integracao_rodoviaria/pos_salvar_rota", views_staff.pos_salvar_rota),
    path("api/staff/integracao_rodoviaria/removepassenger", views_staff.remove_pax_da_lista_rodoviaria),
    path("api/staff/integracao_rodoviaria/revert_hard_stop_empresa", views_staff.revert_hard_stop_empresa_rodoviaria),
    path("api/staff/integracao_rodoviaria/rota/rotinas", views_staff.get_rotinas_rota_marketplace),
    path("api/staff/integracao_rodoviaria/rota_marketplace_para_criar", views_staff.get_rota_marketplace_para_criar),
    path(
        "api/staff/integracao_rodoviaria/save-repasse-referencia-empresa/<int:company_id>",
        views_staff.save_repasse_referencia_empresa_marketplace,
    ),
    path(
        "api/staff/integracao_rodoviaria/save-taxa-servico-referencia-empresa/<int:company_id>",
        views_staff.save_taxa_servico_referencia_empresa_marketplace,
    ),
    path("api/staff/integracao_rodoviaria/sincronizar-rota-hibrido", views_staff.sincronizar_rota_hibrido),
    path("api/staff/integracao_rodoviaria/status-integracao", views_staff.get_status_integracao),
    path("api/staff/integracao_rodoviaria/tem_grupos_abertos", views_staff.tem_grupos_abertos),
    path("api/staff/integracao_rodoviaria/tipo_assento/link", views_staff.rodoviaria_linkar_tipo_assento),
    path("api/staff/integracao_rodoviaria/tipo_assento/list", views_staff.rodoviaria_listar_tipos_assentos),
    path("api/staff/integracao_rodoviaria/trechos_vendidos", views_staff.trechos_vendidos_rodoviaria),
    path("api/staff/integracao_rodoviaria/update-status-integracao", views_staff.update_status_integracao),
    path("api/staff/integracao_rodoviaria/update-status-integracao-grupo", views_staff.update_status_integracao_grupo),
    path("api/staff/integracao_rodoviaria/verify_praxio_login", views_staff.verify_praxio_login),
    path("api/staff/landing_pages", views_staff.list_landing_pages),
    path("api/staff/landing_pages/create", views_staff.create_page),
    path("api/staff/landing_pages/update", views_staff.update_page),
    path("api/staff/links_locais/links", views_staff.list_links_locais_embarque),
    path("api/staff/links_locais/update_link", views_staff.update_links_locais_embarque),
    path("api/staff/list_all_whatsapp_templates", views_staff.list_all_whatsapp_templates),
    path("api/staff/list_permissions", views_staff.list_permissions),
    path("api/staff/list_templates_comunicacao", views_staff.list_templates_comunicacao),
    path("api/staff/list_zap_templates", views_staff.list_zap_templates),
    path("api/staff/locais", views_staff.list_locais),
    path("api/staff/locais/<int:local_id>", views_staff.get_local),
    path("api/staff/locais_simple", views_staff.list_locais_simple),
    path("api/staff/locaisbycity/<int:city_id>", views_staff.get_local_by_city),
    path("api/staff/logs", views_staff.list_logs),
    path("api/staff/logs-rotina-onibus", views_staff_rotina.list_logs_rotina_onibus),
    path("api/staff/logs/reducao-frete/<int:grupo_id>", views_staff.houve_reducao_de_frete),
    path("api/staff/marketplace/retirada", views_staff.list_locais_retirada_marketplace),
    path("api/staff/marketplace/retirada/create", views_staff.create_local_retirada_marketplace),
    path("api/staff/marketplace/retirada/edit", views_staff.edit_local_retirada_marketplace),
    path("api/staff/marketplace/retirada/remove", views_staff.remove_local_retirada_marketplace),
    path(
        "api/staff/marketplace/auto_integra_operacao/atualizar_operacao_completa/<int:company_id>",
        views_staff.auto_integra_operacao_empresa_rodoviaria,
    ),
    path("api/staff/multas-qualidade/<int:multa_id>/aplicar", views_staff.aplicar_multa),
    path("api/staff/multas-qualidade/<int:multa_id>/descontar", views_staff.descontar_multa),
    path("api/staff/multas-qualidade/<int:multa_id>/cancelar", views_staff.cancelar_multa),
    path("api/staff/multas-qualidade/<int:multa_id>/contestar", views_staff.estornar_multa),
    path("api/staff/multas-qualidade/<int:multa_id>/editar", views_staff.editar_multa),
    path("api/staff/multas-qualidade/<int:multa_id>/estornar", views_staff.estornar_multa),
    path("api/staff/multas-qualidade/motivos", views_staff.list_motivos_multas_qualidade),
    path("api/staff/new/incidents/general/groups/infos", views_staff.new_incidents_general_group_infos),
    path("api/staff/novo_extrato_user", views_staff.get_new_buseiro_extrato),
    path(
        "api/staff/novo_extrato_user/<int:user_id>", views_staff.get_new_buseiro_extrato_temp
    ),  # temp - transição do front para endpoint paginado
    path("api/staff/onibus", views_staff.list_onibus),
    path("api/staff/onibus/placas", views_staff.list_onibus_placas),
    path("api/staff/onibus-keyset", views_staff.list_onibus_keyset),
    path("api/staff/onibus/<int:bus_id>/upload/<field>", views_staff.upload_doc_onibus),
    path("api/staff/onibus/<int:onibus_id>", views_staff.detalhar_onibus),
    path("api/staff/onibus/<int:onibus_id>/thumbnail", views_staff.thumbnail_onibus),
    path("api/staff/onibus/inactivate", views_staff.inactivate_onibus),
    path("api/staff/onibus/possible_filter_values", views_staff.onibus_possible_filter_values),
    path("api/staff/onibus/remove", views_staff.remove_onibus),
    path("api/staff/onibus/save", views_staff.save_onibus),
    path("api/staff/onibus/remove-poltrona", views_staff.remove_poltrona),
    path("api/staff/onibus_tipos_e_classes", views_staff.list_types_n_classes),
    path("api/staff/pagamento/divida/pendentes", views_staff.get_pagamentos_divida_pendentes),
    path("api/staff/paradas/create", views_staff.create_parada),
    path("api/staff/paradas/editrota", views_staff.edit_rota_parada),
    path("api/staff/parametros_precificacao", views_staff.get_parametros_precificacao),
    path("api/staff/parametros_precificacao/alterar", views_staff.altera_parametros_precificacao),
    path("api/staff/paymentlogs", views_staff.list_payment_logs),
    path("api/staff/plataformas", views_staff.list_plataformas),
    path("api/staff/plataformas/listgrupos", views_staff.plataformas_list_grupos),
    path("api/staff/plataformas/removeparada", views_staff.remove_parada_plataforma),
    path("api/staff/plataformas/setparada", views_staff.set_parada_plataformas),
    path("api/staff/ponto_de_venda/create", views_staff.create_ponto_de_venda),
    path("api/staff/ponto_de_venda/update", views_staff.update_ponto_de_venda),
    path("api/staff/ponto_de_venda/atribuir_gestor", views_staff.atribuir_gestor_ponto_de_venda),
    path("api/staff/ponto_de_venda/desvincular_gestor", views_staff.desvincular_gestor_ponto_de_venda),
    path("api/staff/pontos_de_venda", views_staff.list_pontos_de_venda),
    path("api/staff/pontos_de_venda/list_configuracao_pdv", views_staff.list_configuracao_ponto_de_venda),
    path("api/staff/pontos_de_venda/editar_configuracao_pdv", views_staff.editar_configuracao_pdv),
    path("api/staff/preview_notifications", views_staff.preview_notifications),
    path("api/staff/privacy/delete_user_data", views_staff.delete_user_data),
    path("api/staff/promos", views_staff.list_promos),
    path("api/staff/promos/categorias", views_staff.get_list_categorias_cupom),
    path("api/staff/promos/download_vouchers/<promo_code>", views_staff.download_vouchers_xls),
    path("api/staff/promos/more_vouchers", views_staff.add_more_vouchers),
    path("api/staff/promos/save", views_staff.create_or_update_promo),
    path("api/staff/remove_cupom", views_staff.remove_cupom),
    path("api/staff/remove_voucher", views_staff.remove_voucher),
    path("api/staff/removecredito", views_staff.remove_credito),
    path("api/staff/removepassengers", views_staff.removepassengers),
    path("api/staff/replacelocal/async", views_staff.replace_local_for_selected_routes_async),
    path("api/staff/replacelocalbatch", views_staff.replace_local_for_selected_routes_batch),
    path("api/staff/alterar_duracoes", views_staff.alterar_duracoes),
    path("api/staff/get_itinerario_dinamico", views_staff.get_itinerario_dinamico),
    path("api/staff/resend_comprovante_estorno/<int:payment_id>", views_staff.resend_comprovante_estorno),
    path("api/staff/resend_comprovante_saque", views_staff.resend_comprovante_saque),
    path(
        "api/staff/resend_comprovante_saque/<int:transaction_id>", views_staff.resend_comprovante_saque
    ),  # em transição no more REST
    path("api/staff/resend_invoice", views_staff.resend_invoice),
    path("api/staff/resend_invoice/<int:payment_id>", views_staff.resend_invoice),  # TODO No more REST
    path("api/staff/resend_phone_confirmation", views_staff.resend_phone_confirmation),
    path("api/staff/ressarcimento/canal_atendimento/create", views_staff.create_canal_atendimento),
    path("api/staff/ressarcimento/canal_atendimento/delete", views_staff.delete_canal_atendimento),
    path("api/staff/ressarcimento/canal_atendimento/list", views_staff.list_all_canais_atendimento),
    path("api/staff/ressarcimento/canal_atendimento/update", views_staff.update_canal_atendimento),
    path("api/staff/ressarcimentos/<int:group_id>", views.get_ressarcimentos),
    path("api/staff/ressarcimentos/lanche", views_staff.ressarcimento_lanche),
    path("api/staff/ressarcir_buseiros", views_staff.ressarcir_buseiros),
    path("api/staff/revendedores/assign_to_travel", views_staff.assign_revendedor_to_travel),
    path("api/staff/revendedores/list", views_staff.get_revendedores),
    path("api/staff/rota/<int:rota_id>/simular", views_staff.simular_edit_rota),  # em transição
    path("api/staff/rota/create", views_staff.create_rota),
    path("api/staff/rota/create_rota_por_excel", views_staff.create_rota_por_excel),
    path("api/staff/rota/list_bus_unsupported", views_staff.check_supported_bus),
    path("api/staff/rota/restricoes", views_staff.list_route_restrictions),
    path("api/staff/rota/restricoes/alternativa", views_staff.list_alternative_route_without_restrictions),
    path("api/staff/rota/restricoes/bypass", views_staff.create_restriction_bypass),
    path("api/staff/rota/simular", views_staff.simular_edit_rota),
    path("api/staff/rotas/<int:rota_id>", views_staff.get_rota_by_id),
    path("api/staff/rotas/<int:rota_id>/ativar", views_staff.edit_rota_status, kwargs={"ativo": True}),
    path("api/staff/rotas/<int:rota_id>/duracoes", views_staff.update_duracoes),
    path("api/staff/rotas/<int:rota_id>/inativar", views_staff.edit_rota_status, kwargs={"ativo": False}),
    path("api/staff/rotas/edit/rota/async", views_staff.edit_rota_async),
    path("api/staff/rotas/interromper_frete", views_staff.interromper_frete_grupo),
    path("api/staff/rotasbylocalid", views_staff.list_rotas_by_local_id),
    path("api/staff/rotasprincipais", views_staff.list_rotas_principais),
    path("api/staff/rotasprincipais/<int:rota_principal_id>/cidades", views_staff.adiciona_cidade_rota_principal),
    path(
        "api/staff/rotasprincipais/<int:rota_principal_id>/cidades/<int:cidade_id>",
        views_staff.remove_cidade_rota_principal,
    ),
    path("api/staff/rotina-onibus/list", views_staff_rotina.list_rotinas_onibus),
    path("api/staff/rotina-onibus/save", views_staff_rotina.save_rotina_onibus),
    path("api/staff/rotina-onibus/<int:rotina_id>", views_staff_rotina.get_rotina_by_id),
    path("api/staff/rotina-onibus/<int:rotina_id>/groups", views_staff_rotina.get_groups_from_rotina),
    path("api/staff/rotina-onibus/<int:rotina_id>/rotas", views_staff_rotina.get_routes_from_rotina),
    path("api/staff/rotina-onibus/calcular_frete_sugerido", views_staff_rotina.calculate_costs),
    path("api/staff/rotina-onibus/contar_grupos_ativos", views_staff_rotina.contar_grupos_ativos),
    path("api/staff/rotina-onibus/ids_rotinas_grupo_extra", views_staff_rotina.get_ids_rotinas_grupo_extra),
    path("api/staff/rotina-onibus/reactive", views_staff_rotina.reativar_rotina),
    path("api/staff/rotina-onibus/remove", views_staff_rotina.inativar_rotina),
    path("api/staff/save-rotina-onibus-inativo", views_staff_rotina.escalar_rotina_onibus_inativo),
    path("api/staff/save_city_info", views_staff.save_cidade_info),
    path("api/staff/search", views_staff.search),
    path("api/staff/searchuser", views_staff.search_user),
    path("api/staff/send_notifications", views_staff.send_notifications),
    path("api/staff/send_whatsapp", views_staff.send_whatsapp),
    path("api/staff/send_whatsapp_template", views_staff.send_whatsapp_template),
    path("api/staff/send_whatsapp_template_v2", views_staff.send_whatsapp_template_v2),
    path("api/staff/send_whatsapp_v2", views_staff.send_whatsapp_v2),
    path("api/staff/set_revendedor", views_staff.set_revendedor_attributes),
    path("api/staff/simularreplacelocal", views_staff.simula_replace_local_for_selected_routes),
    path("api/staff/replace-restrictions", views_staff.replace_restrictions),
    path("api/staff/tags", views_staff.get_tags),
    path("api/staff/tags/update", views_staff.update_tag_cpf),
    path("api/staff/tags/delete", views_staff.delete_tag_cpf),
    path("api/staff/telemetria/<int:grupo_id>/eventos", views_staff.list_telemetria),
    path("api/staff/tracking-records/<int:grupo_id>", views_staff.tabela_auxiliar_telemetria_debug),
    path("api/staff/textos/save", views_staff.salvar_texto),
    path("api/staff/togglecheckin", views_staff.togglecheckin),
    path("api/staff/total_ressarcimento", views.get_total_ressarcimento),
    path("api/staff/travels/<int:travel_id>", views_staff.get_travel),
    path("api/staff/travels/<int:travel_id>/<version>", views_staff.get_travel),
    path("api/staff/updatebuseiro", views_staff.updatebuseiro),
    path("api/staff/updatelocal", views_staff.update_local),
    path("api/staff/user/<int:user_id>/unverify", views_staff.unverify_user),  # em transição
    path("api/staff/user/<int:user_id>/update", views_staff.update_user),  # em transição
    path("api/staff/user/unverify", views_staff.unverify_user),
    path("api/staff/user/update", views_staff.update_user),
    path("api/staff/user_communication/<channel>/<int:message_id>/content", views_staff.get_email_content),
    path("api/staff/user_communication_logs", views_staff.list_user_communication_logs),
    path("api/staff/user_documents/<int:user_id>", views_staff.get_user_documents),
    path("api/staff/valores_ressarcimento", views_staff.get_active_valores_ressarcimento),
    path("api/staff/valores_ressarcimento/create", views_staff.create_valor_ressarcimento),
    path("api/staff/valores_ressarcimento/inactivate", views_staff.inactivate_valor_ressarcimento),
    path("api/staff/valores_ressarcimento/list_all", views_staff.get_all_valores_ressarcimento),
    path("api/staff/valores_ressarcimento/reactivate", views_staff.reactivate_valor_ressarcimento),
    path("api/staff/valores_ressarcimento/update", views_staff.update_valor_ressarcimento),
    path("api/staff/verifica_dados_receita", views_staff.verifica_dados_receita),
    path("api/staff/verify_user", views_staff.verify_user),
    path("api/staff/wikipedia_page", views_staff.get_wikipedia_page),
    path("api/staff/feriados", views_staff.list_feriados),
    path("api/staff/feriados/save", views_staff.create_or_update_feriado),
    path("api/staff/feriados/remove", views_staff.delete_feriado),
    path("api/static/new/incidents/geofence", views_staff.update_geofence),
    path("api/staff/risk-score/<int:user_id>", views_staff.risk_score),
    path("api/staff/user-features/<int:user_id>", views_staff.get_user_features),
    path("api/staff/conexao", views_staff.lista_conexoes),
    path("api/staff/conexao/save", views_staff.create_conexao),
    path("api/staff/conexao/update", views_staff.update_conexao),
    # evento extra
    path("api/staff/evento-extra/save", views_staff.evento_extra_save),
    path("api/staff/evento-extra/<int:evento_extra_id>/details", views_staff.evento_extra_details),
    path("api/staff/evento-extra/<int:evento_extra_id>/delete", views_staff.evento_extra_delete),
    path("api/staff/evento-extra/list", views_staff.evento_extra_list),
    # solicitação
    path("api/staff/evento-extra/solicitacao/save", views_staff.evento_extra_solicitacao_save),
    path("api/staff/evento-extra/solicitacao/<int:solicitacao_id>/delete", views_staff.evento_extra_solicitacao_delete),
    # negociação
    path("api/staff/evento-extra/negociacao/save", views_staff.evento_extra_negociacao_save),
    path("api/staff/evento-extra/negociacao/<int:negociacao_id>/delete", views_staff.evento_extra_negociacao_delete),
    # perna
    path("api/staff/evento-extra/perna/save", views_staff.evento_extra_solicitacao_perna_save),
    path("api/staff/evento-extra/perna/<int:perna_id>/delete", views_staff.evento_extra_solicitacao_perna_delete),
    path("api/staff/evento-extra/perna/list", views_staff.evento_extra_solicitacao_perna_list),
]
