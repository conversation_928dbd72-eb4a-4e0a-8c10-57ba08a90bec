import json
from decimal import Decimal
from functools import reduce
from http import HTT<PERSON>tatus
from uuid import uuid4

import sentry_sdk
from django.contrib.auth.models import User
from django.core.exceptions import SuspiciousOperation, ValidationError
from django.http import Http404, HttpResponse, JsonResponse
from django.shortcuts import get_list_or_404, get_object_or_404, redirect, render
from django.views.decorators.cache import cache_control
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_GET, require_POST
from pydantic.v1 import ValidationError as PydanticValidationError

from accounting.service import accounting_svc
from adapters.stark_adapter.exceptions import StarkBankError
from commons import dateutils, guard, storage
from commons.django_model_utils import get_or_none
from commons.django_utils import error_str
from commons.django_views_utils import (
    ajax_login_required,
    ajax_roles_required,
    ajax_staff_required,
    api_key_required,
    checkout_deslogado_login_fallback,
    feature_hiding,
    geolocation,
    owner,
    public_endpoint,
    verifygrecaptcha,
)
from commons.grouputils import hashid_grupos, unhashid_grupos
from commons.logging import buserlogger
from commons.phone_helper import validate_phone
from commons.squads import SQUAD_AQUISICAO, SQUAD_PVV
from commons.utils import hashint, unhashint
from core.forms import core_forms
from core.forms.core_forms import (
    AddonData,
    ChatbotSearchTravelForm,
    ChatbotSetContextForm,
    ChatbotTriggerEventForm,
    ListGroupsCuponsData,
    PromoDestaqueSearchResultForm,
    PromogroupsForm,
    TravelForm,
    TravelForNoShowCupomForm,
    TrechoForm,
)
from core.forms.pagamento_forms import BankAccountForm
from core.forms.top_destinos_forms import TopDestinosForm
from core.models_grupo import Grupo, TrechoClasse
from core.models_travel import Pagamento, Passageiro, Reserva, Travel
from core.serializers import (
    serializer_gift_card,
    serializer_travel,
    serializer_trecho_classe,
)
from core.service import (
    auth_svc,
    baixa_ocupacao_svc,
    carrossel_home_svc,
    cep_svc,
    chatbot_svc,
    cupomadm_svc,
    gift_cards_svc,
    greatpages_svc,
    grupos_svc,
    landing_page_svc,
    lead_svc,
    log_svc,
    metabase_svc,
    profile_svc,
    rodoviaria_svc,
    taxa_cancelamento_svc,
    trecho_favorito_svc,
    user_documents_svc,
)
from core.service.baixa_ocupacao_svc import is_trecho_classe_low_occupation
from core.service.feriado import feriado_svc
from core.service.grupos_staff import ressarcimento_svc
from core.service.notifications import user_notification_svc
from core.service.pagamento import bank_svc, pagamento_svc
from core.service.price_ab_svc import ab_price
from core.service.reserva import cupom_svc, reserva_svc
from core.service.reserva.promocao.promo_trecho_baixa_ocupacao import (
    PROMO_CODE_LOW_OCCUPATION,
)
from integrations.rodoviaria_client.exceptions import (
    CancelamentoNaoDisponivelException,
    PassageiroJaCanceladoException,
    PassengerTicketAlreadyPrintedException,
    PoltronaTrocadaException,
    RodoviariaException,
)
from search_result.service import grupos_svc as search_grupos_svc
from search_result.service.grupos_svc import SearchItemHotOffer
from threadlocalrequest import threadlocals

# A ideia é com o tempo ir separando as views que tão aqui dentro de arquivos no diretório e só importar


@ajax_login_required
def private_storage(request, model_name, y, m, d, model_id, field_name, filename):
    filename_override = request.GET.get("filename_override")
    key = f"private/{model_name}/{y}/{m}/{d}/{field_name}/{model_id}/{filename}"
    return legacy_storage(request, model_name, model_id, field_name, key, filename_override)


@ajax_login_required
def legacy_storage(request, model_name, model_id, field_name, key, filename_override=None):
    logged_user = request.user
    if auth_svc.has_model_access(logged_user, model_name, model_id, **{field_name: key}):
        url = storage.private_url(key, expire=60, filename_override=filename_override)
        return redirect(url)
    raise SuspiciousOperation("Você não tem permissão para visualizar esse conteúdo.")


@public_endpoint
def servertime(request):
    return JsonResponse(dateutils.now().isoformat(), safe=False)


@public_endpoint
@csrf_exempt
@cache_control(public=True, immutable=True, max_age=300)
def get_top_destinos(request):
    try:
        form = TopDestinosForm.parse_obj(request.GET.dict())
        result = grupos_svc.get_top_destinos(form.slugList)
        return JsonResponse(result, safe=False)
    except PydanticValidationError:
        buserlogger.exception("invalid request (view=get_top_destinos)")
        return JsonResponse({"error": "Bad request."}, status=400)


@public_endpoint
@csrf_exempt
@owner(SQUAD_AQUISICAO)
@require_POST
def link_pagamento_purchase(request):
    params = json.loads(request.POST.get("params"), parse_float=Decimal)
    code = params.get("code")
    try:
        data = reserva_svc.link_pagamento_get(code, reservation_data_cleaned=True)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    if data.get("status") == "paid":
        return JsonResponse({"error": "reserva já foi paga"})
    data["payment"] = params.get("payment")
    data["client"] = data.get("client", request.headers.get("client"))
    data["bypass_low_occupation"] = True
    data["acceptance"] = params.get("acceptance", False)
    unhashid_grupos(data["groups"])
    user_revenda = User.objects.get(pk=data["user_revenda"])

    travels, reserva = reserva_svc.efetuar_reserva_sincrona(data, user_revenda)

    if reserva.status == Reserva.Status.ERRO_NO_PROCESSAMENTO and reserva.error_data:
        return JsonResponse(reserva.error_data, safe=False)

    reserva_svc.set_pagamento_link_pagamento(code, travels[0])
    travelsdic = serializer_travel.serialize_purchase_travels_signed(travels)
    return JsonResponse(travelsdic, safe=False)


@require_POST
@owner(SQUAD_AQUISICAO)
@ajax_roles_required(["Revendedor"])
def link_pagamento_create(request):
    reserva_data = json.loads(request.POST.get("data"))
    reserva_data["client"] = request.headers.get("client")
    price_token = getattr(request.user, "price_token", None)
    reserva_data["price_token"] = price_token
    user = request.user
    try:
        code = reserva_svc.link_pagamento_create(reserva_data, user)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse({"code": code})


@public_endpoint
@owner(SQUAD_AQUISICAO)
def link_pagamento_get(request):
    code = request.GET.get("code", "")
    try:
        data = reserva_svc.link_pagamento_get(code, payment_info=True)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse(data, safe=False)


@public_endpoint
@require_GET
def link_pagamento_get_sanitized(request):
    code = request.GET.get("code")
    try:
        dados_pagamento = reserva_svc.get_dados_link_pagamento_sanitizados(code)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e), "code": "validation_error"})
    return JsonResponse(dados_pagamento)


@ajax_login_required
@owner(SQUAD_PVV)
def travels(request):
    form = TravelForm.parse_obj(request.GET.dict())
    travels = grupos_svc.user_travels_queryset(
        user=request.user, kind=form.kind, only_valid=form.only_valid, linked_user=True
    )[: form.limit]
    dtravels = serializer_travel.serialize_list_travels(travels)
    return JsonResponse(dtravels, safe=False)


@ajax_login_required
@require_POST
def travel_contact_phone(request):
    user = request.user
    travel_id = request.POST.get("id")
    contact_phone = request.POST.get("phone")
    validate_phone(contact_phone)
    try:
        res = reserva_svc.travel_contact_phone(travel_id, contact_phone, user)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)}, status=400)
    return JsonResponse(res, safe=False)


@public_endpoint
@require_POST
def chatbot_set_context(request):
    request_dict = request.POST.dict()
    request_dict["parameters"] = json.loads(request_dict.get("parameters", "{}"))
    form = ChatbotSetContextForm(**request_dict)
    response = chatbot_svc.set_chatbot_context(**form.dict())
    return JsonResponse(response)


@public_endpoint
@require_POST
def chatbot_trigger_event(request):
    form = ChatbotTriggerEventForm(**request.POST.dict())
    response = chatbot_svc.chatbot_trigger_event(**form.dict())
    return JsonResponse(response)


@public_endpoint
@api_key_required("chatbot")
def chatbot_search_travel(request):
    form = ChatbotSearchTravelForm(**request.GET.dict())
    travel = chatbot_svc.search_next_travel_of_day(form)
    if not travel:
        return JsonResponse({})
    return JsonResponse(
        serializer_travel.serialize_object(
            travel,
            withveiculoinfo=True,
            with_embarque_status=True,
            withcompanyinfo=True,
            withitinerario=True,
        )
    )


@ajax_login_required
@owner(SQUAD_PVV)
def simula_geracao_cupom_noshow(request, travel_id):
    form = TravelForNoShowCupomForm(travel_id=travel_id)

    if form.travel.user_id != request.user.id and not guard.is_staff(request.user):
        raise ValidationError("Ops! Parece que você não tem acesso a estas informações")

    passageiros = form.travel.passageiro_set.filter(checkin=False)
    passageiros_com_promocao = passageiros.filter(promocao__isnull=False)
    passageiros_sem_promocao = passageiros.difference(passageiros_com_promocao)

    cupons = cupomadm_svc.simula_cupons_noshow(passageiros_com_promocao) + cupomadm_svc.simula_cupons_noshow(
        passageiros_sem_promocao
    )

    return JsonResponse({"cupons": cupons})


@ajax_login_required
@owner(SQUAD_PVV)
def gera_cupom_noshow(request, travel_id, reason_key=None):
    form = TravelForNoShowCupomForm(travel_id=travel_id)

    if form.travel.user_id != request.user.id and not guard.is_staff(request.user):
        raise ValidationError("Ops! Parece que você não tem acesso a estas informações")

    has_cupom_noshow = cupomadm_svc.give_cupom_noshow(form.travel, reason_key)

    if not has_cupom_noshow:
        return JsonResponse({"error": "Não foi possível gerar seu cupom"})
    return JsonResponse({})


@ajax_login_required
def resend_invoice(request, payment_id=None):
    if payment_id is None:
        payment_id = request.GET["payment_id"]
    payment_id = int(payment_id)
    _throw_if_not_user_payment(payment_id, request.user)

    try:
        pagamento_svc.resend_invoice(payment_id, evento="Django - reenviar comprovante")
    except ValidationError as exc:
        return JsonResponse({"error": exc.message}, status=HTTPStatus.UNPROCESSABLE_ENTITY)

    return JsonResponse({})


@ajax_login_required
def resend_comprovante_pagamento(request, travel_id):
    travel = get_object_or_404(Travel, id=travel_id)
    _throw_if_not_travel_owner("reenvio comprovante", travel, request.user)

    if travel.pagamento and travel.pagamento.status != Pagamento.Status.PAID:
        status = travel.pagamento.status
        if status == Pagamento.Status.WAITING_PAYMENT:
            return JsonResponse(
                {"error": "Pagamento não confirmado, efetue o pagamento e aguarde a confirmação"},
                status=HTTPStatus.UNPROCESSABLE_ENTITY,
            )
        elif status == Pagamento.Status.REFUNDED:
            return JsonResponse(
                {"error": "Pagamento estornado, consulte o comprovante de reembolso na sua carteira Buser"},
                status=HTTPStatus.UNPROCESSABLE_ENTITY,
            )
        else:
            return JsonResponse(
                {"error": "Pagamento não confirmado, não é possível emitir comprovante"},
                status=HTTPStatus.UNPROCESSABLE_ENTITY,
            )

    try:
        user_notification_svc.enviar_comprovante_pagamento(travel, evento="views.resend_comprovante_pagamento")
    except ValidationError as exc:
        return JsonResponse({"error": exc.message}, status=HTTPStatus.BAD_REQUEST)

    return JsonResponse({})


@ajax_login_required
def resend_comprovante_estorno(request, payment_id):
    payment_id = int(payment_id)
    _throw_if_not_user_payment(payment_id, request.user)
    try:
        pagamento_svc.resend_comprovante_estorno(payment_id, evento="views.resend_comprovante_estorno")
    except ValidationError as exc:
        return JsonResponse({"error": exc.message}, status=HTTPStatus.BAD_REQUEST)

    return JsonResponse({})


@csrf_exempt
@ajax_login_required
def list_groups_cupons(request):
    try:
        params = {"trecho_classe_ids": json.loads(request.POST["trecho_classe_ids"])}
    except Exception:
        params = request.POST.dict()
    data = ListGroupsCuponsData.parse_obj(params)
    trecho_classe_ids = [unhashint(tc_id) for tc_id in data.trecho_classe_ids][:200]
    available_cupons = cupom_svc.get_groups_valid_cupons(request.user, trecho_classe_ids)
    return JsonResponse({"cupons": available_cupons})


@ajax_login_required
@require_GET
def resend_comprovante_saque(request, transaction_id=None):
    if transaction_id is None:
        transaction_id = int(request.GET["transaction_id"])
    try:
        pagamento_svc.resend_comprovante_saque(
            transaction_id,
            request.user,
            "Saque do usuário - Reenvio comprovante - views.resend_comprovante_saque",
        )
    except (ValidationError, SuspiciousOperation):
        return HttpResponse("Acesso proibido", status=403)
    return JsonResponse({})


@ajax_login_required
@require_POST
@owner(SQUAD_PVV)
def cancel_travel(request, travel_id):
    travel_id = int(travel_id)
    user_reason = request.POST.get("reason", "")
    travel = get_object_or_404(
        Travel.objects.select_related("trecho_conexao", "travel_conexao", "grupo"),
        id=travel_id,
    )
    _throw_if_not_travel_owner("cancel_travel", travel, request.user)
    try:
        reserva_svc.cancel_travels(
            travel,
            user_reason,
            por_revendedor=guard.is_revendedor(request.user),
        )
    except (
        PassengerTicketAlreadyPrintedException,
        PoltronaTrocadaException,
        CancelamentoNaoDisponivelException,
        PassageiroJaCanceladoException,
    ) as ex:
        return JsonResponse({"error": ex.message, "help": [ex.help], "type": ex.type})
    except RodoviariaException as ex:
        return JsonResponse({"error": ex.message, "help": [ex.help], "type": "rodoviaria_blocked"})
    return JsonResponse({})


@ajax_login_required
@require_GET
@owner(SQUAD_PVV)
def get_taxa_cancelamento(request, travel_id):
    try:
        response = taxa_cancelamento_svc.get_taxa_cancelamento_by_travel(travel_id)
    except Exception as ex:
        return JsonResponse({"error": ex.message}, safe=False, status=400)
    return JsonResponse(response)


@checkout_deslogado_login_fallback
@require_GET
@owner(SQUAD_PVV)
def get_taxa_cancelamento_grupos(request):
    id_ida = request.GET.get("idIda")
    id_volta = request.GET.get("idVolta")
    id_travel_remarcada = request.GET.get("idTravelRemarcada")

    if not id_ida:
        return JsonResponse(
            {"error": "Desculpe, ocorreu um erro. Realize a sua busca novamente"},
            content_type="application/json",
            status=400,
        )

    try:
        id_ida = unhashint(id_ida)
        if id_volta:
            id_volta = unhashint(id_volta)
        response = taxa_cancelamento_svc.get_taxa_cancelamento_by_grupo(id_ida, id_volta, id_travel_remarcada)
    except Exception as ex:
        return JsonResponse({"error": ex}, safe=False, status=400)
    return JsonResponse(response)


@checkout_deslogado_login_fallback
@require_GET
@owner(SQUAD_PVV)
def get_taxa_cancelamento_grupos_v2(request):
    ids = request.GET.getlist("ids[]")
    ids = [unhashint(_id) for _id in ids]
    id_travel_remarcada = request.GET.get("idTravelRemarcada")

    if not ids:
        return JsonResponse(
            {"error": "Desculpe, ocorreu um erro. Realize a sua busca novamente"},
            content_type="application/json",
            status=400,
        )

    try:
        response = taxa_cancelamento_svc.get_taxa_cancelamento_by_grupo_v2(ids, id_travel_remarcada)
        return JsonResponse(response)
    except Exception as ex:
        return JsonResponse({"error": ex}, safe=False, status=400)


@ajax_login_required
@require_POST
@owner(SQUAD_PVV)
def remove_passenger(request, passenger_id):
    passenger_id = int(passenger_id)
    passenger = Passageiro.objects.get(pk=passenger_id)
    _throw_if_not_travel_owner("remove_passenger", passenger.travel, request.user)
    try:
        reserva_svc.remove_passenger(passenger, por_revendedor=guard.is_revendedor(request.user))
    except RodoviariaException as e:
        return JsonResponse({"error": e.message, "help": [e.help]})
    return JsonResponse({})


@ajax_login_required
@require_POST
@owner(SQUAD_PVV)
def travel_feedback(request, travel_id):
    travel_id = int(travel_id)
    feedback = json.loads(request.POST.get("feedback", "{}"))
    travel = get_or_none(Travel, id=travel_id)
    _throw_if_not_travel_owner("travel_feedback", travel, request.user)
    f = grupos_svc.travel_feedback(travel_id, feedback=feedback)
    return JsonResponse(f.to_dict_json())


@public_endpoint
def get_trecho_classe(request):
    ids = []
    id_ida = request.GET.get("idIda")
    id_volta = request.GET.get("idVolta")

    if not id_ida:
        return JsonResponse(
            {"error": "Desculpe, ocorreu um erro. Realize a sua busca novamente"},
            content_type="application/json",
            status=400,
        )

    try:
        id_ida = unhashint(id_ida)
        ids.append(id_ida)
        if id_volta:
            id_volta = unhashint(id_volta)
            ids.append(id_volta)
    except ValueError:
        pass

    dgrupos_map = _get_trecho_classes_by_ids(ids, request.user)

    grupo_ida = dgrupos_map.get(id_ida)
    grupo_volta = dgrupos_map.get(id_volta)

    return JsonResponse(
        {
            "grupoIda": grupo_ida,
            "grupoVolta": grupo_volta,
        }
    )


@public_endpoint
def get_trecho_classe_v2(request):
    ids_ida = request.GET.getlist("idsIda[]", [])
    ids_volta = request.GET.getlist("idsVolta[]", [])

    if not ids_ida:
        return JsonResponse(
            {"error": "Desculpe, ocorreu um erro. Realize a sua busca novamente"},
            content_type="application/json",
            status=400,
        )

    ids_ida = [unhashint(_id) for _id in ids_ida]
    ids_volta = [unhashint(_id) for _id in ids_volta]
    dgrupos_map = _get_trecho_classes_by_ids(ids_ida + ids_volta, request.user)
    for grupo in dgrupos_map.values():
        grupo["is_trecho_promocional"] = is_trecho_classe_low_occupation(grupo["id"])

    grupos_ida = [dgrupos_map[_id] for _id in ids_ida if _id in dgrupos_map]
    grupos_volta = [dgrupos_map[_id] for _id in ids_volta if _id in dgrupos_map]

    return JsonResponse(
        {
            "gruposIda": grupos_ida,
            "gruposVolta": grupos_volta,
        }
    )


@public_endpoint
@require_POST
def upsert_grupo(request):
    if request.POST.get("grupo"):
        search_item = SearchItemHotOffer.from_dgroup(json.loads(request.POST.get("grupo")))
    else:
        # mantendo compatibilidade com front antigo
        search_item = SearchItemHotOffer.from_dgroup(request.POST.dict())

    tc_id, max_split_value = search_grupos_svc.upsert_grupo(search_item)

    logctx = threadlocals.get_log_ctx()
    user_tabid = logctx.get("user_tabid")
    signed = search_grupos_svc.signed_dgroup(tc_id, max_split_value, max_split_value, user_tabid)

    return JsonResponse({"id": hashint(tc_id), "signed": signed})


def _get_trecho_classes_by_ids(ids, user):
    trechos = list(TrechoClasse.objects.filter(pk__in=ids))
    current_price_by_trecho = _get_current_price_by_trecho(trechos, user)

    dtrechos = serializer_trecho_classe.serialize(
        trechos,
        withitinerario=True,
        marketplace_info=True,
        companyinfo=True,
        taxa_servico_info=True,
        user=user,
        with_marcacao_assento=True,
    )
    trechos_map = {}
    for dtrecho in dtrechos:
        # TODO: ainda precisa garantir o preço que exibido na SRP
        dtrecho["max_split_value"] = current_price_by_trecho[dtrecho["id"]]
        trechos_map[dtrecho["id"]] = dtrecho

    # hashids depois de montar o map
    hashid_grupos(dtrechos)
    return trechos_map


def _get_current_price_by_trecho(trechos, user) -> dict[int, Decimal]:
    precos_atuais_by_trecho = {}
    for trecho in trechos:
        # Usuário que caiu em teste de preço
        if hasattr(user, "price_token"):
            current_price = ab_price(user, trecho, trecho.max_split_value_bucket, user.price_token)
        else:
            current_price = trecho.max_split_value_bucket
        precos_atuais_by_trecho[trecho.pk] = current_price

    return precos_atuais_by_trecho


@public_endpoint
def list_trechos_gratis(request):  # TODO: remover após as chamadas do front zerarem
    return JsonResponse([], safe=False)


@public_endpoint
def get_promo_cupom(request, code):
    code = code.strip().upper()
    validate = request.GET.get("validate") == "true"
    device_token = request.GET.get("device_token")
    user = request.user if request.user.is_authenticated else None

    # TO-DO: Remover após validar promoção de techos com baixa ocupação
    if code == PROMO_CODE_LOW_OCCUPATION:
        return JsonResponse({"error": "Nenhum cupom encontrado"}, safe=False)

    try:
        res = cupom_svc.get_cupom_info(code, user=user, validate_user=validate, device_token=device_token)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)}, safe=False)
    return JsonResponse(res, safe=False)


@public_endpoint
@require_GET
@geolocation
@cache_control(public=True, immutable=True, max_age=1 * 60 * 60)
def get_promo_destaque_home(request):
    cupons = cupomadm_svc.list_all_home_promos()
    cupons_serialized = [ch.serialize() for ch in cupons]
    card_promocoes_com_localizacao = carrossel_home_svc.build_card_promocoes_com_localizacao(request.geo.slug)
    res = cupons_serialized + card_promocoes_com_localizacao
    return JsonResponse(res, safe=False)


@public_endpoint
@require_GET
@geolocation
@cache_control(public=True, immutable=True, max_age=1 * 60 * 60)
def get_num_promos_destaque(request):
    cupons = cupomadm_svc.list_all_home_promos()
    num_promos_destaque = len(cupons)
    card_promocoes_com_localizacao = carrossel_home_svc.build_card_promocoes_com_localizacao(request.geo.slug)
    num_promocoes_com_localizacao = len(card_promocoes_com_localizacao)
    return JsonResponse(num_promos_destaque + num_promocoes_com_localizacao, safe=False)


@public_endpoint
def get_addons_extrato(request):
    try:
        params = AddonData(**request.GET.dict())
    except PydanticValidationError:
        response_dict = {
            "valorCarbono": 0,
            "valorSeguroExtra": 0,
            "isSeguroExtraPermitido": False,
            "valoresMarcacaoAssento": {},
            "valorBagagemAdicional": 0,
            "addonsPriceConfig": {},
        }
        return JsonResponse(response_dict)

    ids = []
    id_ida = params.id_ida
    id_volta = params.id_volta

    id_ida = unhashint(id_ida)
    ids.append(id_ida)
    if id_volta:
        id_volta = unhashint(id_volta)
        ids.append(id_volta)

    response_dict = accounting_svc.calcular_valores_addons(ids, request.user)
    return JsonResponse(response_dict)


@public_endpoint
def get_addons_extrato_v2(request):
    try:
        ids = request.GET.getlist("ids[]")
        ids = [unhashint(_id) for _id in ids]
    except KeyError:
        response_dict = {
            "valorCarbono": 0,
            "valorSeguroExtra": 0,
            "isSeguroExtraPermitido": False,
            "valoresMarcacaoAssento": {},
            "valorBagagemAdicional": 0,
            "addonsPriceConfig": {},
        }
        return JsonResponse(response_dict)

    response_dict = accounting_svc.calcular_valores_addons(ids, request.user)
    return JsonResponse(response_dict)


@public_endpoint
def list_promo_groups(request):
    user = request.user
    try:
        form = PromogroupsForm.parse_obj(request.GET.dict())
        grupos = cupom_svc.list_cupom_groups(
            form.code,
            form.origem_slug,
            form.destino_slug,
            form.departure_date,
            user,
            form.device_token,
        )
    except ValidationError:
        return JsonResponse([], status=400, safe=False)
    hashid_grupos(grupos)
    return JsonResponse(grupos, safe=False)


@ajax_login_required
def extrato(request):
    page = int(request.GET.get("page", 1))
    items_per_page = int(request.GET.get("itemsPerPage", 50))
    user_operations = accounting_svc.get_extrato(request.user, page, items_per_page)
    # user_refundable_operations = accounting_svc.get_refundable_operations(request.user)
    res = {
        "operations": user_operations["doperations"],
        "total_length": user_operations["total_length"],
    }
    return JsonResponse(res, safe=False)


@ajax_login_required
def get_bank_list(request):
    return JsonResponse(bank_svc.list_banks(), safe=False)


@ajax_login_required
def bank_account(request):
    return JsonResponse(bank_svc.get_bank_account(request.user), safe=False)


@ajax_login_required
@feature_hiding("enable_saque")
@require_POST
@owner(SQUAD_PVV)
def saque(request):
    value = Decimal(request.POST["value"])
    bank_account_dict = BankAccountForm.parse_raw(request.POST["bank_account"]).dict()

    try:
        res = bank_svc.solicitar_saque(request.user, value, bank_account_dict)
        return JsonResponse(res)
    except (ValidationError, StarkBankError) as e:
        log_svc.log_saque_error(None, value, bank_account_dict, error_str(e))
        return JsonResponse({"error": error_str(e)})
    except Exception as e:
        log_svc.log_saque_error(None, value, bank_account_dict, error_str(e))
        raise e


@ajax_login_required
@feature_hiding("enable_saque")
def confirm_transaction(request, transaction_id, confirmation_code):
    user = request.user
    try:
        res = bank_svc.confirm_transaction(user, transaction_id, confirmation_code)
        return JsonResponse(res)
    except ValidationError as e:
        res = {"error": error_str(e)}
        error_data = e.params if isinstance(e.params, dict) else {}
        if "type" in error_data:
            res["error_type"] = error_data["type"]
        log_svc.log_confirm_transaction_error(transaction_id, error_data)
        return JsonResponse(res)


@ajax_login_required
def get_cupom_lead(request):
    data = request.GET.get("data")
    user = request.user
    if data:
        data = json.loads(data)
        user = reserva_svc.get_travel_user(request.user, data)
    lead = user.lead
    cupons = cupom_svc.get_cupom_lead(lead)
    return JsonResponse({"cupons": cupons})


# TODO: Front removido em 2023-03-10. Olha no Kibana quando tiver pouco request pra matar o endpoint.
@ajax_login_required
def migrated_buedas(request):
    return JsonResponse({"migrated_buedas": False})


@public_endpoint
@cache_control(public=True, immutable=True, max_age=24 * 60 * 60)
def consulta_cep(_, cep):
    try:
        return JsonResponse(cep_svc.consulta_cep(cep))
    except Exception as e:
        buserlogger.warning("consulta_cep.error", extra=dict(cep=cep, error=str(e)))
        return JsonResponse({"error": "CEP desconhecido"})


@ajax_login_required
@require_POST
def validateverifyaccount(request):
    user_data = json.loads(request.POST.get("data"))
    cpf = user_data.get("cpf", None)
    try:
        profile_svc.validate_cpf_is_not_being_used_by_someone(cpf, request.user, True)
    except ValidationError as e:
        return JsonResponse({"cpf_error": error_str(e)})
    return JsonResponse({})


@ajax_login_required
@require_POST
def verifyaccount(request):
    user_id = request.user.id
    user_data = json.loads(request.POST["data"])
    fullname = user_data["name"]
    birth = user_data["birth"]
    rg = user_data.get("rg", None)
    cpf = user_data.get("cpf", None)
    passport = user_data.get("passport", None)
    pics = user_data["pics"]
    try:
        user_documents_svc.send_documents(user_id, fullname, birth, rg, cpf, passport, pics)
        log_svc.log_verify_account(user_data)
    except ValidationError as e:
        return JsonResponse({"error": error_str(e)})
    return JsonResponse({})


@owner(SQUAD_AQUISICAO)
@ajax_roles_required(["Revendedor"])
def get_link_info_buseiro(request):
    user_id = request.GET.get("user_id")
    url = metabase_svc.get_iframe_url_dashboard_informacoes_buseiro(user_id)
    return JsonResponse({"url": url})


@ajax_login_required
def get_status_verificar_cadastro(request):
    status = user_documents_svc.get_status_verificar_cadastro(request.user)
    return JsonResponse(status)


@csrf_exempt
@api_key_required("greatpages", "AUTHORIZATION")
@require_POST
def save_greatpages_lead(request):
    json_data = json.loads(request.body)
    name = json_data.get("name")
    email = json_data.get("email")
    phone = json_data.get("phone")
    referral_source = json_data.get("referral_source")
    landing_page_url = json_data.get("url")
    greatpages_svc.save_lead(name, email, phone, referral_source, landing_page_url)
    return JsonResponse({})


def _throw_if_not_travel_owner(operation, travel, user):
    if not travel:
        raise Exception("Viagem não encontrada")
    if guard.is_staff(user):
        return
    if travel.pax_can_edit is False:
        raise ValidationError("Realize o cancelamento/alteração no site parceiro onde você realizou a compra")
    if travel.revendedor_user_id and travel.revendedor_user_id == user.id:
        return
    if travel.user_id != user.id:
        raise Exception("SUSPEITO! %s suspeito: %s, %s, %s" % (operation, travel.id, travel.user.id, user.id))


def _throw_if_not_user_payment(payment_id, user):
    payment = get_or_none(Pagamento, id=payment_id)
    if not payment:
        raise Exception("Pagamento não encontrado")
    if payment.user.id != user.id and not guard.is_staff(user):
        raise Exception("SUSPEITO! Usuario acessando payment que nao eh dele")


@public_endpoint
def get_landing_page(request):
    code = request.GET.get("code")
    if code is None:
        return JsonResponse({"message": "Not found."}, status=404)

    lp_obj = landing_page_svc.get_landing_page(code)
    if not lp_obj:
        return JsonResponse({})
    return JsonResponse(lp_obj.serialize())


@ajax_login_required
def dados_bpe_passagem(request, reservation_code):
    reservation_code = reservation_code.strip().upper()
    travel = get_object_or_404(Travel, reservation_code=reservation_code)
    if travel and travel.user.id != request.user.id and not guard.is_staff(request.user):
        return JsonResponse({}, status=401)
    if not travel.is_pagamento_ok:
        return JsonResponse({}, status=HTTPStatus.NOT_FOUND)
    resp = rodoviaria_svc.dados_bpe_passagem(travel)
    if not resp:
        return JsonResponse({}, status=404)
    return JsonResponse(resp, safe=False)


@ajax_staff_required
def dados_bpe_passagem_batch(request):
    grupo_id = request.GET.get("grupo")
    grupo = get_object_or_404(Grupo, id=grupo_id)

    try:
        data = rodoviaria_svc.dados_bpe_passagem_batch(grupo)
    except RodoviariaException:
        sentry_sdk.capture_exception()
        data = []

    return JsonResponse(data, safe=False)


@ajax_login_required
def has_bpe(request, reservation_code):
    reservation_code = reservation_code.strip().upper()

    travels = get_list_or_404(Travel, reservation_code=reservation_code)
    if len(travels) > 1:
        travel = travels[-1]
    else:
        travel = travels[0]

    if travel and travel.user.id != request.user.id and not guard.is_staff(request.user):
        return JsonResponse(False, safe=False)
    resp = rodoviaria_svc.has_bpe(travel)
    if not resp:
        return JsonResponse(False, safe=False)
    return JsonResponse(resp.get("has_bpe"), safe=False)


@ajax_login_required
def ressarcimentos_disponiveis(request):
    return JsonResponse(ressarcimento_svc.ressarcimento_lanche_disponivel(request.user), safe=False)


@ajax_login_required
def confirm_ressarcimentos(request):
    status = request.POST["status"]
    user = request.user
    ressarcimento_ids = json.loads(request.POST.get("ressarcimento_ids"))
    ressarcimento_svc.confirm_ressarcimento_lanche(user, status, ressarcimento_ids)
    return JsonResponse(True, safe=False)


@ajax_login_required
def get_ressarcimentos(request, group_id=None):
    ressarcimentos = ressarcimento_svc.get_ressarcimentos_by_group(group_id)
    return JsonResponse(ressarcimentos, safe=False)


@ajax_login_required
def get_total_ressarcimento(request):
    passengers = json.loads(request.GET.get("passengers"))
    porcento_rateio = Decimal(request.GET.get("porcento_rateio"))
    value_reais = Decimal(request.GET.get("value_reais"))
    total_ressarcimento = ressarcimento_svc.get_total_ressarcimento(passengers, porcento_rateio, value_reais)
    return JsonResponse({"total_ressarcimento": total_ressarcimento}, safe=False)


@verifygrecaptcha("add_gift_card")
@ajax_login_required
@owner(SQUAD_AQUISICAO)
@require_POST
def add_gift_card(request):
    code = request.POST.get("code")
    user_id = request.user.id
    gift_card = gift_cards_svc.add_gift_card(code, user_id)
    return JsonResponse(serializer_gift_card.GiftCardSerializer().serialize(gift_card))


@ajax_login_required
@owner(SQUAD_AQUISICAO)
def upload_leads(request):
    if not guard.is_staff(request.user):
        raise Http404
    log_id = ""
    form = core_forms.LeadUploadForm()
    if request.method == "POST":
        form = core_forms.LeadUploadForm(request.POST, request.FILES)
        if form.is_valid():
            log_id = str(uuid4())
            cleaned_data = form.cleaned_data
            file_csv_data = reduce(lambda a, b: a + b, cleaned_data["csv_file"].chunks())
            lead_svc.upload_from_csv.delay(
                file_csv_data.decode("utf-8-sig"),
                cleaned_data["data_extension_name"],
                log_id,
            )
    return render(request, "upload_leads_csv.html", {"form": form, "log_id": log_id})


@ajax_login_required
@require_POST
def add_trecho_favorito(request):
    query = request.POST.dict()
    user_id = request.user.id
    try:
        form = TrechoForm(**query)
        trecho_favorito_svc.add_trecho_favorito(form.origem_id, form.destino_id, user_id)
    except PydanticValidationError as e:
        error = str(e)
        return JsonResponse({"error": error}, status=400)
    return HttpResponse(status=200)


@ajax_login_required
@require_POST
def remove_trecho_favorito(request):
    query = request.POST.dict()
    user_id = request.user.id
    try:
        form = TrechoForm(**query)
        trecho_favorito_svc.remove_trecho_favorito(form.origem_id, form.destino_id, user_id)
    except PydanticValidationError as e:
        error = str(e)
        return JsonResponse({"error": error}, status=400)
    return HttpResponse(status=200)


@ajax_login_required
@require_GET
def list_destinos_favoritos(request):
    user_id = request.user.id
    origem_id = request.GET.get("origem_id")
    destinos = request.GET.get("destinos")
    if not destinos:
        # TODO: Remover quando pararem de vir requests nesse formato
        destinos = request.GET.getlist("destinos[]", [])
    else:
        destinos = destinos.split(",")
    destinos_favoritos = trecho_favorito_svc.list_destinos_favoritos(origem_id, destinos, user_id)
    return JsonResponse({"destinos_favoritos": destinos_favoritos}, safe=False)


@public_endpoint
@cache_control(public=True, immutable=True, max_age=30 * 60)
@require_GET
def promo_destaque_search_result(request):
    form = PromoDestaqueSearchResultForm(**request.GET.dict())
    promo = cupom_svc.get_promo_destaque_search_result(form.origem_slug, form.destino_slug)
    return JsonResponse(promo)


@public_endpoint
@cache_control(public=True, immutable=True, max_age=60 * 60)
def get_feriado(request, code):
    code = code.strip().lower()
    res = feriado_svc.get_feriado_info(code)
    return JsonResponse(res)


@public_endpoint
def get_feriado_no_cache(request, code):
    code = code.strip().lower()
    res = feriado_svc.get_feriado_info_no_cache(code)
    return JsonResponse(res)


@require_GET
@public_endpoint
def get_trechosclasses_low_occupation(request):
    params = request.GET.dict()
    origem_slug = params.get("origem_slug")
    destino_slug = params.get("destino_slug")

    if not (origem_slug and destino_slug):
        return JsonResponse({"error": "Origem e destino são obrigatórios"}, status=400)

    try:
        items = baixa_ocupacao_svc.get_trechosclasses_low_occupation(origem_slug, destino_slug)
        discount = baixa_ocupacao_svc.get_discount_cupom()
        return JsonResponse({"trechos": items, "discount": discount})
    except Exception as e:
        return JsonResponse({"error": error_str(e)}, status=400)
