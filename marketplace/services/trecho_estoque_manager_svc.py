from typing import Any

from pydantic import BaseModel, TypeAdapter, field_validator

from marketplace.models import TrechoEstoqueManager


class BatchUpdateManagerItem(BaseModel):
    origem_slug: str
    destino_slug: str
    companies_cnpj: list[str]
    triggers: list[str]

    @field_validator("triggers")
    @classmethod
    def validate_triggers(cls, v: list[str]) -> list[str]:
        valid_choices = [choice[0] for choice in TrechoEstoqueManager.Trigger.choices]

        invalid = [el for el in v if el not in valid_choices]
        if invalid:
            raise ValueError(f"Triggers inválidos: {invalid}. Opções válidas: {valid_choices}")

        return v

    @field_validator("origem_slug", "destino_slug")
    @classmethod
    def validate_slugs(cls, v: str) -> str:
        if not v or not v.strip():
            raise ValueError("Slug cannot be empty")
        return v.strip()


def batch_update_managers(data: list[dict[str, Any]]) -> dict[str, Any]:
    """
    Atualiza ou cria múltiplos TrechoEstoqueManager em lote usando operações bulk.

    Args:
        data: lista de dicionários com formato:
              [{"origem_slug": str, "destino_slug": str, "companies_cnpj": list[str], "triggers": list[str]}, ...]

    Returns:
        dict com estatísticas da operação: {"created": int, "updated": int, "errors": list}
    """

    def remove_duplicacoes(lst: list[BatchUpdateManagerItem]):
        seen = set()
        unique_items = []
        for item in lst:
            key = (item.origem_slug, item.destino_slug)
            if key not in seen:
                seen.add(key)
                unique_items.append(item)
        return unique_items

    validated_items = TypeAdapter(list[BatchUpdateManagerItem]).validate_python(data)
    validated_items = remove_duplicacoes(validated_items)

    created_count = 0
    updated_count = 0

    existing_keys = set()
    existing_managers = TrechoEstoqueManager.objects.filter(
        origem_slug__in={item.origem_slug for item in validated_items},
        destino_slug__in={item.destino_slug for item in validated_items},
    )
    existing_keys = {(m.origem_slug, m.destino_slug): m for m in existing_managers}

    to_create = []
    to_update = []

    for item in validated_items:
        key = (item.origem_slug, item.destino_slug)
        manager = existing_keys.get(key)

        if manager:
            manager.companies_cnpj = list(item.companies_cnpj)
            manager.triggers = list(item.triggers)
            to_update.append(manager)
        else:
            manager = TrechoEstoqueManager(
                origem_slug=item.origem_slug,
                destino_slug=item.destino_slug,
                companies_cnpj=list(item.companies_cnpj),
                triggers=list(item.triggers),
            )
            to_create.append(manager)

    if to_create:
        TrechoEstoqueManager.objects.bulk_create(to_create)
        created_count = len(to_create)

    if to_update:
        TrechoEstoqueManager.objects.bulk_update(to_update, ["companies_cnpj", "triggers"])
        updated_count = len(to_update)

    return {"created": created_count, "updated": updated_count}
