import io
import json
from http import HTTPStatus

from django.core.exceptions import ValidationError
from model_bakery import baker

from core.views_staff import batch_update_managers as batch_update_managers_view
from marketplace.models import TrechoEstoqueManager
from marketplace.services.trecho_estoque_manager_svc import batch_update_managers


def test_batch_update_managers():
    """Testa operações mistas de criação e atualização"""
    # Cria manager existente
    manager = baker.make(
        TrechoEstoqueManager,
        origem_slug="sao-paulo-sp",
        destino_slug="rio-de-janeiro-rj",
        companies_cnpj=["11111111000111"],
        triggers=["search"],
    )

    data = [
        {
            "origem_slug": "sao-paulo-sp",
            "destino_slug": "rio-de-janeiro-rj",
            "companies_cnpj": ["22222222000222"],
            "triggers": ["periodico"],
        },
        {
            "origem_slug": "brasilia-df",
            "destino_slug": "fortaleza-ce",
            "companies_cnpj": ["44444444000444"],
            "triggers": ["search_background"],
        },
    ]

    result = batch_update_managers(data)

    assert result["created"] == 1
    assert result["updated"] == 1

    manager.refresh_from_db()
    assert manager.companies_cnpj == ["22222222000222"]
    assert manager.triggers == ["periodico"]

    new_manager = TrechoEstoqueManager.objects.get(origem_slug="brasilia-df", destino_slug="fortaleza-ce")
    assert new_manager.companies_cnpj == ["44444444000444"]
    assert new_manager.triggers == ["search_background"]


def test_view_batch_update_managers_success(rf, mocker, user_staff):
    payload = [
        {
            "origem_slug": "cuiaba-mt",
            "destino_slug": "rondonopolis-mt",
            "companies_cnpj": ["12345678000199", "99887766000122"],
            "triggers": ["trigger1", "trigger2"],
        }
    ]
    mock_result = {"created": 3, "updated": 2}
    mocked_service = mocker.patch(
        "marketplace.services.trecho_estoque_manager_svc.batch_update_managers", return_value=mock_result
    )

    file_content = json.dumps(payload).encode("utf-8")
    file = io.BytesIO(file_content)
    file.name = "managers.json"

    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)
    request.user = user_staff

    response = batch_update_managers_view(request)

    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.content)
    assert data["success"] is True
    assert "Criados" in data["message"]
    mocked_service.assert_called_once_with(payload)


def test_view_batch_update_managers_missing_file(rf, user_staff):
    request = rf.post("/api/trechos/managers/batch")
    request.user = user_staff
    response = batch_update_managers_view(request)
    assert response.status_code == 400
    assert json.loads(response.content)["error"] == "Nenhum arquivo foi enviado"


def test_view_batch_update_managers_invalid_extension(rf, user_staff):
    file = io.BytesIO(b"[]")
    file.name = "managers.txt"

    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)
    request.user = user_staff

    response = batch_update_managers_view(request)
    assert response.status_code == 400
    assert "extensão .json" in json.loads(response.content)["error"]


def test_view_batch_update_managers_invalid_json(rf, user_staff):
    file = io.BytesIO(b"{not-json}")
    file.name = "managers.json"

    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)

    request.user = user_staff

    response = batch_update_managers_view(request)
    assert response.status_code == 400
    assert "Erro ao parsear JSON" in json.loads(response.content)["error"]


def test_view_batch_update_managers_not_a_list(rf, user_staff):
    file = io.BytesIO(json.dumps({"origem": "foo"}).encode("utf-8"))
    file.name = "managers.json"

    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)
    request.user = user_staff

    response = batch_update_managers_view(request)
    assert response.status_code == 400
    assert "deve conter uma lista de objetos" in json.loads(response.content)["error"]


def test_view_batch_update_managers_validation_error(rf, mocker, user_staff):
    payload = [{"origem_slug": "foo", "destino_slug": "bar"}]
    mocker.patch(
        "marketplace.services.trecho_estoque_manager_svc.batch_update_managers",
        side_effect=ValidationError("Dados inválidos"),
    )
    file = io.BytesIO(json.dumps(payload).encode("utf-8"))
    file.name = "managers.json"

    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)
    request.user = user_staff

    response = batch_update_managers_view(request)
    assert response.status_code == 400
    assert "Erro de validação" in json.loads(response.content)["error"]


def test_view_batch_update_managers_internal_error(rf, mocker, user_staff):
    payload = [{"origem_slug": "foo", "destino_slug": "bar"}]
    mocker.patch(
        "marketplace.services.trecho_estoque_manager_svc.batch_update_managers",
        side_effect=Exception("Erro inesperado"),
    )

    file = io.BytesIO(json.dumps(payload).encode("utf-8"))
    file.name = "managers.json"
    request = rf.post("/api/trechos/managers/batch")
    request.FILES["file"] = file
    file.seek(0)
    request.user = user_staff

    response = batch_update_managers_view(request)
    assert response.status_code == 500
    data = json.loads(response.content)
    assert "Erro interno" in data["error"]
