import itertools
import logging
import time
from bisect import bisect_left
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta
from decimal import Decimal
from functools import partial, reduce
from itertools import chain

import numpy as np
import sentry_sdk
from beeline import traced
from django.contrib.auth.models import User
from django.db import transaction
from django.db.models import BooleanField, Case, Count, F, IntegerField, Q, Value, When
from django.db.models.expressions import RawSQL
from django.db.models.functions import Coalesce
from django.db.models.functions.datetime import TruncDate
from django.utils import timezone
from django.utils.datastructures import OrderedSet

from buser import settings
from commons import dateutils, guard, signer
from commons.cacheback import cacheback
from commons.memoize import memoize
from commons.storage import public_media_storage
from commons.thumbor import generate_thumbor_url
from core.constants import REPRICE_BOTS_CLIENTS, ClientVendas
from core.models_commons import Cidade
from core.models_grupo import (
    Grupo,
    Grupo<PERSON>lasse,
    PriceManager,
    TrechoClasse,
    TrechoClasseExtra,
)
from core.models_rota import Checkpoint, Rota, TrechoConexao, TrechoVendido
from core.models_travel import Travel
from core.service import globalsettings_svc, preco_svc, price_ab_svc, rodoviaria_svc
from core.service import log_svc as core_log_svc
from core.service.reserva import taxa_servico_checkout_svc
from marketplace.models import TrechoClasseMarketplaceLogger, TrechoEstoqueManager
from marketplace.services import create_group_svc as create_group_marketplace_svc
from marketplace.services import search_svc as search_marketplace_svc
from pagamento_parceiro.service import configuracao_pagamento_svc
from recommender.adapters import TrechoClasseInfo, TrechoClasseInfoV2
from recommender.service import recommendation_svc
from search_result.adapter.cidade_adapter import CidadeAdapter
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter
from search_result.exceptions import RodoviariaOverbooking, TrechoInexistente
from search_result.serializer import serializer_cidade, serializer_trecho_classe
from search_result.serializer.serializer_cidade_proxima import (
    CidadeProximaTrechoClasseSerializer,
)
from search_result.serializer.serializer_trecho_classe import validate_trecho_classe
from search_result.service import (
    badges_svc,
    conexao_svc,
    log_svc,
    repricing_vendas_svc,
    search_svc,
    trecho_classe_svc,
    trechos_vendidos_svc,
)
from search_result.service.cidade_proxima_svc import (
    TipoSubstituicaoEnum,
    find_cidade_proxima,
    find_trechos_classes_cidade_proxima,
)
from search_result.service.conexao_svc import serializer_origem_e_destino_conexoes
from search_result.service.search_svc import search_melhores_viagens_no_trecho
from search_result.service.tp_search_svc import (
    only_valid_groups,
    remove_fields,
    remove_fretamento_company_info,
)
from search_result.utils.hashint import hashid_grupos, hashint, unhashint
from threadlocalrequest import threadlocals

DECIMAL_ZERO = Decimal("0")
_15_MINS = 60 * 15
SEARCH_DAYS_LIMIT = 15
WEEK_DAY_MAP = {
    "segunda-feira": 0,
    "terca-feira": 1,
    "quarta-feira": 2,
    "quinta-feira": 3,
    "sexta-feira": 4,
    "sabado": 5,
    "domingo": 6,
}

DEFAULT_PICTURE_URL = "/public/static/placeholder-80x80.png"

CUSTO_BENEFICIO_NICKNAME_SLUGS = [  # locais de embarque de BH usados para teste de custo beneficio
    "expominas-ponto-buser",
    "edificio-savannah-mall",
    "rede-minas",
    "pca-liberdade",
    "rodoviaria-tiete",
    "barra-funda",
]

logger = logging.getLogger(__name__)
# Para identificar grupos duplicados de marketplace, pode ter falsos positivos e estamos cientes sobre isso.
KEYS_IDENTIFY_DUPLICADED_GROUPS = [
    "datetime_ida",
    "tipo_assento",
    "origem_id",
    "destino_id",
    "company_id",
    "chegada_ida",
]


@dataclass
class SearchItem:
    origem_slug: str
    cidade_origem_slug: str
    destino_slug: str
    cidade_destino_slug: str
    tipo_assento: str
    datetime_ida: str
    vagas: int
    max_split_value: float
    modelo_venda: str
    origem_id: int
    destino_id: int
    duracao_ida: int
    id: str | int | None
    capacidade: int | None
    extra_mkp_servico: str | None
    extra_mkp_extra: dict | None
    extra_mkp_ota_config_id: int | None
    company_cnpj: str | None
    company_id: int | None
    extra_mkp_last_synced_at: str | None
    extra_mkp_stopovers: list[dict] | None = None

    @classmethod
    def from_dgroup(cls, dgroup: dict):
        return cls(
            company_cnpj=dgroup.get("company_cnpj"),
            origem_slug=dgroup["origem_slug"],
            cidade_origem_slug=dgroup["cidade_origem_slug"],
            destino_slug=dgroup["destino_slug"],
            cidade_destino_slug=dgroup["cidade_destino_slug"],
            tipo_assento=dgroup["tipo_assento"],
            datetime_ida=dgroup["datetime_ida"],
            vagas=int(dgroup["vagas"]),
            max_split_value=dgroup["max_split_value"],
            modelo_venda=dgroup["modelo_venda"],
            company_id=dgroup["company_id"],
            id=dgroup.get("id"),
            origem_id=dgroup["origem_id"],
            destino_id=dgroup["destino_id"],
            duracao_ida=int(dgroup["duracao_ida"]),
            capacidade=dgroup.get("capacidade"),
            extra_mkp_servico=dgroup.get("extra_mkp_servico"),
            extra_mkp_extra=dgroup.get("extra_mkp_extra"),
            extra_mkp_ota_config_id=dgroup.get("extra_mkp_ota_config_id"),
            extra_mkp_last_synced_at=dgroup.get("extra_mkp_last_synced_at"),
            extra_mkp_stopovers=dgroup.get("extra_mkp_stopovers"),
        )


class SearchItemHotOffer(SearchItem):
    extra_mkp_servico: str
    extra_mkp_extra: dict
    extra_mkp_ota_config_id: int
    id: str | None
    extra_mkp_last_synced_at: str | None
    extra_mkp_stopovers: list[dict] | None = None


@dataclass
class SearchResponse:
    items: list[SearchItem]
    timezone: str

    @classmethod
    def from_dgroups(cls, dgroups: list[dict], adapter: CidadeAdapter | LocalEmbarqueAdapter):
        return cls(
            timezone=adapter.timezone,
            items=[SearchItem.from_dgroup(dgroup) for dgroup in dgroups],
        )


def _verifica_datetime_ida(inicio, fim, data_ida, timezone) -> bool:
    """Verifica se a data de ida está entre o intervalo de horas informado e retorna True caso não esteja"""

    datetime_ida = dateutils.to_tz(datetime.fromisoformat(data_ida), timezone)
    return not inicio <= datetime_ida.time().hour < fim


def _verifica_sao_paulo_rio_de_janeiro(local_origem, data_ida) -> bool:
    if local_origem["features"] is None:
        return False
    if _verifica_datetime_ida(22, 24, data_ida, local_origem["timezone"]):
        return False
    return "metro" in local_origem["features"]


def _verifica_sao_paulo_belo_horizonte(local_origem, data_ida) -> bool:
    if _verifica_datetime_ida(20, 23, data_ida, local_origem["timezone"]):
        return False
    if local_origem["slug"] == "sao-paulo-sp":
        if local_origem["features"] is None:
            return False
        return "metro" in local_origem["features"]
    return local_origem["nickname_slug"] in CUSTO_BENEFICIO_NICKNAME_SLUGS


def _verifica_rio_de_janeiro_belo_horizonte(local_origem, data_ida) -> bool:
    if _verifica_datetime_ida(20, 23, data_ida, local_origem["timezone"]):
        return False
    if local_origem["slug"] == "rio-de-janeiro-rj":
        if local_origem["features"] is None:
            return False
        return "metro" in local_origem["features"]
    return local_origem["nickname_slug"] in CUSTO_BENEFICIO_NICKNAME_SLUGS


MAP_CUSTO_BENEFICIO = {  # ("sao-paulo-sp", "rio-de-janeiro-rj", "belo-horizonte-mg") cidades em teste para custo
    # beneficio
    "sao-paulo-sp:rio-de-janeiro-rj": _verifica_sao_paulo_rio_de_janeiro,
    "rio-de-janeiro-rj:sao-paulo-sp": _verifica_sao_paulo_rio_de_janeiro,
    "sao-paulo-sp:belo-horizonte-mg": _verifica_sao_paulo_belo_horizonte,
    "belo-horizonte-mg:sao-paulo-sp": _verifica_sao_paulo_belo_horizonte,
    "belo-horizonte-mg:rio-de-janeiro-rj": _verifica_rio_de_janeiro_belo_horizonte,
    "rio-de-janeiro-rj:belo-horizonte-mg": _verifica_rio_de_janeiro_belo_horizonte,
}


def _show_hidden_for_pax(user, client):
    if not user:
        return False

    if client != "vendas":
        return False

    if guard.is_afiliado(user):
        return False
    if guard.is_revendedor(user):
        return True
    if guard.is_vendaslider(user):
        return True
    if guard.is_staff(user):
        return True

    return False


def _origem_destino_trecho_vendido(origem_slug, destino_slug, with_destino_info):
    origem = search_svc.get_local_by_slug(origem_slug)
    destino = search_svc.get_local_by_slug(destino_slug, with_city_info=with_destino_info)

    if not origem or not destino:
        raise TrechoInexistente()

    return origem, destino, trechos_vendidos_svc.exists(origem, destino)


def _trechos_classe_qs(
    origem,
    destino,
    show_hidden_for_pax,
    *,
    serializer=serializer_trecho_classe.SearchSerializer,
):
    trecho_vendido_ids, trechos_vendidos_local_exato = trechos_vendidos_svc.get(origem, destino)

    if trechos_vendidos_local_exato:
        trecho_alternativo_expr = RawSQL(
            "SELECT core_trechoclasse.trecho_vendido_id NOT IN %s",
            (tuple(trechos_vendidos_local_exato),),
            output_field=BooleanField(),
        )
    else:
        # Se a pesquisa é por ponto de embarque e não tem trechos vendidos
        # partindo do local exato então todos os grupos retornados são do
        # trecho alternativo (cidade de origem -> destino).
        # Se a pesquisa é por cidade então todos os grupos retornados são do trecho exato.
        # Apenas pontos de embarque possuem `local_id`
        pesquisando_por_ponto = bool(origem.local_id)
        trecho_alternativo_expr = Value(pesquisando_por_ponto, output_field=BooleanField())
    tc_qs = (
        TrechoClasse.objects.filter(
            trecho_vendido__in=trecho_vendido_ids,
            grupo__status__in=["pending", "travel_confirmed"],
        )
        .to_serialize(serializer)
        .annotate(trecho_alternativo=trecho_alternativo_expr)
        .order_by("datetime_ida")
    )
    if not show_hidden_for_pax:
        tc_qs = tc_qs.exclude(grupo__hidden_for_pax=True)
    return tc_qs


def _grupos_do_dias(data_ida, origem, destino, show_hidden_for_pax):
    data_ida_with_timezone = _data_parse_timezone(data_ida, origem.timezone)
    qs = _trechos_classe_qs(origem, destino, show_hidden_for_pax)
    qs = qs.filter(
        datetime_ida__range=[
            data_ida_with_timezone,
            dateutils.truncate(data_ida_with_timezone) + timedelta(hours=23, minutes=59) + timedelta(hours=2),
        ]  # Pegar grupos até 01h59 da manhã do dia seguinte https://app.clickup.com/t/30953227/AC-1377
    )
    return list(qs)


def _data_parse_timezone(date, desired_timezone):
    data_with_timezone = dateutils.to_tz(date, desired_timezone)
    now = dateutils.to_tz(dateutils.now(), desired_timezone)
    data_with_timezone = max(data_with_timezone, now)
    return data_with_timezone


def _trechos_classe_from(origem, destino, show_hidden_for_pax, *, incluir_trecho_alternativo=False, limit=50):
    data_ida_after = dateutils.now()
    qs = _trechos_classe_qs(origem, destino, show_hidden_for_pax)
    qs = qs.filter(
        datetime_ida__range=[
            data_ida_after,
            data_ida_after + timedelta(days=SEARCH_DAYS_LIMIT),
        ],
        vagas__gt=0,
    )
    return _filter_trecho_classe_by_trecho_alternativo(qs[:limit], incluir_trecho_alternativo)


def _trechos_classe_by_week_day(
    origem,
    destino,
    week_day,
    show_hidden_for_pax,
    *,
    incluir_trecho_alternativo=False,
    limit=50,
):
    now = dateutils.now()
    qs = _trechos_classe_qs(origem, destino, show_hidden_for_pax).filter(
        datetime_ida__gt=now,
        vagas__gt=0,
    )

    if week_day in ("hoje", "amanha"):
        qs = _filter_trechos_classe_by_relative_week_day_qs(qs, week_day)
    else:
        qs = _filter_trechos_classe_by_week_day_qs(qs, week_day)

    return _filter_trecho_classe_by_trecho_alternativo(qs[:limit], incluir_trecho_alternativo)


def _filter_trechos_classe_by_relative_week_day_qs(qs, week_day):
    today = _today()
    week_day_date = today if week_day == "hoje" else today + timedelta(days=1)
    return qs.filter(datetime_ida__range=[week_day_date, week_day_date + timedelta(days=1)])


def _filter_trechos_classe_by_week_day_qs(qs, week_day):
    dates = list_week_day_dates(week_day)
    dates_qs = []
    for date_ in dates:
        dates_qs.append(qs.filter(datetime_ida__range=[date_, date_ + timedelta(days=1)]))
    return TrechoClasse.objects.none().union(*dates_qs).order_by("datetime_ida")


def list_week_day_dates(week_day, weeks_limit=12):
    next_date = today = _today()

    week_day_index = WEEK_DAY_MAP.get(week_day, None)
    if week_day_index is None:
        return []

    current_week_day_index = today.weekday()

    if current_week_day_index != week_day_index:
        next_week_day_index = (week_day_index - current_week_day_index) % 7
        next_date = today + timedelta(next_week_day_index)

    return [next_date + timedelta(weeks=i) for i in range(weeks_limit)]


def _today():
    _now = dateutils.to_default_tz(dateutils.now())
    return _now.replace(hour=0, minute=0, second=0, microsecond=0)


def _filter_trecho_classe_by_trecho_alternativo(trechos_classe, incluir_trecho_alternativo):
    return [
        trecho_classe
        for trecho_classe in trechos_classe
        if incluir_trecho_alternativo or not trecho_classe.trecho_alternativo
    ]


@cacheback(10 * 60)
def search_grupos(origem_slug, destino_slug, data_ida, *, show_hidden_for_pax=False, week_day=None):
    origem, destino, is_trecho_vendido = _origem_destino_trecho_vendido(
        origem_slug, destino_slug, with_destino_info=not data_ida
    )

    if week_day:
        groups = _trechos_classe_by_week_day(
            origem,
            destino,
            week_day,
            show_hidden_for_pax,
            incluir_trecho_alternativo=True,
        )
    elif data_ida:
        groups = _grupos_do_dias(data_ida, origem, destino, show_hidden_for_pax)
    else:
        groups = _trechos_classe_from(origem, destino, show_hidden_for_pax, incluir_trecho_alternativo=True)

    dgroups, itinerarios_map, locais_map = serializer_trecho_classe.serialize_search(groups)

    return (origem, destino, is_trecho_vendido, dgroups, itinerarios_map, locais_map)


@memoize(timeout=60 * 60 * 1)
def search_top_rotas_indo_para(
    cidade_slug,
    bairro_slug=None,
    nickname_slug=None,
    number_of_days_ago=7,
    limit=None,
    withextrainfo=False,
    withitinerario=False,
):
    local = search_svc.get_local_by_slug(cidade_slug)
    if local is None:
        raise Cidade.DoesNotExist

    now_ = dateutils.now()
    search_limit = now_ + timedelta(days=SEARCH_DAYS_LIMIT)  # olhamos apenas os tv com tcs nos próximos 14 dias
    qs_indo_para = Travel.objects.filter(
        trecho_classe__datetime_ida__range=(now_, search_limit),
        trecho_classe__vagas__gt=0,
        grupo__status__in=("travel_confirmed", "pending"),
        **local.as_queryset_filter("trecho_vendido__destino"),
    )
    if bairro_slug is not None:
        qs_indo_para = qs_indo_para.filter(trecho_vendido__destino__bairro_slug=bairro_slug)
    if nickname_slug is not None:
        qs_indo_para = qs_indo_para.filter(trecho_vendido__destino__nickname_slug=nickname_slug)
    qs_indo_para = (
        qs_indo_para.values_list(
            "trecho_vendido__origem__cidade",
        )
        .annotate(
            count=Count("id"),
        )
        .order_by(
            "-count",
            # ordena pela cidade apenas para consistência de ordem no empate do count
            "trecho_vendido__origem__cidade",
        )
    )

    days_ago = timezone.now() - timedelta(days=number_of_days_ago)
    qs_indo_para = qs_indo_para.filter(created_on__gte=days_ago)
    cidades_indo_para = [t[0] for t in qs_indo_para[:limit]]

    qs_nao_vendem = TrechoClasse.objects.filter(
        datetime_ida__range=(now_, search_limit),
        vagas__gt=0,
        grupo__status__in=("travel_confirmed", "pending"),
        **local.as_queryset_filter("trecho_vendido__destino"),
    )

    if bairro_slug is not None:
        qs_nao_vendem = qs_nao_vendem.filter(trecho_vendido__destino__bairro_slug=bairro_slug)
    if nickname_slug is not None:
        qs_nao_vendem = qs_nao_vendem.filter(trecho_vendido__destino__nickname_slug=nickname_slug)
    qs_nao_vendem = (
        qs_nao_vendem.exclude(trecho_vendido__origem__cidade__id__in=cidades_indo_para)
        .distinct()
        .annotate(
            has_grupos=Case(
                When(Q(grupo__datetime_ida__gte=now_, vagas__gt=0), then=Value(2)),
                When(grupo__datetime_ida__gte=now_, then=Value(1)),
                default=Value(0),
                output_field=IntegerField(),
            )
        )
        .exclude(has_grupos=0)
        .order_by("-has_grupos")
        .values_list("trecho_vendido__origem__cidade", flat=True)
    )
    ids_cidades_indo_para_que_nao_vendem = list(OrderedSet(qs_nao_vendem))

    cidades_indo_para.extend(ids_cidades_indo_para_que_nao_vendem)
    has_more = limit is not None and len(cidades_indo_para) > limit
    cidades_indo_para = cidades_indo_para[:limit]
    result_dict = _serialize_local(local, with_extra_info=withextrainfo)
    result_dict["has_more"] = has_more
    result_dict["indo_para"] = _make_rota_dict_indo_para(local, cidades_indo_para, withitinerario=withitinerario)

    log_svc.log_search(destino=local, type="destination_search")

    return result_dict


@traced("grupos_svc._hydrate_groups")
def _hydrate_groups(dgroups, origem):
    group_ids = [g["id"] for g in dgroups]
    trechos_classe_map = _query_to_hydrate_groups(group_ids)
    cleaned = _hydrate_groups_logic(dgroups, origem, trechos_classe_map)

    return cleaned


def _query_to_hydrate_groups(group_ids):
    trechos_classe_map = (
        TrechoClasse.objects.annotate(
            vagas_cleaned=Coalesce("vagas", F("grupo_classe__capacidade") - F("grupo_classe__pessoas")),
            grupo_classe_fechado=F("grupo_classe__closed"),
            grupo_classe_fechado_motivo=F("grupo_classe__closed_reason"),
        )
        .select_related(
            "price_manager",
            "grupo",
            "grupo_classe",
            "trecho_vendido__origem__cidade",
            "trecho_vendido__destino__cidade",
        )
        .prefetch_related("price_manager__buckets")
        .only(
            "pk",
            "grupo__modelo_venda",
            "grupo_classe__tipo_assento",
            "trecho_vendido__origem__cidade__slug",
            "trecho_vendido__destino__cidade__slug",
            # Bucketização depende de max_split_value, pessoas, price_manager e datetime_ida.
            "max_split_value",
            "pessoas",
            "datetime_ida",
            "price_manager",
            # As vagas e o ref_split_value podem mudar.
            "vagas",
            "ref_split_value",
            "closed",
            "closed_reason",
        )
        .in_bulk(group_ids)
    )
    return trechos_classe_map


def _hydrate_groups_logic(dgroups, origem, trechos_classe_map):
    nowstr = dateutils.to_tz(dateutils.now(), origem.timezone).isoformat()  # type: ignore
    cleaned = []
    for dgroup in dgroups:
        try:
            trecho_classe = trechos_classe_map[dgroup["id"]]
        except KeyError:
            # TrechoClasse sumiu, melhor remover.
            continue

        # Remove grupos iniciados. Comparação de datas isoformat funciona.
        # 2021-08-30T14:15:00-03:00
        if dgroup["datetime_ida"] < nowstr:
            continue

        max_split_value = trecho_classe.max_split_value_bucket
        ref_split_value = trecho_classe.ref_split_value_bucket
        vagas_bucket = trecho_classe.vagas_bucket
        trecho_closed = trecho_classe.closed
        trecho_closed_reason = trecho_classe.closed_reason
        is_trecho_rodov_fechado_nao_overbooking = (
            trecho_closed and trecho_closed_reason != RodoviariaOverbooking.message
        )
        is_hard_stop_rodov = (
            trecho_classe.grupo_classe_fechado
            and trecho_classe.grupo_classe_fechado_motivo
            and rodoviaria_svc.HARD_STOP_PREFIX in trecho_classe.grupo_classe_fechado_motivo
        )

        if is_trecho_rodov_fechado_nao_overbooking or is_hard_stop_rodov:
            continue
        dgroup.update(
            {
                "max_split_value": max_split_value,
                "ref_split_value": ref_split_value,
                "preco_base": trecho_classe.max_split_value,
                "vagas_bucket": vagas_bucket,
                "vagas": trecho_classe.vagas_cleaned,
                "pessoas": trecho_classe.pessoas,
                "closed": trecho_classe.grupo_classe_fechado or trecho_classe.closed,
            }
        )
        cleaned.append(dgroup)
    return cleaned


@traced("grupos_svc._reprice_ab")
def _reprice_ab(data, dgroups, user, origem, destino, *, client=None, data_ida=None):
    price_cookie = price_ab_svc.reprice_groups(user, origem.cidade.pk, destino.cidade.pk, dgroups, data_ida)
    logctx = threadlocals.get_log_ctx()
    user_tabid = logctx.get("user_tabid")
    if price_cookie:
        data["price_cookie"] = {"name": price_ab_svc.COOKIE_NAME, "value": price_cookie}

    # Tem decisões de micro-otimização aqui.
    # * Trabalhar com variáveis locais é mais eficiente que ficar relendo dicts.
    # * O dict.update é bem mais lento que atualizar keys individualmente.
    # * Não instanciar Decimal a cada iteração é mais eficiente.
    for dgroup in dgroups:
        max_split_value = dgroup["max_split_value"]
        ref_split_value = dgroup["ref_split_value"]
        preco_base = dgroup.get("preco_base", DECIMAL_ZERO)

        if client:
            max_split_value, ref_split_value, preco_base = repricing_vendas_svc.reprice(
                max_split_value,
                ref_split_value,
                client=client,
                user=user,
                sales_model=dgroup["modelo_venda"],
                preco_base=preco_base,
            )
            dgroup["max_split_value"] = max_split_value
            dgroup["ref_split_value"] = ref_split_value
            dgroup["preco_base"] = preco_base

        dgroup["signed"] = signed_dgroup(dgroup["id"], max_split_value, ref_split_value, user_tabid)


def signed_dgroup(id, max_split_value, ref_split_value, user_tabid):
    return signer.sign_object(
        {
            "id": id,
            "max_split_value": max_split_value,
            "ref_split_value": ref_split_value,
            "user_tabid": user_tabid,
        }
    )


def _remove_full_groups(dgroups):
    if not dgroups:
        return
    for group in list(dgroups):
        if _is_full(group):
            dgroups.remove(group)


def _build_key(dgroup):
    def _function_reduce(acc, current_key):
        current_value = dgroup[current_key]
        if not acc:
            return current_value
        return f"{acc}::{current_value}"

    return reduce(_function_reduce, KEYS_IDENTIFY_DUPLICADED_GROUPS, "")


def _groups_by_date(dgroups):
    groups_by_date = {}
    for dgroup in dgroups:
        # O TrechoClasse.to_dict_json_base converte o datetime_ida para isoformat.
        # 2021-08-30T14:15:00-03:00
        date_ida = dgroup["datetime_ida"].split("T")[0]
        groups_by_date.setdefault(date_ida, []).append(dgroup)
    return [
        {
            "datetime_ida": date_ida,
            "grupos": _dgroups,
        }
        for date_ida, _dgroups in groups_by_date.items()
    ]


def _process_dgroups(
    dgroups,
    map_recommendations_rank_sr_api,
):
    mkt_dgroups = {}
    _dgroups = []
    for dgroup in dgroups:
        key_dgroup_mkt = None
        if _is_full(dgroup):
            continue
        if dgroup["modelo_venda"] == Grupo.ModeloVenda.MARKETPLACE:
            key_dgroup_mkt = _build_key(dgroup)
            if _dgroup := mkt_dgroups.get(key_dgroup_mkt):
                dgroup = max(dgroup, _dgroup, key=lambda x: x["vagas"])

        dgroup.pop("company_cnpj", None)
        dgroup["vagas"] = _limita_vagas_exibidas(dgroup["vagas"])
        dgroup["parcelamento"] = serializer_trecho_classe.parcelamento_from_value(dgroup.get("max_split_value"))
        group_id = dgroup["id"]
        relevance_group_api = map_recommendations_rank_sr_api.get(group_id)
        if relevance_group_api:
            dgroup.setdefault("ranking", {})["searchrank_api"] = relevance_group_api

        if dgroup["modelo_venda"] != Grupo.ModeloVenda.MARKETPLACE:
            _dgroups.append(dgroup)
        if key_dgroup_mkt:
            mkt_dgroups[key_dgroup_mkt] = dgroup
    return list(mkt_dgroups.values()) + _dgroups


def get_city_with_buser_image(city: CidadeAdapter):
    city_dict = city.as_dict()
    info = city_dict.get("info")
    if not info:
        return city_dict

    imagem = [imagem for imagem in info.get("imagens", []) if imagem.get("tipo") == "thumb"]
    if imagem and city.cidade.image:
        imagem[0]["url"] = _get_destino_picture_url(destino=city, width=400, height=300)

    return city_dict


def search(
    data_ida,
    origem_slug,
    destino_slug,
    user,
    *,
    client=None,
    week_day=None,
    source=None,
    device_id=None,
):
    data = {}
    show_hidden_for_pax = _show_hidden_for_pax(user, client)
    origem, destino, is_trecho_vendido, dgroups, itinerarios_map, locais_map = search_grupos(
        origem_slug,
        destino_slug,
        data_ida,
        show_hidden_for_pax=show_hidden_for_pax,
        week_day=week_day,
    )

    if dgroups:
        dgroups = _hydrate_groups(dgroups, origem)
        _reprice_ab(data, dgroups, user, origem, destino, client=client, data_ida=data_ida)
    has_grupos = any(dgroups)  # TODO: matar isso, o front não usa mais, apenas esperar o front velho morrer

    # Busca os rank das recomendações com base no usuário para cada dgroup
    # Só buscamos nesse modelo caso o usuário esteja identificado
    map_recommendations_rank_sr_api = {}
    if data_ida and globalsettings_svc.get("enable_searchrank_api", False):
        map_recommendations_rank_sr_api = _recommendations_map_by_searchrank_api(device_id, dgroups, locais_map)
    dgroups = _process_dgroups(
        dgroups,
        map_recommendations_rank_sr_api,
    )
    locais_de_embarque, locais_de_desembarque = _get_locais_cleaned(locais_map, dgroups)

    if client and dgroups:
        dgroups = _taxa_servico_search_vendas(dgroups, user, client)

    # Usa o has_grupos porque o cache pode estar um pouco desatualizado.
    is_trecho_vendido |= has_grupos

    groups_by_date = _groups_by_date(dgroups)
    if data_ida and not groups_by_date:
        groups_by_date.append(
            {
                "datetime_ida": data_ida.strftime("%Y-%m-%d"),
                "grupos": [],
            }
        )
    origem_dict = origem.as_dict()
    destino_dict = get_city_with_buser_image(destino)

    data.update(
        {
            "trecho": {"origem": origem_dict, "destino": destino_dict},
            "trecho_alternativo": None,
            "has_grupos": has_grupos,  # TODO: matar isso, o front não usa mais, apenas esperar o front velho morrer
            "is_trecho_vendido": is_trecho_vendido,
            "groups_by_date": groups_by_date,
            "itinerarios": itinerarios_map,
            "locais": locais_map,
            "locais_de_embarque": locais_de_embarque,
            "locais_de_desembarque": locais_de_desembarque,
            "badges_by_date": badges_svc.process(dgroups, origem, destino),
            "grupos_recomendados": _find_recommended_groups(dgroups, locais_map, data_ida),
            "destino_picture_url": _get_destino_picture_url(destino),
        }
    )

    if origem.local_alternativo:
        data["trecho_alternativo"] = {
            "origem": origem.local_alternativo.as_dict(),
            "destino": destino_dict,
        }

    return data


def _is_full(dgroup):
    return dgroup["vagas"] <= 0 or dgroup["closed"]


def _get_locais_cleaned_set(locais_map, dgroups):
    locais_de_embarque = set()
    locais_de_desembarque = set()

    for dgroup in dgroups:
        origem_id = dgroup["origem_id"]
        local_origem = locais_map.get(origem_id)
        if local_origem:
            locais_de_embarque.add(origem_id)

        destino_id = dgroup["destino_id"]
        local_destino = locais_map.get(destino_id)
        if local_destino:
            locais_de_desembarque.add(destino_id)

    return locais_de_embarque, locais_de_desembarque


def _get_locais_cleaned(locais_map, dgroups):
    locais_de_embarque, locais_de_desembarque = _get_locais_cleaned_set(locais_map, dgroups)
    return list(locais_de_embarque), list(locais_de_desembarque)


def _get_locais_conexao_cleaned(locais_map, dgroups_by_conexao):
    """
    For all filters right now we just get the locais de embarque from the origin and the final destination, that's why
    we need this logic.
    """
    locais_de_embarque = set()
    locais_de_desembarque = set()
    for dgroups in dgroups_by_conexao.values():
        first_leg = dgroups[0]
        last_leg = dgroups[-1]

        _locais_de_embarque, _ = _get_locais_cleaned_set(locais_map, first_leg)
        _, _locais_de_desembarque = _get_locais_cleaned_set(locais_map, last_leg)
        locais_de_embarque |= _locais_de_embarque
        locais_de_desembarque |= _locais_de_desembarque
    return list(locais_de_embarque), list(locais_de_desembarque)


def _limita_vagas_exibidas(vagas, limit=13):
    # Limita a quantidade de vagas, para entender melhor: https://app.clickup.com/t/30953227/AC-1938
    if vagas > limit:
        return limit
    return vagas


def search_same_group(
    groups: dict,
    user: User,
    client=None,
    serializer=serializer_trecho_classe.SearchSerializer,
):
    data = {}
    ids = [group["id"] for group in groups]
    tc_ref = list(TrechoClasse.objects.filter(id__in=ids).values_list("trecho_vendido_id", "grupo_id"))

    trecho_vendido_ids = {ids[0] for ids in tc_ref}
    grupo_ids = {ids[1] for ids in tc_ref}

    groups = TrechoClasse.objects.filter(
        trecho_vendido_id__in=trecho_vendido_ids,
        grupo_id__in=grupo_ids,
    ).to_serialize(serializer)

    dgroups, _, _ = serializer_trecho_classe.serialize_search(groups)

    if dgroups:
        origem = groups[0].trecho_vendido.origem
        destino = groups[0].trecho_vendido.destino
        origem = CidadeAdapter(origem.cidade, origem, with_city_info=False)
        destino = CidadeAdapter(destino.cidade, destino, with_city_info=False)
        dgroups = _hydrate_groups(dgroups, origem)
        _reprice_ab(data, dgroups, user, origem, destino, client=client)

    data["groups_by_date"] = _groups_by_date(dgroups)

    return data


@memoize(timeout=7 * 24 * 60 * 60)
def _get_destino_picture_url(destino, width=80, height=80):
    picture_url = destino.cidade.picture_url
    if not picture_url:
        return public_media_storage.url(DEFAULT_PICTURE_URL)
    return generate_thumbor_url(picture_url, width=width, height=height)


def _serialize_local(local, with_extra_info=False):
    data = serializer_cidade.serialize(local.cidade, withextrainfo=with_extra_info)
    data.update(**local.as_dict())
    return data


def _merge_cidade_trecho(cidade, trecho_dict):
    cidade["best_price"] = trecho_dict["best_price"]
    cidade["tipo_assento"] = trecho_dict["tipo_assento"]
    cidade["itinerario"] = trecho_dict["itinerario"]
    cidade["max_discount"] = trecho_dict.get("max_discount", DECIMAL_ZERO)
    cidade["promo_value"] = trecho_dict["promo_value"] if "promo_value" in trecho_dict.keys() else None


def _resolve_cidades(cidades_ids, withextrainfo=False):
    # ordem das cidades precisa ser mantida
    cidades_map = Cidade.objects.in_bulk(cidades_ids)
    cidades_objs = [cidades_map[_id] for _id in cidades_ids]
    return serializer_cidade.serialize(cidades_objs, withextrainfo=withextrainfo)


def _get_dados_trecho_indo_para(destino, origem_ids, withitinerario=False):
    data = trecho_classe_svc.get_trechoclasse_indo_para(destino, origem_ids, withitinerario)

    return data


def _make_rota_dict_indo_para(destino, origem_ids, withitinerario=False):
    cidades = _resolve_cidades(origem_ids)
    data = _get_dados_trecho_indo_para(destino, origem_ids=origem_ids, withitinerario=withitinerario)

    for cidade in cidades:
        try:
            trecho_dict = data[cidade["id"]]
        except KeyError:
            continue
        _merge_cidade_trecho(cidade, trecho_dict)

    return cidades


@memoize(timeout=1 * 60 * 60)
def origem_destino(
    origem_slug, destino_slug
) -> tuple[LocalEmbarqueAdapter | CidadeAdapter | None, LocalEmbarqueAdapter | CidadeAdapter | None]:
    origem: LocalEmbarqueAdapter | CidadeAdapter | None = search_svc.get_local_by_slug(origem_slug)
    destino: LocalEmbarqueAdapter | CidadeAdapter | None = search_svc.get_local_by_slug(destino_slug)
    return origem, destino


def _get_trecho_classes_datetimes(after_date: datetime, tv_filter: dict, count: int) -> dict[datetime, id]:
    dates = (
        TrechoClasse.objects.filter(
            datetime_ida__gte=after_date,
            grupo__status__in=("pending", "travel_confirmed"),
            closed=False,
            grupo_classe__closed=False,
            vagas__gt=0,
            grupo__hidden_for_pax=False,
            trecho_vendido__in=TrechoVendido.objects.filter(**tv_filter),
        )
        .values_list("datetime_ida", "id")
        .distinct("datetime_ida__date")
        .order_by("datetime_ida__date")
    )[:count]
    return dict(dates)


def _get_conexoes_datetime(
    after_date: datetime,
    origem: CidadeAdapter | LocalEmbarqueAdapter,
    destino: CidadeAdapter | LocalEmbarqueAdapter,
) -> dict[datetime, int]:
    end_after_date = after_date + timedelta(days=7)
    (
        trechos_by_conexao,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    ) = _get_trechos_classe_por_trecho_conexao(origem, destino, after_date, end_after_date)

    dates = dict()
    for conexao_id, trechos_conexao in trechos_by_conexao.items():
        min_time_between_conexao = min_time_between_conexao_map[conexao_id]
        max_time_between_conexao = max_time_between_conexao_map[conexao_id]
        mesmo_local = conexao_em_mesmo_local.get(conexao_id, [])

        for trechos_classe in itertools.product(*trechos_conexao):
            if _check_trecho_classes_valid_for_conexao(
                trechos_classe,
                min_time_between_conexao,
                max_time_between_conexao,
                mesmo_local,
            ):
                dates.update({trechos_classe[0].datetime_ida: trechos_classe[0].id})

    return dates


@memoize(timeout=10 * 60)
def get_nearby_departures(
    data_ida,
    origem_slug: str,
    destino_slug: str,
    *,
    incluir_trecho_alternativo=False,
    get_cache_validation_data=False,
    count=4,
) -> dict:
    now = dateutils.now()
    if data_ida is None:
        data_ida = now

    origem, destino = origem_destino(origem_slug, destino_slug)

    if not origem or not destino:
        return {"dias_com_grupos": []}

    if incluir_trecho_alternativo and origem.local_alternativo:
        origem = origem.local_alternativo

    tv_filter = {
        **origem.as_queryset_filter("origem"),
        **destino.as_queryset_filter("destino"),
    }
    data_ida = dateutils.to_tz(data_ida, origem.timezone)
    now = dateutils.to_tz(now, origem.timezone)

    if not data_ida or not now:
        return {"dias_com_grupos": []}

    max_before_date = max(now, data_ida - timedelta(days=5))

    datetimes_tc = _get_trecho_classes_datetimes(max_before_date, tv_filter, 2 * count)
    datetimes_conexao = _get_conexoes_datetime(max_before_date, origem, destino)

    trecho_por_datetime = {**datetimes_tc, **datetimes_conexao}
    trechos_por_dia = defaultdict(list)
    datetimes = set()
    for dt, id in trecho_por_datetime.items():
        # convert to tz and to date
        _date = dateutils.to_tz(dt, origem.timezone).date()
        datetimes.add(_date)
        trechos_por_dia[_date].append(id)

    datetimes = sorted(datetimes)

    pos = bisect_left(datetimes, data_ida.date())
    before = datetimes[max(pos - count, 0) : pos]
    after = datetimes[pos : min(pos + count, len(datetimes))]

    before_count, after_count = len(before), len(after)
    if before_count > after_count:
        before = before[-max(count - after_count, 0) :]
    elif after_count > before_count:
        after = after[: min(count - before_count, after_count)]
    else:
        half = count // 2
        before, after = before[-half:], after[:half]

    dias_com_grupos = list(chain(before, after))
    response = {"dias_com_grupos": dias_com_grupos}

    if get_cache_validation_data:
        trechos_por_dia_proximo = {
            datetime.strftime(dia, "%Y-%m-%d"): trecho_id
            for dia, trecho_id in trechos_por_dia.items()
            if dia in dias_com_grupos
        }
        response.update({"trechos_por_dia_proximo": trechos_por_dia_proximo})

    return response


@memoize(timeout=_15_MINS)
def search_top_rotas_saindo_de(
    cidade_slug,
    bairro_slug=None,
    nickname_slug=None,
    number_of_days_ago=7,
    limit=None,
    withextrainfo=False,
    withitinerario=False,
    promo=False,
):
    local = search_svc.get_local_by_slug(cidade_slug)
    if local is None:
        raise Cidade.DoesNotExist
    now_ = dateutils.now()
    search_limit = now_ + timedelta(days=SEARCH_DAYS_LIMIT)  # olhamos apenas os tv com tcs nos próximos 14 dias
    qs_saindo_de = Travel.objects.filter(
        trecho_classe__vagas__gt=0,
        trecho_classe__datetime_ida__range=(now_, search_limit),
        grupo__status__in=("travel_confirmed", "pending"),
        grupo_classe__closed=False,
        grupo__hidden_for_pax=False,
        **local.as_queryset_filter("trecho_vendido__origem"),
    )
    if bairro_slug is not None:
        qs_saindo_de = qs_saindo_de.filter(trecho_vendido__origem__bairro_slug=bairro_slug)
    if nickname_slug is not None:
        qs_saindo_de = qs_saindo_de.filter(trecho_vendido__origem__nickname_slug=nickname_slug)
    qs_saindo_de = (
        qs_saindo_de.values_list(
            "trecho_vendido__destino__cidade",
        )
        .annotate(
            count=Count("id"),
        )
        .order_by(
            "-count",
            # ordena pela cidade apenas para consistência de ordem no empate do count
            "trecho_vendido__destino__cidade",
        )
    )

    days_ago = timezone.now() - timedelta(days=number_of_days_ago)
    qs_saindo_de = qs_saindo_de.filter(created_on__gte=days_ago)
    cidades_saindo_de = [t[0] for t in qs_saindo_de[:limit]]

    qs_nao_vendem = TrechoClasse.objects.filter(
        vagas__gt=0,
        datetime_ida__range=(now_, search_limit),
        grupo__status__in=("travel_confirmed", "pending"),
        grupo_classe__closed=False,
        grupo__hidden_for_pax=False,
        **local.as_queryset_filter("trecho_vendido__origem"),
    )
    if bairro_slug is not None:
        qs_nao_vendem = qs_nao_vendem.filter(trecho_vendido__origem__bairro_slug=bairro_slug)
    if nickname_slug is not None:
        qs_nao_vendem = qs_nao_vendem.filter(trecho_vendido__origem__nickname_slug=nickname_slug)
    qs_nao_vendem = (
        qs_nao_vendem.exclude(trecho_vendido__destino__cidade_id__in=cidades_saindo_de)
        .distinct()
        .annotate(
            has_grupos=Case(
                When(Q(grupo__datetime_ida__gte=now_, vagas__gt=0), then=Value(2)),
                When(grupo__datetime_ida__gte=now_, then=Value(1)),
                default=Value(0),
                output_field=IntegerField(),
            )
        )
        .exclude(has_grupos=0)
        .order_by("-has_grupos")
        .values_list("trecho_vendido__destino__cidade", flat=True)
    )
    ids_cidades_saindo_de_que_nao_vendem = list(OrderedSet(qs_nao_vendem))

    cidades_saindo_de.extend(ids_cidades_saindo_de_que_nao_vendem)
    has_more = limit is not None and len(cidades_saindo_de) > limit
    cidades_saindo_de = cidades_saindo_de[:limit]
    result_dict = _serialize_local(local, with_extra_info=withextrainfo)
    result_dict["has_more"] = has_more
    result_dict["saindo_de"] = _make_rota_dict_saindo_de(
        local,
        cidades_saindo_de,
        withitinerario=withitinerario,
        withextrainfo=withextrainfo,
        promo=promo,
    )

    log_svc.log_search(origem=local, type="origin_search")
    return result_dict


def _make_rota_dict_saindo_de(
    origem,
    destino_ids,
    withitinerario=False,
    withextrainfo=False,
    promo=False,
):
    cidades = _resolve_cidades(destino_ids, withextrainfo)
    data = _get_dados_trecho_saindo_de(origem, destino_ids=destino_ids, withitinerario=withitinerario, promo=promo)

    for cidade in cidades:
        try:
            trecho_dict = data[cidade["id"]]
        except KeyError:
            continue
        _merge_cidade_trecho(cidade, trecho_dict)
    if promo:
        cidades = order_by_discount_and_price(cidades)

    return cidades


def order_by_discount_and_price(cidades):
    return sorted(
        cidades,
        key=lambda cidade: (
            -cidade.get("max_discount", DECIMAL_ZERO),
            cidade.get("best_price", DECIMAL_ZERO),
        ),
    )


def _get_dados_trecho_saindo_de(origem, destino_ids, withitinerario=False, promo=False):
    data = trecho_classe_svc.get_trechoclasse_saindo_de(origem, destino_ids, withitinerario, promo=promo)
    return data


@memoize(timeout=60 * 60 * 1)
def search_top_rotas_conexoes(
    origem_slug,
    destino_slug,
    limit=None,
    cidade_intermediaria_slug=None,
    withextrainfo=False,
    withitinerario=False,
):
    origem = search_svc.get_local_by_slug(origem_slug, with_local_info=True)
    destino = search_svc.get_local_by_slug(destino_slug, with_local_info=True)
    cidade_intermediaria = search_svc.get_local_by_slug(cidade_intermediaria_slug)

    if origem is None or destino is None or (cidade_intermediaria_slug and cidade_intermediaria is None):
        raise Cidade.DoesNotExist

    qs_saem_origem = TrechoVendido.objects.filter(
        rota__ativo=True,
        **origem.as_queryset_filter("origem"),
    )

    qs_chegam_destino = TrechoVendido.objects.filter(
        rota__ativo=True,
        **destino.as_queryset_filter("destino"),
    )

    if cidade_intermediaria:
        qs_saem_origem = qs_saem_origem.filter(**cidade_intermediaria.as_queryset_filter("destino"))
        qs_chegam_destino = qs_chegam_destino.filter(**cidade_intermediaria.as_queryset_filter("origem"))

    qs_saem_origem = qs_saem_origem.values_list("destino__cidade", flat=True)
    qs_chegam_destino = qs_chegam_destino.values_list("origem__cidade", flat=True)

    ids_cidades_intermediarias = list(qs_saem_origem.intersection(qs_chegam_destino))

    has_more = limit is not None and len(ids_cidades_intermediarias) > limit
    ids_cidades_intermediarias = ids_cidades_intermediarias[:limit]

    conexoes, trechos, cidades = _make_rota_dict_conexoes(
        origem,
        destino,
        ids_cidades_intermediarias,
        withitinerario=withitinerario,
        withextrainfo=withextrainfo,
    )
    result_dict = {
        "cidades": cidades,
        "trechos": trechos,
        "conexoes": conexoes,
        "has_more": has_more,
    }

    return result_dict


def _make_rota_dict_conexoes(origem, destino, intermediario_ids, withitinerario=False, withextrainfo=False):
    cidades = _resolve_cidades(intermediario_ids)
    origem_id = origem.cidade.id
    destino_id = destino.cidade.id
    conexao_service = conexao_svc.Conexao(origem, destino)
    trecho_inicio, trecho_fim = search_svc.search_conexao(
        origem.slug,
        destino.slug,
        intermediario_ids,
        withextrainfo=withextrainfo,
        withitinerario=withitinerario,
    )

    trecho_inicio = {trecho["id"]: trecho for trecho in trecho_inicio}
    trecho_fim = {trecho["id"]: trecho for trecho in trecho_fim}

    conexoes = []
    trechos = {}

    cidades_dict = serializer_origem_e_destino_conexoes(origem.cidade, destino.cidade, withextrainfo=withextrainfo)

    for cidade in cidades:
        try:
            trecho_inicio_dict = trecho_inicio[cidade["id"]]
            trecho_fim_dict = trecho_fim[cidade["id"]]
        except KeyError:
            continue
        trechos[f"{origem_id}-{cidade['id']}"] = {
            "origem": origem_id,
            "destino": cidade["id"],
            **trecho_inicio_dict,
        }
        trechos[f"{cidade['id']}-{destino_id}"] = {
            "origem": cidade["id"],
            "destino": destino_id,
            **trecho_fim_dict,
        }
        conexoes.append(
            (
                (f"{origem_id}-{cidade['id']}", f"{cidade['id']}-{destino_id}"),
                conexao_service.calc_score(trecho_inicio_dict, trecho_fim_dict, cidade["id"]),
            )
        )
        cidades_dict[cidade["id"]] = cidade
    conexoes = [c for c, score in sorted(conexoes, key=lambda x: x[1]) if score > 0]
    return conexoes, trechos, cidades_dict


def search_remove_campos(data_ida, origem_slug, destino_slug, user, **kwargs):
    result = search(data_ida, origem_slug, destino_slug, user, **kwargs)
    qtd_grupos = 0
    for d in result["groups_by_date"]:
        hashid_grupos(d["grupos"])
        d["grupos"] = only_valid_groups(d["grupos"])
        remove_fretamento_company_info(d["grupos"])
        qtd_grupos += len(d["grupos"])
    if qtd_grupos == 0:
        result["has_grupos"] = False
    remove_fields(
        result,
        [
            "price_cookie",
            "trecho_alternativo",
            "is_trecho_vendido",
            "badges_by_date",
            "counts",
        ],
    )
    return result


def _filter_valid_groups(groups, data_ida):
    valid_groups = []

    for g in groups:
        if not g["vagas"]:
            continue
        if g["closed"]:
            continue
        if g["status"] not in ["pending", "travel_confirmed"]:
            continue
        if g["trecho_alternativo"]:
            continue
        if data_ida and g["datetime_ida"].split("T")[0] != data_ida.strftime("%Y-%m-%d"):
            continue
        valid_groups.append(g)

    return valid_groups


def _find_recommended_groups(groups, locais_map, data_ida, is_conexao=False):
    valid_groups = _filter_valid_groups(groups, data_ida)

    if not valid_groups:
        return

    recommended_groups = {}

    # menor preço
    group_id = _find_lowest_price_group(valid_groups)
    if group_id:
        recommended_groups["menor_preco"] = group_id

    # ab de custo beneficio
    enable_ab_custo_beneficio = globalsettings_svc.get("enable_ab_custo_beneficio")

    # skipa conexões em grupos de custo beneficio, os trechos que estão presentes nesse teste são trechos fortes
    # e que não possuem conexões
    if enable_ab_custo_beneficio and not is_conexao:
        group_id = _find_best_cost_benefit_groups(valid_groups, locais_map)
        if group_id:
            recommended_groups["custo_beneficio"] = group_id

    return recommended_groups


def _gera_key_origem_destino(slug_origem, slug_destino, separator=":"):
    return f"{slug_origem}{separator}{slug_destino}"


def _find_best_cost_benefit_groups(groups, locais_map):
    cost_benefit_groups = []

    for g in groups:
        local_origem = locais_map[g["origem_id"]]
        local_destino = locais_map[g["destino_id"]]
        key_origem_destino = _gera_key_origem_destino(local_origem["slug"], local_destino["slug"])
        if not MAP_CUSTO_BENEFICIO.get(key_origem_destino):
            return  # Na busca atual se um grupo não for IDA <-> VOLTA, nenhum outro será, então não faz sentido
            # continuar
        if not MAP_CUSTO_BENEFICIO[key_origem_destino](local_origem, g["datetime_ida"]):
            continue
        if g["modelo_venda"] != Grupo.ModeloVenda.BUSER:
            continue

        if g["tipo_assento"] not in ["leito", "semi leito"]:
            continue

        cost_benefit_groups.append(g)

    if not cost_benefit_groups:
        return

    min_group = min(cost_benefit_groups, key=lambda g: g["max_split_value"])

    return min_group["id"]


@traced("search_result.service.grupos_svc._recommendations_map_by_searchrank_api")
def _recommendations_map_by_searchrank_api(device_id, dgroups, locais_map):
    if not len(dgroups):
        return {}
    trechos = _mount_trechos_classe_info_v2(dgroups, locais_map)
    recommendation = recommendation_svc.get_recommendations_searchrank_api(device_id, trechos)

    rankorder = np.argsort(recommendation)
    return {trechos[i].id: recommendation[i] for i in rankorder}


def _make_trecho(group, origem, destino):
    tc_info = TrechoClasseInfoV2(
        id=group["id"],
        cidade_origem_slug=origem["slug"],
        cidade_destino_slug=destino["slug"],
        origem_slug=origem["slug_local"],  # Sempre slug local pois é recomendado um local de embarque
        destino_slug=destino["slug_local"],  # Sempre slug local pois é recomendado um local de embarque
        tipo_assento=group["tipo_assento"],
        preco=group["max_split_value"],
        datetime_ida_origem_tz=group["datetime_ida"],
    )
    return tc_info


def _mount_trechos_classe_info_v2(groups, locais_map):
    trechos_classe_info = [
        _make_trecho(group, locais_map[group["origem_id"]], locais_map[group["destino_id"]]) for group in groups
    ]
    return trechos_classe_info


def _mount_trechos_classe_info(groups, locais_map):
    trechos_classe_info = []
    for group in groups:
        origem = locais_map[group["origem_id"]]
        destino = locais_map[group["destino_id"]]

        tc_info = TrechoClasseInfo(
            id=group["id"],
            origem_slug=origem["slug_local"],  # Sempre slug local pois é recomendado um local de embarque
            destino_slug=destino["slug_local"],  # Sempre slug local pois é recomendado um local de embarque
            tipo_assento=group["tipo_assento"],
            preco=group["max_split_value"],
            datetime_ida_origem_tz=group["datetime_ida"],
        )

        trechos_classe_info.append(tc_info)
    return trechos_classe_info


def _find_lowest_price_group(groups):
    min_group = min(groups, key=lambda g: g["max_split_value"])

    return min_group["id"]


@traced("grupos_svc._taxa_servico_search_vendas")
def _taxa_servico_search_vendas(dgroups, user, client=""):
    porcentual_reprecificacao_vendas = DECIMAL_ZERO
    reprecificar_revendedor = (client == ClientVendas.VENDAS) and repricing_vendas_svc.should_reprice_for_user(user)
    reprecificar_bot = client in REPRICE_BOTS_CLIENTS
    if reprecificar_revendedor or reprecificar_bot:
        fator_reprecificacao_vendas = repricing_vendas_svc.get_reprice_factor(user, client)
        porcentual_reprecificacao_vendas = fator_reprecificacao_vendas * 100

    tc_qs = TrechoClasse.objects.filter(
        id__in=[dgroup["id"] for dgroup in dgroups], grupo_id__in=[dgroup["grupo_id"] for dgroup in dgroups]
    ).select_related("price_manager", "grupo")

    tc_taxa_dict = {}
    for tc in tc_qs:
        if not taxa_servico_checkout_svc.is_enabled(tc.grupo, user, client):
            continue

        tc_taxa_dict[tc.id] = (
            tc.price_manager.percentual_taxa_servico or tc.grupo.percentual_taxa_servico or DECIMAL_ZERO
        ) + porcentual_reprecificacao_vendas

    for dgroup in dgroups:
        dgroup["percentual_taxa_servico"] = tc_taxa_dict.get(dgroup["id"], 0)

    return dgroups


def search_places(origem_slug, destino_slug):
    origem = search_svc.get_local_by_slug(origem_slug)
    destino = search_svc.get_local_by_slug(destino_slug)
    origem_dict = origem.as_dict() if origem else {}
    destino_dict = destino.as_dict() if destino else {}

    return origem_dict, destino_dict


@cacheback(_15_MINS)
def search_cidade_proxima(origem_slug, destino_slug, date_search):
    distance_offset = 0.2
    locais_tuple = None

    cidade_proxima, type_substituicao = find_cidade_proxima(origem_slug, destino_slug, distance_offset)

    if type_substituicao == TipoSubstituicaoEnum.destino:
        locais_tuple = (origem_slug, cidade_proxima.slug)
    elif type_substituicao == TipoSubstituicaoEnum.origem:
        locais_tuple = (cidade_proxima.slug, destino_slug)

    if not locais_tuple:
        return {"origem": {}, "destino": {}, "trecho": {}}

    data = search_melhores_viagens_no_trecho(locais_tuple[0], locais_tuple[1], data_ida=date_search, limit=1)

    trechos = data.get("trechos")

    if isinstance(trechos, list):
        data["trecho"] = trechos[0] if len(trechos) > 0 else None
        del data["trechos"]

    return data


@traced("grupos_svc.search_cidade_proxima_v2")
def search_cidade_proxima_v2(origem_slug, destino_slug, date_search):
    cidades_origem, cidades_destino, trechos_classes = find_trechos_classes_cidade_proxima(
        origem_slug, destino_slug, date_search
    )
    if not ((cidades_origem or cidades_destino) and trechos_classes):
        return {}

    map_cidades_proximas = {
        cidade.slug: {
            "distance": round(cidade.distance.km),
            "cidade_proxima_type": "origem",
            "name": cidade.name,
            "slug": cidade.slug,
            "trechos_by_date": {},
        }
        for cidade in cidades_origem
    }

    map_cidades_proximas.update(
        {
            cidade.slug: {
                "distance": round(cidade.distance.km),
                "cidade_proxima_type": "destino",
                "name": cidade.name,
                "slug": cidade.slug,
                "trechos_by_date": {},
            }
            for cidade in cidades_destino
        }
    )

    trechos_classes = _get_trechos_classe_cidade_proxima(trechos_classes)
    if not trechos_classes:
        return map_cidades_proximas

    _default_price = Decimal("inf")
    now = dateutils.now()
    for tc in trechos_classes:
        if not validate_trecho_classe(tc, now):
            continue
        tc = tc.serialize()
        cidade_origem = tc["cidade_origem"]
        cidade_destino = tc["cidade_destino"]
        date_ida = tc["datetime_ida"]
        if cidade_origem in map_cidades_proximas:
            current_date = map_cidades_proximas[cidade_origem]["trechos_by_date"].get(
                date_ida, {"total_trechos": 0, "best_price": _default_price}
            )
            current_date["best_price"] = min(current_date["best_price"], tc["price"])
            current_date["total_trechos"] += 1
            map_cidades_proximas[cidade_origem]["trechos_by_date"][date_ida] = current_date

        elif cidade_destino in map_cidades_proximas:
            current_date = map_cidades_proximas[cidade_destino]["trechos_by_date"].get(
                date_ida, {"total_trechos": 0, "best_price": _default_price}
            )
            current_date["best_price"] = min(current_date["best_price"], tc["price"])
            current_date["total_trechos"] += 1
            map_cidades_proximas[cidade_destino]["trechos_by_date"][date_ida] = current_date

    return map_cidades_proximas


def _get_trechos_classe_cidade_proxima(tcs_ids):
    return TrechoClasse.objects.filter(pk__in=tcs_ids).to_serialize(CidadeProximaTrechoClasseSerializer)


@traced("grupos_svc._get_map_trechos_classes_conexao")
def _get_map_trechos_classes_conexao(
    trechos_conexao: list[TrechoConexao],
    origem: LocalEmbarqueAdapter | CidadeAdapter,
    destino: LocalEmbarqueAdapter | CidadeAdapter,
    data_ida: datetime | None = None,
    end_data_ida: datetime | None = None,
):
    """
    Itera sobre as instâncias de TrechoConexao para que possamos buscar os TrechoClasse correspondente por perna da
    conexão. Assim conseguimos limitar a quantidade de resultados de TrechoClasse usando os horários de desembarque
    da perna anterior para a perna nova.
    Caso não tenha data pesquisada, pesquisamos apenas os TrechoClasse da data atual.
    A partir disso mapeamos os TrechoClasse correspondentes em um dicionário com as chaves sendo os ids de cidade de
    embarque e desembarque.

    :param trechos_conexao: lista de TrechoConexao
    :param origem: origem pesquisada
    :param data_ida: data pesquisada caso tenha
    :param end_data_ida: final do range para pesquisar a data_ida
    :return: dict com chaves da forma origem.cidade_id:destino.cidade_id que agrega os TrechoClasse com origem e
    destino correspondentes.
    """
    map_trechos_classe = defaultdict(list)
    trechos_conexao_por_perna = defaultdict(list)
    company_id_por_perna = defaultdict(list)
    modelo_venda_por_perna = defaultdict(list)
    perna_aceita_qualquer_company = defaultdict(bool)
    perna_aceita_qualquer_modelo_venda = defaultdict(bool)

    for trecho in trechos_conexao:
        trechos_conexao_por_perna[trecho.idx].append(trecho)
        if trecho.company_id:
            company_id_por_perna[trecho.idx].append(trecho.company_id)
        else:
            perna_aceita_qualquer_company[trecho.idx] = True

        if trecho.modelo_venda:
            modelo_venda_por_perna[trecho.idx].append(trecho.modelo_venda)
        else:
            perna_aceita_qualquer_modelo_venda[trecho.idx] = True

    numero_pernas = len(trechos_conexao_por_perna.keys())

    id_trechos_vendidos_por_perna = defaultdict(set[int])
    for perna_idx in range(numero_pernas):
        id_trechos_vendidos_por_perna[perna_idx] = _get_trechos_vendidos_from_conexoes(
            trechos_conexao_por_perna[perna_idx], origem, destino
        )

    if not data_ida:
        data_ida = dateutils.now()
        days = globalsettings_svc.get("connection_days_filter", 2)
        end_data_ida = dateutils.truncate(data_ida) + timedelta(days=days)

    data_ida_with_timezone = _data_parse_timezone(data_ida, origem.timezone)

    start_data_ida = data_ida_with_timezone
    if end_data_ida:
        end_data_ida = _data_parse_timezone(end_data_ida, origem.timezone)
    else:
        end_data_ida = dateutils.truncate(start_data_ida) + timedelta(hours=23, minutes=59) + timedelta(hours=2)

    for perna_idx in range(numero_pernas):
        trechos_classe = (
            TrechoClasse.objects.filter(
                trecho_vendido_id__in=id_trechos_vendidos_por_perna[perna_idx],
                grupo__status__in=["pending", "travel_confirmed"],
                datetime_ida__range=[start_data_ida, end_data_ida],
                closed=False,
                vagas__gt=0,
                grupo_classe__closed=False,
                grupo__hidden_for_pax=False,
            )
            .select_related(
                "trecho_vendido__origem",
                "trecho_vendido__destino",
                "trecho_vendido__origem__cidade",
                "trecho_vendido__destino__cidade",
                "grupo",
                "grupo_classe",
            )
            .to_serialize(serializer_trecho_classe.SearchSerializer)
            .order_by("datetime_ida")
        )
        partial_valida_chegada_ou_ida_madrugada = partial(
            _valida_chegada_ou_ida_madrugada,
            is_last_perna=perna_idx == numero_pernas,
            is_first_perna=perna_idx == 0,
        )
        trechos_classe = list(filter(partial_valida_chegada_ou_ida_madrugada, trechos_classe))

        if not trechos_classe:
            return {}

        start_data_ida, end_data_ida = _horarios_possiveis_trechos_proxima_perna(
            trechos_conexao_por_perna[perna_idx], trechos_classe
        )

        _atualiza_map_trechos_classe(
            map_trechos_classe,
            trechos_classe,
            perna_idx,
            perna_aceita_qualquer_company,
            company_id_por_perna,
            perna_aceita_qualquer_modelo_venda,
            modelo_venda_por_perna,
        )
    return map_trechos_classe


def _valida_chegada_ou_ida_madrugada(
    trecho_classe: TrechoClasse,
    is_last_perna: bool = False,
    is_first_perna: bool = False,
    hora_ref: int = 5,
) -> bool:
    chegada_apos_as_hora_ref = trecho_classe.get_horario_chegada().hour >= hora_ref

    if is_first_perna:
        return chegada_apos_as_hora_ref

    partida_apos_hora_ref = hora_ref <= trecho_classe.get_horario_partida().hour
    if is_last_perna:
        return partida_apos_hora_ref

    return chegada_apos_as_hora_ref and partida_apos_hora_ref


@traced("grupos_svc._horarios_possiveis_trechos_proxima_perna")
def _horarios_possiveis_trechos_proxima_perna(trechos_conexao, trechos_classe):
    tempo_min_entre_conexoes = min([trecho.min_time_between_conexoes for trecho in trechos_conexao])
    tempo_max_entre_conexoes = max([trecho.max_time_between_conexoes for trecho in trechos_conexao])
    horarios_chegada = [tc.get_horario_chegada() for tc in trechos_classe]
    start_data_ida = min(horarios_chegada) + tempo_min_entre_conexoes
    end_data_ida = max(horarios_chegada) + tempo_max_entre_conexoes

    return start_data_ida, end_data_ida


@traced("grupos_svc._atualiza_map_trechos_classe")
def _atualiza_map_trechos_classe(
    map_trechos_classe,
    trechos_classe,
    perna_idx,
    perna_aceita_qualquer_company,
    company_id_por_perna,
    perna_aceita_qualquer_modelo_venda,
    modelo_venda_por_perna,
):
    for trecho_classe in trechos_classe:
        # Filtramos os trechos caso exista alguma restrição de company ou mondelo_venda no TrechoConexao da perna
        if (
            not perna_aceita_qualquer_company[perna_idx]
            and trecho_classe.grupo.company_id not in company_id_por_perna[perna_idx]
        ):
            continue

        if (
            not perna_aceita_qualquer_modelo_venda[perna_idx]
            and trecho_classe.grupo.modelo_venda not in modelo_venda_por_perna[perna_idx]
        ):
            continue

        key = "%s::%s" % (
            trecho_classe.trecho_vendido.origem.cidade_id,
            trecho_classe.trecho_vendido.destino.cidade_id,
        )
        map_trechos_classe[key].append(trecho_classe)


def _get_trechos_classe_por_trecho_conexao(
    origem: CidadeAdapter | LocalEmbarqueAdapter,
    destino: CidadeAdapter | LocalEmbarqueAdapter,
    data_ida: datetime | None,
    end_data_ida: datetime | None = None,
) -> tuple[dict[int, list], dict[int, list], dict[int, list], dict[int, list]]:
    tc_filter = (
        Q(conexao__cidade_origem_id=origem.cidade.pk)
        & Q(conexao__cidade_destino_id=destino.cidade.pk)
        & Q(deleted_at__isnull=True)
        & Q(conexao__deleted_at__isnull=True)
    )
    if not data_ida:
        tc_filter &= Q(conexao__exibir_busca_sem_data=True)

    trechos_by_conexao = defaultdict(list)
    min_time_between_conexao_map = defaultdict(list)
    max_time_between_conexao_map = defaultdict(list)
    conexao_em_mesmo_local = defaultdict(list)

    trechos_conexao = list(TrechoConexao.objects.filter(tc_filter).select_related("origem", "destino"))
    if not trechos_conexao:
        return (
            trechos_by_conexao,
            min_time_between_conexao_map,
            max_time_between_conexao_map,
            conexao_em_mesmo_local,
        )

    map_trechos_classe = _get_map_trechos_classes_conexao(trechos_conexao, origem, destino, data_ida, end_data_ida)
    if not map_trechos_classe:
        return (
            trechos_by_conexao,
            min_time_between_conexao_map,
            max_time_between_conexao_map,
            conexao_em_mesmo_local,
        )

    # store the conexões that can't be fullfilled because we have some legs without trechos_classes
    invalid_conexoes_ids = set()

    for trecho in trechos_conexao:
        conexao_id = trecho.conexao_id

        if conexao_id in invalid_conexoes_ids:
            continue

        key = "%s::%s" % (trecho.origem_id, trecho.destino_id)
        trechos_classe = map_trechos_classe.get(key, [])

        if trecho.conexao_em_mesmo_local:
            conexao_em_mesmo_local[trecho.conexao_id].append(trecho.idx)

        if len(trechos_classe) == 0:
            # one of the legs doesn't have trechos, given that we can skip processing this conexao as we wouldn't be
            # able to finish it
            invalid_conexoes_ids.add(conexao_id)
            continue

        trechos_by_conexao[conexao_id].append(trechos_classe)
        min_time_between_conexao_map[conexao_id].append(trecho.min_time_between_conexoes)
        max_time_between_conexao_map[conexao_id].append(trecho.max_time_between_conexoes)

    # better to remove the invalid conexões to avoid extra work
    for invalid_conexao_id in invalid_conexoes_ids:
        if invalid_conexao_id in trechos_by_conexao:
            del trechos_by_conexao[invalid_conexao_id]
        if invalid_conexao_id in min_time_between_conexao_map:
            del min_time_between_conexao_map[invalid_conexao_id]
        if invalid_conexao_id in max_time_between_conexao_map:
            del max_time_between_conexao_map[invalid_conexao_id]
        if invalid_conexao_id in conexao_em_mesmo_local:
            del conexao_em_mesmo_local[invalid_conexao_id]

    return (
        trechos_by_conexao,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    )


@cacheback(10 * 60)
@traced("grupos_svc.search_grupos_conexao")
def search_grupos_conexao(
    origem_slug: str, destino_slug: str, data_ida: datetime | None
) -> tuple[
    LocalEmbarqueAdapter | CidadeAdapter,
    LocalEmbarqueAdapter | CidadeAdapter,
    dict,
    dict,
    dict,
    dict,
    dict,
    dict,
]:
    origem: LocalEmbarqueAdapter | CidadeAdapter | None = search_svc.get_local_by_slug(origem_slug)
    destino: LocalEmbarqueAdapter | CidadeAdapter | None = search_svc.get_local_by_slug(destino_slug)

    if not origem or not destino:
        raise TrechoInexistente()

    (
        trechos_by_conexao,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    ) = _get_trechos_classe_por_trecho_conexao(origem, destino, data_ida)

    itinerarios_map = {}
    locais_map = {}
    groups_by_conexao = defaultdict(list)
    for conexao_id, trechos_classe_varias_pernas in trechos_by_conexao.items():
        for trechos_classe in trechos_classe_varias_pernas:
            dgroups, _itinerarios_map, _locais_map = serializer_trecho_classe.serialize_search(trechos_classe)

            groups_by_conexao[conexao_id].append(dgroups)
            itinerarios_map.update(_itinerarios_map)
            locais_map.update(_locais_map)

    return (
        origem,
        destino,
        groups_by_conexao,
        itinerarios_map,
        locais_map,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    )


def _get_trechos_vendidos_from_conexoes(
    trechos_conexoes: list,
    origem: CidadeAdapter | LocalEmbarqueAdapter,
    destino: CidadeAdapter | LocalEmbarqueAdapter,
) -> set[int]:
    trecho_vendidos_ids = set()
    trechos_existentes = set()

    for trecho_conexao in trechos_conexoes:
        trecho_atual_key = "%s::%s" % (
            trecho_conexao.origem_id,
            trecho_conexao.destino_id,
        )
        if trecho_atual_key in trechos_existentes:
            continue

        (usar_loc_exato_origem, usar_loc_exato_destino) = (
            trecho_conexao.origem_id == origem.cidade.pk and isinstance(origem, LocalEmbarqueAdapter),
            trecho_conexao.destino_id == destino.cidade.pk and isinstance(destino, LocalEmbarqueAdapter),
        )

        # Usamos o local exato, caso a origem seja a origem da busca (primeiro trecho),
        # e a busca seja um local de emb., ou caso o mesmo seja verdadeiro para o destino.
        adapter_origem = CidadeAdapter(trecho_conexao.origem) if not usar_loc_exato_origem else origem
        adapter_destino = CidadeAdapter(trecho_conexao.destino) if not usar_loc_exato_destino else destino
        locais, locais_exatos = trechos_vendidos_svc.get(adapter_origem, adapter_destino)

        usar_loc_exato = usar_loc_exato_origem or usar_loc_exato_destino
        trecho_vendidos_ids.update(locais_exatos if usar_loc_exato else locais)
        trechos_existentes.add(trecho_atual_key)

    return trecho_vendidos_ids


def _create_additional_attrs(trechos_classe: list | tuple) -> dict:
    ids = []
    status_valids = ["pending", "travel_confirmed"]
    max_split_value = DECIMAL_ZERO
    ref_split_value = DECIMAL_ZERO
    vagas = trechos_classe[0]["vagas"]
    closed = trechos_classe[0]["closed"]
    duracao_ida = (
        datetime.fromisoformat(trechos_classe[-1]["chegada_ida"])
        - datetime.fromisoformat(trechos_classe[0]["datetime_ida"])
    ).total_seconds() * 1000
    status = "pending"
    for tc in trechos_classe:
        try:
            tc["id"] = hashint(tc["id"])
        except ValueError:
            if not isinstance(tc["id"], str):
                # if it's str means that we already hashed if not means that we have another error
                raise
        ids.append(tc["id"])
        max_split_value += tc["max_split_value"]
        ref_split_value += tc["ref_split_value"]
        vagas = min(tc["vagas"], vagas)
        status = tc["status"] if tc["status"] not in status_valids else status
        closed &= tc["closed"]

    return dict(
        id=":".join(ids),
        vagas=_limita_vagas_exibidas(vagas),
        parcelamento=serializer_trecho_classe.parcelamento_from_value(max_split_value),
        max_split_value=max_split_value,
        ref_split_value=ref_split_value,
        datetime_ida=trechos_classe[0]["datetime_ida"],
        duracao_ida=duracao_ida,
        status=status,
        closed=closed,
        is_conexao=True,
        trecho_alternativo=False,
    )


def _check_arrival_datetimes_conexao(trechos_classe, min_time_between_conexoes: list, max_time_between_conexoes: list):
    """
    Check if all trechos on a conexao have valid arrival and departure datetimes, we consider that a "conexao" is valid
    if the difference between all the departure and arrivals are less than min_time_between_conexoes or greater than
    max_time_between_conexoes.

    :param trechos_classe: list of trecho classes we want to check
    :param min_time_between_conexoes: list of min_time_between_conexoes, the list will have the timedelta for each leg
    of the conexão, for example if we have a conexão of 3 points we will have two legs, then we will have the
    min_time_between_conexoes as [timedelta1, timedelta2, timedelta3], but the last timedelta will be ignored.
    :param max_time_between_conexoes: list of max_time_between_conexoes, the list will have the timedelta for each leg
    of the conexão, for example if we have a conexão of 3 points we will have two legs, then we will have the
    max_time_between_conexoes as [timedelta1, timedelta2, timedelta3], but the last timedelta will be ignored.
    :return: True if the conexao is valid and all the checkpoints have valid arrivals/departures.
    """
    for i in range(len(trechos_classe) - 1):
        tc1 = trechos_classe[i]
        tc2 = trechos_classe[i + 1]

        horario_chegada = datetime.fromisoformat(tc1["chegada_ida"])
        horario_partida_proximo_trecho = datetime.fromisoformat(tc2["datetime_ida"])

        if horario_partida_proximo_trecho < horario_chegada:
            return False

        # check if the chegada time is enough to cover the partida of the next trecho
        difference_chegada = horario_partida_proximo_trecho - horario_chegada

        min_time_between_conexao = min_time_between_conexoes[i]
        if min_time_between_conexao is not None and difference_chegada < min_time_between_conexao:
            return False

        max_time_between_conexao = max_time_between_conexoes[i]
        if max_time_between_conexao is not None and difference_chegada > max_time_between_conexao:
            return False

    return True


def _check_conexao_em_mesmo_local(trechos_classe, conexao_em_mesmo_local: list):
    for idx in conexao_em_mesmo_local:
        pivo_trecho = trechos_classe[idx]
        try:
            next_trecho = trechos_classe[idx + 1]
        except IndexError:
            # Aqui nesse caso o ultimo trecho da conexão foi cadastrado como `conexao_em_mesmo_local`, mas não existe
            # mais um trecho depois do atual, apenas ignoramos
            return True

        if pivo_trecho["destino_id"] != next_trecho["origem_id"]:
            return False
    return True


def _check_trecho_classes_valid_for_conexao(
    trechos_classe, min_time_between_conexao, max_time_between_conexao, mesmo_local
) -> bool:
    # check if the arrival time is greater than the next trecho if not we should discard
    dict_tc_list = [tc if isinstance(tc, dict) else tc.to_dict_json_base() for tc in trechos_classe]
    valid_trecho_classes = _check_arrival_datetimes_conexao(
        dict_tc_list,
        min_time_between_conexao,
        max_time_between_conexao,
    )

    valid_all_connection_same_local = _check_conexao_em_mesmo_local(dict_tc_list, mesmo_local)

    return valid_trecho_classes and valid_all_connection_same_local


def _mount_conexoes_map(
    dgroups_by_conexao: dict,
    min_time_between_conexao_map: dict,
    max_time_between_conexao_map: dict,
    conexao_em_mesmo_local: dict,
) -> list:
    result_conexoes = []
    logctx = threadlocals.get_log_ctx()
    user_tabid = logctx.get("user_tabid")
    for conexao_id, trechos_conexao in dgroups_by_conexao.items():
        min_time_between_conexao = min_time_between_conexao_map[conexao_id]
        max_time_between_conexao = max_time_between_conexao_map[conexao_id]
        mesmo_local = conexao_em_mesmo_local.get(conexao_id, [])

        for trechos_classe in itertools.product(*trechos_conexao):
            if not _check_trecho_classes_valid_for_conexao(
                trechos_classe,
                min_time_between_conexao,
                max_time_between_conexao,
                mesmo_local,
            ):
                continue

            conexao_obj = _create_additional_attrs(trechos_classe)
            conexao_obj["conexoes"] = trechos_classe

            for trecho in trechos_classe:
                if "signed" in trecho:
                    continue
                trecho["signed"] = signed_dgroup(
                    unhashint(trecho["id"]),
                    trecho["max_split_value"],
                    trecho["ref_split_value"],
                    user_tabid,
                )

            result_conexoes.append(conexao_obj)

    return result_conexoes


@traced("grupos_svc.search_conexao")
def search_conexao(data_ida: datetime | None, origem_slug: str, destino_slug: str):
    (
        origem,
        destino,
        dgroups_by_conexao,
        itinerarios_map,
        locais_map,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    ) = search_grupos_conexao(origem_slug, destino_slug, data_ida)

    # to optimize the _hydrate_groups we first do the query with grouped tcs and after we use the query values to
    # execute the logic on each conexão
    group_ids = []
    for dgroups_list in dgroups_by_conexao.values():
        for dgroups in dgroups_list:
            group_ids.extend(g["id"] for g in dgroups)
    updated_trechos_classe = _query_to_hydrate_groups(group_ids)

    if dgroups_by_conexao:
        for conexao_id, trecho_conexoes in dgroups_by_conexao.items():
            for i in range(len(trecho_conexoes)):
                trecho_conexoes[i] = _hydrate_groups_logic(trecho_conexoes[i], origem, updated_trechos_classe)
                _remove_full_groups(trecho_conexoes[i])

    locais_de_embarque, locais_de_desembarque = _get_locais_conexao_cleaned(locais_map, dgroups_by_conexao)

    conexoes = _mount_conexoes_map(
        dgroups_by_conexao,
        min_time_between_conexao_map,
        max_time_between_conexao_map,
        conexao_em_mesmo_local,
    )

    groups_by_date = _groups_by_date(conexoes)
    if data_ida and not groups_by_date:
        groups_by_date.append(
            {
                "datetime_ida": data_ida.strftime("%Y-%m-%d"),
                "grupos": [],
            }
        )
    origem_dict = origem.as_dict()
    destino_dict = destino.as_dict()
    data = {
        "trecho": {
            "origem": origem_dict,
            "destino": destino_dict,
        },
        "trecho_alternativo": None,
        "groups_by_date": groups_by_date,
        "itinerarios": itinerarios_map,
        "locais": locais_map,
        "locais_de_embarque": locais_de_embarque,
        "locais_de_desembarque": locais_de_desembarque,
        "grupos_recomendados": _find_recommended_groups(conexoes, locais_map, data_ida, True),
        "destino_picture_url": _get_destino_picture_url(destino),
    }
    return data


def filtra_trechos_liberados_search_marketplace(
    trechos: list[TrechoClasse], managers: list[TrechoEstoqueManager]
) -> list[TrechoClasse]:
    managers_map = {(m.origem_slug, m.destino_slug): m for m in managers}
    trechos_filtrados = []
    for trecho in trechos:
        assert trecho.trecho_vendido.origem.itree_slug
        assert trecho.trecho_vendido.destino.itree_slug
        manager = managers_map.get((trecho.trecho_vendido.origem.itree_slug, trecho.trecho_vendido.destino.itree_slug))
        if not manager or not trecho.grupo.company or trecho.grupo.company.cnpj is None:
            continue
        if manager.should_create(trecho.grupo.company.cnpj, TrechoEstoqueManager.Trigger.SEARCH):
            trechos_filtrados.append(trecho)
    return trechos_filtrados


@traced("grupos_svc.search_marketplace")
def search_marketplace(data_ida: date, origem_slug: str, destino_slug: str, return_all: bool = False):
    start = time.monotonic()
    estoque_manager = create_group_marketplace_svc.get_estoque_manager(origem_slug, destino_slug)

    if not return_all and not estoque_manager.should_create(None, TrechoEstoqueManager.Trigger.SEARCH):
        logger.info(
            "grupos_svc.search_marketplace without marketplace request", extra={"duration": time.monotonic() - start}
        )
        return []

    trechos: list[TrechoClasse] = search_marketplace_svc.search(origem_slug, destino_slug, data_ida, fetch_db_data=True)

    if return_all:
        trechos_estao_no_estoque = trechos
    else:
        trechos_estao_no_estoque = [
            t
            for t in trechos
            if t.grupo.company
            and estoque_manager.should_create(t.grupo.company.cnpj, TrechoEstoqueManager.Trigger.SEARCH)
        ]

    dgroups, _, _ = serializer_trecho_classe.serialize_search(
        trechos_estao_no_estoque, serializer_trecho_classe.SearchMarketplaceSerializer
    )
    logger.info("grupos_svc.search_marketplace with marketplace request", extra={"duration": time.monotonic() - start})
    return dgroups


@memoize(timeout=60)
def upsert_grupo(search_item: SearchItemHotOffer) -> tuple[int, Decimal]:
    def send_to_sentry_if_not_should_offer_new(tc, search_item):
        # Se não está fechado e não foi criado recentemente, então não deveríamos estar mostrando uma oferta nova na SR
        estava_fechado = tc.closed or tc.grupo_classe.closed
        foi_criado_recente = tc.created_on > timezone.now() - timedelta(hours=1)
        so_fazendo_update = search_item.id
        if not (estava_fechado or foi_criado_recente or so_fazendo_update):
            context = {"id": tc, "extra__mkp_servico": search_item.extra_mkp_servico}
            logger.warning("This trecho already exists", extra=context)

    def send_to_sentry_if_any_inconsistency(tc, search_item, should_update):
        new_max_split_value = Decimal(str(search_item.max_split_value))
        vagas_divergentes = tc.vagas != search_item.vagas
        preco_divergente = abs(tc.max_split_value - new_max_split_value) > Decimal("0.1")
        if tc.closed or vagas_divergentes or preco_divergente:
            context = {
                "id": tc.id,
                "extra__mkp_servico": search_item.extra_mkp_servico,
                "vagas": search_item.vagas,
                "max_split_value": search_item.max_split_value,
                "last_synced": search_item.extra_mkp_last_synced_at,
                "should_update": should_update,
                "closed": tc.closed,
                "vagas_divergentes": vagas_divergentes,
                "preco_divergente": preco_divergente,
            }
            logger.warning("Trecho não atualizado", extra=context)

    def update(tc: TrechoClasse, search_item: SearchItemHotOffer) -> tuple[int, Decimal]:
        send_to_sentry_if_not_should_offer_new(tc, search_item)

        try:
            new_last_synced_at = datetime.fromisoformat(search_item.extra_mkp_last_synced_at)  # type: ignore
            desatualizado = not tc.extra_mkp_last_synced_at or new_last_synced_at > tc.extra_mkp_last_synced_at
        except (TypeError, ValueError) as err:
            sentry_sdk.capture_exception(err)
            new_last_synced_at = timezone.now()
            desatualizado = True

        assert tc.max_split_value
        new_max_split_value = Decimal(str(search_item.max_split_value))
        should_update = desatualizado and (
            tc.closed or tc.vagas != search_item.vagas or abs(new_max_split_value - tc.max_split_value) > Decimal("0.1")
        )

        if should_update:
            tc, price_manager, price_bucket, price_log = create_group_marketplace_svc.update_trecho_classe(
                tc, new_max_split_value, search_item.vagas, new_last_synced_at
            )
            preco_svc.bulk_update_price_objects([price_manager], price_bucket, price_log)
            if price_manager:
                tc.price_manager_id = price_manager.id
            rodoviaria_svc.atualizar_trechos_classes(
                [tc],
                TrechoClasseMarketplaceLogger.GatilhoAtualizacao.MARKETPLACE_SEARCH_UPDATE,
            )

        send_to_sentry_if_any_inconsistency(tc, search_item, should_update)

        assert tc.max_split_value
        return tc.id, tc.max_split_value

    def create(search_item: SearchItemHotOffer) -> tuple[int, Decimal]:
        tc = to_trecho_classe(search_item)
        provider_data = tc.grupo._provider_data  # type: ignore

        with transaction.atomic():
            if not tc.trecho_vendido.rota.id:
                tc.trecho_vendido.rota.save()
                tc.trecho_vendido.rota._checkpoints[0].save()  # type: ignore
                tc.trecho_vendido.rota._checkpoints[1].save()  # type: ignore
                tc.trecho_vendido.save()

            tc.grupo.save()
            tc.grupo_classe.save()
            assert tc.price_manager
            tc.price_manager.save()
            tc.save()

        core_log_svc.log_search_marketplace_create_group(tc.grupo, data=provider_data)
        assert tc.max_split_value
        return tc.id, tc.max_split_value

    if search_item.id:
        tc = TrechoClasse.objects.filter(id=unhashint(search_item.id)).first()
    else:
        tc = TrechoClasse.objects.filter(
            datetime_ida=search_item.datetime_ida,
            grupo__company_id=search_item.company_id,
            grupo__rota__tipo=Rota.Tipos.MARKETPLACE,
            grupo__status=Grupo.Status.TRAVEL_CONFIRMED,
            grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
            grupo_classe__tipo_assento=search_item.tipo_assento,
            extra__mkp_servico=search_item.extra_mkp_servico,
        ).first()

    if tc:
        return update(tc, search_item)
    return create(search_item)


def to_trecho_classe(item: SearchItemHotOffer) -> TrechoClasse:
    duracao = timedelta(milliseconds=item.duracao_ida)
    velocidade_media = 60 / 3600  # 60km/h
    distancia = duracao.total_seconds() * velocidade_media

    rota = Rota.objects.filter(
        origem_id=item.origem_id,
        destino_id=item.destino_id,
        tipo=Rota.Tipos.MARKETPLACE,
        duracao_total=duracao,
    ).first()

    if rota:
        trecho_vendido = TrechoVendido.objects.filter(
            origem_id=item.origem_id, destino_id=item.destino_id, rota=rota
        ).first()
    else:
        rota = Rota(
            origem_id=item.origem_id,
            destino_id=item.destino_id,
            tipo=Rota.Tipos.MARKETPLACE,
            duracao_total=duracao,
            distancia_total=distancia,
        )
        trecho_vendido = TrechoVendido(origem_id=item.origem_id, destino_id=item.destino_id, rota=rota)
        checkpoint_origem = Checkpoint(
            local_id=item.origem_id,
            rota=rota,
            duracao=timedelta(seconds=0),
            distancia_km=0,
            idx=0,
        )
        checkpoint_destino = Checkpoint(
            local_id=item.destino_id,
            rota=rota,
            duracao=duracao,
            distancia_km=distancia,
            idx=1,
        )
        rota._checkpoints = [checkpoint_origem, checkpoint_destino]  # type: ignore

    datetime_ida = datetime.fromisoformat(item.datetime_ida)
    user, _ = User.objects.get_or_create(email=settings.EMAIL_AUTOMACAO_MARKETPLACE)
    grupo = Grupo(
        company_id=item.company_id,
        rota=rota,
        duracao_total=rota.duracao_total,
        datetime_ida=datetime_ida,
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        status=Grupo.Status.TRAVEL_CONFIRMED,
        confirming_probability="high",
        created_by=user,
        percentual_repasse=configuracao_pagamento_svc.get_porcentagem_repasse(item.company_id),
        percentual_taxa_servico=taxa_servico_checkout_svc.get_percentual_referencia_marketplace_taxa_servico_checkout(
            item.company_id
        ),
    )
    grupo._provider_data = asdict(item)  # type: ignore
    grupo_classe = GrupoClasse(grupo=grupo, tipo_assento=item.tipo_assento, capacidade=item.capacidade)

    return TrechoClasse(
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        datetime_ida=datetime_ida,
        max_split_value=item.max_split_value,
        ref_split_value=item.max_split_value,
        vagas=item.vagas,
        duracao_ida=rota.duracao_total,
        distancia_km=rota.distancia_total,
        price_manager=PriceManager(
            min_pessoas=0,
            value=Decimal(str(item.max_split_value)),
            ref_value=Decimal(str(item.max_split_value)),
        ),
        extra=TrechoClasseExtra(
            mkp_servico=item.extra_mkp_servico,
            mkp_extra=item.extra_mkp_extra,
            mkp_ota_config_id=item.extra_mkp_ota_config_id,
            mkp_last_synced_at=item.extra_mkp_last_synced_at,
            mkp_stopovers=item.extra_mkp_stopovers,
        ),
    )


def todas_datas_ainda_possuem_trechos_abertos(trechos_por_dia: dict[datetime.date, int]):
    """
    Verifica se existe pelo menos um trecho aberto para cada data.
    """
    dias = trechos_por_dia.keys()
    trechos_ids = list(itertools.chain.from_iterable(trechos_por_dia.values()))
    queryset = (
        TrechoClasse.objects.filter(
            datetime_ida__date__in=dias,
            id__in=trechos_ids,
            grupo__status__in=("pending", "travel_confirmed"),
            closed=False,
            grupo_classe__closed=False,
            vagas__gt=0,
            grupo__hidden_for_pax=False,
        )
        .annotate(data=TruncDate("datetime_ida"))
        .values_list("data", flat=True)
        .distinct("data")
    )

    return len(list(queryset)) == len(dias)
