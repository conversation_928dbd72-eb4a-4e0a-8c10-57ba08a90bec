import json
from datetime import datetime, timedelta
from decimal import Decimal
from http import HTTPStatus
from zoneinfo import ZoneInfo

from django.utils import timezone
from model_bakery import baker

from commons.utils import unhashint
from core.models_commons import ActivityLog
from core.models_company import Company
from core.models_grupo import Grupo, GrupoClasse, TrechoClasse
from core.models_rota import Checkpoint, LocalEmbarque, Rota, TrechoVendido
from core.views import upsert_grupo
from search_result.service.grupos_svc import SearchItemHotOffer, signer
from search_result.service.grupos_svc import upsert_grupo as upsert_grupo_svc
from search_result.utils.hashint import hashint


def test_view_upsert_grupo_old_front(rf, mocker):
    grupo = {
        "company_cnpj": "12345678000199",
        "origem_slug": "sao-paulo-sp",
        "cidade_origem_slug": "sao-paulo",
        "destino_slug": "rio-de-janeiro-rj",
        "cidade_destino_slug": "rio-de-janeiro",
        "tipo_assento": "leito",
        "datetime_ida": "2025-02-13T12:00:00",
        "vagas": "2",
        "max_split_value": 150.0,
        "modelo_venda": "marketplace",
        "company_id": 1001,
        "origem_id": 1,
        "destino_id": 2,
        "duracao_ida": 360,
        "capacidade": 44,
        "extra_mkp_servico": "beautiful_code",
        "extra_mkp_extra": {"dataCorrida": "2025-10-20", "servico": 321},
        "extra_mkp_ota_config_id": 2,
        "extra_mkp_stopovers": [
            {"dataCorrida": "2025-10-20", "servico": 321},
            {"dataCorrida": "2025-10-20", "servico": 321},
        ],
    }

    request = rf.post(
        "/api/groups/upsert",
        grupo,
    )
    trecho_classe_id = 23124212
    svc = mocker.patch(
        "search_result.views.grupos_svc.upsert_grupo",
        return_value=(trecho_classe_id, Decimal("150.00")),
    )
    mocker.patch("search_result.views.grupos_svc.threadlocals.get_log_ctx", return_value={"user_tabid": "123"})

    response = upsert_grupo(request)

    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.content)
    assert data["id"] == hashint(trecho_classe_id)
    assert signer.unsign_object(data["signed"]) == {
        "id": trecho_classe_id,
        "max_split_value": Decimal("150.00"),
        "ref_split_value": Decimal("150.00"),
        "user_tabid": "123",
    }
    svc.assert_called_once()
    assert len(svc.call_args[0]) == 1
    assert svc.call_args[0][0] == SearchItemHotOffer(
        origem_slug="sao-paulo-sp",
        cidade_origem_slug="sao-paulo",
        destino_slug="rio-de-janeiro-rj",
        cidade_destino_slug="rio-de-janeiro",
        tipo_assento="leito",
        datetime_ida="2025-02-13T12:00:00",
        vagas=2,
        max_split_value="150.0",  # type: ignore
        modelo_venda="marketplace",
        origem_id="1",  # type: ignore
        destino_id="2",  # type: ignore
        duracao_ida=360,
        id=None,
        capacidade="44",  # type: ignore
        extra_mkp_servico="beautiful_code",
        extra_mkp_extra="servico",  # type: ignore - o request.POST.dict() não parceira bem os dicionários
        extra_mkp_ota_config_id="2",  # type: ignore
        company_cnpj="12345678000199",
        company_id="1001",  # type: ignore
        extra_mkp_last_synced_at=None,
        extra_mkp_stopovers="{'dataCorrida': '2025-10-20', 'servico': 321}",  # type: ignore
    )


def test_view_upsert_grupo_new_front(rf, mocker):
    grupo = {
        "company_cnpj": "12345678000199",
        "origem_slug": "sao-paulo-sp",
        "cidade_origem_slug": "sao-paulo",
        "destino_slug": "rio-de-janeiro-rj",
        "cidade_destino_slug": "rio-de-janeiro",
        "tipo_assento": "leito",
        "datetime_ida": "2025-02-13T12:00:00",
        "vagas": "2",
        "max_split_value": 150.0,
        "modelo_venda": "marketplace",
        "company_id": 1001,
        "origem_id": 1,
        "destino_id": 2,
        "duracao_ida": 360,
        "capacidade": 44,
        "extra_mkp_servico": "beautiful_code",
        "extra_mkp_extra": {"dataCorrida": "2025-10-20", "servico": 321},
        "extra_mkp_ota_config_id": 2,
        "extra_mkp_stopovers": [
            {"dataCorrida": "2025-10-20", "servico": 321},
            {"dataCorrida": "2025-10-20", "servico": 321},
        ],
    }

    request = rf.post(
        "/api/groups/upsert",
        {"grupo": json.dumps(grupo)},
    )
    trecho_classe_id = 23124212
    svc = mocker.patch(
        "search_result.views.grupos_svc.upsert_grupo",
        return_value=(trecho_classe_id, Decimal("150.00")),
    )
    mocker.patch("search_result.views.grupos_svc.threadlocals.get_log_ctx", return_value={"user_tabid": "123"})

    response = upsert_grupo(request)

    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.content)
    assert data["id"] == hashint(trecho_classe_id)
    assert signer.unsign_object(data["signed"]) == {
        "id": trecho_classe_id,
        "max_split_value": Decimal("150.00"),
        "ref_split_value": Decimal("150.00"),
        "user_tabid": "123",
    }
    svc.assert_called_once()
    assert len(svc.call_args[0]) == 1
    assert svc.call_args[0][0] == SearchItemHotOffer(
        origem_slug="sao-paulo-sp",
        cidade_origem_slug="sao-paulo",
        destino_slug="rio-de-janeiro-rj",
        cidade_destino_slug="rio-de-janeiro",
        tipo_assento="leito",
        datetime_ida="2025-02-13T12:00:00",
        vagas=2,
        max_split_value=150.0,
        modelo_venda="marketplace",
        origem_id=1,
        destino_id=2,
        duracao_ida=360,
        id=None,
        capacidade=44,
        extra_mkp_servico="beautiful_code",
        extra_mkp_extra={"dataCorrida": "2025-10-20", "servico": 321},
        extra_mkp_ota_config_id=2,
        company_cnpj="12345678000199",
        company_id=1001,
        extra_mkp_last_synced_at=None,
        extra_mkp_stopovers=[
            {"dataCorrida": "2025-10-20", "servico": 321},
            {"dataCorrida": "2025-10-20", "servico": 321},
        ],
    )


def test_integracao_upsert_grupo(rf, mocker):
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    company = baker.make(Company, cnpj="12345678000199")
    grupo = {
        "company_cnpj": company.cnpj,
        "origem_slug": "sao-paulo-sp",
        "cidade_origem_slug": "sao-paulo",
        "destino_slug": "rio-de-janeiro-rj",
        "cidade_destino_slug": "rio-de-janeiro",
        "tipo_assento": "leito",
        "datetime_ida": "2025-02-13T12:00:00-03:00",
        "vagas": "2",
        "max_split_value": 150.0,
        "modelo_venda": "marketplace",
        "company_id": company.id,
        "origem_id": origem.id,
        "destino_id": destino.id,
        "duracao_ida": 360,
        "capacidade": 44,
        "extra_mkp_servico": "beautiful_code",
        "extra_mkp_extra": {"dataCorrida": "2025-10-20", "servico": 321},
        "extra_mkp_ota_config_id": 2,
        "extra_mkp_stopovers": [
            {"dataCorrida": "2025-10-20", "servico": 321},
            {"dataCorrida": "2025-10-20", "servico": 321},
        ],
    }

    request = rf.post(
        "/api/groups/upsert",
        {"grupo": json.dumps(grupo)},
    )
    mocker.patch("search_result.views.grupos_svc.threadlocals.get_log_ctx", return_value={"user_tabid": "123"})

    response = upsert_grupo(request)

    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.content)
    trecho_classe_id = unhashint(data["id"])
    assert signer.unsign_object(data["signed"]) == {
        "id": trecho_classe_id,
        "max_split_value": Decimal("150.00"),
        "ref_split_value": Decimal("150.00"),
        "user_tabid": "123",
    }
    tc = TrechoClasse.objects.get(id=trecho_classe_id)
    assert tc.grupo.company == company
    assert tc.grupo.status == Grupo.Status.TRAVEL_CONFIRMED
    assert tc.grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE
    assert tc.grupo_classe.tipo_assento == "leito"
    assert tc.vagas == 2
    assert tc.max_split_value == Decimal("150.00")


def test_svc_update_grupo_existente():
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    company = baker.make(Company)
    search_item = SearchItemHotOffer(
        origem_slug="sao-paulo-sp",
        cidade_origem_slug="sao-paulo",
        destino_slug="rio-de-janeiro-rj",
        cidade_destino_slug="rio-de-janeiro",
        tipo_assento="leito",
        datetime_ida="2025-02-13T12:01:00-03:00",
        vagas=2,
        max_split_value=150.0,
        modelo_venda="marketplace",
        origem_id=origem.id,
        destino_id=destino.id,
        duracao_ida=360,
        id=None,
        capacidade=44,
        extra_mkp_servico="BEAUTIFUL_CODE",
        extra_mkp_ota_config_id=2,
        extra_mkp_extra={"dataCorrida": "2025-10-20"},
        extra_mkp_last_synced_at=timezone.now().isoformat(),
        company_cnpj="12.345.678/0001-99",
        company_id=company.id,
    )
    grupo = baker.make(
        Grupo,
        company=company,
        rota__tipo=Rota.Tipos.MARKETPLACE,
        status=Grupo.Status.TRAVEL_CONFIRMED,
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    tc_duplicado = baker.make(
        TrechoClasse,
        datetime_ida=datetime(2025, 2, 13, 12, 1, tzinfo=ZoneInfo("America/Sao_Paulo")),
        grupo=grupo,
        grupo_classe=baker.make(GrupoClasse, tipo_assento=search_item.tipo_assento),
        extra={"mkp_servico": search_item.extra_mkp_servico},
        closed=True,
        closed_reason="[NOT_FOUND]",
        vagas=52,
        max_split_value=Decimal("85.00"),
    )
    expected_signed_id = hashint(tc_duplicado.id)
    search_item.id = expected_signed_id

    tc_id, max_split_value = upsert_grupo_svc(search_item)

    assert tc_id == tc_duplicado.id
    assert max_split_value == Decimal("150")
    tc_duplicado.refresh_from_db()
    assert tc_duplicado.vagas == search_item.vagas
    assert tc_duplicado.max_split_value == Decimal(str(search_item.max_split_value))
    assert tc_duplicado.closed is False
    assert tc_duplicado.closed_reason is None


def test_svc_upsert_grupo_novo_rota_antiga():
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    company = baker.make(Company)
    search_item = SearchItemHotOffer(
        origem_slug="sao-paulo-sp",
        cidade_origem_slug="sao-paulo",
        destino_slug="rio-de-janeiro-rj",
        cidade_destino_slug="rio-de-janeiro",
        tipo_assento="leito",
        datetime_ida="2025-02-13T12:01:00-03:00",
        vagas=2,
        max_split_value=150.0,
        modelo_venda="marketplace",
        origem_id=origem.id,
        destino_id=destino.id,
        duracao_ida=360,
        id=None,
        capacidade=44,
        extra_mkp_servico="BEAUTIFUL_CODE",
        extra_mkp_ota_config_id=2,
        extra_mkp_extra={"dataCorrida": "2025-10-20"},
        extra_mkp_stopovers=[{"IdViagem": 123}],
        extra_mkp_last_synced_at="2025-02-11T12:01:00-03:00",
        company_cnpj="12.345.678/0001-99",
        company_id=company.id,
    )
    rota = baker.make(
        Rota,
        tipo=Rota.Tipos.MARKETPLACE,
        origem=origem,
        destino=destino,
        duracao_total=timedelta(milliseconds=search_item.duracao_ida),
    )
    baker.make(TrechoVendido, origem=origem, destino=destino, rota=rota)

    tc_id, max_split_value = upsert_grupo_svc(search_item)

    assert max_split_value == Decimal("150.00")
    tc = TrechoClasse.objects.get(id=tc_id)
    assert tc.grupo.company == company
    assert tc.grupo.rota == rota
    assert tc.grupo.status == Grupo.Status.TRAVEL_CONFIRMED
    assert tc.grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE
    assert tc.grupo_classe.tipo_assento == search_item.tipo_assento
    assert tc.extra["mkp_servico"] == search_item.extra_mkp_servico
    assert tc.extra["mkp_extra"] == search_item.extra_mkp_extra
    assert tc.extra["mkp_stopovers"] == search_item.extra_mkp_stopovers
    assert tc.extra["mkp_ota_config_id"] == search_item.extra_mkp_ota_config_id
    assert tc.datetime_ida == datetime(2025, 2, 13, 12, 1, tzinfo=ZoneInfo("America/Sao_Paulo"))
    assert tc.vagas == search_item.vagas
    assert tc.grupo_classe.capacidade == search_item.capacidade
    assert tc.max_split_value == search_item.max_split_value
    assert ActivityLog.objects.filter(grupo=tc.grupo, type="search_marketplace_create_group").exists()


def test_svc_upsert_grupo_novo():
    origem = baker.make(LocalEmbarque)
    destino = baker.make(LocalEmbarque)
    company = baker.make(Company)
    search_item = SearchItemHotOffer(
        origem_slug="sao-paulo-sp",
        cidade_origem_slug="sao-paulo",
        destino_slug="rio-de-janeiro-rj",
        cidade_destino_slug="rio-de-janeiro",
        tipo_assento="leito",
        datetime_ida="2025-02-13T12:01:00-03:00",
        vagas=2,
        max_split_value=150.0,
        modelo_venda="marketplace",
        origem_id=origem.id,
        destino_id=destino.id,
        duracao_ida=360,
        id=None,
        capacidade=44,
        extra_mkp_servico="BEAUTIFUL_CODE",
        extra_mkp_ota_config_id=2,
        extra_mkp_extra={"dataCorrida": "2025-10-20"},
        extra_mkp_stopovers=None,
        extra_mkp_last_synced_at="2025-02-11T12:01:00-03:00",
        company_cnpj="12.345.678/0001-99",
        company_id=company.id,
    )

    tc_id, max_split_value = upsert_grupo_svc(search_item)

    assert max_split_value == Decimal("150.00")
    tc = TrechoClasse.objects.get(id=tc_id)
    assert tc.grupo.company == company
    assert tc.grupo.rota.origem == origem
    assert tc.grupo.rota.destino == destino
    assert tc.grupo.rota.duracao_total == timedelta(milliseconds=search_item.duracao_ida)
    assert tc.trecho_vendido.origem == origem
    assert tc.trecho_vendido.destino == destino
    assert tc.trecho_vendido.rota == tc.grupo.rota
    assert tc.grupo.status == Grupo.Status.TRAVEL_CONFIRMED
    assert tc.grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE
    assert tc.grupo_classe.tipo_assento == search_item.tipo_assento
    assert tc.extra["mkp_servico"] == search_item.extra_mkp_servico
    assert tc.extra["mkp_extra"] == search_item.extra_mkp_extra
    assert tc.extra["mkp_ota_config_id"] == search_item.extra_mkp_ota_config_id
    assert tc.datetime_ida == datetime(2025, 2, 13, 12, 1, tzinfo=ZoneInfo("America/Sao_Paulo"))
    assert tc.vagas == search_item.vagas
    assert tc.grupo_classe.capacidade == search_item.capacidade
    assert tc.max_split_value == search_item.max_split_value

    ckps = Checkpoint.objects.filter(rota=tc.grupo.rota).order_by("idx")
    assert ckps[0].local == origem
    assert ckps[0].idx == 0
    assert ckps[0].duracao == timedelta(milliseconds=0)
    assert ckps[1].local == destino
    assert ckps[1].idx == 1
    assert ckps[1].duracao == timedelta(milliseconds=search_item.duracao_ida)
    assert ActivityLog.objects.filter(grupo=tc.grupo, type="search_marketplace_create_group").exists()
