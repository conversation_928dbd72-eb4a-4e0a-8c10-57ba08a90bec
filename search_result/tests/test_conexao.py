import itertools
import json
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal

import pytest
import time_machine
from django.utils import timezone

from commons import dateutils
from commons.enum import ModeloVenda
from core.models_rota import <PERSON><PERSON><PERSON>, TrechoConexao
from search_result.adapter.local_embarque_adapter import LocalEmbarqueAdapter
from search_result.serializer.serializer_trecho_classe import parcelamento_from_value
from search_result.service.grupos_svc import (
    _check_arrival_datetimes_conexao,
    _create_additional_attrs,
    _get_map_trechos_classes_conexao,
    _mount_conexoes_map,
    _query_to_hydrate_groups,
    search_conexao,
    search_grupos_conexao,
    signed_dgroup,
)
from search_result.tests.conftest import _create_conexao, _create_trecho_classe
from search_result.utils.hashint import hashint
from search_result.views import search_conexaoV2

DEFAULT_TZ = timezone.get_default_timezone()
NOW: datetime = dateutils.today_at(hour=6)  # type: ignore


def test_create_additional_attrs():
    id_1 = 1
    id_2 = 2
    max_split_value_1 = Decimal("20.5")
    max_split_value_2 = Decimal("35.5")
    ref_split_value_1 = Decimal("30.5")
    ref_split_value_2 = Decimal("30.5")
    amostra = [
        {
            "id": id_1,
            "vagas": 5,
            "max_split_value": max_split_value_1,
            "ref_split_value": ref_split_value_1,
            "datetime_ida": "2024-11-01T05:00:00Z",
            "chegada_ida": "2024-11-01T23:00:00Z",
            "closed": False,
            "status": "pending",
        },
        {
            "id": id_2,
            "vagas": 12,
            "max_split_value": max_split_value_2,
            "ref_split_value": ref_split_value_2,
            "datetime_ida": "2024-11-02T01:00:00Z",
            "chegada_ida": "2024-11-02T03:00:00Z",
            "closed": False,
            "status": "pending",
        },
    ]

    expected = {
        "datetime_ida": "2024-11-01T05:00:00Z",
        "duracao_ida": 79200000,
        "id": f"{hashint(id_1)}:{hashint(id_2)}",
        "max_split_value": max_split_value_1 + max_split_value_2,
        "ref_split_value": ref_split_value_1 + ref_split_value_2,
        "parcelamento": parcelamento_from_value(max_split_value_1 + max_split_value_2),
        "vagas": 5,
        "is_conexao": True,
        "closed": False,
        "status": "pending",
        "trecho_alternativo": False,
    }

    assert expected == _create_additional_attrs(amostra)


def test_mount_conexoes_map(mocker):
    data_test = {
        "ids": [1, 2, 3, 4],
        "max_split_values": [Decimal("20.5"), Decimal("30.5"), Decimal("40.5"), Decimal("50.5")],
        "ref_split_values": [Decimal("30.5"), Decimal("30.5"), Decimal("30.5"), Decimal("30.5")],
        "vagas": [5, 12, 15, 10],
        "datetime_ida": [
            "2024-11-01T02:00:00Z",
            "2024-11-01T05:00:00Z",
            "2024-11-02T01:00:00Z",
            "2024-11-03T01:00:00Z",
        ],
        "chegada_ida": [
            "2024-11-01T04:00:00Z",
            "2024-11-01T08:00:00Z",
            "2024-11-02T03:00:00Z",
            "2024-11-03T04:00:00Z",
        ],
    }
    amostra = {
        1: [
            [
                {
                    "id": data_test["ids"][0],
                    "vagas": data_test["vagas"][0],
                    "max_split_value": data_test["max_split_values"][0],
                    "ref_split_value": data_test["ref_split_values"][0],
                    "datetime_ida": data_test["datetime_ida"][0],
                    "chegada_ida": data_test["chegada_ida"][0],
                    "closed": False,
                    "status": "pending",
                },
                {
                    "id": data_test["ids"][1],
                    "vagas": data_test["vagas"][1],
                    "max_split_value": data_test["max_split_values"][1],
                    "ref_split_value": data_test["ref_split_values"][1],
                    "datetime_ida": data_test["datetime_ida"][1],
                    "chegada_ida": data_test["chegada_ida"][1],
                    "closed": False,
                    "status": "pending",
                },
            ],
            [
                {
                    "id": data_test["ids"][2],
                    "vagas": data_test["vagas"][2],
                    "max_split_value": data_test["max_split_values"][2],
                    "ref_split_value": data_test["ref_split_values"][2],
                    "datetime_ida": data_test["datetime_ida"][2],
                    "chegada_ida": data_test["chegada_ida"][2],
                    "closed": False,
                    "status": "pending",
                },
                {
                    "id": data_test["ids"][3],
                    "vagas": data_test["vagas"][3],
                    "max_split_value": data_test["max_split_values"][3],
                    "ref_split_value": data_test["ref_split_values"][3],
                    "datetime_ida": data_test["datetime_ida"][3],
                    "chegada_ida": data_test["chegada_ida"][3],
                    "closed": False,
                    "status": "pending",
                },
            ],
        ]
    }

    mocker.patch("search_result.service.grupos_svc._check_arrival_datetimes_conexao", return_value=True)
    result = _mount_conexoes_map(amostra, {1: [None, None]}, {1: [None, None]}, {})

    conexoes = [[0, 2], [0, 3], [1, 2], [1, 3]]
    expected = []
    for first, second in conexoes:
        conexao_1 = amostra[1][0][first]
        conexao_2 = amostra[1][1][second - 2]

        conexao_1["id"] = hashint(conexao_1["id"]) if isinstance(conexao_1["id"], int) else conexao_1["id"]
        conexao_2["id"] = hashint(conexao_2["id"]) if isinstance(conexao_2["id"], int) else conexao_2["id"]
        conexao_1["signed"] = signed_dgroup(
            conexao_1["id"], conexao_1["max_split_value"], conexao_1["ref_split_value"], None
        )
        conexao_2["signed"] = signed_dgroup(
            conexao_2["id"], conexao_2["max_split_value"], conexao_2["ref_split_value"], None
        )

        conexao_obj = {
            "id": f"{hashint(data_test['ids'][first])}:{hashint(data_test['ids'][second])}",
            "vagas": min(data_test["vagas"][first], data_test["vagas"][second]),
            "datetime_ida": data_test["datetime_ida"][first],
            "max_split_value": sum([data_test["max_split_values"][first], data_test["max_split_values"][second]]),
            "ref_split_value": sum([data_test["ref_split_values"][first], data_test["ref_split_values"][second]]),
            "parcelamento": parcelamento_from_value(
                data_test["max_split_values"][first] + data_test["max_split_values"][second]
            ),
            "conexoes": (conexao_1, conexao_2),
            "duracao_ida": (
                datetime.fromisoformat(conexao_2["chegada_ida"]) - datetime.fromisoformat(conexao_1["datetime_ida"])
            ).total_seconds()
            * 1000,
            "is_conexao": True,
            "closed": False,
            "status": "pending",
            "trecho_alternativo": False,
        }
        expected.append(conexao_obj)

    assert result == expected


def test_search_grupos_conexao_without_results(conexao):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino
    _, _, groups, _, _, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.pk]) == 0


@time_machine.travel(NOW)
def test_search_grupos_conexao_one_trecho_classe(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino
    tc1 = _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    tc2 = _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 2
    assert len(groups[conexao.id][0]) == 1
    assert groups[conexao.id][0][0]["id"] == tc1.id
    assert len(groups[conexao.id][1]) == 1
    assert groups[conexao.id][1][0]["id"] == tc2.id

    assert len(locais_map) == 3
    assert len(itinerarios_map) == 2


@time_machine.travel(NOW)
def test_search_grupos_conexao_deleted_connection(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    conexao.deleted_at = dateutils.now()
    conexao.save()

    origem = conexao.cidade_origem
    destino = conexao.cidade_destino
    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 0
    assert len(locais_map) == 0
    assert len(itinerarios_map) == 0


@time_machine.travel(NOW)
def test_search_grupos_conexao_deleted_trecho_conexao(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    for tc in conexao.trechos_conexao.all():
        tc.deleted_at = dateutils.now()
        tc.save()

    origem = conexao.cidade_origem
    destino = conexao.cidade_destino
    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 0
    assert len(locais_map) == 0
    assert len(itinerarios_map) == 0


@time_machine.travel(NOW)
def test_search_grupos_conexao_many_trecho_classes(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    tcs_first_trecho = []
    for i in range(2):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    tcs_second_trecho = []
    for i in range(3):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i, minutes=50 + i),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 2
    assert len(groups[conexao.id][0]) == 2
    assert len(groups[conexao.id][1]) == 2

    assert len(locais_map) == 3
    assert len(itinerarios_map) == 2


@time_machine.travel(NOW)
def test_search_grupos_conexao_with_only_first_trecho(conexao, trecho_vendido_sp_sjc):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 0

    assert len(locais_map) == 0
    assert len(itinerarios_map) == 0


@time_machine.travel(NOW)
@pytest.mark.parametrize(
    "modelos_vendas,companies_bool",
    [
        ([ModeloVenda.BUSER, ModeloVenda.HIBRIDO], [True, True]),
        ([ModeloVenda.HIBRIDO, ModeloVenda.MARKETPLACE], [False, False]),
        ([ModeloVenda.MARKETPLACE, None], [False, None]),
        ([None, ModeloVenda.HIBRIDO], [None, False]),
        ([ModeloVenda.BUSER, None], [True, None]),
        ([ModeloVenda.BUSER, ModeloVenda.HIBRIDO], [None, None]),
        ([None, None], [True, False]),
        ([None, None], [None, None]),
    ],
)
def test_search_grupos_conexao_different_rules(
    trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, modelos_vendas, companies_bool, company_buser, company_pudim
):
    companies = []
    for company_bool in companies_bool:
        if company_bool is None:
            companies.append(None)
        if company_bool:
            companies.append(company_buser)
        else:
            companies.append(company_pudim)

    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    conexao = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        companies,
        modelos_vendas,
    )

    for _modelo, _company in itertools.product(
        [ModeloVenda.BUSER, ModeloVenda.HIBRIDO, ModeloVenda.MARKETPLACE], [company_buser, company_pudim, None]
    ):
        _create_trecho_classe(
            NOW + timedelta(hours=2),
            trecho_vendido_sp_sjc,
            vagas=10,
            split_value=Decimal("25.5"),
            modelo_venda=_modelo,
            company=_company,
        )

        _create_trecho_classe(
            NOW + timedelta(hours=2, minutes=59),
            trecho_vendido_sjc_rj,
            vagas=8,
            split_value=Decimal("35.5"),
            modelo_venda=_modelo,
            company=_company,
        )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(origem.slug, destino.slug, NOW)
    assert len(groups[conexao.id]) == 2
    assert len(locais_map) == 3
    assert len(itinerarios_map) == 2

    for i in range(2):
        _modelo = modelos_vendas[i]
        _company = companies[i]

        print(f"Asserting {_modelo} and {_company} has {len(groups[conexao.id][i])} on pos {i}")
        if _modelo is None and _company is None:
            # grab all models
            assert len(groups[conexao.id][i]) == 3 * 3
        elif _modelo is not None and _company is not None:
            assert len(groups[conexao.id][i]) == 1
        else:
            # either _modelo or _company is None
            assert len(groups[conexao.id][i]) == 3


def test_search_conexao_without_results(conexao):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 0


@time_machine.travel(NOW)
def test_search_conexao(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    tcs_first_trecho = []
    for i in range(2):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    # add in another day
    _create_trecho_classe(
        NOW + timedelta(days=1),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        dateutils.truncate(NOW) + timedelta(days=2, hours=2, minutes=0),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    tcs_second_trecho = []
    for i in range(3):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i, minutes=59),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    # add in another day
    _create_trecho_classe(
        NOW + timedelta(days=1, hours=1, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        dateutils.truncate(NOW) + timedelta(days=2, hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert list(data["locais"]) == [
        trecho_vendido_sp_sjc.origem.id,
        trecho_vendido_sp_sjc.destino.id,
        trecho_vendido_sjc_rj.destino.id,
    ]
    assert data["trecho"]["origem"]["name"] == origem.name
    assert data["trecho"]["destino"]["name"] == destino.name
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 2

    grupos = data["groups_by_date"][0]["grupos"]
    for i in range(2):
        tc1, tc2 = tcs_first_trecho[i], tcs_second_trecho[i]
        assert grupos[i]["id"] == f"{hashint(tc1.id)}:{hashint(tc2.id)}"
        assert grupos[i]["vagas"] == min(tc1.vagas, tc2.vagas)
        assert grupos[i]["parcelamento"] is not None
        assert len(grupos[i]["conexoes"]) == 2
        assert grupos[i]["conexoes"][0]["id"] == hashint(tc1.id)
        assert grupos[i]["conexoes"][1]["id"] == hashint(tc2.id)


@pytest.mark.parametrize("is_enabled", [True, False])
@time_machine.travel(NOW)
def test_search_conexao_gs_connections_enabled(
    rf, conexao, user, user_buser, globalsettings_mock, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, is_enabled
):
    globalsettings_mock("connections_enabled", is_enabled)
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    request = rf.get(
        "/api/search/conexao/v2",
        {
            "data_ida": dateutils.to_default_tz_required(NOW).strftime("%Y-%m-%d"),
            "origem_slug": origem.slug,
            "destino_slug": destino.slug,
        },
    )

    request.user = user_buser
    response = search_conexaoV2(request)
    data = json.loads(response.content)
    assert response.status_code == 200
    if is_enabled:
        assert len(data["groups_by_date"]) == 1
    else:
        assert data == {}


@time_machine.travel(NOW)
def test_search_conexao_with_only_first_trecho(conexao, trecho_vendido_sp_sjc):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    for i in range(2):
        _create_trecho_classe(
            NOW + timedelta(hours=2 + i),
            trecho_vendido_sp_sjc,
            vagas=10 + i,
            split_value=Decimal("25.5"),
            modelo_venda=ModeloVenda.BUSER,
        )

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 0


@time_machine.travel(NOW)
def test_search_conexao_with_only_second_trecho(conexao, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    for i in range(2):
        _create_trecho_classe(
            NOW + timedelta(hours=2 + i),
            trecho_vendido_sjc_rj,
            vagas=10 + i,
            split_value=Decimal("25.5"),
            modelo_venda=ModeloVenda.BUSER,
        )

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 0


@time_machine.travel(NOW)
def test_search_conexao_without_vagas(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    for i, vagas in enumerate([0, 10, 0]):
        _create_trecho_classe(
            NOW + timedelta(hours=2 + i),
            trecho_vendido_sp_sjc,
            vagas=vagas,
            split_value=Decimal("25.5"),
            modelo_venda=ModeloVenda.BUSER,
        )

    for i, vagas in enumerate([0, 8, 0]):
        _create_trecho_classe(
            NOW + timedelta(hours=2 + i, minutes=45),
            trecho_vendido_sjc_rj,
            vagas=vagas,
            split_value=Decimal("35.5"),
            modelo_venda=ModeloVenda.BUSER,
        )

    # the only combination that has vagas is the first trecho with the first conexao and the second on the second
    # conexao
    data = search_conexao(NOW, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 1
    assert data["groups_by_date"][0]["grupos"][0]["vagas"] == 8


@time_machine.travel(NOW)
def test_search_conexao_without_data_ida(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    tcs_first_trecho = []
    for i in range(2):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(days=i, minutes=15),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    tcs_second_trecho = []
    for i in range(3):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(days=i, minutes=45),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )
    data = search_conexao(None, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 2
    assert len(data["groups_by_date"][0]["grupos"]) == 1


@time_machine.travel(NOW)
def test_many_conexao_qty_queries(
    trecho_vendido_sp_sjc,
    trecho_vendido_sp_sjc_2,
    trecho_vendido_sjc_rj,
    company_buser,
    company_pudim,
    django_assert_max_num_queries,
    globalsettings_mock,
):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    cnx_1 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_between_conexoes=[timedelta(0)] * 2,
        max_time_between_conexoes=[timedelta(minutes=30)] * 2,
    )
    cnx_2 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc_2, trecho_vendido_sjc_rj],
        [company_pudim, company_buser],
        [None] * 2,
        min_time_between_conexoes=[timedelta(0)] * 2,
        max_time_between_conexoes=[timedelta(minutes=60)] * 2,
    )
    cnx_3 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [ModeloVenda.BUSER, ModeloVenda.MARKETPLACE],
        min_time_between_conexoes=[timedelta(0)] * 2,
        max_time_between_conexoes=[timedelta(minutes=90)] * 2,
    )
    _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc_2, trecho_vendido_sjc_rj],
        [None] * 2,
        [ModeloVenda.MARKETPLACE, ModeloVenda.BUSER],
        min_time_between_conexoes=[timedelta(0)] * 2,
        max_time_between_conexoes=[timedelta(minutes=120)] * 2,
    )

    for i in range(3):
        _create_trecho_classe(
            NOW + timedelta(hours=2),
            trecho_vendido_sp_sjc,
            vagas=10,
            split_value=Decimal("25.5"),
            modelo_venda=[ModeloVenda.BUSER, ModeloVenda.MARKETPLACE][i % 2],
            company=[None, company_pudim, company_buser][i],
        )

    for i in range(3):
        _create_trecho_classe(
            NOW + timedelta(hours=2),
            trecho_vendido_sp_sjc_2,
            vagas=10,
            split_value=Decimal("25.5"),
            modelo_venda=[ModeloVenda.BUSER, ModeloVenda.MARKETPLACE][i % 2],
            company=[None, company_pudim, company_buser][i],
        )

    for i in range(3):
        _create_trecho_classe(
            NOW + timedelta(hours=2, minutes=45),
            trecho_vendido_sjc_rj,
            vagas=8,
            split_value=Decimal("35.5"),
            modelo_venda=[ModeloVenda.BUSER, ModeloVenda.MARKETPLACE][i % 2],
            company=[None, company_pudim, company_buser][i],
        )
    with django_assert_max_num_queries(16):
        # === PREPARATION === 5
        # 1 Cidade + 1 LocalEmbarque (get_local_by_slug origem) = 2
        # 1 Cidade + 1 LocalEmbarque (get_local_by_slug destino) = 2
        # 1 TrechoConexao
        # === MAIN QUERY === 5
        # Globalssetings (1)
        # TrechoVendido (2) = Uma chamada pra cada perna
        # TrechoClasse(2) = Uma chamada pra cada perna
        # === SERIALIZATION === 5
        # Checkpoint (2) + Company (1) + LocalRetiradaMarketplace(2)
        # TOTAL: 15 queries
        (
            res_origem,
            res_destino,
            dgroups_by_conexao,
            itinerarios_map,
            locais_map,
            min_time_between_conexao_map,
            max_time_between_conexao_map,
            _,
        ) = search_grupos_conexao(origem.slug, destino.slug, None)

    assert origem.id == res_origem.local_id
    assert destino.id == res_destino.local_id
    assert len(dgroups_by_conexao) == 4

    for cnx_id, groups in dgroups_by_conexao.items():
        assert len(groups) == 2
        if cnx_id == cnx_1.id:
            assert len(groups[0]) == 3
            assert len(groups[1]) == 3
        elif cnx_id == cnx_2.id:
            assert len(groups[0]) == 3
            assert len(groups[1]) == 3
        elif cnx_id == cnx_3.id:
            assert len(groups[0]) == 3
            assert len(groups[1]) == 3
        else:
            assert len(groups[0]) == 3
            assert len(groups[1]) == 3

    assert len(itinerarios_map) == 2
    assert len(locais_map) == 3
    assert len(min_time_between_conexao_map) == 4
    assert len(max_time_between_conexao_map) == 4


@time_machine.travel(NOW)
@pytest.mark.parametrize("duracao_ida,next_perna_partida", [(6, 4), (3, 4), (6, 5)])
def test_search_conexao_second_trecho_another_day_madrugada(
    conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, duracao_ida, next_perna_partida
):
    # conexões de madrugada podem ser arriscadas

    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    _create_trecho_classe(
        dateutils.end_of_day(NOW) - timedelta(hours=4),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
        duracao_ida=timedelta(duracao_ida),
    )

    _create_trecho_classe(
        dateutils.end_of_day(NOW) + timedelta(hours=next_perna_partida),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 0


@pytest.mark.parametrize(
    "arrivals,departures,min_time_between_conexoes,max_time_between_conexoes,expected",
    [
        (
            [NOW],
            [NOW - timedelta(minutes=15)],
            [timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            False,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=2)],
            [timedelta(minutes=3), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            False,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=1)],
            [timedelta(minutes=1), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            True,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=2)],
            [timedelta(minutes=1), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            True,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=1)],
            [timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            True,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=16)],
            [timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            False,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=15)],
            [timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            True,
        ),
        (
            [NOW],
            [NOW + timedelta(minutes=14)],
            [timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(0)],
            True,
        ),
        ([NOW], [NOW + timedelta(minutes=14)], [timedelta(0), timedelta(0)], [timedelta(0), timedelta(0)], False),
        (
            [NOW, NOW + timedelta(minutes=30)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(0)],
            [timedelta(minutes=15), timedelta(minutes=15), timedelta(minutes=15), timedelta(0)],
            True,
        ),
        (
            [NOW, NOW + timedelta(minutes=30), NOW + timedelta(minutes=60)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45), NOW + timedelta(minutes=75)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0)],
            [
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(0),
            ],
            True,
        ),
        (
            [NOW, NOW + timedelta(minutes=30), NOW + timedelta(minutes=60)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45), NOW + timedelta(minutes=76)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0)],
            [
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(0),
            ],
            False,
        ),
        (
            [NOW, NOW + timedelta(minutes=30), NOW + timedelta(minutes=59)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45), NOW + timedelta(minutes=75)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0)],
            [
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(0),
            ],
            False,
        ),
        (
            [NOW, NOW + timedelta(minutes=30), NOW + timedelta(minutes=59)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45), NOW + timedelta(minutes=74)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0), timedelta(0)],
            [
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=14),
                timedelta(minutes=15),
                timedelta(0),
            ],
            True,
        ),
        (
            [NOW, NOW + timedelta(minutes=30), NOW + timedelta(minutes=59)],
            [NOW + timedelta(minutes=15), NOW + timedelta(minutes=45), NOW + timedelta(minutes=74)],
            [timedelta(0), timedelta(0), timedelta(0), timedelta(minutes=15), timedelta(0), timedelta(0)],
            [
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=15),
                timedelta(minutes=14),
                timedelta(minutes=15),
                timedelta(0),
            ],
            False,
        ),
    ],
)
def test_check_arrival_datetimes_conexao(
    arrivals, departures, min_time_between_conexoes, max_time_between_conexoes, expected
):
    trechos_classe = []
    for i in range(len(arrivals)):
        trechos_classe.append(
            {
                "chegada_ida": arrivals[i].isoformat(),
                "datetime_ida": arrivals[i].isoformat(),
            }
        )
        trechos_classe.append(
            {
                "chegada_ida": departures[i].isoformat(),
                "datetime_ida": departures[i].isoformat(),
            }
        )

    assert (
        _check_arrival_datetimes_conexao(trechos_classe, min_time_between_conexoes, max_time_between_conexoes)
        == expected
    )


@time_machine.travel(NOW)
def test_search_grupos_conexao_min_and_max_time_between_conexao_map(trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    min_time_conexoes_1 = [timedelta(minutes=5), timedelta(0)]
    max_time_conexoes_1 = [timedelta(minutes=10), timedelta(0)]
    conexao_1 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_conexoes_1,
        max_time_conexoes_1,
    )
    min_time_conexoes_2 = [timedelta(minutes=9), timedelta(0)]
    max_time_conexoes_2 = [timedelta(minutes=65), timedelta(0)]
    conexao_2 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_conexoes_2,
        max_time_conexoes_2,
    )

    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=39),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, _, _, _, min_time_between_conexao_map, max_time_between_conexao_map, _ = search_grupos_conexao(
        origem.slug, destino.slug, NOW
    )
    assert min_time_between_conexao_map[conexao_1.id] == min_time_conexoes_1
    assert min_time_between_conexao_map[conexao_2.id] == min_time_conexoes_2
    assert max_time_between_conexao_map[conexao_1.id] == max_time_conexoes_1
    assert max_time_between_conexao_map[conexao_2.id] == max_time_conexoes_2


@pytest.mark.parametrize(
    "data_ida,exibir_busca_sem_data,should_return_results",
    [
        (None, True, True),
        (None, False, False),
        (NOW, True, True),
        (NOW, False, True),
    ],
)
@time_machine.travel(NOW)
def test_exibir_busca_sem_data_on_search_grupos_conexao(
    trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, data_ida, exibir_busca_sem_data, should_return_results
):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        exibir_busca_sem_data=exibir_busca_sem_data,
    )

    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _, _, groups, itinerarios_map, locais_map, _, _, _ = search_grupos_conexao(
        origem.slug, destino.slug, data_ida=data_ida
    )
    if should_return_results:
        assert len(groups) >= 1
        assert len(locais_map) >= 1
        assert len(itinerarios_map) >= 1
    else:
        assert len(groups) == 0
        assert len(locais_map) == 0
        assert len(itinerarios_map) == 0


@time_machine.travel(NOW)
def test_hydrate_groups(mocker, conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    tcs_first_trecho = []
    for i in range(3):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    tcs_second_trecho = []
    for i in range(3):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i, minutes=59),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    def modify_some_groups(group_ids):
        trechos_classe_map = _query_to_hydrate_groups(group_ids)

        # remove vagas from the first leg
        trechos_classe_map[tcs_first_trecho[0].id].vagas_cleaned = 0
        trechos_classe_map[tcs_second_trecho[0].id].vagas_cleaned = 0

        # close the third leg travels
        trechos_classe_map[tcs_first_trecho[2].id].closed = True
        trechos_classe_map[tcs_second_trecho[2].id].closed = True
        return trechos_classe_map

    mocker.patch("search_result.service.grupos_svc._query_to_hydrate_groups", side_effect=modify_some_groups)

    groups_by_date = search_conexao(NOW, origem.slug, destino.slug)["groups_by_date"]
    assert len(groups_by_date) == 1
    assert len(groups_by_date[0]["grupos"]) == 1
    assert (
        groups_by_date[0]["grupos"][0]["id"] == f"{hashint(tcs_first_trecho[1].id)}:{hashint(tcs_second_trecho[1].id)}"
    )


@time_machine.travel(NOW)
def test_search_conexao_with_signed_group(conexao, trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = conexao.cidade_origem
    destino = conexao.cidade_destino

    tcs_first_trecho = []
    for i in range(2):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    tcs_second_trecho = []
    for i in range(3):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i, minutes=59),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    data = search_conexao(NOW, origem.slug, destino.slug)

    assert len(data["groups_by_date"]) == 1
    assert len(data["groups_by_date"][0]["grupos"]) == 2

    first = data["groups_by_date"][0]

    assert all("signed" in conexao for grupo in first["grupos"] for conexao in grupo["conexoes"])


@time_machine.travel(NOW)
@pytest.mark.parametrize("trecho_ids_mesmo_local", [{0}, {1}, {0, 1}])
def test_search_conexao_conexao_same_local(
    trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, trecho_vendido_sp_sjc_3, trecho_ids_mesmo_local
):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        conexao_em_mesmo_local=trecho_ids_mesmo_local,
    )

    tcs_first_trecho = []
    for i in range(2):
        tcs_first_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i),
                trecho_vendido_sp_sjc,
                vagas=10 + i,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )
    for i in range(2):
        _create_trecho_classe(
            NOW,
            trecho_vendido_sp_sjc_3,
            vagas=10,
            split_value=Decimal("25.5"),
            modelo_venda=ModeloVenda.BUSER,
        )
    tcs_second_trecho = []
    for i in range(2):
        tcs_second_trecho.append(
            _create_trecho_classe(
                NOW + timedelta(hours=2 + i, minutes=59),
                trecho_vendido_sjc_rj,
                vagas=8 + i,
                split_value=Decimal("35.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    data = search_conexao(NOW, origem.slug, destino.slug)
    assert list(data["locais"]) == [
        trecho_vendido_sp_sjc.origem.id,
        trecho_vendido_sp_sjc.destino.id,
        trecho_vendido_sjc_rj.destino.id,
    ]
    assert data["trecho"]["origem"]["name"] == origem.nickname
    assert data["trecho"]["destino"]["name"] == destino.nickname
    assert len(data["groups_by_date"]) == 1
    grupos_by_date = data["groups_by_date"][0]["grupos"]

    for grupo in grupos_by_date:
        conexoes = grupo["conexoes"]
        for i in range(len(conexoes) - 1):
            pivot = conexoes[i]
            next_pivot = conexoes[i + 1]
            assert pivot["destino_id"] == next_pivot["origem_id"]


@time_machine.travel(NOW)
def test_get_map_trechos_classes_conexao_num_queries(
    trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, django_assert_max_num_queries
):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    min_time_conexoes_1 = [timedelta(minutes=5), timedelta(0)]
    max_time_conexoes_1 = [timedelta(minutes=10), timedelta(0)]
    conexao_1 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_conexoes_1,
        max_time_conexoes_1,
    )
    min_time_conexoes_2 = [timedelta(minutes=9), timedelta(0)]
    max_time_conexoes_2 = [timedelta(minutes=65), timedelta(0)]
    conexao_2 = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_conexoes_2,
        max_time_conexoes_2,
    )

    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=10,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=39),
        trecho_vendido_sjc_rj,
        vagas=8,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )

    trechos_conexao = TrechoConexao.objects.select_related("origem", "destino").filter(
        conexao_id__in=[conexao_1.pk, conexao_2.pk]
    )

    with django_assert_max_num_queries(7):
        # === MAIN QUERY === 5
        # TrechoConexao (1)
        # Globalssetings (1)
        # TrechoVendido (2) = Uma chamada pra cada perna
        # TrechoClasse(2) = Uma chamada pra cada perna
        _get_map_trechos_classes_conexao(trechos_conexao, LocalEmbarqueAdapter(origem), LocalEmbarqueAdapter(destino))


@time_machine.travel(NOW)
def test_get_map_trechos_classe_conexao_sem_trechos(trecho_vendido_sp_sjc, trecho_vendido_sjc_rj):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_sjc_rj.destino
    min_time_conexoes = [timedelta(minutes=5), timedelta(0)]
    max_time_conexoes = [timedelta(minutes=10), timedelta(0)]
    conexao = _create_conexao(
        origem,
        destino,
        [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj],
        [None] * 2,
        [None] * 2,
        min_time_conexoes,
        max_time_conexoes,
    )

    trechos_conexao = TrechoConexao.objects.filter(conexao_id=conexao.pk)

    tcs_map = _get_map_trechos_classes_conexao(
        trechos_conexao, LocalEmbarqueAdapter(origem), LocalEmbarqueAdapter(destino)
    )
    assert not tcs_map


@time_machine.travel(NOW)
def test_get_map_trechos_classe_conexao_com_n_pernas(
    trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, trecho_vendido_rj_sp, django_assert_max_num_queries
):
    origem = trecho_vendido_sp_sjc.origem
    destino = trecho_vendido_rj_sp.destino

    conexao = Conexao.objects.create(cidade_origem=origem.cidade, cidade_destino=destino.cidade)

    min_time_between_conexoes = [timedelta(0), timedelta(0), timedelta(0)]
    max_time_between_conexoes = [timedelta(minutes=30), timedelta(minutes=60), timedelta(minutes=90)]

    trechos_vendidos = [trecho_vendido_sp_sjc, trecho_vendido_sjc_rj, trecho_vendido_rj_sp]
    expected_tc_map = defaultdict(list)
    trechos_conexao = []
    for i, trecho_vendido in enumerate(trechos_vendidos):
        # create TrechoConexao
        trechos_conexao.append(
            TrechoConexao.objects.create(
                idx=i,
                conexao=conexao,
                origem=trecho_vendido.origem.cidade,
                destino=trecho_vendido.destino.cidade,
                min_time_between_conexoes=min_time_between_conexoes[i],
                max_time_between_conexoes=max_time_between_conexoes[i],
                conexao_em_mesmo_local=False,
            )
        )
        key = "%s::%s" % (trecho_vendido.origem.cidade_id, trecho_vendido.destino.cidade_id)
        expected_tc_map[key].append(
            _create_trecho_classe(
                NOW + timedelta(hours=2, minutes=30 * i),
                trecho_vendido,
                vagas=10,
                split_value=Decimal("25.5"),
                modelo_venda=ModeloVenda.BUSER,
            )
        )

    with django_assert_max_num_queries(8):  # Uma a mais por causa de uma perna a mais
        trechos_classe_map = _get_map_trechos_classes_conexao(
            trechos_conexao, LocalEmbarqueAdapter(origem), LocalEmbarqueAdapter(destino)
        )

    assert expected_tc_map == trechos_classe_map


@time_machine.travel(NOW)
@pytest.mark.parametrize(
    "vagas, closed, grupo_status, grupo_hidden_for_pax, grupo_classe_closed, expect_tcs",
    [
        (10, False, "pending", False, False, True),
        (0, False, "pending", False, False, False),
        (10, True, "pending", False, False, False),
        (10, False, "canceled", False, False, False),
        (10, False, "pending", True, False, False),
        (10, False, "pending", False, True, False),
    ],
)
def test_get_map_trechos_classe_conexao_retorna_tcs_filtrados(
    vagas,
    closed,
    grupo_status,
    grupo_hidden_for_pax,
    grupo_classe_closed,
    expect_tcs,
    conexao,
    trecho_vendido_sp_sjc,
    trecho_vendido_sjc_rj,
):
    origem_primeira_perna = trecho_vendido_sp_sjc.origem
    destino_primeira_perna = trecho_vendido_sp_sjc.destino

    origem_segunda_perna = destino_primeira_perna
    destino_segunda_perna = trecho_vendido_sjc_rj.destino
    _create_trecho_classe(
        NOW + timedelta(hours=2),
        trecho_vendido_sp_sjc,
        vagas=vagas,
        closed=closed,
        status=grupo_status,
        hidden_for_pax=grupo_hidden_for_pax,
        grupo_classe_closed=grupo_classe_closed,
        split_value=Decimal("25.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    _create_trecho_classe(
        NOW + timedelta(hours=2, minutes=59),
        trecho_vendido_sjc_rj,
        vagas=vagas,
        closed=closed,
        status=grupo_status,
        hidden_for_pax=grupo_hidden_for_pax,
        grupo_classe_closed=grupo_classe_closed,
        split_value=Decimal("35.5"),
        modelo_venda=ModeloVenda.BUSER,
    )
    trechos_conexao = TrechoConexao.objects.filter(conexao_id=conexao.pk)

    tcs = _get_map_trechos_classes_conexao(
        trechos_conexao, LocalEmbarqueAdapter(origem_primeira_perna), LocalEmbarqueAdapter(destino_segunda_perna)
    )
    if expect_tcs:
        key_primeira_perna = "%s::%s" % (origem_primeira_perna.cidade_id, destino_primeira_perna.cidade_id)
        key_segunda_perna = "%s::%s" % (origem_segunda_perna.cidade_id, destino_segunda_perna.cidade_id)
        assert tcs[key_primeira_perna]
        assert tcs[key_segunda_perna]
    else:
        assert not tcs
